"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9586],{41746:(t,e,a)=>{a.d(e,{CP:()=>h,HT:()=>u,PB:()=>d,aC:()=>o,lC:()=>s,m:()=>l,tk:()=>r});var n=a(74504),i=a(33572),r=(0,n.K2)((t,e)=>{let a=t.append("rect");if(a.attr("x",e.x),a.attr("y",e.y),a.attr("fill",e.fill),a.attr("stroke",e.stroke),a.attr("width",e.width),a.attr("height",e.height),e.name&&a.attr("name",e.name),e.rx&&a.attr("rx",e.rx),e.ry&&a.attr("ry",e.ry),void 0!==e.attrs)for(let t in e.attrs)a.attr(t,e.attrs[t]);return e.class&&a.attr("class",e.class),a},"drawRect"),s=(0,n.K2)((t,e)=>{r(t,{x:e.startx,y:e.starty,width:e.stopx-e.startx,height:e.stopy-e.starty,fill:e.fill,stroke:e.stroke,class:"rect"}).lower()},"drawBackgroundRect"),l=(0,n.K2)((t,e)=>{let a=e.text.replace(n.H1," "),i=t.append("text");i.attr("x",e.x),i.attr("y",e.y),i.attr("class","legend"),i.style("text-anchor",e.anchor),e.class&&i.attr("class",e.class);let r=i.append("tspan");return r.attr("x",e.x+2*e.textMargin),r.text(a),i},"drawText"),o=(0,n.K2)((t,e,a,n)=>{let r=t.append("image");r.attr("x",e),r.attr("y",a);let s=(0,i.J)(n);r.attr("xlink:href",s)},"drawImage"),h=(0,n.K2)((t,e,a,n)=>{let r=t.append("use");r.attr("x",e),r.attr("y",a);let s=(0,i.J)(n);r.attr("xlink:href",`#${s}`)},"drawEmbeddedImage"),d=(0,n.K2)(()=>({x:0,y:0,width:100,height:100,fill:"#EDF2AE",stroke:"#666",anchor:"start",rx:0,ry:0}),"getNoteRect"),u=(0,n.K2)(()=>({x:0,y:0,width:100,height:100,"text-anchor":"start",style:"#666",textMargin:0,rx:0,ry:0,tspan:!0}),"getTextObj")},99586:(t,e,a)=>{a.d(e,{diagram:()=>tv});var n,i=a(41746),r=a(75868),s=a(74504),l=a(55508),o=a(33572),h=function(){var t=(0,s.K2)(function(t,e,a,n){for(a=a||{},n=t.length;n--;a[t[n]]=e);return a},"o"),e=[1,24],a=[1,25],n=[1,26],i=[1,27],r=[1,28],l=[1,63],o=[1,64],h=[1,65],d=[1,66],u=[1,67],p=[1,68],y=[1,69],b=[1,29],g=[1,30],f=[1,31],x=[1,32],_=[1,33],m=[1,34],E=[1,35],A=[1,36],S=[1,37],C=[1,38],k=[1,39],O=[1,40],w=[1,41],T=[1,42],v=[1,43],R=[1,44],D=[1,45],N=[1,46],P=[1,47],B=[1,48],M=[1,50],j=[1,51],I=[1,52],K=[1,53],L=[1,54],Y=[1,55],U=[1,56],F=[1,57],X=[1,58],z=[1,59],W=[1,60],Q=[14,42],$=[14,34,36,37,38,39,40,41,42,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74],H=[12,14,34,36,37,38,39,40,41,42,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74],V=[1,82],q=[1,83],G=[1,84],J=[1,85],Z=[12,14,42],tt=[12,14,33,42],te=[12,14,33,42,76,77,79,80],ta=[12,33],tn=[34,36,37,38,39,40,41,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74],ti={trace:(0,s.K2)(function(){},"trace"),yy:{},symbols_:{error:2,start:3,mermaidDoc:4,direction:5,direction_tb:6,direction_bt:7,direction_rl:8,direction_lr:9,graphConfig:10,C4_CONTEXT:11,NEWLINE:12,statements:13,EOF:14,C4_CONTAINER:15,C4_COMPONENT:16,C4_DYNAMIC:17,C4_DEPLOYMENT:18,otherStatements:19,diagramStatements:20,otherStatement:21,title:22,accDescription:23,acc_title:24,acc_title_value:25,acc_descr:26,acc_descr_value:27,acc_descr_multiline_value:28,boundaryStatement:29,boundaryStartStatement:30,boundaryStopStatement:31,boundaryStart:32,LBRACE:33,ENTERPRISE_BOUNDARY:34,attributes:35,SYSTEM_BOUNDARY:36,BOUNDARY:37,CONTAINER_BOUNDARY:38,NODE:39,NODE_L:40,NODE_R:41,RBRACE:42,diagramStatement:43,PERSON:44,PERSON_EXT:45,SYSTEM:46,SYSTEM_DB:47,SYSTEM_QUEUE:48,SYSTEM_EXT:49,SYSTEM_EXT_DB:50,SYSTEM_EXT_QUEUE:51,CONTAINER:52,CONTAINER_DB:53,CONTAINER_QUEUE:54,CONTAINER_EXT:55,CONTAINER_EXT_DB:56,CONTAINER_EXT_QUEUE:57,COMPONENT:58,COMPONENT_DB:59,COMPONENT_QUEUE:60,COMPONENT_EXT:61,COMPONENT_EXT_DB:62,COMPONENT_EXT_QUEUE:63,REL:64,BIREL:65,REL_U:66,REL_D:67,REL_L:68,REL_R:69,REL_B:70,REL_INDEX:71,UPDATE_EL_STYLE:72,UPDATE_REL_STYLE:73,UPDATE_LAYOUT_CONFIG:74,attribute:75,STR:76,STR_KEY:77,STR_VALUE:78,ATTRIBUTE:79,ATTRIBUTE_EMPTY:80,$accept:0,$end:1},terminals_:{2:"error",6:"direction_tb",7:"direction_bt",8:"direction_rl",9:"direction_lr",11:"C4_CONTEXT",12:"NEWLINE",14:"EOF",15:"C4_CONTAINER",16:"C4_COMPONENT",17:"C4_DYNAMIC",18:"C4_DEPLOYMENT",22:"title",23:"accDescription",24:"acc_title",25:"acc_title_value",26:"acc_descr",27:"acc_descr_value",28:"acc_descr_multiline_value",33:"LBRACE",34:"ENTERPRISE_BOUNDARY",36:"SYSTEM_BOUNDARY",37:"BOUNDARY",38:"CONTAINER_BOUNDARY",39:"NODE",40:"NODE_L",41:"NODE_R",42:"RBRACE",44:"PERSON",45:"PERSON_EXT",46:"SYSTEM",47:"SYSTEM_DB",48:"SYSTEM_QUEUE",49:"SYSTEM_EXT",50:"SYSTEM_EXT_DB",51:"SYSTEM_EXT_QUEUE",52:"CONTAINER",53:"CONTAINER_DB",54:"CONTAINER_QUEUE",55:"CONTAINER_EXT",56:"CONTAINER_EXT_DB",57:"CONTAINER_EXT_QUEUE",58:"COMPONENT",59:"COMPONENT_DB",60:"COMPONENT_QUEUE",61:"COMPONENT_EXT",62:"COMPONENT_EXT_DB",63:"COMPONENT_EXT_QUEUE",64:"REL",65:"BIREL",66:"REL_U",67:"REL_D",68:"REL_L",69:"REL_R",70:"REL_B",71:"REL_INDEX",72:"UPDATE_EL_STYLE",73:"UPDATE_REL_STYLE",74:"UPDATE_LAYOUT_CONFIG",76:"STR",77:"STR_KEY",78:"STR_VALUE",79:"ATTRIBUTE",80:"ATTRIBUTE_EMPTY"},productions_:[0,[3,1],[3,1],[5,1],[5,1],[5,1],[5,1],[4,1],[10,4],[10,4],[10,4],[10,4],[10,4],[13,1],[13,1],[13,2],[19,1],[19,2],[19,3],[21,1],[21,1],[21,2],[21,2],[21,1],[29,3],[30,3],[30,3],[30,4],[32,2],[32,2],[32,2],[32,2],[32,2],[32,2],[32,2],[31,1],[20,1],[20,2],[20,3],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,1],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[35,1],[35,2],[75,1],[75,2],[75,1],[75,1]],performAction:(0,s.K2)(function(t,e,a,n,i,r,s){var l=r.length-1;switch(i){case 3:n.setDirection("TB");break;case 4:n.setDirection("BT");break;case 5:n.setDirection("RL");break;case 6:n.setDirection("LR");break;case 8:case 9:case 10:case 11:case 12:n.setC4Type(r[l-3]);break;case 19:n.setTitle(r[l].substring(6)),this.$=r[l].substring(6);break;case 20:n.setAccDescription(r[l].substring(15)),this.$=r[l].substring(15);break;case 21:this.$=r[l].trim(),n.setTitle(this.$);break;case 22:case 23:this.$=r[l].trim(),n.setAccDescription(this.$);break;case 28:r[l].splice(2,0,"ENTERPRISE"),n.addPersonOrSystemBoundary(...r[l]),this.$=r[l];break;case 29:r[l].splice(2,0,"SYSTEM"),n.addPersonOrSystemBoundary(...r[l]),this.$=r[l];break;case 30:n.addPersonOrSystemBoundary(...r[l]),this.$=r[l];break;case 31:r[l].splice(2,0,"CONTAINER"),n.addContainerBoundary(...r[l]),this.$=r[l];break;case 32:n.addDeploymentNode("node",...r[l]),this.$=r[l];break;case 33:n.addDeploymentNode("nodeL",...r[l]),this.$=r[l];break;case 34:n.addDeploymentNode("nodeR",...r[l]),this.$=r[l];break;case 35:n.popBoundaryParseStack();break;case 39:n.addPersonOrSystem("person",...r[l]),this.$=r[l];break;case 40:n.addPersonOrSystem("external_person",...r[l]),this.$=r[l];break;case 41:n.addPersonOrSystem("system",...r[l]),this.$=r[l];break;case 42:n.addPersonOrSystem("system_db",...r[l]),this.$=r[l];break;case 43:n.addPersonOrSystem("system_queue",...r[l]),this.$=r[l];break;case 44:n.addPersonOrSystem("external_system",...r[l]),this.$=r[l];break;case 45:n.addPersonOrSystem("external_system_db",...r[l]),this.$=r[l];break;case 46:n.addPersonOrSystem("external_system_queue",...r[l]),this.$=r[l];break;case 47:n.addContainer("container",...r[l]),this.$=r[l];break;case 48:n.addContainer("container_db",...r[l]),this.$=r[l];break;case 49:n.addContainer("container_queue",...r[l]),this.$=r[l];break;case 50:n.addContainer("external_container",...r[l]),this.$=r[l];break;case 51:n.addContainer("external_container_db",...r[l]),this.$=r[l];break;case 52:n.addContainer("external_container_queue",...r[l]),this.$=r[l];break;case 53:n.addComponent("component",...r[l]),this.$=r[l];break;case 54:n.addComponent("component_db",...r[l]),this.$=r[l];break;case 55:n.addComponent("component_queue",...r[l]),this.$=r[l];break;case 56:n.addComponent("external_component",...r[l]),this.$=r[l];break;case 57:n.addComponent("external_component_db",...r[l]),this.$=r[l];break;case 58:n.addComponent("external_component_queue",...r[l]),this.$=r[l];break;case 60:n.addRel("rel",...r[l]),this.$=r[l];break;case 61:n.addRel("birel",...r[l]),this.$=r[l];break;case 62:n.addRel("rel_u",...r[l]),this.$=r[l];break;case 63:n.addRel("rel_d",...r[l]),this.$=r[l];break;case 64:n.addRel("rel_l",...r[l]),this.$=r[l];break;case 65:n.addRel("rel_r",...r[l]),this.$=r[l];break;case 66:n.addRel("rel_b",...r[l]),this.$=r[l];break;case 67:r[l].splice(0,1),n.addRel("rel",...r[l]),this.$=r[l];break;case 68:n.updateElStyle("update_el_style",...r[l]),this.$=r[l];break;case 69:n.updateRelStyle("update_rel_style",...r[l]),this.$=r[l];break;case 70:n.updateLayoutConfig("update_layout_config",...r[l]),this.$=r[l];break;case 71:this.$=[r[l]];break;case 72:r[l].unshift(r[l-1]),this.$=r[l];break;case 73:case 75:this.$=r[l].trim();break;case 74:let o={};o[r[l-1].trim()]=r[l].trim(),this.$=o;break;case 76:this.$=""}},"anonymous"),table:[{3:1,4:2,5:3,6:[1,5],7:[1,6],8:[1,7],9:[1,8],10:4,11:[1,9],15:[1,10],16:[1,11],17:[1,12],18:[1,13]},{1:[3]},{1:[2,1]},{1:[2,2]},{1:[2,7]},{1:[2,3]},{1:[2,4]},{1:[2,5]},{1:[2,6]},{12:[1,14]},{12:[1,15]},{12:[1,16]},{12:[1,17]},{12:[1,18]},{13:19,19:20,20:21,21:22,22:e,23:a,24:n,26:i,28:r,29:49,30:61,32:62,34:l,36:o,37:h,38:d,39:u,40:p,41:y,43:23,44:b,45:g,46:f,47:x,48:_,49:m,50:E,51:A,52:S,53:C,54:k,55:O,56:w,57:T,58:v,59:R,60:D,61:N,62:P,63:B,64:M,65:j,66:I,67:K,68:L,69:Y,70:U,71:F,72:X,73:z,74:W},{13:70,19:20,20:21,21:22,22:e,23:a,24:n,26:i,28:r,29:49,30:61,32:62,34:l,36:o,37:h,38:d,39:u,40:p,41:y,43:23,44:b,45:g,46:f,47:x,48:_,49:m,50:E,51:A,52:S,53:C,54:k,55:O,56:w,57:T,58:v,59:R,60:D,61:N,62:P,63:B,64:M,65:j,66:I,67:K,68:L,69:Y,70:U,71:F,72:X,73:z,74:W},{13:71,19:20,20:21,21:22,22:e,23:a,24:n,26:i,28:r,29:49,30:61,32:62,34:l,36:o,37:h,38:d,39:u,40:p,41:y,43:23,44:b,45:g,46:f,47:x,48:_,49:m,50:E,51:A,52:S,53:C,54:k,55:O,56:w,57:T,58:v,59:R,60:D,61:N,62:P,63:B,64:M,65:j,66:I,67:K,68:L,69:Y,70:U,71:F,72:X,73:z,74:W},{13:72,19:20,20:21,21:22,22:e,23:a,24:n,26:i,28:r,29:49,30:61,32:62,34:l,36:o,37:h,38:d,39:u,40:p,41:y,43:23,44:b,45:g,46:f,47:x,48:_,49:m,50:E,51:A,52:S,53:C,54:k,55:O,56:w,57:T,58:v,59:R,60:D,61:N,62:P,63:B,64:M,65:j,66:I,67:K,68:L,69:Y,70:U,71:F,72:X,73:z,74:W},{13:73,19:20,20:21,21:22,22:e,23:a,24:n,26:i,28:r,29:49,30:61,32:62,34:l,36:o,37:h,38:d,39:u,40:p,41:y,43:23,44:b,45:g,46:f,47:x,48:_,49:m,50:E,51:A,52:S,53:C,54:k,55:O,56:w,57:T,58:v,59:R,60:D,61:N,62:P,63:B,64:M,65:j,66:I,67:K,68:L,69:Y,70:U,71:F,72:X,73:z,74:W},{14:[1,74]},t(Q,[2,13],{43:23,29:49,30:61,32:62,20:75,34:l,36:o,37:h,38:d,39:u,40:p,41:y,44:b,45:g,46:f,47:x,48:_,49:m,50:E,51:A,52:S,53:C,54:k,55:O,56:w,57:T,58:v,59:R,60:D,61:N,62:P,63:B,64:M,65:j,66:I,67:K,68:L,69:Y,70:U,71:F,72:X,73:z,74:W}),t(Q,[2,14]),t($,[2,16],{12:[1,76]}),t(Q,[2,36],{12:[1,77]}),t(H,[2,19]),t(H,[2,20]),{25:[1,78]},{27:[1,79]},t(H,[2,23]),{35:80,75:81,76:V,77:q,79:G,80:J},{35:86,75:81,76:V,77:q,79:G,80:J},{35:87,75:81,76:V,77:q,79:G,80:J},{35:88,75:81,76:V,77:q,79:G,80:J},{35:89,75:81,76:V,77:q,79:G,80:J},{35:90,75:81,76:V,77:q,79:G,80:J},{35:91,75:81,76:V,77:q,79:G,80:J},{35:92,75:81,76:V,77:q,79:G,80:J},{35:93,75:81,76:V,77:q,79:G,80:J},{35:94,75:81,76:V,77:q,79:G,80:J},{35:95,75:81,76:V,77:q,79:G,80:J},{35:96,75:81,76:V,77:q,79:G,80:J},{35:97,75:81,76:V,77:q,79:G,80:J},{35:98,75:81,76:V,77:q,79:G,80:J},{35:99,75:81,76:V,77:q,79:G,80:J},{35:100,75:81,76:V,77:q,79:G,80:J},{35:101,75:81,76:V,77:q,79:G,80:J},{35:102,75:81,76:V,77:q,79:G,80:J},{35:103,75:81,76:V,77:q,79:G,80:J},{35:104,75:81,76:V,77:q,79:G,80:J},t(Z,[2,59]),{35:105,75:81,76:V,77:q,79:G,80:J},{35:106,75:81,76:V,77:q,79:G,80:J},{35:107,75:81,76:V,77:q,79:G,80:J},{35:108,75:81,76:V,77:q,79:G,80:J},{35:109,75:81,76:V,77:q,79:G,80:J},{35:110,75:81,76:V,77:q,79:G,80:J},{35:111,75:81,76:V,77:q,79:G,80:J},{35:112,75:81,76:V,77:q,79:G,80:J},{35:113,75:81,76:V,77:q,79:G,80:J},{35:114,75:81,76:V,77:q,79:G,80:J},{35:115,75:81,76:V,77:q,79:G,80:J},{20:116,29:49,30:61,32:62,34:l,36:o,37:h,38:d,39:u,40:p,41:y,43:23,44:b,45:g,46:f,47:x,48:_,49:m,50:E,51:A,52:S,53:C,54:k,55:O,56:w,57:T,58:v,59:R,60:D,61:N,62:P,63:B,64:M,65:j,66:I,67:K,68:L,69:Y,70:U,71:F,72:X,73:z,74:W},{12:[1,118],33:[1,117]},{35:119,75:81,76:V,77:q,79:G,80:J},{35:120,75:81,76:V,77:q,79:G,80:J},{35:121,75:81,76:V,77:q,79:G,80:J},{35:122,75:81,76:V,77:q,79:G,80:J},{35:123,75:81,76:V,77:q,79:G,80:J},{35:124,75:81,76:V,77:q,79:G,80:J},{35:125,75:81,76:V,77:q,79:G,80:J},{14:[1,126]},{14:[1,127]},{14:[1,128]},{14:[1,129]},{1:[2,8]},t(Q,[2,15]),t($,[2,17],{21:22,19:130,22:e,23:a,24:n,26:i,28:r}),t(Q,[2,37],{19:20,20:21,21:22,43:23,29:49,30:61,32:62,13:131,22:e,23:a,24:n,26:i,28:r,34:l,36:o,37:h,38:d,39:u,40:p,41:y,44:b,45:g,46:f,47:x,48:_,49:m,50:E,51:A,52:S,53:C,54:k,55:O,56:w,57:T,58:v,59:R,60:D,61:N,62:P,63:B,64:M,65:j,66:I,67:K,68:L,69:Y,70:U,71:F,72:X,73:z,74:W}),t(H,[2,21]),t(H,[2,22]),t(Z,[2,39]),t(tt,[2,71],{75:81,35:132,76:V,77:q,79:G,80:J}),t(te,[2,73]),{78:[1,133]},t(te,[2,75]),t(te,[2,76]),t(Z,[2,40]),t(Z,[2,41]),t(Z,[2,42]),t(Z,[2,43]),t(Z,[2,44]),t(Z,[2,45]),t(Z,[2,46]),t(Z,[2,47]),t(Z,[2,48]),t(Z,[2,49]),t(Z,[2,50]),t(Z,[2,51]),t(Z,[2,52]),t(Z,[2,53]),t(Z,[2,54]),t(Z,[2,55]),t(Z,[2,56]),t(Z,[2,57]),t(Z,[2,58]),t(Z,[2,60]),t(Z,[2,61]),t(Z,[2,62]),t(Z,[2,63]),t(Z,[2,64]),t(Z,[2,65]),t(Z,[2,66]),t(Z,[2,67]),t(Z,[2,68]),t(Z,[2,69]),t(Z,[2,70]),{31:134,42:[1,135]},{12:[1,136]},{33:[1,137]},t(ta,[2,28]),t(ta,[2,29]),t(ta,[2,30]),t(ta,[2,31]),t(ta,[2,32]),t(ta,[2,33]),t(ta,[2,34]),{1:[2,9]},{1:[2,10]},{1:[2,11]},{1:[2,12]},t($,[2,18]),t(Q,[2,38]),t(tt,[2,72]),t(te,[2,74]),t(Z,[2,24]),t(Z,[2,35]),t(tn,[2,25]),t(tn,[2,26],{12:[1,138]}),t(tn,[2,27])],defaultActions:{2:[2,1],3:[2,2],4:[2,7],5:[2,3],6:[2,4],7:[2,5],8:[2,6],74:[2,8],126:[2,9],127:[2,10],128:[2,11],129:[2,12]},parseError:(0,s.K2)(function(t,e){if(e.recoverable)this.trace(t);else{var a=Error(t);throw a.hash=e,a}},"parseError"),parse:(0,s.K2)(function(t){var e=this,a=[0],n=[],i=[null],r=[],l=this.table,o="",h=0,d=0,u=0,p=r.slice.call(arguments,1),y=Object.create(this.lexer),b={};for(var g in this.yy)Object.prototype.hasOwnProperty.call(this.yy,g)&&(b[g]=this.yy[g]);y.setInput(t,b),b.lexer=y,b.parser=this,void 0===y.yylloc&&(y.yylloc={});var f=y.yylloc;r.push(f);var x=y.options&&y.options.ranges;function _(){var t;return"number"!=typeof(t=n.pop()||y.lex()||1)&&(t instanceof Array&&(t=(n=t).pop()),t=e.symbols_[t]||t),t}"function"==typeof b.parseError?this.parseError=b.parseError:this.parseError=Object.getPrototypeOf(this).parseError,(0,s.K2)(function(t){a.length=a.length-2*t,i.length=i.length-t,r.length=r.length-t},"popStack"),(0,s.K2)(_,"lex");for(var m,E,A,S,C,k,O,w,T,v={};;){if(A=a[a.length-1],this.defaultActions[A]?S=this.defaultActions[A]:(null==m&&(m=_()),S=l[A]&&l[A][m]),void 0===S||!S.length||!S[0]){var R="";for(k in T=[],l[A])this.terminals_[k]&&k>2&&T.push("'"+this.terminals_[k]+"'");R=y.showPosition?"Parse error on line "+(h+1)+":\n"+y.showPosition()+"\nExpecting "+T.join(", ")+", got '"+(this.terminals_[m]||m)+"'":"Parse error on line "+(h+1)+": Unexpected "+(1==m?"end of input":"'"+(this.terminals_[m]||m)+"'"),this.parseError(R,{text:y.match,token:this.terminals_[m]||m,line:y.yylineno,loc:f,expected:T})}if(S[0]instanceof Array&&S.length>1)throw Error("Parse Error: multiple actions possible at state: "+A+", token: "+m);switch(S[0]){case 1:a.push(m),i.push(y.yytext),r.push(y.yylloc),a.push(S[1]),m=null,E?(m=E,E=null):(d=y.yyleng,o=y.yytext,h=y.yylineno,f=y.yylloc,u>0&&u--);break;case 2:if(O=this.productions_[S[1]][1],v.$=i[i.length-O],v._$={first_line:r[r.length-(O||1)].first_line,last_line:r[r.length-1].last_line,first_column:r[r.length-(O||1)].first_column,last_column:r[r.length-1].last_column},x&&(v._$.range=[r[r.length-(O||1)].range[0],r[r.length-1].range[1]]),void 0!==(C=this.performAction.apply(v,[o,d,h,b,S[1],i,r].concat(p))))return C;O&&(a=a.slice(0,-1*O*2),i=i.slice(0,-1*O),r=r.slice(0,-1*O)),a.push(this.productions_[S[1]][0]),i.push(v.$),r.push(v._$),w=l[a[a.length-2]][a[a.length-1]],a.push(w);break;case 3:return!0}}return!0},"parse")};function tr(){this.yy={}}return ti.lexer={EOF:1,parseError:(0,s.K2)(function(t,e){if(this.yy.parser)this.yy.parser.parseError(t,e);else throw Error(t)},"parseError"),setInput:(0,s.K2)(function(t,e){return this.yy=e||this.yy||{},this._input=t,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:(0,s.K2)(function(){var t=this._input[0];return this.yytext+=t,this.yyleng++,this.offset++,this.match+=t,this.matched+=t,t.match(/(?:\r\n?|\n).*/g)?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),t},"input"),unput:(0,s.K2)(function(t){var e=t.length,a=t.split(/(?:\r\n?|\n)/g);this._input=t+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-e),this.offset-=e;var n=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),a.length-1&&(this.yylineno-=a.length-1);var i=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:a?(a.length===n.length?this.yylloc.first_column:0)+n[n.length-a.length].length-a[0].length:this.yylloc.first_column-e},this.options.ranges&&(this.yylloc.range=[i[0],i[0]+this.yyleng-e]),this.yyleng=this.yytext.length,this},"unput"),more:(0,s.K2)(function(){return this._more=!0,this},"more"),reject:(0,s.K2)(function(){return this.options.backtrack_lexer?(this._backtrack=!0,this):this.parseError("Lexical error on line "+(this.yylineno+1)+". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})},"reject"),less:(0,s.K2)(function(t){this.unput(this.match.slice(t))},"less"),pastInput:(0,s.K2)(function(){var t=this.matched.substr(0,this.matched.length-this.match.length);return(t.length>20?"...":"")+t.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:(0,s.K2)(function(){var t=this.match;return t.length<20&&(t+=this._input.substr(0,20-t.length)),(t.substr(0,20)+(t.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:(0,s.K2)(function(){var t=this.pastInput(),e=Array(t.length+1).join("-");return t+this.upcomingInput()+"\n"+e+"^"},"showPosition"),test_match:(0,s.K2)(function(t,e){var a,n,i;if(this.options.backtrack_lexer&&(i={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(i.yylloc.range=this.yylloc.range.slice(0))),(n=t[0].match(/(?:\r\n?|\n).*/g))&&(this.yylineno+=n.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:n?n[n.length-1].length-n[n.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+t[0].length},this.yytext+=t[0],this.match+=t[0],this.matches=t,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(t[0].length),this.matched+=t[0],a=this.performAction.call(this,this.yy,this,e,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),a)return a;if(this._backtrack)for(var r in i)this[r]=i[r];return!1},"test_match"),next:(0,s.K2)(function(){if(this.done)return this.EOF;this._input||(this.done=!0),this._more||(this.yytext="",this.match="");for(var t,e,a,n,i=this._currentRules(),r=0;r<i.length;r++)if((a=this._input.match(this.rules[i[r]]))&&(!e||a[0].length>e[0].length)){if(e=a,n=r,this.options.backtrack_lexer){if(!1!==(t=this.test_match(a,i[r])))return t;if(!this._backtrack)return!1;e=!1;continue}if(!this.options.flex)break}return e?!1!==(t=this.test_match(e,i[n]))&&t:""===this._input?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+". Unrecognized text.\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:(0,s.K2)(function(){var t=this.next();return t||this.lex()},"lex"),begin:(0,s.K2)(function(t){this.conditionStack.push(t)},"begin"),popState:(0,s.K2)(function(){return this.conditionStack.length-1>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:(0,s.K2)(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:(0,s.K2)(function(t){return(t=this.conditionStack.length-1-Math.abs(t||0))>=0?this.conditionStack[t]:"INITIAL"},"topState"),pushState:(0,s.K2)(function(t){this.begin(t)},"pushState"),stateStackSize:(0,s.K2)(function(){return this.conditionStack.length},"stateStackSize"),options:{},performAction:(0,s.K2)(function(t,e,a,n){switch(a){case 0:return 6;case 1:return 7;case 2:return 8;case 3:return 9;case 4:return 22;case 5:return 23;case 6:return this.begin("acc_title"),24;case 7:return this.popState(),"acc_title_value";case 8:return this.begin("acc_descr"),26;case 9:return this.popState(),"acc_descr_value";case 10:this.begin("acc_descr_multiline");break;case 11:case 73:this.popState();break;case 12:return"acc_descr_multiline_value";case 13:case 16:case 70:break;case 14:c;break;case 15:return 12;case 17:return 11;case 18:return 15;case 19:return 16;case 20:return 17;case 21:return 18;case 22:return this.begin("person_ext"),45;case 23:return this.begin("person"),44;case 24:return this.begin("system_ext_queue"),51;case 25:return this.begin("system_ext_db"),50;case 26:return this.begin("system_ext"),49;case 27:return this.begin("system_queue"),48;case 28:return this.begin("system_db"),47;case 29:return this.begin("system"),46;case 30:return this.begin("boundary"),37;case 31:return this.begin("enterprise_boundary"),34;case 32:return this.begin("system_boundary"),36;case 33:return this.begin("container_ext_queue"),57;case 34:return this.begin("container_ext_db"),56;case 35:return this.begin("container_ext"),55;case 36:return this.begin("container_queue"),54;case 37:return this.begin("container_db"),53;case 38:return this.begin("container"),52;case 39:return this.begin("container_boundary"),38;case 40:return this.begin("component_ext_queue"),63;case 41:return this.begin("component_ext_db"),62;case 42:return this.begin("component_ext"),61;case 43:return this.begin("component_queue"),60;case 44:return this.begin("component_db"),59;case 45:return this.begin("component"),58;case 46:case 47:return this.begin("node"),39;case 48:return this.begin("node_l"),40;case 49:return this.begin("node_r"),41;case 50:return this.begin("rel"),64;case 51:return this.begin("birel"),65;case 52:case 53:return this.begin("rel_u"),66;case 54:case 55:return this.begin("rel_d"),67;case 56:case 57:return this.begin("rel_l"),68;case 58:case 59:return this.begin("rel_r"),69;case 60:return this.begin("rel_b"),70;case 61:return this.begin("rel_index"),71;case 62:return this.begin("update_el_style"),72;case 63:return this.begin("update_rel_style"),73;case 64:return this.begin("update_layout_config"),74;case 65:return"EOF_IN_STRUCT";case 66:return this.begin("attribute"),"ATTRIBUTE_EMPTY";case 67:this.begin("attribute");break;case 68:case 79:this.popState(),this.popState();break;case 69:case 71:return 80;case 72:this.begin("string");break;case 74:case 80:return"STR";case 75:this.begin("string_kv");break;case 76:return this.begin("string_kv_key"),"STR_KEY";case 77:this.popState(),this.begin("string_kv_value");break;case 78:return"STR_VALUE";case 81:return"LBRACE";case 82:return"RBRACE";case 83:return"SPACE";case 84:return"EOL";case 85:return 14}},"anonymous"),rules:[/^(?:.*direction\s+TB[^\n]*)/,/^(?:.*direction\s+BT[^\n]*)/,/^(?:.*direction\s+RL[^\n]*)/,/^(?:.*direction\s+LR[^\n]*)/,/^(?:title\s[^#\n;]+)/,/^(?:accDescription\s[^#\n;]+)/,/^(?:accTitle\s*:\s*)/,/^(?:(?!\n||)*[^\n]*)/,/^(?:accDescr\s*:\s*)/,/^(?:(?!\n||)*[^\n]*)/,/^(?:accDescr\s*\{\s*)/,/^(?:[\}])/,/^(?:[^\}]*)/,/^(?:%%(?!\{)*[^\n]*(\r?\n?)+)/,/^(?:%%[^\n]*(\r?\n)*)/,/^(?:\s*(\r?\n)+)/,/^(?:\s+)/,/^(?:C4Context\b)/,/^(?:C4Container\b)/,/^(?:C4Component\b)/,/^(?:C4Dynamic\b)/,/^(?:C4Deployment\b)/,/^(?:Person_Ext\b)/,/^(?:Person\b)/,/^(?:SystemQueue_Ext\b)/,/^(?:SystemDb_Ext\b)/,/^(?:System_Ext\b)/,/^(?:SystemQueue\b)/,/^(?:SystemDb\b)/,/^(?:System\b)/,/^(?:Boundary\b)/,/^(?:Enterprise_Boundary\b)/,/^(?:System_Boundary\b)/,/^(?:ContainerQueue_Ext\b)/,/^(?:ContainerDb_Ext\b)/,/^(?:Container_Ext\b)/,/^(?:ContainerQueue\b)/,/^(?:ContainerDb\b)/,/^(?:Container\b)/,/^(?:Container_Boundary\b)/,/^(?:ComponentQueue_Ext\b)/,/^(?:ComponentDb_Ext\b)/,/^(?:Component_Ext\b)/,/^(?:ComponentQueue\b)/,/^(?:ComponentDb\b)/,/^(?:Component\b)/,/^(?:Deployment_Node\b)/,/^(?:Node\b)/,/^(?:Node_L\b)/,/^(?:Node_R\b)/,/^(?:Rel\b)/,/^(?:BiRel\b)/,/^(?:Rel_Up\b)/,/^(?:Rel_U\b)/,/^(?:Rel_Down\b)/,/^(?:Rel_D\b)/,/^(?:Rel_Left\b)/,/^(?:Rel_L\b)/,/^(?:Rel_Right\b)/,/^(?:Rel_R\b)/,/^(?:Rel_Back\b)/,/^(?:RelIndex\b)/,/^(?:UpdateElementStyle\b)/,/^(?:UpdateRelStyle\b)/,/^(?:UpdateLayoutConfig\b)/,/^(?:$)/,/^(?:[(][ ]*[,])/,/^(?:[(])/,/^(?:[)])/,/^(?:,,)/,/^(?:,)/,/^(?:[ ]*["]["])/,/^(?:[ ]*["])/,/^(?:["])/,/^(?:[^"]*)/,/^(?:[ ]*[\$])/,/^(?:[^=]*)/,/^(?:[=][ ]*["])/,/^(?:[^"]+)/,/^(?:["])/,/^(?:[^,]+)/,/^(?:\{)/,/^(?:\})/,/^(?:[\s]+)/,/^(?:[\n\r]+)/,/^(?:$)/],conditions:{acc_descr_multiline:{rules:[11,12],inclusive:!1},acc_descr:{rules:[9],inclusive:!1},acc_title:{rules:[7],inclusive:!1},string_kv_value:{rules:[78,79],inclusive:!1},string_kv_key:{rules:[77],inclusive:!1},string_kv:{rules:[76],inclusive:!1},string:{rules:[73,74],inclusive:!1},attribute:{rules:[68,69,70,71,72,75,80],inclusive:!1},update_layout_config:{rules:[65,66,67,68],inclusive:!1},update_rel_style:{rules:[65,66,67,68],inclusive:!1},update_el_style:{rules:[65,66,67,68],inclusive:!1},rel_b:{rules:[65,66,67,68],inclusive:!1},rel_r:{rules:[65,66,67,68],inclusive:!1},rel_l:{rules:[65,66,67,68],inclusive:!1},rel_d:{rules:[65,66,67,68],inclusive:!1},rel_u:{rules:[65,66,67,68],inclusive:!1},rel_bi:{rules:[],inclusive:!1},rel:{rules:[65,66,67,68],inclusive:!1},node_r:{rules:[65,66,67,68],inclusive:!1},node_l:{rules:[65,66,67,68],inclusive:!1},node:{rules:[65,66,67,68],inclusive:!1},index:{rules:[],inclusive:!1},rel_index:{rules:[65,66,67,68],inclusive:!1},component_ext_queue:{rules:[],inclusive:!1},component_ext_db:{rules:[65,66,67,68],inclusive:!1},component_ext:{rules:[65,66,67,68],inclusive:!1},component_queue:{rules:[65,66,67,68],inclusive:!1},component_db:{rules:[65,66,67,68],inclusive:!1},component:{rules:[65,66,67,68],inclusive:!1},container_boundary:{rules:[65,66,67,68],inclusive:!1},container_ext_queue:{rules:[65,66,67,68],inclusive:!1},container_ext_db:{rules:[65,66,67,68],inclusive:!1},container_ext:{rules:[65,66,67,68],inclusive:!1},container_queue:{rules:[65,66,67,68],inclusive:!1},container_db:{rules:[65,66,67,68],inclusive:!1},container:{rules:[65,66,67,68],inclusive:!1},birel:{rules:[65,66,67,68],inclusive:!1},system_boundary:{rules:[65,66,67,68],inclusive:!1},enterprise_boundary:{rules:[65,66,67,68],inclusive:!1},boundary:{rules:[65,66,67,68],inclusive:!1},system_ext_queue:{rules:[65,66,67,68],inclusive:!1},system_ext_db:{rules:[65,66,67,68],inclusive:!1},system_ext:{rules:[65,66,67,68],inclusive:!1},system_queue:{rules:[65,66,67,68],inclusive:!1},system_db:{rules:[65,66,67,68],inclusive:!1},system:{rules:[65,66,67,68],inclusive:!1},person_ext:{rules:[65,66,67,68],inclusive:!1},person:{rules:[65,66,67,68],inclusive:!1},INITIAL:{rules:[0,1,2,3,4,5,6,8,10,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,81,82,83,84,85],inclusive:!0}}},(0,s.K2)(tr,"Parser"),tr.prototype=ti,ti.Parser=tr,new tr}();h.parser=h;var d=[],u=[""],p="global",y="",b=[{alias:"global",label:{text:"global"},type:{text:"global"},tags:null,link:null,parentBoundary:""}],g=[],f="",x=!1,_=4,m=2,E=(0,s.K2)(function(){return n},"getC4Type"),A=(0,s.K2)(function(t){n=(0,s.jZ)(t,(0,s.D7)())},"setC4Type"),S=(0,s.K2)(function(t,e,a,n,i,r,s,l,o){if(null==t||null==e||null==a||null==n)return;let h={},d=g.find(t=>t.from===e&&t.to===a);if(d?h=d:g.push(h),h.type=t,h.from=e,h.to=a,h.label={text:n},null==i)h.techn={text:""};else if("object"==typeof i){let[t,e]=Object.entries(i)[0];h[t]={text:e}}else h.techn={text:i};if(null==r)h.descr={text:""};else if("object"==typeof r){let[t,e]=Object.entries(r)[0];h[t]={text:e}}else h.descr={text:r};if("object"==typeof s){let[t,e]=Object.entries(s)[0];h[t]=e}else h.sprite=s;if("object"==typeof l){let[t,e]=Object.entries(l)[0];h[t]=e}else h.tags=l;if("object"==typeof o){let[t,e]=Object.entries(o)[0];h[t]=e}else h.link=o;h.wrap=W()},"addRel"),C=(0,s.K2)(function(t,e,a,n,i,r,s){if(null===e||null===a)return;let l={},o=d.find(t=>t.alias===e);if(o&&e===o.alias?l=o:(l.alias=e,d.push(l)),null==a?l.label={text:""}:l.label={text:a},null==n)l.descr={text:""};else if("object"==typeof n){let[t,e]=Object.entries(n)[0];l[t]={text:e}}else l.descr={text:n};if("object"==typeof i){let[t,e]=Object.entries(i)[0];l[t]=e}else l.sprite=i;if("object"==typeof r){let[t,e]=Object.entries(r)[0];l[t]=e}else l.tags=r;if("object"==typeof s){let[t,e]=Object.entries(s)[0];l[t]=e}else l.link=s;l.typeC4Shape={text:t},l.parentBoundary=p,l.wrap=W()},"addPersonOrSystem"),k=(0,s.K2)(function(t,e,a,n,i,r,s,l){if(null===e||null===a)return;let o={},h=d.find(t=>t.alias===e);if(h&&e===h.alias?o=h:(o.alias=e,d.push(o)),null==a?o.label={text:""}:o.label={text:a},null==n)o.techn={text:""};else if("object"==typeof n){let[t,e]=Object.entries(n)[0];o[t]={text:e}}else o.techn={text:n};if(null==i)o.descr={text:""};else if("object"==typeof i){let[t,e]=Object.entries(i)[0];o[t]={text:e}}else o.descr={text:i};if("object"==typeof r){let[t,e]=Object.entries(r)[0];o[t]=e}else o.sprite=r;if("object"==typeof s){let[t,e]=Object.entries(s)[0];o[t]=e}else o.tags=s;if("object"==typeof l){let[t,e]=Object.entries(l)[0];o[t]=e}else o.link=l;o.wrap=W(),o.typeC4Shape={text:t},o.parentBoundary=p},"addContainer"),O=(0,s.K2)(function(t,e,a,n,i,r,s,l){if(null===e||null===a)return;let o={},h=d.find(t=>t.alias===e);if(h&&e===h.alias?o=h:(o.alias=e,d.push(o)),null==a?o.label={text:""}:o.label={text:a},null==n)o.techn={text:""};else if("object"==typeof n){let[t,e]=Object.entries(n)[0];o[t]={text:e}}else o.techn={text:n};if(null==i)o.descr={text:""};else if("object"==typeof i){let[t,e]=Object.entries(i)[0];o[t]={text:e}}else o.descr={text:i};if("object"==typeof r){let[t,e]=Object.entries(r)[0];o[t]=e}else o.sprite=r;if("object"==typeof s){let[t,e]=Object.entries(s)[0];o[t]=e}else o.tags=s;if("object"==typeof l){let[t,e]=Object.entries(l)[0];o[t]=e}else o.link=l;o.wrap=W(),o.typeC4Shape={text:t},o.parentBoundary=p},"addComponent"),w=(0,s.K2)(function(t,e,a,n,i){if(null===t||null===e)return;let r={},s=b.find(e=>e.alias===t);if(s&&t===s.alias?r=s:(r.alias=t,b.push(r)),null==e?r.label={text:""}:r.label={text:e},null==a)r.type={text:"system"};else if("object"==typeof a){let[t,e]=Object.entries(a)[0];r[t]={text:e}}else r.type={text:a};if("object"==typeof n){let[t,e]=Object.entries(n)[0];r[t]=e}else r.tags=n;if("object"==typeof i){let[t,e]=Object.entries(i)[0];r[t]=e}else r.link=i;r.parentBoundary=p,r.wrap=W(),y=p,p=t,u.push(y)},"addPersonOrSystemBoundary"),T=(0,s.K2)(function(t,e,a,n,i){if(null===t||null===e)return;let r={},s=b.find(e=>e.alias===t);if(s&&t===s.alias?r=s:(r.alias=t,b.push(r)),null==e?r.label={text:""}:r.label={text:e},null==a)r.type={text:"container"};else if("object"==typeof a){let[t,e]=Object.entries(a)[0];r[t]={text:e}}else r.type={text:a};if("object"==typeof n){let[t,e]=Object.entries(n)[0];r[t]=e}else r.tags=n;if("object"==typeof i){let[t,e]=Object.entries(i)[0];r[t]=e}else r.link=i;r.parentBoundary=p,r.wrap=W(),y=p,p=t,u.push(y)},"addContainerBoundary"),v=(0,s.K2)(function(t,e,a,n,i,r,s,l){if(null===e||null===a)return;let o={},h=b.find(t=>t.alias===e);if(h&&e===h.alias?o=h:(o.alias=e,b.push(o)),null==a?o.label={text:""}:o.label={text:a},null==n)o.type={text:"node"};else if("object"==typeof n){let[t,e]=Object.entries(n)[0];o[t]={text:e}}else o.type={text:n};if(null==i)o.descr={text:""};else if("object"==typeof i){let[t,e]=Object.entries(i)[0];o[t]={text:e}}else o.descr={text:i};if("object"==typeof s){let[t,e]=Object.entries(s)[0];o[t]=e}else o.tags=s;if("object"==typeof l){let[t,e]=Object.entries(l)[0];o[t]=e}else o.link=l;o.nodeType=t,o.parentBoundary=p,o.wrap=W(),y=p,p=e,u.push(y)},"addDeploymentNode"),R=(0,s.K2)(function(){p=y,u.pop(),y=u.pop(),u.push(y)},"popBoundaryParseStack"),D=(0,s.K2)(function(t,e,a,n,i,r,s,l,o,h,u){let p=d.find(t=>t.alias===e);if(void 0!==p||void 0!==(p=b.find(t=>t.alias===e))){if(null!=a)if("object"==typeof a){let[t,e]=Object.entries(a)[0];p[t]=e}else p.bgColor=a;if(null!=n)if("object"==typeof n){let[t,e]=Object.entries(n)[0];p[t]=e}else p.fontColor=n;if(null!=i)if("object"==typeof i){let[t,e]=Object.entries(i)[0];p[t]=e}else p.borderColor=i;if(null!=r)if("object"==typeof r){let[t,e]=Object.entries(r)[0];p[t]=e}else p.shadowing=r;if(null!=s)if("object"==typeof s){let[t,e]=Object.entries(s)[0];p[t]=e}else p.shape=s;if(null!=l)if("object"==typeof l){let[t,e]=Object.entries(l)[0];p[t]=e}else p.sprite=l;if(null!=o)if("object"==typeof o){let[t,e]=Object.entries(o)[0];p[t]=e}else p.techn=o;if(null!=h)if("object"==typeof h){let[t,e]=Object.entries(h)[0];p[t]=e}else p.legendText=h;if(null!=u)if("object"==typeof u){let[t,e]=Object.entries(u)[0];p[t]=e}else p.legendSprite=u}},"updateElStyle"),N=(0,s.K2)(function(t,e,a,n,i,r,s){let l=g.find(t=>t.from===e&&t.to===a);if(void 0!==l){if(null!=n)if("object"==typeof n){let[t,e]=Object.entries(n)[0];l[t]=e}else l.textColor=n;if(null!=i)if("object"==typeof i){let[t,e]=Object.entries(i)[0];l[t]=e}else l.lineColor=i;if(null!=r)if("object"==typeof r){let[t,e]=Object.entries(r)[0];l[t]=parseInt(e)}else l.offsetX=parseInt(r);if(null!=s)if("object"==typeof s){let[t,e]=Object.entries(s)[0];l[t]=parseInt(e)}else l.offsetY=parseInt(s)}},"updateRelStyle"),P=(0,s.K2)(function(t,e,a){let n=_,i=m;n="object"==typeof e?parseInt(Object.values(e)[0]):parseInt(e),i="object"==typeof a?parseInt(Object.values(a)[0]):parseInt(a),n>=1&&(_=n),i>=1&&(m=i)},"updateLayoutConfig"),B=(0,s.K2)(function(){return _},"getC4ShapeInRow"),M=(0,s.K2)(function(){return m},"getC4BoundaryInRow"),j=(0,s.K2)(function(){return p},"getCurrentBoundaryParse"),I=(0,s.K2)(function(){return y},"getParentBoundaryParse"),K=(0,s.K2)(function(t){return null==t?d:d.filter(e=>e.parentBoundary===t)},"getC4ShapeArray"),L=(0,s.K2)(function(t){return d.find(e=>e.alias===t)},"getC4Shape"),Y=(0,s.K2)(function(t){return Object.keys(K(t))},"getC4ShapeKeys"),U=(0,s.K2)(function(t){return null==t?b:b.filter(e=>e.parentBoundary===t)},"getBoundaries"),F=(0,s.K2)(function(){return g},"getRels"),X=(0,s.K2)(function(){return f},"getTitle"),z=(0,s.K2)(function(t){x=t},"setWrap"),W=(0,s.K2)(function(){return x},"autoWrap"),Q=(0,s.K2)(function(){d=[],b=[{alias:"global",label:{text:"global"},type:{text:"global"},tags:null,link:null,parentBoundary:""}],y="",p="global",u=[""],g=[],u=[""],f="",x=!1,_=4,m=2},"clear"),$=(0,s.K2)(function(t){f=(0,s.jZ)(t,(0,s.D7)())},"setTitle"),H={addPersonOrSystem:C,addPersonOrSystemBoundary:w,addContainer:k,addContainerBoundary:T,addComponent:O,addDeploymentNode:v,popBoundaryParseStack:R,addRel:S,updateElStyle:D,updateRelStyle:N,updateLayoutConfig:P,autoWrap:W,setWrap:z,getC4ShapeArray:K,getC4Shape:L,getC4ShapeKeys:Y,getBoundaries:U,getBoundarys:U,getCurrentBoundaryParse:j,getParentBoundaryParse:I,getRels:F,getTitle:X,getC4Type:E,getC4ShapeInRow:B,getC4BoundaryInRow:M,setAccTitle:s.SV,getAccTitle:s.iN,getAccDescription:s.m7,setAccDescription:s.EI,getConfig:(0,s.K2)(()=>(0,s.D7)().c4,"getConfig"),clear:Q,LINETYPE:{SOLID:0,DOTTED:1,NOTE:2,SOLID_CROSS:3,DOTTED_CROSS:4,SOLID_OPEN:5,DOTTED_OPEN:6,LOOP_START:10,LOOP_END:11,ALT_START:12,ALT_ELSE:13,ALT_END:14,OPT_START:15,OPT_END:16,ACTIVE_START:17,ACTIVE_END:18,PAR_START:19,PAR_AND:20,PAR_END:21,RECT_START:22,RECT_END:23,SOLID_POINT:24,DOTTED_POINT:25},ARROWTYPE:{FILLED:0,OPEN:1},PLACEMENT:{LEFTOF:0,RIGHTOF:1,OVER:2},setTitle:$,setC4Type:A},V=(0,s.K2)(function(t,e){return(0,i.tk)(t,e)},"drawRect"),q=(0,s.K2)(function(t,e,a,n,i,r){let s=t.append("image");s.attr("width",e),s.attr("height",a),s.attr("x",n),s.attr("y",i);let l=r.startsWith("data:image/png;base64")?r:(0,o.J)(r);s.attr("xlink:href",l)},"drawImage"),G=(0,s.K2)((t,e,a)=>{let n=t.append("g"),i=0;for(let t of e){let e=t.textColor?t.textColor:"#444444",r=t.lineColor?t.lineColor:"#444444",s=t.offsetX?parseInt(t.offsetX):0,l=t.offsetY?parseInt(t.offsetY):0;if(0===i){let e=n.append("line");e.attr("x1",t.startPoint.x),e.attr("y1",t.startPoint.y),e.attr("x2",t.endPoint.x),e.attr("y2",t.endPoint.y),e.attr("stroke-width","1"),e.attr("stroke",r),e.style("fill","none"),"rel_b"!==t.type&&e.attr("marker-end","url(#arrowhead)"),("birel"===t.type||"rel_b"===t.type)&&e.attr("marker-start","url(#arrowend)"),i=-1}else{let e=n.append("path");e.attr("fill","none").attr("stroke-width","1").attr("stroke",r).attr("d","Mstartx,starty Qcontrolx,controly stopx,stopy ".replaceAll("startx",t.startPoint.x).replaceAll("starty",t.startPoint.y).replaceAll("controlx",t.startPoint.x+(t.endPoint.x-t.startPoint.x)/2-(t.endPoint.x-t.startPoint.x)/4).replaceAll("controly",t.startPoint.y+(t.endPoint.y-t.startPoint.y)/2).replaceAll("stopx",t.endPoint.x).replaceAll("stopy",t.endPoint.y)),"rel_b"!==t.type&&e.attr("marker-end","url(#arrowhead)"),("birel"===t.type||"rel_b"===t.type)&&e.attr("marker-start","url(#arrowend)")}let o=a.messageFont();to(a)(t.label.text,n,Math.min(t.startPoint.x,t.endPoint.x)+Math.abs(t.endPoint.x-t.startPoint.x)/2+s,Math.min(t.startPoint.y,t.endPoint.y)+Math.abs(t.endPoint.y-t.startPoint.y)/2+l,t.label.width,t.label.height,{fill:e},o),t.techn&&""!==t.techn.text&&(o=a.messageFont(),to(a)("["+t.techn.text+"]",n,Math.min(t.startPoint.x,t.endPoint.x)+Math.abs(t.endPoint.x-t.startPoint.x)/2+s,Math.min(t.startPoint.y,t.endPoint.y)+Math.abs(t.endPoint.y-t.startPoint.y)/2+a.messageFontSize+5+l,Math.max(t.label.width,t.techn.width),t.techn.height,{fill:e,"font-style":"italic"},o))}},"drawRels"),J=(0,s.K2)(function(t,e,a){let n=t.append("g"),i=e.bgColor?e.bgColor:"none",r=e.borderColor?e.borderColor:"#444444",s=e.fontColor?e.fontColor:"black",l={"stroke-width":1,"stroke-dasharray":"7.0,7.0"};e.nodeType&&(l={"stroke-width":1}),V(n,{x:e.x,y:e.y,fill:i,stroke:r,width:e.width,height:e.height,rx:2.5,ry:2.5,attrs:l});let o=a.boundaryFont();o.fontWeight="bold",o.fontSize=o.fontSize+2,o.fontColor=s,to(a)(e.label.text,n,e.x,e.y+e.label.Y,e.width,e.height,{fill:"#444444"},o),e.type&&""!==e.type.text&&((o=a.boundaryFont()).fontColor=s,to(a)(e.type.text,n,e.x,e.y+e.type.Y,e.width,e.height,{fill:"#444444"},o)),e.descr&&""!==e.descr.text&&((o=a.boundaryFont()).fontSize=o.fontSize-2,o.fontColor=s,to(a)(e.descr.text,n,e.x,e.y+e.descr.Y,e.width,e.height,{fill:"#444444"},o))},"drawBoundary"),Z=(0,s.K2)(function(t,e,a){let n=e.bgColor?e.bgColor:a[e.typeC4Shape.text+"_bg_color"],r=e.borderColor?e.borderColor:a[e.typeC4Shape.text+"_border_color"],s=e.fontColor?e.fontColor:"#FFFFFF",l="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAIAAADYYG7QAAACD0lEQVR4Xu2YoU4EMRCGT+4j8Ai8AhaH4QHgAUjQuFMECUgMIUgwJAgMhgQsAYUiJCiQIBBY+EITsjfTdme6V24v4c8vyGbb+ZjOtN0bNcvjQXmkH83WvYBWto6PLm6v7p7uH1/w2fXD+PBycX1Pv2l3IdDm/vn7x+dXQiAubRzoURa7gRZWd0iGRIiJbOnhnfYBQZNJjNbuyY2eJG8fkDE3bbG4ep6MHUAsgYxmE3nVs6VsBWJSGccsOlFPmLIViMzLOB7pCVO2AtHJMohH7Fh6zqitQK7m0rJvAVYgGcEpe//PLdDz65sM4pF9N7ICcXDKIB5Nv6j7tD0NoSdM2QrU9Gg0ewE1LqBhHR3BBdvj2vapnidjHxD/q6vd7Pvhr31AwcY8eXMTXAKECZZJFXuEq27aLgQK5uLMohCenGGuGewOxSjBvYBqeG6B+Nqiblggdjnc+ZXDy+FNFpFzw76O3UBAROuXh6FoiAcf5g9eTvUgzy0nWg6I8cXHRUpg5bOVBCo+KDpFajOf23GgPme7RSQ+lacIENUgJ6gg1k6HjgOlqnLqip4tEuhv0hNEMXUD0clyXE3p6pZA0S2nnvTlXwLJEZWlb7cTQH1+USgTN4VhAenm/wea1OCAOmqo6fE1WCb9WSKBah+rbUWPWAmE2Rvk0ApiB45eOyNAzU8xcTvj8KvkKEoOaIYeHNA3ZuygAvFMUO0AAAAASUVORK5CYII=";switch(e.typeC4Shape.text){case"person":l="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAIAAADYYG7QAAACD0lEQVR4Xu2YoU4EMRCGT+4j8Ai8AhaH4QHgAUjQuFMECUgMIUgwJAgMhgQsAYUiJCiQIBBY+EITsjfTdme6V24v4c8vyGbb+ZjOtN0bNcvjQXmkH83WvYBWto6PLm6v7p7uH1/w2fXD+PBycX1Pv2l3IdDm/vn7x+dXQiAubRzoURa7gRZWd0iGRIiJbOnhnfYBQZNJjNbuyY2eJG8fkDE3bbG4ep6MHUAsgYxmE3nVs6VsBWJSGccsOlFPmLIViMzLOB7pCVO2AtHJMohH7Fh6zqitQK7m0rJvAVYgGcEpe//PLdDz65sM4pF9N7ICcXDKIB5Nv6j7tD0NoSdM2QrU9Gg0ewE1LqBhHR3BBdvj2vapnidjHxD/q6vd7Pvhr31AwcY8eXMTXAKECZZJFXuEq27aLgQK5uLMohCenGGuGewOxSjBvYBqeG6B+Nqiblggdjnc+ZXDy+FNFpFzw76O3UBAROuXh6FoiAcf5g9eTvUgzy0nWg6I8cXHRUpg5bOVBCo+KDpFajOf23GgPme7RSQ+lacIENUgJ6gg1k6HjgOlqnLqip4tEuhv0hNEMXUD0clyXE3p6pZA0S2nnvTlXwLJEZWlb7cTQH1+USgTN4VhAenm/wea1OCAOmqo6fE1WCb9WSKBah+rbUWPWAmE2Rvk0ApiB45eOyNAzU8xcTvj8KvkKEoOaIYeHNA3ZuygAvFMUO0AAAAASUVORK5CYII=";break;case"external_person":l="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAIAAADYYG7QAAAB6ElEQVR4Xu2YLY+EMBCG9+dWr0aj0Wg0Go1Go0+j8Xdv2uTCvv1gpt0ebHKPuhDaeW4605Z9mJvx4AdXUyTUdd08z+u6flmWZRnHsWkafk9DptAwDPu+f0eAYtu2PEaGWuj5fCIZrBAC2eLBAnRCsEkkxmeaJp7iDJ2QMDdHsLg8SxKFEJaAo8lAXnmuOFIhTMpxxKATebo4UiFknuNo4OniSIXQyRxEA3YsnjGCVEjVXD7yLUAqxBGUyPv/Y4W2beMgGuS7kVQIBycH0fD+oi5pezQETxdHKmQKGk1eQEYldK+jw5GxPfZ9z7Mk0Qnhf1W1m3w//EUn5BDmSZsbR44QQLBEqrBHqOrmSKaQAxdnLArCrxZcM7A7ZKs4ioRq8LFC+NpC3WCBJsvpVw5edm9iEXFuyNfxXAgSwfrFQ1c0iNda8AdejvUgnktOtJQQxmcfFzGglc5WVCj7oDgFqU18boeFSs52CUh8LE8BIVQDT1ABrB0HtgSEYlX5doJnCwv9TXocKCaKbnwhdDKPq4lf3SwU3HLq4V/+WYhHVMa/3b4IlfyikAduCkcBc7mQ3/z/Qq/cTuikhkzB12Ae/mcJC9U+Vo8Ej1gWAtgbeGgFsAMHr50BIWOLCbezvhpBFUdY6EJuJ/QDW0XoMX60zZ0AAAAASUVORK5CYII="}let o=t.append("g");o.attr("class","person-man");let h=(0,i.PB)();switch(e.typeC4Shape.text){case"person":case"external_person":case"system":case"external_system":case"container":case"external_container":case"component":case"external_component":h.x=e.x,h.y=e.y,h.fill=n,h.width=e.width,h.height=e.height,h.stroke=r,h.rx=2.5,h.ry=2.5,h.attrs={"stroke-width":.5},V(o,h);break;case"system_db":case"external_system_db":case"container_db":case"external_container_db":case"component_db":case"external_component_db":o.append("path").attr("fill",n).attr("stroke-width","0.5").attr("stroke",r).attr("d","Mstartx,startyc0,-10 half,-10 half,-10c0,0 half,0 half,10l0,heightc0,10 -half,10 -half,10c0,0 -half,0 -half,-10l0,-height".replaceAll("startx",e.x).replaceAll("starty",e.y).replaceAll("half",e.width/2).replaceAll("height",e.height)),o.append("path").attr("fill","none").attr("stroke-width","0.5").attr("stroke",r).attr("d","Mstartx,startyc0,10 half,10 half,10c0,0 half,0 half,-10".replaceAll("startx",e.x).replaceAll("starty",e.y).replaceAll("half",e.width/2));break;case"system_queue":case"external_system_queue":case"container_queue":case"external_container_queue":case"component_queue":case"external_component_queue":o.append("path").attr("fill",n).attr("stroke-width","0.5").attr("stroke",r).attr("d","Mstartx,startylwidth,0c5,0 5,half 5,halfc0,0 0,half -5,halfl-width,0c-5,0 -5,-half -5,-halfc0,0 0,-half 5,-half".replaceAll("startx",e.x).replaceAll("starty",e.y).replaceAll("width",e.width).replaceAll("half",e.height/2)),o.append("path").attr("fill","none").attr("stroke-width","0.5").attr("stroke",r).attr("d","Mstartx,startyc-5,0 -5,half -5,halfc0,half 5,half 5,half".replaceAll("startx",e.x+e.width).replaceAll("starty",e.y).replaceAll("half",e.height/2))}let d=tl(a,e.typeC4Shape.text);switch(o.append("text").attr("fill",s).attr("font-family",d.fontFamily).attr("font-size",d.fontSize-2).attr("font-style","italic").attr("lengthAdjust","spacing").attr("textLength",e.typeC4Shape.width).attr("x",e.x+e.width/2-e.typeC4Shape.width/2).attr("y",e.y+e.typeC4Shape.Y).text("<<"+e.typeC4Shape.text+">>"),e.typeC4Shape.text){case"person":case"external_person":q(o,48,48,e.x+e.width/2-24,e.y+e.image.Y,l)}let u=a[e.typeC4Shape.text+"Font"]();return u.fontWeight="bold",u.fontSize=u.fontSize+2,u.fontColor=s,to(a)(e.label.text,o,e.x,e.y+e.label.Y,e.width,e.height,{fill:s},u),(u=a[e.typeC4Shape.text+"Font"]()).fontColor=s,e.techn&&e.techn?.text!==""?to(a)(e.techn.text,o,e.x,e.y+e.techn.Y,e.width,e.height,{fill:s,"font-style":"italic"},u):e.type&&""!==e.type.text&&to(a)(e.type.text,o,e.x,e.y+e.type.Y,e.width,e.height,{fill:s,"font-style":"italic"},u),e.descr&&""!==e.descr.text&&((u=a.personFont()).fontColor=s,to(a)(e.descr.text,o,e.x,e.y+e.descr.Y,e.width,e.height,{fill:s},u)),e.height},"drawC4Shape"),tt=(0,s.K2)(function(t){t.append("defs").append("symbol").attr("id","database").attr("fill-rule","evenodd").attr("clip-rule","evenodd").append("path").attr("transform","scale(.5)").attr("d","M12.258.001l.256.***************.**************.***************.***************.**************.***************.***************.**************.***************.**************.***************.***************.**************.**************.***************.**************.**************.**************.***************.**************.***************.***************.***************.*************.***************.***************.***************.**************.***************.**************.*************.*************.***************.***************.**************.***************.***************.***************.**************.***************.***************.***************.***************.***************.045.001.045v17l-.001.045-.002.045-.004.045-.006.045-.007.045-.009.044-.011.045-.012.044-.013.044-.015.044-.017.043-.018.044-.02.043-.021.043-.023.043-.024.043-.026.043-.027.042-.029.042-.03.042-.032.042-.033.042-.034.041-.036.041-.037.041-.039.041-.04.041-.041.04-.043.04-.044.04-.045.04-.047.039-.048.039-.05.039-.051.039-.052.038-.053.038-.055.038-.055.038-.058.037-.058.037-.06.037-.06.036-.062.036-.064.036-.064.036-.066.035-.067.035-.068.035-.069.035-.07.034-.071.034-.073.033-.074.033-.15.066-.155.064-.16.063-.163.061-.168.06-.172.059-.175.057-.18.056-.183.054-.187.053-.191.051-.194.05-.198.048-.201.046-.205.045-.208.043-.211.041-.214.04-.217.038-.22.036-.223.034-.225.032-.229.031-.231.028-.233.027-.236.024-.239.023-.241.02-.242.019-.246.016-.247.015-.249.012-.251.01-.253.008-.255.005-.256.004-.258.001-.258-.001-.256-.004-.255-.005-.253-.008-.251-.01-.249-.012-.247-.015-.245-.016-.243-.019-.241-.02-.238-.023-.236-.024-.234-.027-.231-.028-.228-.031-.226-.032-.223-.034-.22-.036-.217-.038-.214-.04-.211-.041-.208-.043-.204-.045-.201-.046-.198-.048-.195-.05-.19-.051-.187-.053-.184-.054-.179-.056-.176-.057-.172-.059-.167-.06-.164-.061-.159-.063-.155-.064-.151-.066-.074-.033-.072-.033-.072-.034-.07-.034-.069-.035-.068-.035-.067-.035-.066-.035-.064-.036-.063-.036-.062-.036-.061-.036-.06-.037-.058-.037-.057-.037-.056-.038-.055-.038-.053-.038-.052-.038-.051-.039-.049-.039-.049-.039-.046-.039-.046-.04-.044-.04-.043-.04-.041-.04-.04-.041-.039-.041-.037-.041-.036-.041-.034-.041-.033-.042-.032-.042-.03-.042-.029-.042-.027-.042-.026-.043-.024-.043-.023-.043-.021-.043-.02-.043-.018-.044-.017-.043-.015-.044-.013-.044-.012-.044-.011-.045-.009-.044-.007-.045-.006-.045-.004-.045-.002-.045-.001-.045v-17l.001-.045.002-.045.004-.045.006-.045.007-.045.009-.044.011-.045.012-.044.013-.044.015-.044.017-.043.018-.044.02-.043.021-.043.023-.043.024-.043.026-.043.027-.042.029-.042.03-.042.032-.042.033-.042.034-.041.036-.041.037-.041.039-.041.04-.041.041-.04.043-.04.044-.04.046-.04.046-.039.049-.039.049-.039.051-.039.052-.038.053-.038.055-.038.056-.038.057-.037.058-.037.06-.037.061-.036.062-.036.063-.036.064-.036.066-.035.067-.035.068-.035.069-.035.07-.034.072-.034.072-.033.074-.033.151-.066.155-.064.159-.063.164-.061.167-.06.172-.059.176-.057.179-.056.184-.054.187-.053.19-.051.195-.05.198-.048.201-.046.204-.045.208-.043.211-.041.214-.04.217-.038.22-.036.223-.034.226-.032.228-.031.231-.028.234-.027.236-.024.238-.023.241-.02.243-.019.245-.016.247-.015.249-.012.251-.01.253-.008.255-.005.256-.004.258-.001.258.001zm-9.258 20.499v.01l.001.021.003.021.004.022.005.021.006.022.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.023.018.024.019.024.021.024.022.025.023.024.024.025.052.049.056.05.061.051.066.051.07.051.075.051.079.052.084.052.088.052.092.052.097.052.102.051.105.052.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.048.144.049.147.047.152.047.155.047.16.045.163.045.167.043.171.043.176.041.178.041.183.039.187.039.19.037.194.035.197.035.202.033.204.031.209.03.212.029.216.027.219.025.222.024.226.021.23.02.233.018.236.016.24.015.243.012.246.01.249.008.253.005.256.004.259.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.021.224-.024.22-.026.216-.027.212-.028.21-.031.205-.031.202-.034.198-.034.194-.036.191-.037.187-.039.183-.04.179-.04.175-.042.172-.043.168-.044.163-.045.16-.046.155-.046.152-.047.148-.048.143-.049.139-.049.136-.05.131-.05.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.053.083-.051.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.05.023-.024.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.023.01-.022.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.127l-.077.055-.08.053-.083.054-.085.053-.087.052-.09.052-.093.051-.095.05-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.045-.118.044-.12.043-.122.042-.124.042-.126.041-.128.04-.13.04-.132.038-.134.038-.135.037-.138.037-.139.035-.142.035-.143.034-.144.033-.147.032-.148.031-.15.03-.151.03-.153.029-.154.027-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.01-.179.008-.179.008-.181.006-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.006-.179-.008-.179-.008-.178-.01-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.027-.153-.029-.151-.03-.15-.03-.148-.031-.146-.032-.145-.033-.143-.034-.141-.035-.14-.035-.137-.037-.136-.037-.134-.038-.132-.038-.13-.04-.128-.04-.126-.041-.124-.042-.122-.042-.12-.044-.117-.043-.116-.045-.113-.045-.112-.046-.109-.047-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.05-.093-.052-.09-.051-.087-.052-.085-.053-.083-.054-.08-.054-.077-.054v4.127zm0-5.654v.011l.001.021.003.021.004.021.005.022.006.022.007.022.009.022.01.022.011.023.012.023.013.023.015.024.016.023.017.024.018.024.019.024.021.024.022.024.023.025.024.024.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.052.11.051.114.051.119.052.123.05.127.051.131.05.135.049.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.044.171.042.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.022.23.02.233.018.236.016.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.012.241-.015.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.048.139-.05.136-.049.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.051.051-.049.023-.025.023-.024.021-.025.02-.024.019-.024.018-.024.017-.024.015-.023.014-.023.013-.024.012-.022.01-.023.01-.023.008-.022.006-.022.006-.022.004-.021.004-.022.001-.021.001-.021v-4.139l-.077.054-.08.054-.083.054-.085.052-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.044-.118.044-.12.044-.122.042-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.035-.143.033-.144.033-.147.033-.148.031-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.009-.179.009-.179.007-.181.007-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.007-.179-.007-.179-.009-.178-.009-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.031-.146-.033-.145-.033-.143-.033-.141-.035-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.04-.126-.041-.124-.042-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.051-.093-.051-.09-.051-.087-.053-.085-.052-.083-.054-.08-.054-.077-.054v4.139zm0-5.666v.011l.001.02.003.022.004.021.005.022.006.021.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.024.018.023.019.024.021.025.022.024.023.024.024.025.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.051.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.043.171.043.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.021.23.02.233.018.236.017.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.013.241-.014.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.049.139-.049.136-.049.131-.051.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.049.023-.025.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.022.01-.023.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.153l-.077.054-.08.054-.083.053-.085.053-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.048-.105.048-.106.048-.109.046-.111.046-.114.046-.115.044-.118.044-.12.043-.122.043-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.034-.143.034-.144.033-.147.032-.148.032-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.024-.161.024-.162.023-.163.023-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.01-.178.01-.179.009-.179.007-.181.006-.182.006-.182.004-.184.003-.184.001-.185.001-.185-.001-.184-.001-.184-.003-.182-.004-.182-.006-.181-.006-.179-.007-.179-.009-.178-.01-.176-.01-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.023-.162-.023-.161-.024-.159-.024-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.032-.146-.032-.145-.033-.143-.034-.141-.034-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.041-.126-.041-.124-.041-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.048-.105-.048-.102-.048-.1-.05-.097-.049-.095-.051-.093-.051-.09-.052-.087-.052-.085-.053-.083-.053-.08-.054-.077-.054v4.153zm8.74-8.179l-.257.004-.254.005-.25.008-.247.011-.244.012-.241.014-.237.016-.233.018-.231.021-.226.022-.224.023-.22.026-.216.027-.212.028-.21.031-.205.032-.202.033-.198.034-.194.036-.191.038-.187.038-.183.04-.179.041-.175.042-.172.043-.168.043-.163.045-.16.046-.155.046-.152.048-.148.048-.143.048-.139.049-.136.05-.131.05-.126.051-.123.051-.118.051-.114.052-.11.052-.106.052-.101.052-.096.052-.092.052-.088.052-.083.052-.079.052-.074.051-.07.052-.065.051-.06.05-.056.05-.051.05-.023.025-.023.024-.021.024-.02.025-.019.024-.018.024-.017.023-.015.024-.014.023-.013.023-.012.023-.01.023-.01.022-.008.022-.006.023-.006.021-.004.022-.004.021-.001.021-.001.021.001.021.001.021.004.021.004.022.006.021.006.023.008.022.01.022.01.023.012.023.013.023.014.023.015.024.017.023.018.024.019.024.02.025.021.024.023.024.023.025.051.05.056.05.06.05.065.051.07.052.074.051.079.052.083.052.088.052.092.052.096.052.101.052.106.052.11.052.114.052.118.051.123.051.126.051.131.05.136.05.139.049.143.048.148.048.152.048.155.046.16.046.163.045.168.043.172.043.175.042.179.041.183.04.187.038.191.038.194.036.198.034.202.033.205.032.21.031.212.028.216.027.22.026.224.023.226.022.231.021.233.018.237.016.241.014.244.012.247.011.25.008.254.005.257.004.26.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.022.224-.023.22-.026.216-.027.212-.028.21-.031.205-.032.202-.033.198-.034.194-.036.191-.038.187-.038.183-.04.179-.041.175-.042.172-.043.168-.043.163-.045.16-.046.155-.046.152-.048.148-.048.143-.048.139-.049.136-.05.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.05.051-.05.023-.025.023-.024.021-.024.02-.025.019-.024.018-.024.017-.023.015-.024.014-.023.013-.023.012-.023.01-.023.01-.022.008-.022.006-.023.006-.021.004-.022.004-.021.001-.021.001-.021-.001-.021-.001-.021-.004-.021-.004-.022-.006-.021-.006-.023-.008-.022-.01-.022-.01-.023-.012-.023-.013-.023-.014-.023-.015-.024-.017-.023-.018-.024-.019-.024-.02-.025-.021-.024-.023-.024-.023-.025-.051-.05-.056-.05-.06-.05-.065-.051-.07-.052-.074-.051-.079-.052-.083-.052-.088-.052-.092-.052-.096-.052-.101-.052-.106-.052-.11-.052-.114-.052-.118-.051-.123-.051-.126-.051-.131-.05-.136-.05-.139-.049-.143-.048-.148-.048-.152-.048-.155-.046-.16-.046-.163-.045-.168-.043-.172-.043-.175-.042-.179-.041-.183-.04-.187-.038-.191-.038-.194-.036-.198-.034-.202-.033-.205-.032-.21-.031-.212-.028-.216-.027-.22-.026-.224-.023-.226-.022-.231-.021-.233-.018-.237-.016-.241-.014-.244-.012-.247-.011-.25-.008-.254-.005-.257-.004-.26-.001-.26.001z")},"insertDatabaseIcon"),te=(0,s.K2)(function(t){t.append("defs").append("symbol").attr("id","computer").attr("width","24").attr("height","24").append("path").attr("transform","scale(.5)").attr("d","M2 2v13h20v-13h-20zm18 11h-16v-9h16v9zm-10.228 6l.466-1h3.524l.467 1h-4.457zm14.228 3h-24l2-6h2.104l-1.33 4h18.45l-1.297-4h2.073l2 6zm-5-10h-14v-7h14v7z")},"insertComputerIcon"),ta=(0,s.K2)(function(t){t.append("defs").append("symbol").attr("id","clock").attr("width","24").attr("height","24").append("path").attr("transform","scale(.5)").attr("d","M12 2c5.514 0 10 4.486 10 10s-4.486 10-10 10-10-4.486-10-10 4.486-10 10-10zm0-2c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm5.848 12.459c.202.038.202.333.001.372-1.907.361-6.045 1.111-6.547 1.111-.719 0-1.301-.582-1.301-1.301 0-.512.77-5.447 1.125-7.445.034-.192.312-.181.343.014l.985 6.238 5.394 1.011z")},"insertClockIcon"),tn=(0,s.K2)(function(t){t.append("defs").append("marker").attr("id","arrowhead").attr("refX",9).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",12).attr("markerHeight",12).attr("orient","auto").append("path").attr("d","M 0 0 L 10 5 L 0 10 z")},"insertArrowHead"),ti=(0,s.K2)(function(t){t.append("defs").append("marker").attr("id","arrowend").attr("refX",1).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",12).attr("markerHeight",12).attr("orient","auto").append("path").attr("d","M 10 0 L 0 5 L 10 10 z")},"insertArrowEnd"),tr=(0,s.K2)(function(t){t.append("defs").append("marker").attr("id","filled-head").attr("refX",18).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L14,7 L9,1 Z")},"insertArrowFilledHead"),ts=(0,s.K2)(function(t){let e=t.append("defs").append("marker").attr("id","crosshead").attr("markerWidth",15).attr("markerHeight",8).attr("orient","auto").attr("refX",16).attr("refY",4);e.append("path").attr("fill","black").attr("stroke","#000000").style("stroke-dasharray","0, 0").attr("stroke-width","1px").attr("d","M 9,2 V 6 L16,4 Z"),e.append("path").attr("fill","none").attr("stroke","#000000").style("stroke-dasharray","0, 0").attr("stroke-width","1px").attr("d","M 0,1 L 6,7 M 6,1 L 0,7")},"insertArrowCrossHead"),tl=(0,s.K2)((t,e)=>({fontFamily:t[e+"FontFamily"],fontSize:t[e+"FontSize"],fontWeight:t[e+"FontWeight"]}),"getC4ShapeFont"),to=function(){function t(t,e,a,i,r,s,l){n(e.append("text").attr("x",a+r/2).attr("y",i+s/2+5).style("text-anchor","middle").text(t),l)}function e(t,e,a,i,r,l,o,h){let{fontSize:d,fontFamily:u,fontWeight:p}=h,y=t.split(s.Y2.lineBreakRegex);for(let t=0;t<y.length;t++){let s=t*d-d*(y.length-1)/2,l=e.append("text").attr("x",a+r/2).attr("y",i).style("text-anchor","middle").attr("dominant-baseline","middle").style("font-size",d).style("font-weight",p).style("font-family",u);l.append("tspan").attr("dy",s).text(y[t]).attr("alignment-baseline","mathematical"),n(l,o)}}function a(t,a,i,r,s,l,o,h){let d=a.append("switch"),u=d.append("foreignObject").attr("x",i).attr("y",r).attr("width",s).attr("height",l).append("xhtml:div").style("display","table").style("height","100%").style("width","100%");u.append("div").style("display","table-cell").style("text-align","center").style("vertical-align","middle").text(t),e(t,d,i,r,s,l,o,h),n(u,o)}function n(t,e){for(let a in e)e.hasOwnProperty(a)&&t.attr(a,e[a])}return(0,s.K2)(t,"byText"),(0,s.K2)(e,"byTspan"),(0,s.K2)(a,"byFo"),(0,s.K2)(n,"_setTextAttrs"),function(n){return"fo"===n.textPlacement?a:"old"===n.textPlacement?t:e}}(),tc={drawBoundary:J,drawC4Shape:Z,drawRels:G,insertArrowHead:tn,insertArrowEnd:ti,insertArrowFilledHead:tr,insertArrowCrossHead:ts,insertDatabaseIcon:tt,insertComputerIcon:te,insertClockIcon:ta},th=0,td=0,tu=4,tp=2;h.yy=H;var ty={},tb=class{static{(0,s.K2)(this,"Bounds")}constructor(t){this.name="",this.data={},this.data.startx=void 0,this.data.stopx=void 0,this.data.starty=void 0,this.data.stopy=void 0,this.data.widthLimit=void 0,this.nextData={},this.nextData.startx=void 0,this.nextData.stopx=void 0,this.nextData.starty=void 0,this.nextData.stopy=void 0,this.nextData.cnt=0,tg(t.db.getConfig())}setData(t,e,a,n){this.nextData.startx=this.data.startx=t,this.nextData.stopx=this.data.stopx=e,this.nextData.starty=this.data.starty=a,this.nextData.stopy=this.data.stopy=n}updateVal(t,e,a,n){void 0===t[e]?t[e]=a:t[e]=n(a,t[e])}insert(t){this.nextData.cnt=this.nextData.cnt+1;let e=this.nextData.startx===this.nextData.stopx?this.nextData.stopx+t.margin:this.nextData.stopx+2*t.margin,a=e+t.width,n=this.nextData.starty+2*t.margin,i=n+t.height;(e>=this.data.widthLimit||a>=this.data.widthLimit||this.nextData.cnt>tu)&&(e=this.nextData.startx+t.margin+ty.nextLinePaddingX,n=this.nextData.stopy+2*t.margin,this.nextData.stopx=a=e+t.width,this.nextData.starty=this.nextData.stopy,this.nextData.stopy=i=n+t.height,this.nextData.cnt=1),t.x=e,t.y=n,this.updateVal(this.data,"startx",e,Math.min),this.updateVal(this.data,"starty",n,Math.min),this.updateVal(this.data,"stopx",a,Math.max),this.updateVal(this.data,"stopy",i,Math.max),this.updateVal(this.nextData,"startx",e,Math.min),this.updateVal(this.nextData,"starty",n,Math.min),this.updateVal(this.nextData,"stopx",a,Math.max),this.updateVal(this.nextData,"stopy",i,Math.max)}init(t){this.name="",this.data={startx:void 0,stopx:void 0,starty:void 0,stopy:void 0,widthLimit:void 0},this.nextData={startx:void 0,stopx:void 0,starty:void 0,stopy:void 0,cnt:0},tg(t.db.getConfig())}bumpLastMargin(t){this.data.stopx+=t,this.data.stopy+=t}},tg=(0,s.K2)(function(t){(0,s.hH)(ty,t),t.fontFamily&&(ty.personFontFamily=ty.systemFontFamily=ty.messageFontFamily=t.fontFamily),t.fontSize&&(ty.personFontSize=ty.systemFontSize=ty.messageFontSize=t.fontSize),t.fontWeight&&(ty.personFontWeight=ty.systemFontWeight=ty.messageFontWeight=t.fontWeight)},"setConf"),tf=(0,s.K2)((t,e)=>({fontFamily:t[e+"FontFamily"],fontSize:t[e+"FontSize"],fontWeight:t[e+"FontWeight"]}),"c4ShapeFont"),tx=(0,s.K2)(t=>({fontFamily:t.boundaryFontFamily,fontSize:t.boundaryFontSize,fontWeight:t.boundaryFontWeight}),"boundaryFont"),t_=(0,s.K2)(t=>({fontFamily:t.messageFontFamily,fontSize:t.messageFontSize,fontWeight:t.messageFontWeight}),"messageFont");function tm(t,e,a,n,i){if(!e[t].width)if(a)e[t].text=(0,r.bH)(e[t].text,i,n),e[t].textLines=e[t].text.split(s.Y2.lineBreakRegex).length,e[t].width=i,e[t].height=(0,r.ru)(e[t].text,n);else{let a=e[t].text.split(s.Y2.lineBreakRegex);e[t].textLines=a.length;let i=0;for(let s of(e[t].height=0,e[t].width=0,a))e[t].width=Math.max((0,r.Un)(s,n),e[t].width),i=(0,r.ru)(s,n),e[t].height=e[t].height+i}}(0,s.K2)(tm,"calcC4ShapeTextWH");var tE=(0,s.K2)(function(t,e,a){e.x=a.data.startx,e.y=a.data.starty,e.width=a.data.stopx-a.data.startx,e.height=a.data.stopy-a.data.starty,e.label.y=ty.c4ShapeMargin-35;let n=e.wrap&&ty.wrap,i=tx(ty);i.fontSize=i.fontSize+2,i.fontWeight="bold";let s=(0,r.Un)(e.label.text,i);tm("label",e,n,i,s),tc.drawBoundary(t,e,ty)},"drawBoundary"),tA=(0,s.K2)(function(t,e,a,n){let i=0;for(let s of n){i=0;let n=a[s],l=tf(ty,n.typeC4Shape.text);switch(l.fontSize=l.fontSize-2,n.typeC4Shape.width=(0,r.Un)("\xab"+n.typeC4Shape.text+"\xbb",l),n.typeC4Shape.height=l.fontSize+2,n.typeC4Shape.Y=ty.c4ShapePadding,i=n.typeC4Shape.Y+n.typeC4Shape.height-4,n.image={width:0,height:0,Y:0},n.typeC4Shape.text){case"person":case"external_person":n.image.width=48,n.image.height=48,n.image.Y=i,i=n.image.Y+n.image.height}n.sprite&&(n.image.width=48,n.image.height=48,n.image.Y=i,i=n.image.Y+n.image.height);let o=n.wrap&&ty.wrap,h=ty.width-2*ty.c4ShapePadding,d=tf(ty,n.typeC4Shape.text);if(d.fontSize=d.fontSize+2,d.fontWeight="bold",tm("label",n,o,d,h),n.label.Y=i+8,i=n.label.Y+n.label.height,n.type&&""!==n.type.text){n.type.text="["+n.type.text+"]";let t=tf(ty,n.typeC4Shape.text);tm("type",n,o,t,h),n.type.Y=i+5,i=n.type.Y+n.type.height}else if(n.techn&&""!==n.techn.text){n.techn.text="["+n.techn.text+"]";let t=tf(ty,n.techn.text);tm("techn",n,o,t,h),n.techn.Y=i+5,i=n.techn.Y+n.techn.height}let u=i,p=n.label.width;if(n.descr&&""!==n.descr.text){let t=tf(ty,n.typeC4Shape.text);tm("descr",n,o,t,h),n.descr.Y=i+20,i=n.descr.Y+n.descr.height,p=Math.max(n.label.width,n.descr.width),u=i-5*n.descr.textLines}p+=ty.c4ShapePadding,n.width=Math.max(n.width||ty.width,p,ty.width),n.height=Math.max(n.height||ty.height,u,ty.height),n.margin=n.margin||ty.c4ShapeMargin,t.insert(n),tc.drawC4Shape(e,n,ty)}t.bumpLastMargin(ty.c4ShapeMargin)},"drawC4ShapeArray"),tS=class{static{(0,s.K2)(this,"Point")}constructor(t,e){this.x=t,this.y=e}},tC=(0,s.K2)(function(t,e){let a=t.x,n=t.y,i=e.x,r=e.y,s=a+t.width/2,l=n+t.height/2,o=Math.abs(a-i),h=Math.abs(n-r),d=h/o,u=t.height/t.width,p=null;return n==r&&a<i?p=new tS(a+t.width,l):n==r&&a>i?p=new tS(a,l):a==i&&n<r?p=new tS(s,n+t.height):a==i&&n>r&&(p=new tS(s,n)),a>i&&n<r?p=u>=d?new tS(a,l+d*t.width/2):new tS(s-o/h*t.height/2,n+t.height):a<i&&n<r?p=u>=d?new tS(a+t.width,l+d*t.width/2):new tS(s+o/h*t.height/2,n+t.height):a<i&&n>r?p=u>=d?new tS(a+t.width,l-d*t.width/2):new tS(s+t.height/2*o/h,n):a>i&&n>r&&(p=u>=d?new tS(a,l-t.width/2*d):new tS(s-t.height/2*o/h,n)),p},"getIntersectPoint"),tk=(0,s.K2)(function(t,e){let a={x:0,y:0};a.x=e.x+e.width/2,a.y=e.y+e.height/2;let n=tC(t,a);return a.x=t.x+t.width/2,a.y=t.y+t.height/2,{startPoint:n,endPoint:tC(e,a)}},"getIntersectPoints"),tO=(0,s.K2)(function(t,e,a,n){let i=0;for(let t of e){i+=1;let e=t.wrap&&ty.wrap,s=t_(ty);"C4Dynamic"===n.db.getC4Type()&&(t.label.text=i+": "+t.label.text);let l=(0,r.Un)(t.label.text,s);tm("label",t,e,s,l),t.techn&&""!==t.techn.text&&(l=(0,r.Un)(t.techn.text,s),tm("techn",t,e,s,l)),t.descr&&""!==t.descr.text&&(l=(0,r.Un)(t.descr.text,s),tm("descr",t,e,s,l));let o=tk(a(t.from),a(t.to));t.startPoint=o.startPoint,t.endPoint=o.endPoint}tc.drawRels(t,e,ty)},"drawRels");function tw(t,e,a,n,i){let r=new tb(i);for(let[s,l]of(r.data.widthLimit=a.data.widthLimit/Math.min(tp,n.length),n.entries())){let n=0;l.image={width:0,height:0,Y:0},l.sprite&&(l.image.width=48,l.image.height=48,l.image.Y=n,n=l.image.Y+l.image.height);let o=l.wrap&&ty.wrap,h=tx(ty);if(h.fontSize=h.fontSize+2,h.fontWeight="bold",tm("label",l,o,h,r.data.widthLimit),l.label.Y=n+8,n=l.label.Y+l.label.height,l.type&&""!==l.type.text&&(l.type.text="["+l.type.text+"]",tm("type",l,o,tx(ty),r.data.widthLimit),l.type.Y=n+5,n=l.type.Y+l.type.height),l.descr&&""!==l.descr.text){let t=tx(ty);t.fontSize=t.fontSize-2,tm("descr",l,o,t,r.data.widthLimit),l.descr.Y=n+20,n=l.descr.Y+l.descr.height}if(0==s||s%tp==0){let t=a.data.startx+ty.diagramMarginX,e=a.data.stopy+ty.diagramMarginY+n;r.setData(t,t,e,e)}else{let t=r.data.stopx!==r.data.startx?r.data.stopx+ty.diagramMarginX:r.data.startx,e=r.data.starty;r.setData(t,t,e,e)}r.name=l.alias;let d=i.db.getC4ShapeArray(l.alias),u=i.db.getC4ShapeKeys(l.alias);u.length>0&&tA(r,t,d,u),e=l.alias;let p=i.db.getBoundaries(e);p.length>0&&tw(t,e,r,p,i),"global"!==l.alias&&tE(t,l,r),a.data.stopy=Math.max(r.data.stopy+ty.c4ShapeMargin,a.data.stopy),a.data.stopx=Math.max(r.data.stopx+ty.c4ShapeMargin,a.data.stopx),th=Math.max(th,a.data.stopx),td=Math.max(td,a.data.stopy)}}(0,s.K2)(tw,"drawInsideBoundary");var tT={drawPersonOrSystemArray:tA,drawBoundary:tE,setConf:tg,draw:(0,s.K2)(function(t,e,a,n){let i;ty=(0,s.D7)().c4;let r=(0,s.D7)().securityLevel;"sandbox"===r&&(i=(0,l.Ltv)("#i"+e));let o="sandbox"===r?(0,l.Ltv)(i.nodes()[0].contentDocument.body):(0,l.Ltv)("body"),h=n.db;n.db.setWrap(ty.wrap),tu=h.getC4ShapeInRow(),tp=h.getC4BoundaryInRow(),s.Rm.debug(`C:${JSON.stringify(ty,null,2)}`);let d="sandbox"===r?o.select(`[id="${e}"]`):(0,l.Ltv)(`[id="${e}"]`);tc.insertComputerIcon(d),tc.insertDatabaseIcon(d),tc.insertClockIcon(d);let u=new tb(n);u.setData(ty.diagramMarginX,ty.diagramMarginX,ty.diagramMarginY,ty.diagramMarginY),u.data.widthLimit=screen.availWidth,th=ty.diagramMarginX,td=ty.diagramMarginY;let p=n.db.getTitle();tw(d,"",u,n.db.getBoundaries(""),n),tc.insertArrowHead(d),tc.insertArrowEnd(d),tc.insertArrowCrossHead(d),tc.insertArrowFilledHead(d),tO(d,n.db.getRels(),n.db.getC4Shape,n),u.data.stopx=th,u.data.stopy=td;let y=u.data,b=y.stopy-y.starty+2*ty.diagramMarginY,g=y.stopx-y.startx+2*ty.diagramMarginX;p&&d.append("text").text(p).attr("x",(y.stopx-y.startx)/2-4*ty.diagramMarginX).attr("y",y.starty+ty.diagramMarginY),(0,s.a$)(d,b,g,ty.useMaxWidth);let f=60*!!p;d.attr("viewBox",y.startx-ty.diagramMarginX+" -"+(ty.diagramMarginY+f)+" "+g+" "+(b+f)),s.Rm.debug("models:",y)},"draw")},tv={parser:h,db:H,renderer:tT,styles:(0,s.K2)(t=>`.person {
    stroke: ${t.personBorder};
    fill: ${t.personBkg};
  }
`,"getStyles"),init:(0,s.K2)(({c4:t,wrap:e})=>{tT.setConf(t),H.setWrap(e)},"init")}}}]);