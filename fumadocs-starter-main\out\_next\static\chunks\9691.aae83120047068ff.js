"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9691],{79691:(e,s,t)=>{t.r(s),t.d(s,{default:()=>i});var a=t(22342),n=t(47342),l=t(33670),h=t(79384),c=t(53878),r=t(371);function i(e){let{defaultTag:s,tags:t=[],api:i,delayMs:u,type:d="fetch",allowClear:x=!1,links:j=[],footer:o,...p}=e,{locale:g}=(0,c.s9)(),[f,m]=(0,l.useState)(s),{search:C,setSearch:k,query:v}=(0,n.J)("fetch"===d?{type:"fetch",api:i,locale:g,tag:f,delayMs:u}:{type:"static",from:i,locale:g,tag:f,delayMs:u}),y=(0,l.useMemo)(()=>0===j.length?null:j.map(e=>{let[s,t]=e;return{type:"page",id:s,content:s,url:t}}),[j]);return(0,h.T)(s,e=>{m(e)}),(0,a.jsxs)(r.Rc,{search:C,onSearchChange:k,isLoading:v.isLoading,...p,children:[(0,a.jsx)(r.Xq,{}),(0,a.jsxs)(r.Ct,{children:[(0,a.jsxs)(r.i,{children:[(0,a.jsx)(r.y,{}),(0,a.jsx)(r.vo,{}),(0,a.jsx)(r.hx,{})]}),(0,a.jsx)(r.FI,{items:"empty"!==v.data?v.data:y})]}),(0,a.jsxs)(r.iz,{children:[t.length>0&&(0,a.jsx)(r.Sk,{tag:f,onTagChange:m,allowClear:x,children:t.map(e=>(0,a.jsx)(r.dZ,{value:e.value,children:e.name},e.value))}),o]})]})}}}]);