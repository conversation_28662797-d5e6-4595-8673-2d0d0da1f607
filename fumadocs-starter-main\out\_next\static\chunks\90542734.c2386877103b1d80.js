"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8747],{71452:(e,t,n)=>{function r(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function i(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}function a(e,t,n){return t&&function(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,c(r.key),r)}}(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function o(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=h(e))||t){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){s=!0,a=e},f:function(){try{o||null==n.return||n.return()}finally{if(s)throw a}}}}function s(e,t,n){return(t=c(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function l(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,a,o,s=[],l=!0,u=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=a.call(n)).done)&&(s.push(r.value),s.length!==t);l=!0);}catch(e){u=!0,i=e}finally{try{if(!l&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(u)throw i}}return s}}(e,t)||h(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function u(e){return function(e){if(Array.isArray(e))return r(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||h(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function c(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t);if("object"!=typeof r)return r;throw TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e,"string");return"symbol"==typeof t?t:t+""}function d(e){return(d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function h(e,t){if(e){if("string"==typeof e)return r(e,t);var n=({}).toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?r(e,t):void 0}}n.d(t,{A:()=>cs});var f,p,g,v,y,b,x,w="undefined"==typeof window?null:window,E=w?w.navigator:null;w&&w.document;var T=d(""),C=d({}),k=d(function(){}),P="undefined"==typeof HTMLElement?"undefined":d(HTMLElement),S=function(e){return e&&e.instanceString&&D(e.instanceString)?e.instanceString():null},B=function(e){return null!=e&&d(e)==T},D=function(e){return null!=e&&d(e)===k},_=function(e){return!I(e)&&(Array.isArray?Array.isArray(e):null!=e&&e instanceof Array)},A=function(e){return null!=e&&d(e)===C&&!_(e)&&e.constructor===Object},M=function(e){return null!=e&&d(e)===d(1)&&!isNaN(e)},R=function(e){if("undefined"!==P)return null!=e&&e instanceof HTMLElement},I=function(e){return N(e)||L(e)},N=function(e){return"collection"===S(e)&&e._private.single},L=function(e){return"collection"===S(e)&&!e._private.single},O=function(e){return"core"===S(e)},z=function(e){return"stylesheet"===S(e)},V=function(e){return null==e||!!(""===e||e.match(/^\s+$/))},F=function(e){return null!=e&&d(e)===C&&D(e.then)},X=function(e,t){t||(t=function(){if(1==arguments.length)return arguments[0];if(0==arguments.length)return"undefined";for(var e=[],t=0;t<arguments.length;t++)e.push(arguments[t]);return e.join("$")});var n=function(){var r,i=arguments,a=t.apply(this,i),o=n.cache;return(r=o[a])||(r=o[a]=e.apply(this,i)),r};return n.cache={},n},j=X(function(e){return e.replace(/([A-Z])/g,function(e){return"-"+e.toLowerCase()})}),Y=X(function(e){return e.replace(/(-\w)/g,function(e){return e[1].toUpperCase()})}),q=X(function(e,t){return e+t[0].toUpperCase()+t.substring(1)},function(e,t){return e+"$"+t}),W=function(e){return V(e)?e:e.charAt(0).toUpperCase()+e.substring(1)},U=function(e,t){return e.slice(-1*t.length)===t},G="(?:[-+]?(?:(?:\\d+|\\d*\\.\\d+)(?:[Ee][+-]?\\d+)?))",H="rgb[a]?\\(("+G+"[%]?)\\s*,\\s*("+G+"[%]?)\\s*,\\s*("+G+"[%]?)(?:\\s*,\\s*("+G+"))?\\)",K="rgb[a]?\\((?:"+G+"[%]?)\\s*,\\s*(?:"+G+"[%]?)\\s*,\\s*(?:"+G+"[%]?)(?:\\s*,\\s*(?:"+G+"))?\\)",Z="hsl[a]?\\(("+G+")\\s*,\\s*("+G+"[%])\\s*,\\s*("+G+"[%])(?:\\s*,\\s*("+G+"))?\\)",$="hsl[a]?\\((?:"+G+")\\s*,\\s*(?:"+G+"[%])\\s*,\\s*(?:"+G+"[%])(?:\\s*,\\s*(?:"+G+"))?\\)",Q=function(e,t){return e<t?-1:+(e>t)},J=null!=Object.assign?Object.assign.bind(Object):function(e){for(var t=arguments,n=1;n<t.length;n++){var r=t[n];if(null!=r)for(var i=Object.keys(r),a=0;a<i.length;a++){var o=i[a];e[o]=r[o]}}return e},ee=function(e){if((4===e.length||7===e.length)&&"#"===e[0]){var t,n,r;return 4===e.length?(t=parseInt(e[1]+e[1],16),n=parseInt(e[2]+e[2],16),r=parseInt(e[3]+e[3],16)):(t=parseInt(e[1]+e[2],16),n=parseInt(e[3]+e[4],16),r=parseInt(e[5]+e[6],16)),[t,n,r]}},et=function(e){function t(e,t,n){return(n<0&&(n+=1),n>1&&(n-=1),n<1/6)?e+(t-e)*6*n:n<.5?t:n<2/3?e+(t-e)*(2/3-n)*6:e}var n,r,i,a,o,s,l,u,c=RegExp("^"+Z+"$").exec(e);if(c){if((r=parseInt(c[1]))<0?r=(360- -1*r%360)%360:r>360&&(r%=360),r/=360,(i=parseFloat(c[2]))<0||i>100||(i/=100,(a=parseFloat(c[3]))<0||a>100)||(a/=100,void 0!==(o=c[4])&&((o=parseFloat(o))<0||o>1)))return;if(0===i)s=l=u=Math.round(255*a);else{var d=a<.5?a*(1+i):a+i-a*i,h=2*a-d;s=Math.round(255*t(h,d,r+1/3)),l=Math.round(255*t(h,d,r)),u=Math.round(255*t(h,d,r-1/3))}n=[s,l,u,o]}return n},en=function(e){var t,n=RegExp("^"+H+"$").exec(e);if(n){t=[];for(var r=[],i=1;i<=3;i++){var a=n[i];if("%"===a[a.length-1]&&(r[i]=!0),a=parseFloat(a),r[i]&&(a=a/100*255),a<0||a>255)return;t.push(Math.floor(a))}var o=r[1]||r[2]||r[3],s=r[1]&&r[2]&&r[3];if(o&&!s)return;var l=n[4];if(void 0!==l){if((l=parseFloat(l))<0||l>1)return;t.push(l)}}return t},er=function(e){return(_(e)?e:null)||ei[e.toLowerCase()]||ee(e)||en(e)||et(e)},ei={transparent:[0,0,0,0],aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],grey:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]},ea=function(e){for(var t=e.map,n=e.keys,r=n.length,i=0;i<r;i++){var a=n[i];if(A(a))throw Error("Tried to set map with object key");i<n.length-1?(null==t[a]&&(t[a]={}),t=t[a]):t[a]=e.value}},eo=function(e){for(var t=e.map,n=e.keys,r=n.length,i=0;i<r;i++){var a=n[i];if(A(a))throw Error("Tried to get map with object key");if(null==(t=t[a]))break}return t},es="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function el(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function eu(){return a8?a6:(a8=1,a6=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)})}function ec(){if(on)return ot;on=1;var e=oe?a7:(oe=1,a7="object"==typeof es&&es&&es.Object===Object&&es),t="object"==typeof self&&self&&self.Object===Object&&self;return ot=e||t||Function("return this")()}function ed(){return oc?ou:(oc=1,ou=ec().Symbol)}function eh(){if(ov)return og;ov=1;var e=ed(),t=function(){if(oh)return od;oh=1;var e=ed(),t=Object.prototype,n=t.hasOwnProperty,r=t.toString,i=e?e.toStringTag:void 0;return od=function(e){var t=n.call(e,i),a=e[i];try{e[i]=void 0;var o=!0}catch(e){}var s=r.call(e);return o&&(t?e[i]=a:delete e[i]),s}}(),n=function(){if(op)return of;op=1;var e=Object.prototype.toString;return of=function(t){return e.call(t)}}(),r=e?e.toStringTag:void 0;return og=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":r&&r in Object(e)?t(e):n(e)}}function ef(){if(ox)return ob;ox=1;var e=eh(),t=om?oy:(om=1,oy=function(e){return null!=e&&"object"==typeof e});return ob=function(n){return"symbol"==typeof n||t(n)&&"[object Symbol]"==e(n)}}var ep=el(function(){if(oC)return oT;oC=1;var e=eu(),t=function(){if(oi)return or;oi=1;var e=ec();return or=function(){return e.Date.now()}}(),n=function(){if(oE)return ow;oE=1;var e=function(){if(ol)return os;ol=1;var e=function(){if(oo)return oa;oo=1;var e=/\s/;return oa=function(t){for(var n=t.length;n--&&e.test(t.charAt(n)););return n}}(),t=/^\s+/;return os=function(n){return n?n.slice(0,e(n)+1).replace(t,""):n}}(),t=eu(),n=ef(),r=0/0,i=/^[-+]0x[0-9a-f]+$/i,a=/^0b[01]+$/i,o=/^0o[0-7]+$/i,s=parseInt;return ow=function(l){if("number"==typeof l)return l;if(n(l))return r;if(t(l)){var u="function"==typeof l.valueOf?l.valueOf():l;l=t(u)?u+"":u}if("string"!=typeof l)return 0===l?l:+l;l=e(l);var c=a.test(l);return c||o.test(l)?s(l.slice(2),c?2:8):i.test(l)?r:+l}}(),r=Math.max,i=Math.min;return oT=function(a,o,s){var l,u,c,d,h,f,p=0,g=!1,v=!1,y=!0;if("function"!=typeof a)throw TypeError("Expected a function");function b(e){var t=l,n=u;return l=u=void 0,p=e,d=a.apply(n,t)}function x(e){var t=e-f,n=e-p;return void 0===f||t>=o||t<0||v&&n>=c}function w(){var e,n,r,a=t();if(x(a))return E(a);h=setTimeout(w,(e=a-f,n=a-p,r=o-e,v?i(r,c-n):r))}function E(e){return(h=void 0,y&&l)?b(e):(l=u=void 0,d)}function T(){var e,n=t(),r=x(n);if(l=arguments,u=this,f=n,r){if(void 0===h)return p=e=f,h=setTimeout(w,o),g?b(e):d;if(v)return clearTimeout(h),h=setTimeout(w,o),b(f)}return void 0===h&&(h=setTimeout(w,o)),d}return o=n(o)||0,e(s)&&(g=!!s.leading,c=(v="maxWait"in s)?r(n(s.maxWait)||0,o):c,y="trailing"in s?!!s.trailing:y),T.cancel=function(){void 0!==h&&clearTimeout(h),p=0,l=f=u=h=void 0},T.flush=function(){return void 0===h?d:E(t())},T}}()),eg=w?w.performance:null,ev=eg&&eg.now?function(){return eg.now()}:function(){return Date.now()},ey=function(){if(w){if(w.requestAnimationFrame)return function(e){w.requestAnimationFrame(e)};else if(w.mozRequestAnimationFrame)return function(e){w.mozRequestAnimationFrame(e)};else if(w.webkitRequestAnimationFrame)return function(e){w.webkitRequestAnimationFrame(e)};else if(w.msRequestAnimationFrame)return function(e){w.msRequestAnimationFrame(e)}}return function(e){e&&setTimeout(function(){e(ev())},1e3/60)}}(),em=function(e){return ey(e)},eb=function(e){for(var t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:9261,r=n;!(t=e.next()).done;)r=65599*r+t.value|0;return r},ex=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:9261;return 65599*t+e|0},ew=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:5381;return(t<<5)+t+e|0},eE=function(e){return 2097152*e[0]+e[1]},eT=function(e,t){return[ex(e[0],t[0]),ew(e[1],t[1])]},eC=function(e,t){var n={value:0,done:!1},r=0,i=e.length;return eb({next:function(){return r<i?n.value=e[r++]:n.done=!0,n}},t)},ek=function(e,t){var n={value:0,done:!1},r=0,i=e.length;return eb({next:function(){return r<i?n.value=e.charCodeAt(r++):n.done=!0,n}},t)},eP=function(){return eS(arguments)},eS=function(e){for(var t,n=0;n<e.length;n++){var r=e[n];t=0===n?ek(r):ek(r,t)}return t},eB=!0,eD=null!=console.warn,e_=null!=console.trace,eA=Number.MAX_SAFE_INTEGER||0x1fffffffffffff,eM=function(){return!0},eR=function(){return!1},eI=function(){return 0},eN=function(){},eL=function(e){throw Error(e)},eO=function(e){if(void 0===e)return eB;eB=!!e},ez=function(e){eO()&&(eD?console.warn(e):(console.log(e),e_&&console.trace()))},eV=function(e){return null==e?e:_(e)?e.slice():A(e)?J({},e):e},eF=function(e,t){for(t=e="";e++<36;t+=51*e&52?(15^e?8^Math.random()*(20^e?16:4):4).toString(16):"-");return t},eX={},ej=function(){return eX},eY=function(e){var t=Object.keys(e);return function(n){for(var r={},i=0;i<t.length;i++){var a=t[i],o=null==n?void 0:n[a];r[a]=void 0===o?e[a]:o}return r}},eq=function(e,t,n){for(var r=e.length-1;r>=0;r--)e[r]===t&&e.splice(r,1)},eW=function(e){e.splice(0,e.length)},eU=function(e,t){for(var n=0;n<t.length;n++){var r=t[n];e.push(r)}},eG=function(e,t,n){return n&&(t=q(n,t)),e[t]},eH=function(e,t,n,r){n&&(t=q(n,t)),e[t]=r},eK=a(function e(){i(this,e),this._obj={}},[{key:"set",value:function(e,t){return this._obj[e]=t,this}},{key:"delete",value:function(e){return this._obj[e]=void 0,this}},{key:"clear",value:function(){this._obj={}}},{key:"has",value:function(e){return void 0!==this._obj[e]}},{key:"get",value:function(e){return this._obj[e]}}]),eZ="undefined"!=typeof Map?Map:eK,e$=a(function e(t){if(i(this,e),this._obj=Object.create(null),this.size=0,null!=t){var n;n=null!=t.instanceString&&t.instanceString()===this.instanceString()?t.toArray():t;for(var r=0;r<n.length;r++)this.add(n[r])}},[{key:"instanceString",value:function(){return"set"}},{key:"add",value:function(e){var t=this._obj;1!==t[e]&&(t[e]=1,this.size++)}},{key:"delete",value:function(e){var t=this._obj;1===t[e]&&(t[e]=0,this.size--)}},{key:"clear",value:function(){this._obj=Object.create(null)}},{key:"has",value:function(e){return 1===this._obj[e]}},{key:"toArray",value:function(){var e=this;return Object.keys(this._obj).filter(function(t){return e.has(t)})}},{key:"forEach",value:function(e,t){return this.toArray().forEach(e,t)}}]),eQ=("undefined"==typeof Set?"undefined":d(Set))!=="undefined"?Set:e$,eJ=function(e,t){var n=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(void 0===e||void 0===t||!O(e))return void eL("An element must have a core reference and parameters set");var r=t.group;if(null==r&&(r=t.data&&null!=t.data.source&&null!=t.data.target?"edges":"nodes"),"nodes"!==r&&"edges"!==r)return void eL("An element must be of type `nodes` or `edges`; you specified `"+r+"`");this.length=1,this[0]=this;var i=this._private={cy:e,single:!0,data:t.data||{},position:t.position||{x:0,y:0},autoWidth:void 0,autoHeight:void 0,autoPadding:void 0,compoundBoundsClean:!1,listeners:[],group:r,style:{},rstyle:{},styleCxts:[],styleKeys:{},removed:!0,selected:!!t.selected,selectable:void 0===t.selectable||!!t.selectable,locked:!!t.locked,grabbed:!1,grabbable:void 0===t.grabbable||!!t.grabbable,pannable:void 0===t.pannable?"edges"===r:!!t.pannable,active:!1,classes:new eQ,animation:{current:[],queue:[]},rscratch:{},scratch:t.scratch||{},edges:[],children:[],parent:t.parent&&t.parent.isNode()?t.parent:null,traversalCache:{},backgrounding:!1,bbCache:null,bbCacheShift:{x:0,y:0},bodyBounds:null,overlayBounds:null,labelBounds:{all:null,source:null,target:null,main:null},arrowBounds:{source:null,target:null,"mid-source":null,"mid-target":null}};if(null==i.position.x&&(i.position.x=0),null==i.position.y&&(i.position.y=0),t.renderedPosition){var a=t.renderedPosition,o=e.pan(),s=e.zoom();i.position={x:(a.x-o.x)/s,y:(a.y-o.y)/s}}var l=[];_(t.classes)?l=t.classes:B(t.classes)&&(l=t.classes.split(/\s+/));for(var u=0,c=l.length;u<c;u++){var d=l[u];d&&""!==d&&i.classes.add(d)}this.createEmitter(),(void 0===n||n)&&this.restore();var h=t.style||t.css;h&&(ez("Setting a `style` bypass at element creation should be done only when absolutely necessary.  Try to use the stylesheet instead."),this.style(h))},e0=function(e){return e={bfs:e.bfs||!e.dfs,dfs:e.dfs||!e.bfs},function(t,n,r){A(t)&&!I(t)&&(t=(i=t).roots||i.root,n=i.visit,r=i.directed),r=2!=arguments.length||D(n)?r:n,n=D(n)?n:function(){};for(var i,a,o,s=this._private.cy,l=t=B(t)?this.filter(t):t,u=[],c=[],d={},h={},f={},p=0,g=this.byGroup(),v=g.nodes,y=g.edges,b=0;b<l.length;b++){var x=l[b],w=x.id();x.isNode()&&(u.unshift(x),e.bfs&&(f[w]=!0,c.push(x)),h[w]=0)}for(;0!==u.length&&(0===(o=function(){var t,i=e.bfs?u.shift():u.pop(),o=i.id();if(e.dfs){if(f[o])return 0;f[o]=!0,c.push(i)}var s=h[o],l=d[o],g=null!=l?l.source():null,b=null!=l?l.target():null,x=null==l?void 0:i.same(g)?b[0]:g[0];if(!0===(t=n(i,l,x,p++,s)))return a=i,1;if(!1===t)return 1;for(var w=i.connectedEdges().filter(function(e){return(!r||e.source().same(i))&&y.has(e)}),E=0;E<w.length;E++){var T=w[E],C=T.connectedNodes().filter(function(e){return!e.same(i)&&v.has(e)}),k=C.id();0===C.length||f[k]||(C=C[0],u.push(C),e.bfs&&(f[k]=!0,c.push(C)),d[k]=T,h[k]=h[o]+1)}}())||1!==o););for(var E=s.collection(),T=0;T<c.length;T++){var C=c[T],k=d[C.id()];null!=k&&E.push(k),E.push(C)}return{path:s.collection(E),found:s.collection(a)}}},e1={breadthFirstSearch:e0({bfs:!0}),depthFirstSearch:e0({dfs:!0})};e1.bfs=e1.breadthFirstSearch,e1.dfs=e1.depthFirstSearch;var e2={exports:{}},e5=e2.exports,e3=el(oS?oP:(oS=1,oP=function(){return ok?e2.exports:(ok=1,(function(){var e,t,n,r,i,a,o,s,l,u,c,d,h,f,p;n=Math.floor,u=Math.min,t=function(e,t){return e<t?-1:+(e>t)},l=function(e,r,i,a,o){var s;if(null==i&&(i=0),null==o&&(o=t),i<0)throw Error("lo must be non-negative");for(null==a&&(a=e.length);i<a;)0>o(r,e[s=n((i+a)/2)])?a=s:i=s+1;return[].splice.apply(e,[i,i-i].concat(r)),r},a=function(e,n,r){return null==r&&(r=t),e.push(n),f(e,0,e.length-1,r)},i=function(e,n){var r,i;return null==n&&(n=t),r=e.pop(),e.length?(i=e[0],e[0]=r,p(e,0,n)):i=r,i},s=function(e,n,r){var i;return null==r&&(r=t),i=e[0],e[0]=n,p(e,0,r),i},o=function(e,n,r){var i;return null==r&&(r=t),e.length&&0>r(e[0],n)&&(n=(i=[e[0],n])[0],e[0]=i[1],p(e,0,r)),n},r=function(e,r){var i,a,o,s,l,u;for(null==r&&(r=t),s=(function(){u=[];for(var t=0,r=n(e.length/2);0<=r?t<r:t>r;0<=r?t++:t--)u.push(t);return u}).apply(this).reverse(),l=[],a=0,o=s.length;a<o;a++)i=s[a],l.push(p(e,i,r));return l},h=function(e,n,r){var i;if(null==r&&(r=t),-1!==(i=e.indexOf(n)))return f(e,0,i,r),p(e,i,r)},c=function(e,n,i){var a,s,l,u;if(null==i&&(i=t),!(a=e.slice(0,n)).length)return a;for(r(a,i),s=0,l=(u=e.slice(n)).length;s<l;s++)o(a,u[s],i);return a.sort(i).reverse()},d=function(e,n,a){var o,s,c,d,h,f,p,g,v;if(null==a&&(a=t),10*n<=e.length){if(!(c=e.slice(0,n).sort(a)).length)return c;for(d=0,s=c[c.length-1],f=(p=e.slice(n)).length;d<f;d++)0>a(o=p[d],s)&&(l(c,o,0,null,a),c.pop(),s=c[c.length-1]);return c}for(r(e,a),v=[],h=0,g=u(n,e.length);0<=g?h<g:h>g;0<=g?++h:--h)v.push(i(e,a));return v},f=function(e,n,r,i){var a,o,s;for(null==i&&(i=t),a=e[r];r>n;){if(0>i(a,o=e[s=r-1>>1])){e[r]=o,r=s;continue}break}return e[r]=a},p=function(e,n,r){var i,a,o,s,l;for(null==r&&(r=t),a=e.length,l=n,o=e[n],i=2*n+1;i<a;)(s=i+1)<a&&!(0>r(e[i],e[s]))&&(i=s),e[n]=e[i],i=2*(n=i)+1;return e[n]=o,f(e,l,n,r)},e=function(){function e(e){this.cmp=null!=e?e:t,this.nodes=[]}return e.push=a,e.pop=i,e.replace=s,e.pushpop=o,e.heapify=r,e.updateItem=h,e.nlargest=c,e.nsmallest=d,e.prototype.push=function(e){return a(this.nodes,e,this.cmp)},e.prototype.pop=function(){return i(this.nodes,this.cmp)},e.prototype.peek=function(){return this.nodes[0]},e.prototype.contains=function(e){return -1!==this.nodes.indexOf(e)},e.prototype.replace=function(e){return s(this.nodes,e,this.cmp)},e.prototype.pushpop=function(e){return o(this.nodes,e,this.cmp)},e.prototype.heapify=function(){return r(this.nodes,this.cmp)},e.prototype.updateItem=function(e){return h(this.nodes,e,this.cmp)},e.prototype.clear=function(){return this.nodes=[]},e.prototype.empty=function(){return 0===this.nodes.length},e.prototype.size=function(){return this.nodes.length},e.prototype.clone=function(){var t;return(t=new e).nodes=this.nodes.slice(0),t},e.prototype.toArray=function(){return this.nodes.slice(0)},e.prototype.insert=e.prototype.push,e.prototype.top=e.prototype.peek,e.prototype.front=e.prototype.peek,e.prototype.has=e.prototype.contains,e.prototype.copy=e.prototype.clone,e}(),e2.exports=e}).call(e5),e2.exports)}())),e4=eY({root:null,weight:function(e){return 1},directed:!1}),e9=eY({root:null,goal:null,weight:function(e){return 1},heuristic:function(e){return 0},directed:!1}),e6=eY({weight:function(e){return 1},directed:!1}),e8=eY({weight:function(e){return 1},directed:!1,root:null}),e7=Math.sqrt(2),te=function(e,t,n){0===n.length&&eL("Karger-Stein must be run on a connected (sub)graph");for(var r=n[e],i=r[1],a=r[2],o=t[i],s=t[a],l=n.length-1;l>=0;l--){var u=n[l],c=u[1],d=u[2];(t[c]===o&&t[d]===s||t[c]===s&&t[d]===o)&&n.splice(l,1)}for(var h=0;h<n.length;h++){var f=n[h];f[1]===s?(n[h]=f.slice(),n[h][1]=o):f[2]===s&&(n[h]=f.slice(),n[h][2]=o)}for(var p=0;p<t.length;p++)t[p]===s&&(t[p]=o);return n},tt=function(e,t,n,r){for(;n>r;)t=te(Math.floor(Math.random()*t.length),e,t),n--;return t},tn=function(e,t,n){return{x:e.x*t+n.x,y:e.y*t+n.y}},tr=function(e,t,n){return{x:(e.x-n.x)/t,y:(e.y-n.y)/t}},ti=function(e){return{x:e[0],y:e[1]}},ta=function(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e.length,r=1/0,i=t;i<n;i++){var a=e[i];isFinite(a)&&(r=Math.min(a,r))}return r},to=function(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e.length,r=-1/0,i=t;i<n;i++){var a=e[i];isFinite(a)&&(r=Math.max(a,r))}return r},ts=function(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e.length,r=0,i=0,a=t;a<n;a++){var o=e[a];isFinite(o)&&(r+=o,i++)}return r/i},tl=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e.length,r=!(arguments.length>3)||void 0===arguments[3]||arguments[3],i=!(arguments.length>4)||void 0===arguments[4]||arguments[4],a=!(arguments.length>5)||void 0===arguments[5]||arguments[5];r?e=e.slice(t,n):(n<e.length&&e.splice(n,e.length-n),t>0&&e.splice(0,t));for(var o=0,s=e.length-1;s>=0;s--){var l=e[s];a?!isFinite(l)&&(e[s]=-1/0,o++):e.splice(s,1)}i&&e.sort(function(e,t){return e-t});var u=e.length,c=Math.floor(u/2);return u%2!=0?e[c+1+o]:(e[c-1+o]+e[c+o])/2},tu=function(e,t){return Math.atan2(t,e)-Math.PI/2},tc=Math.log2||function(e){return Math.log(e)/Math.log(2)},td=function(e){return e>0?1:e<0?-1:0},th=function(e,t){return Math.sqrt(tf(e,t))},tf=function(e,t){var n=t.x-e.x,r=t.y-e.y;return n*n+r*r},tp=function(e){for(var t=e.length,n=0,r=0;r<t;r++)n+=e[r];for(var i=0;i<t;i++)e[i]=e[i]/n;return e},tg=function(e,t,n,r){return(1-r)*(1-r)*e+2*(1-r)*r*t+r*r*n},tv=function(e,t,n,r){return{x:tg(e.x,t.x,n.x,r),y:tg(e.y,t.y,n.y,r)}},ty=function(e,t,n,r){var i={x:t.x-e.x,y:t.y-e.y},a=th(e,t),o={x:i.x/a,y:i.y/a};return n=null==n?0:n,r=null!=r?r:n*a,{x:e.x+o.x*r,y:e.y+o.y*r}},tm=function(e,t,n){return Math.max(e,Math.min(n,t))},tb=function(e){if(null==e)return{x1:1/0,y1:1/0,x2:-1/0,y2:-1/0,w:0,h:0};if(null!=e.x1&&null!=e.y1){if(null!=e.x2&&null!=e.y2&&e.x2>=e.x1&&e.y2>=e.y1)return{x1:e.x1,y1:e.y1,x2:e.x2,y2:e.y2,w:e.x2-e.x1,h:e.y2-e.y1};else if(null!=e.w&&null!=e.h&&e.w>=0&&e.h>=0)return{x1:e.x1,y1:e.y1,x2:e.x1+e.w,y2:e.y1+e.h,w:e.w,h:e.h}}},tx=function(e){e.x1=1/0,e.y1=1/0,e.x2=-1/0,e.y2=-1/0,e.w=0,e.h=0},tw=function(e,t){e.x1=Math.min(e.x1,t.x1),e.x2=Math.max(e.x2,t.x2),e.w=e.x2-e.x1,e.y1=Math.min(e.y1,t.y1),e.y2=Math.max(e.y2,t.y2),e.h=e.y2-e.y1},tE=function(e,t,n){e.x1=Math.min(e.x1,t),e.x2=Math.max(e.x2,t),e.w=e.x2-e.x1,e.y1=Math.min(e.y1,n),e.y2=Math.max(e.y2,n),e.h=e.y2-e.y1},tT=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return e.x1-=t,e.x2+=t,e.y1-=t,e.y2+=t,e.w=e.x2-e.x1,e.h=e.y2-e.y1,e},tC=function(e){var t,n,r,i,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[0];if(1===a.length)t=n=r=i=a[0];else if(2===a.length)t=r=a[0],i=n=a[1];else if(4===a.length){var o=l(a,4);t=o[0],n=o[1],r=o[2],i=o[3]}return e.x1-=i,e.x2+=n,e.y1-=t,e.y2+=r,e.w=e.x2-e.x1,e.h=e.y2-e.y1,e},tk=function(e,t){e.x1=t.x1,e.y1=t.y1,e.x2=t.x2,e.y2=t.y2,e.w=e.x2-e.x1,e.h=e.y2-e.y1},tP=function(e,t){return!(e.x1>t.x2)&&!(t.x1>e.x2)&&!(e.x2<t.x1)&&!(t.x2<e.x1)&&!(e.y2<t.y1)&&!(t.y2<e.y1)&&!(e.y1>t.y2)&&!(t.y1>e.y2)&&!0},tS=function(e,t,n){return e.x1<=t&&t<=e.x2&&e.y1<=n&&n<=e.y2},tB=function(e,t){return tS(e,t.x,t.y)},tD=function(e,t){return tS(e,t.x1,t.y1)&&tS(e,t.x2,t.y2)},t_=null!=(oB=Math.hypot)?oB:function(e,t){return Math.sqrt(e*e+t*t)},tA=function(e,t,n,r,i,a,o){var s,l,u=arguments.length>7&&void 0!==arguments[7]?arguments[7]:"auto",c="auto"===u?t1(i,a):u,d=i/2,h=a/2,f=(c=Math.min(c,d,h))!==d,p=c!==h;if(f){var g=n-d+c-o,v=r-h-o,y=n+d-c+o;if((s=tG(e,t,n,r,g,v,y,v,!1)).length>0)return s}if(p){var b=n+d+o,x=r-h+c-o,w=r+h-c+o;if((s=tG(e,t,n,r,b,x,b,w,!1)).length>0)return s}if(f){var E=n-d+c-o,T=r+h+o,C=n+d-c+o;if((s=tG(e,t,n,r,E,T,C,T,!1)).length>0)return s}if(p){var k=n-d-o,P=r-h+c-o,S=r+h-c+o;if((s=tG(e,t,n,r,k,P,k,S,!1)).length>0)return s}var B=n-d+c,D=r-h+c;if((l=tW(e,t,n,r,B,D,c+o)).length>0&&l[0]<=B&&l[1]<=D)return[l[0],l[1]];var _=n+d-c,A=r-h+c;if((l=tW(e,t,n,r,_,A,c+o)).length>0&&l[0]>=_&&l[1]<=A)return[l[0],l[1]];var M=n+d-c,R=r+h-c;if((l=tW(e,t,n,r,M,R,c+o)).length>0&&l[0]>=M&&l[1]>=R)return[l[0],l[1]];var I=n-d+c,N=r+h-c;return(l=tW(e,t,n,r,I,N,c+o)).length>0&&l[0]<=I&&l[1]>=N?[l[0],l[1]]:[]},tM=function(e,t,n,r,i,a,o){var s=Math.min(n,i),l=Math.max(n,i),u=Math.min(r,a),c=Math.max(r,a);return s-o<=e&&e<=l+o&&u-o<=t&&t<=c+o},tR=function(e,t,n,r,i,a,o,s,l){var u={x1:Math.min(n,o,i)-l,x2:Math.max(n,o,i)+l,y1:Math.min(r,s,a)-l,y2:Math.max(r,s,a)+l};return!(e<u.x1)&&!(e>u.x2)&&!(t<u.y1)&&!(t>u.y2)},tI=function(e,t,n,r){var i=t*t-4*e*(n-=r);if(i<0)return[];var a=Math.sqrt(i),o=2*e;return[(-t+a)/o,(-t-a)/o]},tN=function(e,t,n,r,i){var a,o,s,l,u,c,d,h;if(0===e&&(e=1e-5),t/=e,n/=e,r/=e,a=(o=(3*n-t*t)/9)*o*o+(s=(-(27*r)+t*(9*n-t*t*2))/54)*s,i[1]=0,d=t/3,a>0){u=(u=s+Math.sqrt(a))<0?-Math.pow(-u,1/3):Math.pow(u,1/3),c=(c=s-Math.sqrt(a))<0?-Math.pow(-c,1/3):Math.pow(c,1/3),i[0]=-d+u+c,d+=(u+c)/2,i[4]=i[2]=-d,d=Math.sqrt(3)*(-c+u)/2,i[3]=d,i[5]=-d;return}if(i[5]=i[3]=0,0===a){h=s<0?-Math.pow(-s,1/3):Math.pow(s,1/3),i[0]=-d+2*h,i[4]=i[2]=-(h+d);return}l=Math.acos(s/Math.sqrt(l=(o=-o)*o*o)),h=2*Math.sqrt(o),i[0]=-d+h*Math.cos(l/3),i[2]=-d+h*Math.cos((l+2*Math.PI)/3),i[4]=-d+h*Math.cos((l+4*Math.PI)/3)},tL=function(e,t,n,r,i,a,o,s){var l,u=[];tN(n*n-4*n*i+2*n*o+4*i*i-4*i*o+o*o+r*r-4*r*a+2*r*s+4*a*a-4*a*s+s*s,9*n*i-3*n*n-3*n*o-6*i*i+3*i*o+9*r*a-3*r*r-3*r*s-6*a*a+3*a*s,3*n*n-6*n*i+n*o-n*e+2*i*i+2*i*e-o*e+3*r*r-6*r*a+r*s-r*t+2*a*a+2*a*t-s*t,n*i-n*n+n*e-i*e+r*a-r*r+r*t-a*t,u);for(var c=[],d=0;d<6;d+=2)1e-7>Math.abs(u[d+1])&&u[d]>=0&&u[d]<=1&&c.push(u[d]);c.push(1),c.push(0);for(var h=-1,f=0;f<c.length;f++)l=Math.pow(Math.pow(1-c[f],2)*n+2*(1-c[f])*c[f]*i+c[f]*c[f]*o-e,2)+Math.pow(Math.pow(1-c[f],2)*r+2*(1-c[f])*c[f]*a+c[f]*c[f]*s-t,2),h>=0?l<h&&(h=l):h=l;return h},tO=function(e,t,n,r,i,a){var o=[e-n,t-r],s=[i-n,a-r],l=s[0]*s[0]+s[1]*s[1],u=o[0]*o[0]+o[1]*o[1],c=o[0]*s[0]+o[1]*s[1],d=c*c/l;return c<0?u:d>l?(e-i)*(e-i)+(t-a)*(t-a):u-d},tz=function(e,t,n){for(var r,i,a,o,s=0,l=0;l<n.length/2;l++)if(r=n[2*l],i=n[2*l+1],l+1<n.length/2?(a=n[(l+1)*2],o=n[(l+1)*2+1]):(a=n[(l+1-n.length/2)*2],o=n[(l+1-n.length/2)*2+1]),r==e&&a==e);else{if((!(r>=e)||!(e>=a))&&(!(r<=e)||!(e<=a)))continue;(e-r)/(a-r)*(o-i)+i>t&&s++}return s%2!=0},tV=function(e,t,n,r,i,a,o,s,l){var u,c=Array(n.length);null!=s[0]?(u=Math.atan(s[1]/s[0]),s[0]<0?u+=Math.PI/2:u=-u-Math.PI/2):u=s;for(var d=Math.cos(-u),h=Math.sin(-u),f=0;f<c.length/2;f++)c[2*f]=a/2*(n[2*f]*d-n[2*f+1]*h),c[2*f+1]=o/2*(n[2*f+1]*d+n[2*f]*h),c[2*f]+=r,c[2*f+1]+=i;return tz(e,t,l>0?tX(tj(c,-l)):c)},tF=function(e,t,n,r,i,a,o,s){for(var l=Array(2*n.length),u=0;u<s.length;u++){var c=s[u];if(l[4*u+0]=c.startX,l[4*u+1]=c.startY,l[4*u+2]=c.stopX,l[4*u+3]=c.stopY,Math.pow(c.cx-e,2)+Math.pow(c.cy-t,2)<=Math.pow(c.radius,2))return!0}return tz(e,t,l)},tX=function(e){for(var t,n,r,i,a,o,s,l,u=Array(e.length/2),c=0;c<e.length/4;c++){t=e[4*c],n=e[4*c+1],r=e[4*c+2],i=e[4*c+3],c<e.length/4-1?(a=e[(c+1)*4],o=e[(c+1)*4+1],s=e[(c+1)*4+2],l=e[(c+1)*4+3]):(a=e[0],o=e[1],s=e[2],l=e[3]);var d=tG(t,n,r,i,a,o,s,l,!0);u[2*c]=d[0],u[2*c+1]=d[1]}return u},tj=function(e,t){for(var n,r,i,a,o=Array(2*e.length),s=0;s<e.length/2;s++){n=e[2*s],r=e[2*s+1],s<e.length/2-1?(i=e[(s+1)*2],a=e[(s+1)*2+1]):(i=e[0],a=e[1]);var l=a-r,u=-(i-n),c=Math.sqrt(l*l+u*u),d=l/c,h=u/c;o[4*s]=n+d*t,o[4*s+1]=r+h*t,o[4*s+2]=i+d*t,o[4*s+3]=a+h*t}return o},tY=function(e,t,n,r,i,a){var o=n-e,s=r-t,l=Math.sqrt((o/=i)*o+(s/=a)*s),u=l-1;if(u<0)return[];var c=u/l;return[(n-e)*c+e,(r-t)*c+t]},tq=function(e,t,n,r,i,a,o){return e-=i,t-=a,(e/=n/2+o)*e+(t/=r/2+o)*t<=1},tW=function(e,t,n,r,i,a,o){var s=[n-e,r-t],l=[e-i,t-a],u=s[0]*s[0]+s[1]*s[1],c=2*(l[0]*s[0]+l[1]*s[1]),d=c*c-4*u*(l[0]*l[0]+l[1]*l[1]-o*o);if(d<0)return[];var h=(-c+Math.sqrt(d))/(2*u),f=(-c-Math.sqrt(d))/(2*u),p=Math.min(h,f),g=Math.max(h,f),v=[];if(p>=0&&p<=1&&v.push(p),g>=0&&g<=1&&v.push(g),0===v.length)return[];var y=v[0]*s[0]+e,b=v[0]*s[1]+t;return v.length>1?v[0]==v[1]?[y,b]:[y,b,v[1]*s[0]+e,v[1]*s[1]+t]:[y,b]},tU=function(e,t,n){return t<=e&&e<=n||n<=e&&e<=t?e:e<=t&&t<=n||n<=t&&t<=e?t:n},tG=function(e,t,n,r,i,a,o,s,l){var u=e-i,c=n-e,d=o-i,h=t-a,f=r-t,p=s-a,g=d*h-p*u,v=c*h-f*u,y=p*c-d*f;if(0!==y){var b=g/y,x=v/y;return -.001<=b&&b<=1.001&&-.001<=x&&x<=1.001||l?[e+b*c,t+b*f]:[]}return 0!==g&&0!==v?[]:tU(e,n,o)===o?[o,s]:tU(e,n,i)===i?[i,a]:tU(i,o,n)===n?[n,r]:[]},tH=function(e,t,n,r,i){var a=[],o=r/2,s=i/2;a.push({x:t+o*e[0],y:n+s*e[1]});for(var l=1;l<e.length/2;l++)a.push({x:t+o*e[2*l],y:n+s*e[2*l+1]});return a},tK=function(e,t,n,r,i,a,o,s){var l,u,c,d,h,f,p=[],g=Array(n.length),v=!0;if(null==a&&(v=!1),v){for(var y=0;y<g.length/2;y++)g[2*y]=n[2*y]*a+r,g[2*y+1]=n[2*y+1]*o+i;u=s>0?tX(tj(g,-s)):g}else u=n;for(var b=0;b<u.length/2;b++)c=u[2*b],d=u[2*b+1],b<u.length/2-1?(h=u[(b+1)*2],f=u[(b+1)*2+1]):(h=u[0],f=u[1]),0!==(l=tG(e,t,r,i,c,d,h,f)).length&&p.push(l[0],l[1]);return p},tZ=function(e,t,n,r,i,a,o,s,l){var u,c=[],d=Array(2*n.length);l.forEach(function(n,a){0===a?(d[d.length-2]=n.startX,d[d.length-1]=n.startY):(d[4*a-2]=n.startX,d[4*a-1]=n.startY),d[4*a]=n.stopX,d[4*a+1]=n.stopY,0!==(u=tW(e,t,r,i,n.cx,n.cy,n.radius)).length&&c.push(u[0],u[1])});for(var h=0;h<d.length/4;h++)0!==(u=tG(e,t,r,i,d[4*h],d[4*h+1],d[4*h+2],d[4*h+3],!1)).length&&c.push(u[0],u[1]);if(c.length>2){for(var f=[c[0],c[1]],p=Math.pow(f[0]-e,2)+Math.pow(f[1]-t,2),g=1;g<c.length/2;g++){var v=Math.pow(c[2*g]-e,2)+Math.pow(c[2*g+1]-t,2);v<=p&&(f[0]=c[2*g],f[1]=c[2*g+1],p=v)}return f}return c},t$=function(e,t,n){var r=[e[0]-t[0],e[1]-t[1]],i=Math.sqrt(r[0]*r[0]+r[1]*r[1]),a=(i-n)/i;return a<0&&(a=1e-5),[t[0]+a*r[0],t[1]+a*r[1]]},tQ=function(e,t){var n=t0(e,t);return tJ(n)},tJ=function(e){for(var t,n,r=e.length/2,i=1/0,a=1/0,o=-1/0,s=-1/0,l=0;l<r;l++)t=e[2*l],n=e[2*l+1],i=Math.min(i,t),o=Math.max(o,t),a=Math.min(a,n),s=Math.max(s,n);for(var u=2/(o-i),c=2/(s-a),d=0;d<r;d++)t=e[2*d]=e[2*d]*u,n=e[2*d+1]=e[2*d+1]*c,i=Math.min(i,t),o=Math.max(o,t),a=Math.min(a,n),s=Math.max(s,n);if(a<-1)for(var h=0;h<r;h++)n=e[2*h+1]=e[2*h+1]+(-1-a);return e},t0=function(e,t){var n,r=1/e*2*Math.PI,i=e%2==0?Math.PI/2+r/2:Math.PI/2;i+=t;for(var a=Array(2*e),o=0;o<e;o++)n=o*r+i,a[2*o]=Math.cos(n),a[2*o+1]=Math.sin(-n);return a},t1=function(e,t){return Math.min(e/4,t/4,8)},t2=function(e,t){return Math.min(e/10,t/10,8)},t5=function(){return 8},t3=function(e,t){return{heightOffset:Math.min(15,.05*t),widthOffset:Math.min(100,.25*e),ctrlPtOffsetPct:.05}};function t4(e,t){function n(e){for(var t=[],n=0;n<e.length;n++){var r=e[n],i=e[(n+1)%e.length],a={x:i.x-r.x,y:i.y-r.y},o={x:-a.y,y:a.x},s=Math.sqrt(o.x*o.x+o.y*o.y);t.push({x:o.x/s,y:o.y/s})}return t}function r(e,t){var n,r=1/0,i=-1/0,a=o(e);try{for(a.s();!(n=a.n()).done;){var s=n.value,l=s.x*t.x+s.y*t.y;r=Math.min(r,l),i=Math.max(i,l)}}catch(e){a.e(e)}finally{a.f()}return{min:r,max:i}}var i,a=o([].concat(u(n(e)),u(n(t))));try{for(a.s();!(i=a.n()).done;){var s=i.value,l=r(e,s),c=r(t,s);if(l.max<c.min||c.max<l.min)return!1}}catch(e){a.e(e)}finally{a.f()}return!0}var t9=eY({dampingFactor:.8,precision:1e-6,iterations:200,weight:function(e){return 1}}),t6=eY({root:null,weight:function(e){return 1},directed:!1,alpha:0}),t8={degreeCentralityNormalized:function(e){e=t6(e);var t=this.cy(),n=this.nodes(),r=n.length;if(e.directed){for(var i={},a={},o=0,s=0,l=0;l<r;l++){var u=n[l],c=u.id();e.root=u;var d=this.degreeCentrality(e);o<d.indegree&&(o=d.indegree),s<d.outdegree&&(s=d.outdegree),i[c]=d.indegree,a[c]=d.outdegree}return{indegree:function(e){return 0==o?0:(B(e)&&(e=t.filter(e)),i[e.id()]/o)},outdegree:function(e){return 0===s?0:(B(e)&&(e=t.filter(e)),a[e.id()]/s)}}}for(var h={},f=0,p=0;p<r;p++){var g=n[p];e.root=g;var v=this.degreeCentrality(e);f<v.degree&&(f=v.degree),h[g.id()]=v.degree}return{degree:function(e){return 0===f?0:(B(e)&&(e=t.filter(e)),h[e.id()]/f)}}},degreeCentrality:function(e){e=t6(e);var t=this.cy(),n=this,r=e,i=r.root,a=r.weight,o=r.directed,s=r.alpha;if(i=t.collection(i)[0],o){for(var l=i.connectedEdges(),u=l.filter(function(e){return e.target().same(i)&&n.has(e)}),c=l.filter(function(e){return e.source().same(i)&&n.has(e)}),d=u.length,h=c.length,f=0,p=0,g=0;g<u.length;g++)f+=a(u[g]);for(var v=0;v<c.length;v++)p+=a(c[v]);return{indegree:Math.pow(d,1-s)*Math.pow(f,s),outdegree:Math.pow(h,1-s)*Math.pow(p,s)}}for(var y=i.connectedEdges().intersection(n),b=y.length,x=0,w=0;w<y.length;w++)x+=a(y[w]);return{degree:Math.pow(b,1-s)*Math.pow(x,s)}}};t8.dc=t8.degreeCentrality,t8.dcn=t8.degreeCentralityNormalised=t8.degreeCentralityNormalized;var t7=eY({harmonic:!0,weight:function(){return 1},directed:!1,root:null}),ne={closenessCentralityNormalized:function(e){for(var t=t7(e),n=t.harmonic,r=t.weight,i=t.directed,a=this.cy(),o={},s=0,l=this.nodes(),u=this.floydWarshall({weight:r,directed:i}),c=0;c<l.length;c++){for(var d=0,h=l[c],f=0;f<l.length;f++)if(c!==f){var p=u.distance(h,l[f]);n?d+=1/p:d+=p}n||(d=1/d),s<d&&(s=d),o[h.id()]=d}return{closeness:function(e){return 0==s?0:o[e=B(e)?a.filter(e)[0].id():e.id()]/s}}},closenessCentrality:function(e){var t=t7(e),n=t.root,r=t.weight,i=t.directed,a=t.harmonic;n=this.filter(n)[0];for(var o=this.dijkstra({root:n,weight:r,directed:i}),s=0,l=this.nodes(),u=0;u<l.length;u++){var c=l[u];if(!c.same(n)){var d=o.distanceTo(c);a?s+=1/d:s+=d}}return a?s:1/s}};ne.cc=ne.closenessCentrality,ne.ccn=ne.closenessCentralityNormalised=ne.closenessCentralityNormalized;var nt=eY({weight:null,directed:!1}),nn={betweennessCentrality:function(e){for(var t=nt(e),n=t.directed,r=t.weight,i=null!=r,a=this.cy(),o=this.nodes(),s={},l={},u=0,c=function(e,t){l[e]=t,t>u&&(u=t)},d=function(e){return l[e]},h=0;h<o.length;h++){var f=o[h],p=f.id();n?s[p]=f.outgoers().nodes():s[p]=f.openNeighborhood().nodes(),c(p,0)}for(var g=0;g<o.length;g++)!function(){for(var e=o[g].id(),t=[],n={},l={},u={},h=new e3(function(e,t){return u[e]-u[t]}),f=0;f<o.length;f++){var p=o[f].id();n[p]=[],l[p]=0,u[p]=1/0}for(l[e]=1,u[e]=0,h.push(e);!h.empty();){var v=h.pop();if(t.push(v),i)for(var y=0;y<s[v].length;y++){var b=s[v][y],x=a.getElementById(v),w=r(x.edgesTo(b).length>0?x.edgesTo(b)[0]:b.edgesTo(x)[0]);u[b=b.id()]>u[v]+w&&(u[b]=u[v]+w,0>h.nodes.indexOf(b)?h.push(b):h.updateItem(b),l[b]=0,n[b]=[]),u[b]==u[v]+w&&(l[b]=l[b]+l[v],n[b].push(v))}else for(var E=0;E<s[v].length;E++){var T=s[v][E].id();u[T]==1/0&&(h.push(T),u[T]=u[v]+1),u[T]==u[v]+1&&(l[T]=l[T]+l[v],n[T].push(v))}}for(var C={},k=0;k<o.length;k++)C[o[k].id()]=0;for(;t.length>0;){for(var P=t.pop(),S=0;S<n[P].length;S++){var B=n[P][S];C[B]=C[B]+l[B]/l[P]*(1+C[P])}P!=o[g].id()&&c(P,d(P)+C[P])}}();var v={betweenness:function(e){return d(a.collection(e).id())},betweennessNormalized:function(e){return 0==u?0:d(a.collection(e).id())/u}};return v.betweennessNormalised=v.betweennessNormalized,v}};nn.bc=nn.betweennessCentrality;var nr=eY({expandFactor:2,inflateFactor:2,multFactor:1,maxIterations:20,attributes:[function(e){return 1}]}),ni=function(e,t){for(var n=0,r=0;r<t.length;r++)n+=t[r](e);return n},na=function(e,t,n){for(var r=0;r<t;r++)e[r*t+r]=n},no=function(e,t){for(var n,r=0;r<t;r++){n=0;for(var i=0;i<t;i++)n+=e[i*t+r];for(var a=0;a<t;a++)e[a*t+r]=e[a*t+r]/n}},ns=function(e,t,n){for(var r=Array(n*n),i=0;i<n;i++){for(var a=0;a<n;a++)r[i*n+a]=0;for(var o=0;o<n;o++)for(var s=0;s<n;s++)r[i*n+s]+=e[i*n+o]*t[o*n+s]}return r},nl=function(e,t,n){for(var r=e.slice(0),i=1;i<n;i++)e=ns(e,r,t);return e},nu=function(e,t,n){for(var r=Array(t*t),i=0;i<t*t;i++)r[i]=Math.pow(e[i],n);return no(r,t),r},nc=function(e,t,n,r){for(var i=0;i<n;i++)if(Math.round(e[i]*Math.pow(10,r))/Math.pow(10,r)!=Math.round(t[i]*Math.pow(10,r))/Math.pow(10,r))return!1;return!0},nd=function(e,t,n,r){for(var i=[],a=0;a<t;a++){for(var o=[],s=0;s<t;s++)Math.round(1e3*e[a*t+s])/1e3>0&&o.push(n[s]);0!==o.length&&i.push(r.collection(o))}return i},nh=function(e,t){for(var n=0;n<e.length;n++)if(!t[n]||e[n].id()!==t[n].id())return!1;return!0},nf=function(e){for(var t=0;t<e.length;t++)for(var n=0;n<e.length;n++)t!=n&&nh(e[t],e[n])&&e.splice(n,1);return e},np=function(e){for(var t=this.nodes(),n=this.edges(),r=this.cy(),i=nr(e),a={},o=0;o<t.length;o++)a[t[o].id()]=o;for(var s,l=t.length,u=l*l,c=Array(u),d=0;d<u;d++)c[d]=0;for(var h=0;h<n.length;h++){var f=n[h],p=a[f.source().id()],g=a[f.target().id()],v=ni(f,i.attributes);c[p*l+g]+=v,c[g*l+p]+=v}na(c,l,i.multFactor),no(c,l);for(var y=!0,b=0;y&&b<i.maxIterations;)y=!1,nc(c=nu(s=nl(c,l,i.expandFactor),l,i.inflateFactor),s,u,4)||(y=!0),b++;var x=nd(c,l,t,r);return nf(x)},ng=function(e){return e},nv=function(e,t){return Math.abs(t-e)},ny=function(e,t,n){return e+nv(t,n)},nm=function(e,t,n){return e+Math.pow(n-t,2)},nb=function(e){return Math.sqrt(e)},nx=function(e,t,n){return Math.max(e,nv(t,n))},nw=function(e,t,n,r,i){for(var a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:ng,o=r,s=0;s<e;s++)o=i(o,t(s),n(s));return a(o)},nE={euclidean:function(e,t,n){return e>=2?nw(e,t,n,0,nm,nb):nw(e,t,n,0,ny)},squaredEuclidean:function(e,t,n){return nw(e,t,n,0,nm)},manhattan:function(e,t,n){return nw(e,t,n,0,ny)},max:function(e,t,n){return nw(e,t,n,-1/0,nx)}};function nT(e,t,n,r,i,a){var o;return(o=D(e)?e:nE[e]||nE.euclidean,0===t&&D(e))?o(i,a):o(t,n,r,i,a)}nE["squared-euclidean"]=nE.squaredEuclidean,nE.squaredeuclidean=nE.squaredEuclidean;var nC=eY({k:2,m:2,sensitivityThreshold:1e-4,distance:"euclidean",maxIterations:10,attributes:[],testMode:!1,testCentroids:null}),nk=function(e){return nC(e)},nP=function(e,t,n,r,i){var a="kMedoids"!==i?function(e){return n[e]}:function(e){return r[e](n)};return nT(e,r.length,a,function(e){return r[e](t)},n,t)},nS=function(e,t,n){for(var r=n.length,i=Array(r),a=Array(r),o=Array(t),s=null,l=0;l<r;l++)i[l]=e.min(n[l]).value,a[l]=e.max(n[l]).value;for(var u=0;u<t;u++){s=[];for(var c=0;c<r;c++)s[c]=Math.random()*(a[c]-i[c])+i[c];o[u]=s}return o},nB=function(e,t,n,r,i){for(var a=1/0,o=0,s=0;s<t.length;s++){var l=nP(n,e,t[s],r,i);l<a&&(a=l,o=s)}return o},nD=function(e,t,n){for(var r=[],i=null,a=0;a<t.length;a++)n[(i=t[a]).id()]===e&&r.push(i);return r},n_=function(e,t,n){for(var r=0;r<e.length;r++)for(var i=0;i<e[r].length;i++)if(Math.abs(e[r][i]-t[r][i])>n)return!1;return!0},nA=function(e,t,n){for(var r=0;r<n;r++)if(e===t[r])return!0;return!1},nM=function(e,t){var n=Array(t);if(e.length<50)for(var r=0;r<t;r++){for(var i=e[Math.floor(Math.random()*e.length)];nA(i,n,r);)i=e[Math.floor(Math.random()*e.length)];n[r]=i}else for(var a=0;a<t;a++)n[a]=e[Math.floor(Math.random()*e.length)];return n},nR=function(e,t,n){for(var r=0,i=0;i<t.length;i++)r+=nP("manhattan",t[i],e,n,"kMedoids");return r},nI=function(e,t,n,r,i){for(var a,o,s=0;s<t.length;s++)for(var l=0;l<e.length;l++)r[s][l]=Math.pow(n[s][l],i.m);for(var u=0;u<e.length;u++)for(var c=0;c<i.attributes.length;c++){a=0,o=0;for(var d=0;d<t.length;d++)a+=r[d][u]*i.attributes[c](t[d]),o+=r[d][u];e[u][c]=a/o}},nN=function(e,t,n,r,i){for(var a,o=0;o<e.length;o++)t[o]=e[o].slice();for(var s=2/(i.m-1),l=0;l<n.length;l++)for(var u=0;u<r.length;u++){a=0;for(var c=0;c<n.length;c++)a+=Math.pow(nP(i.distance,r[u],n[l],i.attributes,"cmeans")/nP(i.distance,r[u],n[c],i.attributes,"cmeans"),s);e[u][l]=1/a}},nL=function(e,t,n,r){for(var i,a,o=Array(n.k),s=0;s<o.length;s++)o[s]=[];for(var l=0;l<t.length;l++){i=-1/0,a=-1;for(var u=0;u<t[0].length;u++)t[l][u]>i&&(i=t[l][u],a=u);o[a].push(e[l])}for(var c=0;c<o.length;c++)o[c]=r.collection(o[c]);return o},nO=function(e){var t,n,r,i,a=this.cy(),o=this.nodes(),s=nk(e);r=Array(o.length);for(var l=0;l<o.length;l++)r[l]=Array(s.k);n=Array(o.length);for(var u=0;u<o.length;u++)n[u]=Array(s.k);for(var c=0;c<o.length;c++){for(var d=0,h=0;h<s.k;h++)n[c][h]=Math.random(),d+=n[c][h];for(var f=0;f<s.k;f++)n[c][f]=n[c][f]/d}t=Array(s.k);for(var p=0;p<s.k;p++)t[p]=Array(s.attributes.length);i=Array(o.length);for(var g=0;g<o.length;g++)i[g]=Array(s.k);for(var v=!0,y=0;v&&y<s.maxIterations;)v=!1,nI(t,o,n,i,s),nN(n,r,t,o,s),n_(n,r,s.sensitivityThreshold)||(v=!0),y++;return{clusters:nL(o,n,s,a),degreeOfMembership:n}},nz=eY({distance:"euclidean",linkage:"min",mode:"threshold",threshold:1/0,addDendrogram:!1,dendrogramDepth:0,attributes:[]}),nV={single:"min",complete:"max"},nF=function(e){var t=nz(e),n=nV[t.linkage];return null!=n&&(t.linkage=n),t},nX=function(e,t,n,r,i){for(var a,o,s=0,l=1/0,u=i.attributes,c=function(e,t){return nT(i.distance,u.length,function(t){return u[t](e)},function(e){return u[e](t)},e,t)},d=0;d<e.length;d++){var h=e[d].key,f=n[h][r[h]];f<l&&(s=h,l=f)}if("threshold"===i.mode&&l>=i.threshold||"dendrogram"===i.mode&&1===e.length)return!1;var p=t[s],g=t[r[s]];o="dendrogram"===i.mode?{left:p,right:g,key:p.key}:{value:p.value.concat(g.value),key:p.key},e[p.index]=o,e.splice(g.index,1),t[p.key]=o;for(var v=0;v<e.length;v++){var y=e[v];p.key===y.key?a=1/0:"min"===i.linkage?(a=n[p.key][y.key],n[p.key][y.key]>n[g.key][y.key]&&(a=n[g.key][y.key])):"max"===i.linkage?(a=n[p.key][y.key],n[p.key][y.key]<n[g.key][y.key]&&(a=n[g.key][y.key])):a="mean"===i.linkage?(n[p.key][y.key]*p.size+n[g.key][y.key]*g.size)/(p.size+g.size):"dendrogram"===i.mode?c(y.value,p.value):c(y.value[0],p.value[0]),n[p.key][y.key]=n[y.key][p.key]=a}for(var b=0;b<e.length;b++){var x=e[b].key;if(r[x]===p.key||r[x]===g.key){for(var w=x,E=0;E<e.length;E++){var T=e[E].key;n[x][T]<n[x][w]&&(w=T)}r[x]=w}e[b].index=b}return p.key=g.key=p.index=g.index=null,!0},nj=function(e,t,n){e&&(e.value?t.push(e.value):(e.left&&nj(e.left,t),e.right&&nj(e.right,t)))},nY=function(e,t){if(!e)return"";if(e.left&&e.right){var n=nY(e.left,t),r=nY(e.right,t),i=t.add({group:"nodes",data:{id:n+","+r}});return t.add({group:"edges",data:{source:n,target:i.id()}}),t.add({group:"edges",data:{source:r,target:i.id()}}),i.id()}if(e.value)return e.value.id()},nq=function(e,t,n){if(!e)return[];var r=[],i=[],a=[];if(0===t)return e.left&&nj(e.left,r),e.right&&nj(e.right,i),a=r.concat(i),[n.collection(a)];if(1===t)if(e.value)return[n.collection(e.value)];else return e.left&&nj(e.left,r),e.right&&nj(e.right,i),[n.collection(r),n.collection(i)];return e.value?[n.collection(e.value)]:(e.left&&(r=nq(e.left,t-1,n)),e.right&&(i=nq(e.right,t-1,n)),r.concat(i))},nW=function(e){for(var t,n=this.cy(),r=this.nodes(),i=nF(e),a=i.attributes,o=function(e,t){return nT(i.distance,a.length,function(t){return a[t](e)},function(e){return a[e](t)},e,t)},s=[],l=[],u=[],c=[],d=0;d<r.length;d++){var h={value:"dendrogram"===i.mode?r[d]:[r[d]],key:d,index:d};s[d]=h,c[d]=h,l[d]=[],u[d]=0}for(var f=0;f<s.length;f++)for(var p=0;p<=f;p++){var g=void 0;g="dendrogram"===i.mode?f===p?1/0:o(s[f].value,s[p].value):f===p?1/0:o(s[f].value[0],s[p].value[0]),l[f][p]=g,l[p][f]=g,g<l[f][u[f]]&&(u[f]=p)}for(var v=nX(s,c,l,u,i);v;)v=nX(s,c,l,u,i);return"dendrogram"===i.mode?(t=nq(s[0],i.dendrogramDepth,n),i.addDendrogram&&nY(s[0],n)):(t=Array(s.length),s.forEach(function(e,r){e.key=e.index=null,t[r]=n.collection(e.value)})),t},nU=eY({distance:"euclidean",preference:"median",damping:.8,maxIterations:1e3,minIterations:100,attributes:[]}),nG=function(e){var t=e.damping,n=e.preference;.5<=t&&t<1||eL("Damping must range on [0.5, 1).  Got: ".concat(t));var r=["median","mean","min","max"];return r.some(function(e){return e===n})||M(n)||eL("Preference must be one of [".concat(r.map(function(e){return"'".concat(e,"'")}).join(", "),"] or a number.  Got: ").concat(n)),nU(e)},nH=function(e,t,n,r){var i=function(e,t){return r[t](e)};return-nT(e,r.length,function(e){return i(t,e)},function(e){return i(n,e)},t,n)},nK=function(e,t){return"median"===t?tl(e):"mean"===t?ts(e):"min"===t?ta(e):"max"===t?to(e):t},nZ=function(e,t,n){for(var r=[],i=0;i<e;i++)t[i*e+i]+n[i*e+i]>0&&r.push(i);return r},n$=function(e,t,n){for(var r=[],i=0;i<e;i++){for(var a=-1,o=-1/0,s=0;s<n.length;s++){var l=n[s];t[i*e+l]>o&&(a=l,o=t[i*e+l])}a>0&&r.push(a)}for(var u=0;u<n.length;u++)r[n[u]]=n[u];return r},nQ=function(e,t,n){for(var r=n$(e,t,n),i=0;i<n.length;i++){for(var a=[],o=0;o<r.length;o++)r[o]===n[i]&&a.push(o);for(var s=-1,l=-1/0,u=0;u<a.length;u++){for(var c=0,d=0;d<a.length;d++)c+=t[a[d]*e+a[u]];c>l&&(s=u,l=c)}n[i]=a[s]}return n$(e,t,n)},nJ=function(e){for(var t,n,r,i,a,o,s,l=this.cy(),u=this.nodes(),c=nG(e),d={},h=0;h<u.length;h++)d[u[h].id()]=h;r=Array(n=(t=u.length)*t);for(var f=0;f<n;f++)r[f]=-1/0;for(var p=0;p<t;p++)for(var g=0;g<t;g++)p!==g&&(r[p*t+g]=nH(c.distance,u[p],u[g],c.attributes));i=nK(r,c.preference);for(var v=0;v<t;v++)r[v*t+v]=i;a=Array(n);for(var y=0;y<n;y++)a[y]=0;o=Array(n);for(var b=0;b<n;b++)o[b]=0;for(var x=Array(t),w=Array(t),E=Array(t),T=0;T<t;T++)x[T]=0,w[T]=0,E[T]=0;for(var C=Array(t*c.minIterations),k=0;k<C.length;k++)C[k]=0;for(s=0;s<c.maxIterations;s++){for(var P=0;P<t;P++){for(var S=-1/0,B=-1/0,D=-1,_=0,A=0;A<t;A++)x[A]=a[P*t+A],(_=o[P*t+A]+r[P*t+A])>=S?(B=S,S=_,D=A):_>B&&(B=_);for(var M=0;M<t;M++)a[P*t+M]=(1-c.damping)*(r[P*t+M]-S)+c.damping*x[M];a[P*t+D]=(1-c.damping)*(r[P*t+D]-B)+c.damping*x[D]}for(var R=0;R<t;R++){for(var I=0,N=0;N<t;N++)x[N]=o[N*t+R],w[N]=Math.max(0,a[N*t+R]),I+=w[N];I-=w[R],w[R]=a[R*t+R],I+=w[R];for(var L=0;L<t;L++)o[L*t+R]=(1-c.damping)*Math.min(0,I-w[L])+c.damping*x[L];o[R*t+R]=(1-c.damping)*(I-w[R])+c.damping*x[R]}for(var O=0,z=0;z<t;z++){var V=+(o[z*t+z]+a[z*t+z]>0);C[s%c.minIterations*t+z]=V,O+=V}if(O>0&&(s>=c.minIterations-1||s==c.maxIterations-1)){for(var F=0,X=0;X<t;X++){E[X]=0;for(var j=0;j<c.minIterations;j++)E[X]+=C[j*t+X];(0===E[X]||E[X]===c.minIterations)&&F++}if(F===t)break}}for(var Y=nZ(t,a,o),q=nQ(t,r,Y),W={},U=0;U<Y.length;U++)W[Y[U]]=[];for(var G=0;G<u.length;G++){var H=q[d[u[G].id()]];null!=H&&W[H].push(u[G])}for(var K=Array(Y.length),Z=0;Z<Y.length;Z++)K[Z]=l.collection(W[Y[Z]]);return K},n0=eY({root:void 0,directed:!1}),n1=function(){var e=this,t={},n=0,r=0,i=[],a=[],o={},s=function(n,r){for(var o=a.length-1,s=[],l=e.spawn();a[o].x!=n||a[o].y!=r;)s.push(a.pop().edge),o--;s.push(a.pop().edge),s.forEach(function(n){var r=n.connectedNodes().intersection(e);l.merge(n),r.forEach(function(n){var r=n.id(),i=n.connectedEdges().intersection(e);l.merge(n),t[r].cutVertex?l.merge(i.filter(function(e){return e.isLoop()})):l.merge(i)})}),i.push(l)},l=function(u,c,d){u===d&&(r+=1),t[c]={id:n,low:n++,cutVertex:!1};var h,f,p,g,v=e.getElementById(c).connectedEdges().intersection(e);0===v.size()?i.push(e.spawn(e.getElementById(c))):v.forEach(function(e){h=e.source().id(),f=e.target().id(),(p=h===c?f:h)!==d&&(o[g=e.id()]||(o[g]=!0,a.push({x:c,y:p,edge:e})),p in t?t[c].low=Math.min(t[c].low,t[p].id):(l(u,p,c),t[c].low=Math.min(t[c].low,t[p].low),t[c].id<=t[p].low&&(t[c].cutVertex=!0,s(c,p))))})};e.forEach(function(e){if(e.isNode()){var n=e.id();n in t||(r=0,l(n,n),t[n].cutVertex=r>1)}});var u=Object.keys(t).filter(function(e){return t[e].cutVertex}).map(function(t){return e.getElementById(t)});return{cut:e.spawn(u),components:i}},n2=function(){var e=this,t={},n=0,r=[],i=[],a=e.spawn(e),o=function(s){if(i.push(s),t[s]={index:n,low:n++,explored:!1},e.getElementById(s).connectedEdges().intersection(e).forEach(function(e){var n=e.target().id();n!==s&&(n in t||o(n),t[n].explored||(t[s].low=Math.min(t[s].low,t[n].low)))}),t[s].index===t[s].low){for(var l=e.spawn();;){var u=i.pop();if(l.merge(e.getElementById(u)),t[u].low=t[s].index,t[u].explored=!0,u===s)break}var c=l.edgesWith(l),d=l.merge(c);r.push(d),a=a.difference(d)}};return e.forEach(function(e){if(e.isNode()){var n=e.id();n in t||o(n)}}),{cut:a,components:r}},n5={};[e1,{dijkstra:function(e){if(!A(e)){var t=arguments;e={root:t[0],weight:t[1],directed:t[2]}}var n=e4(e),r=n.root,i=n.weight,a=n.directed,o=this,s=B(r)?this.filter(r)[0]:r[0],l={},u={},c={},d=this.byGroup(),h=d.nodes,f=d.edges;f.unmergeBy(function(e){return e.isLoop()});for(var p=function(e){return l[e.id()]},g=new e3(function(e,t){return p(e)-p(t)}),v=0;v<h.length;v++){var y=h[v];l[y.id()]=y.same(s)?0:1/0,g.push(y)}for(;g.size()>0;){var b=g.pop(),x=p(b);if(c[b.id()]=x,x!==1/0)for(var w=b.neighborhood().intersect(h),E=0;E<w.length;E++){var T=w[E],C=T.id(),k=function(e,t){for(var n,r=(a?e.edgesTo(t):e.edgesWith(t)).intersect(f),o=1/0,s=0;s<r.length;s++){var l=r[s],u=i(l);(u<o||!n)&&(o=u,n=l)}return{edge:n,dist:o}}(b,T),P=x+k.dist;P<p(T)&&(l[T.id()]=P,g.updateItem(T),u[C]={node:b,edge:k.edge})}}return{distanceTo:function(e){return c[(B(e)?h.filter(e)[0]:e[0]).id()]},pathTo:function(e){var t=B(e)?h.filter(e)[0]:e[0],n=[],r=t,i=r.id();if(t.length>0)for(n.unshift(t);u[i];){var a=u[i];n.unshift(a.edge),n.unshift(a.node),i=(r=a.node).id()}return o.spawn(n)}}}},{kruskal:function(e){e=e||function(e){return 1};for(var t=this.byGroup(),n=t.nodes,r=t.edges,i=n.length,a=Array(i),o=function(e){for(var t=0;t<a.length;t++)if(a[t].has(e))return t},s=0;s<i;s++)a[s]=this.spawn(n[s]);for(var l=r.sort(function(t,n){return e(t)-e(n)}),u=0;u<l.length;u++){var c=l[u],d=c.source()[0],h=c.target()[0],f=o(d),p=o(h),g=a[f],v=a[p];f!==p&&(n.merge(c),g.merge(v),a.splice(p,1))}return n}},{aStar:function(e){var t,n,r=this.cy(),i=e9(e),a=i.root,o=i.goal,s=i.heuristic,l=i.directed,u=i.weight;a=r.collection(a)[0],o=r.collection(o)[0];var c=a.id(),d=o.id(),h={},f={},p={},g=new e3(function(e,t){return f[e.id()]-f[t.id()]}),v=new eQ,y={},b={},x=function(e,t){g.push(e),v.add(t)};x(a,c),h[c]=0,f[c]=s(a);for(var w=0;g.size()>0;){if(n=(t=g.pop()).id(),v.delete(n),w++,n===d){for(var E=[],T=o,C=d,k=b[C];E.unshift(T),null!=k&&E.unshift(k),null!=(T=y[C]);)k=b[C=T.id()];return{found:!0,distance:h[n],path:this.spawn(E),steps:w}}p[n]=!0;for(var P=t._private.edges,S=0;S<P.length;S++){var B=P[S];if(this.hasElementWithId(B.id())&&(!l||B.data("source")===n)){var D=B.source(),_=B.target(),A=D.id()!==n?D:_,M=A.id();if(this.hasElementWithId(M)&&!p[M]){var R=h[n]+u(B);if(!v.has(M)){h[M]=R,f[M]=R+s(A),x(A,M),y[M]=t,b[M]=B;continue}R<h[M]&&(h[M]=R,f[M]=R+s(A),y[M]=t,b[M]=B)}}}}return{found:!1,distance:void 0,path:void 0,steps:w}}},{floydWarshall:function(e){for(var t=this.cy(),n=e6(e),r=n.weight,i=n.directed,a=this.byGroup(),o=a.nodes,s=a.edges,l=o.length,u=l*l,c=function(e){return o.indexOf(e)},d=function(e){return o[e]},h=Array(u),f=0;f<u;f++){var p=f%l;(f-p)/l===p?h[f]=0:h[f]=1/0}for(var g=Array(u),v=Array(u),y=0;y<s.length;y++){var b=s[y],x=b.source()[0],w=b.target()[0];if(x!==w){var E=c(x),T=c(w),C=E*l+T,k=r(b);if(h[C]>k&&(h[C]=k,g[C]=T,v[C]=b),!i){var P=T*l+E;!i&&h[P]>k&&(h[P]=k,g[P]=E,v[P]=b)}}}for(var S=0;S<l;S++)for(var D=0;D<l;D++)for(var _=D*l+S,A=0;A<l;A++){var M=D*l+A,R=S*l+A;h[_]+h[R]<h[M]&&(h[M]=h[_]+h[R],g[M]=g[_])}var I=function(e){return c((B(e)?t.filter(e):e)[0])};return{distance:function(e,t){return h[I(e)*l+I(t)]},path:function(e,n){var r,i=I(e),a=I(n),o=d(i);if(i===a)return o.collection();if(null==g[i*l+a])return t.collection();var s=t.collection(),u=i;for(s.merge(o);i!==a;)u=i,i=g[i*l+a],r=v[u*l+i],s.merge(r),s.merge(d(i));return s}}}},{bellmanFord:function(e){var t=this,n=e8(e),r=n.weight,i=n.directed,a=n.root,o=this,s=this.cy(),l=this.byGroup(),u=l.edges,c=l.nodes,d=c.length,h=new eZ,f=!1,p=[];a=s.collection(a)[0],u.unmergeBy(function(e){return e.isLoop()});for(var g=u.length,v=function(e){var t=h.get(e.id());return t||(t={},h.set(e.id(),t)),t},y=function(e){return(B(e)?s.$(e):e)[0]},b=0;b<d;b++){var x=c[b],w=v(x);x.same(a)?w.dist=0:w.dist=1/0,w.pred=null,w.edge=null}for(var E=!1,T=function(e,t,n,r,i,a){var o=r.dist+a;o<i.dist&&!n.same(r.edge)&&(i.dist=o,i.pred=e,i.edge=n,E=!0)},C=1;C<d;C++){E=!1;for(var k=0;k<g;k++){var P=u[k],S=P.source(),D=P.target(),_=r(P),A=v(S),M=v(D);T(S,D,P,A,M,_),i||T(D,S,P,M,A,_)}if(!E)break}if(E)for(var R=[],I=0;I<g;I++){var N=u[I],L=N.source(),O=N.target(),z=r(N),V=v(L).dist,F=v(O).dist;if(V+z<F||!i&&F+z<V)if(f||(ez("Graph contains a negative weight cycle for Bellman-Ford"),f=!0),!1!==e.findNegativeWeightCycles){var X=[];V+z<F&&X.push(L),!i&&F+z<V&&X.push(O);for(var j=X.length,Y=0;Y<j;Y++){var q=X[Y],W=[q];W.push(v(q).edge);for(var U=v(q).pred;-1===W.indexOf(U);)W.push(U),W.push(v(U).edge),U=v(U).pred;for(var G=(W=W.slice(W.indexOf(U)))[0].id(),H=0,K=2;K<W.length;K+=2)W[K].id()<G&&(G=W[K].id(),H=K);(W=W.slice(H).concat(W.slice(0,H))).push(W[0]);var Z=W.map(function(e){return e.id()}).join(",");-1===R.indexOf(Z)&&(p.push(o.spawn(W)),R.push(Z))}}else break}return{distanceTo:function(e){return v(y(e)).dist},pathTo:function(e){for(var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:a,r=y(e),i=[],s=r;;){if(null==s)return t.spawn();var l=v(s),u=l.edge,c=l.pred;if(i.unshift(s[0]),s.same(n)&&i.length>0)break;null!=u&&i.unshift(u),s=c}return o.spawn(i)},hasNegativeWeightCycle:f,negativeWeightCycles:p}}},{kargerStein:function(){var e=this,t=this.byGroup(),n=t.nodes,r=t.edges;r.unmergeBy(function(e){return e.isLoop()});var i=n.length,a=r.length,o=Math.ceil(Math.pow(Math.log(i)/Math.LN2,2)),s=Math.floor(i/e7);if(i<2)return void eL("At least 2 nodes are required for Karger-Stein algorithm");for(var l=[],u=0;u<a;u++){var c=r[u];l.push([u,n.indexOf(c.source()),n.indexOf(c.target())])}for(var d=1/0,h=[],f=Array(i),p=Array(i),g=Array(i),v=function(e,t){for(var n=0;n<i;n++)t[n]=e[n]},y=0;y<=o;y++){for(var b=0;b<i;b++)p[b]=b;var x=tt(p,l.slice(),i,s),w=x.slice();v(p,g);var E=tt(p,x,s,2),T=tt(g,w,s,2);E.length<=T.length&&E.length<d?(d=E.length,h=E,v(p,f)):T.length<=E.length&&T.length<d&&(d=T.length,h=T,v(g,f))}for(var C=this.spawn(h.map(function(e){return r[e[0]]})),k=this.spawn(),P=this.spawn(),S=f[0],B=0;B<f.length;B++){var D=f[B],_=n[B];D===S?k.merge(_):P.merge(_)}var A=function(t){var n=e.spawn();return t.forEach(function(t){n.merge(t),t.connectedEdges().forEach(function(t){e.contains(t)&&!C.contains(t)&&n.merge(t)})}),n};return{cut:C,components:[A(k),A(P)],partition1:k,partition2:P}}},{pageRank:function(e){for(var t,n=t9(e),r=n.dampingFactor,i=n.precision,a=n.iterations,o=n.weight,s=this._private.cy,l=this.byGroup(),u=l.nodes,c=l.edges,d=u.length,h=d*d,f=c.length,p=Array(h),g=Array(d),v=(1-r)/d,y=0;y<d;y++){for(var b=0;b<d;b++)p[y*d+b]=0;g[y]=0}for(var x=0;x<f;x++){var w=c[x],E=w.data("source"),T=w.data("target");if(E!==T){var C=u.indexOfId(E),k=u.indexOfId(T),P=o(w),S=k*d+C;p[S]+=P,g[C]+=P}}for(var B=1/d+v,D=0;D<d;D++)if(0===g[D])for(var _=0;_<d;_++)p[_*d+D]=B;else for(var A=0;A<d;A++){var M=A*d+D;p[M]=p[M]/g[D]+v}for(var R=Array(d),I=Array(d),N=0;N<d;N++)R[N]=1;for(var L=0;L<a;L++){for(var O=0;O<d;O++)I[O]=0;for(var z=0;z<d;z++)for(var V=0;V<d;V++){var F=z*d+V;I[z]+=p[F]*R[V]}tp(I),t=R,R=I,I=t;for(var X=0,j=0;j<d;j++){var Y=t[j]-R[j];X+=Y*Y}if(X<i)break}return{rank:function(e){return e=s.collection(e)[0],R[u.indexOf(e)]}}}},t8,ne,nn,{markovClustering:np,mcl:np},{kMeans:function(e){var t,n=this.cy(),r=this.nodes(),i=null,a=nk(e),o=Array(a.k),s={};a.testMode?"number"==typeof a.testCentroids?(a.testCentroids,t=nS(r,a.k,a.attributes)):t="object"===d(a.testCentroids)?a.testCentroids:nS(r,a.k,a.attributes):t=nS(r,a.k,a.attributes);for(var l=!0,u=0;l&&u<a.maxIterations;){for(var c=0;c<r.length;c++)s[(i=r[c]).id()]=nB(i,t,a.distance,a.attributes,"kMeans");l=!1;for(var h=0;h<a.k;h++){var f=nD(h,r,s);if(0!==f.length){for(var p=a.attributes.length,g=t[h],v=Array(p),y=Array(p),b=0;b<p;b++){y[b]=0;for(var x,w=0;w<f.length;w++)i=f[w],y[b]+=a.attributes[b](i);v[b]=y[b]/f.length,x=v[b],Math.abs(g[b]-x)<=a.sensitivityThreshold||(l=!0)}t[h]=v,o[h]=n.collection(f)}}u++}return o},kMedoids:function(e){var t,n,r=this.cy(),i=this.nodes(),a=null,o=nk(e),s=Array(o.k),l={},u=Array(o.k);o.testMode?"number"==typeof o.testCentroids||(t="object"===d(o.testCentroids)?o.testCentroids:nM(i,o.k)):t=nM(i,o.k);for(var c=!0,h=0;c&&h<o.maxIterations;){for(var f=0;f<i.length;f++)l[(a=i[f]).id()]=nB(a,t,o.distance,o.attributes,"kMedoids");c=!1;for(var p=0;p<t.length;p++){var g=nD(p,i,l);if(0!==g.length){u[p]=nR(t[p],g,o.attributes);for(var v=0;v<g.length;v++)(n=nR(g[v],g,o.attributes))<u[p]&&(u[p]=n,t[p]=g[v],c=!0);s[p]=r.collection(g)}}h++}return s},fuzzyCMeans:nO,fcm:nO},{hierarchicalClustering:nW,hca:nW},{affinityPropagation:nJ,ap:nJ},{hierholzer:function(e){if(!A(e)){var t,n,r,i=arguments;e={root:i[0],directed:i[1]}}var a=n0(e),o=a.root,s=a.directed,l=!1;o&&(r=B(o)?this.filter(o)[0].id():o[0].id());var u={},c={};s?this.forEach(function(e){var r=e.id();if(e.isNode()){var i=e.indegree(!0),a=e.outdegree(!0),o=i-a,s=a-i;1==o?t?l=!0:t=r:1==s?n?l=!0:n=r:(s>1||o>1)&&(l=!0),u[r]=[],e.outgoers().forEach(function(e){e.isEdge()&&u[r].push(e.id())})}else c[r]=[void 0,e.target().id()]}):this.forEach(function(e){var r=e.id();e.isNode()?(e.degree(!0)%2&&(t?n?l=!0:n=r:t=r),u[r]=[],e.connectedEdges().forEach(function(e){return u[r].push(e.id())})):c[r]=[e.source().id(),e.target().id()]});var d={found:!1,trail:void 0};if(l)return d;if(n&&t)if(s){if(r&&n!=r)return d;r=n}else{if(r&&n!=r&&t!=r)return d;r||(r=n)}else r||(r=this[0].id());var h=function(e){for(var t,n,r,i=e,a=[e];u[i].length;)n=c[t=u[i].shift()][0],i!=(r=c[t][1])?(u[r]=u[r].filter(function(e){return e!=t}),i=r):s||i==n||(u[n]=u[n].filter(function(e){return e!=t}),i=n),a.unshift(t),a.unshift(i);return a},f=[],p=[];for(p=h(r);1!=p.length;)0==u[p[0]].length?(f.unshift(this.getElementById(p.shift())),f.unshift(this.getElementById(p.shift()))):p=h(p.shift()).concat(p);for(var g in f.unshift(this.getElementById(p.shift())),u)if(u[g].length)return d;return d.found=!0,d.trail=this.spawn(f,!0),d}},{hopcroftTarjanBiconnected:n1,htbc:n1,htb:n1,hopcroftTarjanBiconnectedComponents:n1},{tarjanStronglyConnected:n2,tsc:n2,tscc:n2,tarjanStronglyConnectedComponents:n2}].forEach(function(e){J(n5,e)});var n3=function(e){if(!(this instanceof n3))return new n3(e);this.id="Thenable/1.0.7",this.state=0,this.fulfillValue=void 0,this.rejectReason=void 0,this.onFulfilled=[],this.onRejected=[],this.proxy={then:this.then.bind(this)},"function"==typeof e&&e.call(this,this.fulfill.bind(this),this.reject.bind(this))};n3.prototype={fulfill:function(e){return n4(this,1,"fulfillValue",e)},reject:function(e){return n4(this,2,"rejectReason",e)},then:function(e,t){var n=new n3;return this.onFulfilled.push(n8(e,n,"fulfill")),this.onRejected.push(n8(t,n,"reject")),n9(this),n.proxy}};var n4=function(e,t,n,r){return 0===e.state&&(e.state=t,e[n]=r,n9(e)),e},n9=function(e){1===e.state?n6(e,"onFulfilled",e.fulfillValue):2===e.state&&n6(e,"onRejected",e.rejectReason)},n6=function(e,t,n){if(0!==e[t].length){var r=e[t];e[t]=[];var i=function(){for(var e=0;e<r.length;e++)r[e](n)};"function"==typeof setImmediate?setImmediate(i):setTimeout(i,0)}},n8=function(e,t,n){return function(r){if("function"!=typeof e)t[n].call(t,r);else{var i;try{i=e(r)}catch(e){t.reject(e);return}n7(t,i)}}},n7=function(e,t){if(e===t||e.proxy===t)return void e.reject(TypeError("cannot resolve promise with itself"));if("object"===d(t)&&null!==t||"function"==typeof t)try{n=t.then}catch(t){e.reject(t);return}if("function"==typeof n){var n,r=!1;try{n.call(t,function(n){r||(r=!0,n===t?e.reject(TypeError("circular thenable chain")):n7(e,n))},function(t){r||(r=!0,e.reject(t))})}catch(t){r||e.reject(t)}return}e.fulfill(t)};n3.all=function(e){return new n3(function(t,n){for(var r=Array(e.length),i=0,a=function(n,a){r[n]=a,++i===e.length&&t(r)},o=0;o<e.length;o++)!function(t){var r=e[t];null!=r&&null!=r.then?r.then(function(e){a(t,e)},function(e){n(e)}):a(t,r)}(o)})},n3.resolve=function(e){return new n3(function(t,n){t(e)})},n3.reject=function(e){return new n3(function(t,n){n(e)})};var re="undefined"!=typeof Promise?Promise:n3,rt=function(e,t,n){var r=O(e),i=this._private=J({duration:1e3},t,n);if(i.target=e,i.style=i.style||i.css,i.started=!1,i.playing=!1,i.hooked=!1,i.applying=!1,i.progress=0,i.completes=[],i.frames=[],i.complete&&D(i.complete)&&i.completes.push(i.complete),!r){var a=e.position();i.startPosition=i.startPosition||{x:a.x,y:a.y},i.startStyle=i.startStyle||e.cy().style().getAnimationStartStyle(e,i.style)}if(r){var o=e.pan();i.startPan={x:o.x,y:o.y},i.startZoom=e.zoom()}this.length=1,this[0]=this},rn=rt.prototype;function rr(){return o_?oD:(o_=1,oD=Array.isArray)}function ri(){if(oU)return oW;oU=1;var e=function(){if(oj)return oX;oj=1;var e=function(){if(oI)return oR;oI=1;var e=eh(),t=eu();return oR=function(n){if(!t(n))return!1;var r=e(n);return"[object Function]"==r||"[object GeneratorFunction]"==r||"[object AsyncFunction]"==r||"[object Proxy]"==r}}(),t=function(){if(oz)return oO;oz=1;var e,t=oL?oN:(oL=1,oN=ec()["__core-js_shared__"]),n=(e=/[^.]+$/.exec(t&&t.keys&&t.keys.IE_PROTO||""))?"Symbol(src)_1."+e:"";return oO=function(e){return!!n&&n in e}}(),n=eu(),r=function(){if(oF)return oV;oF=1;var e=Function.prototype.toString;return oV=function(t){if(null!=t){try{return e.call(t)}catch(e){}try{return t+""}catch(e){}}return""}}(),i=/^\[object .+?Constructor\]$/,a=Object.prototype,o=Function.prototype.toString,s=a.hasOwnProperty,l=RegExp("^"+o.call(s).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");return oX=function(a){return!(!n(a)||t(a))&&(e(a)?l:i).test(r(a))}}(),t=oq?oY:(oq=1,oY=function(e,t){return null==e?void 0:e[t]});return oW=function(n,r){var i=t(n,r);return e(i)?i:void 0}}function ra(){return oH?oG:(oH=1,oG=ri()(Object,"create"))}function ro(){return se?o7:(se=1,o7=function(e,t){return e===t||e!=e&&t!=t})}function rs(){if(sn)return st;sn=1;var e=ro();return st=function(t,n){for(var r=t.length;r--;)if(e(t[r][0],n))return r;return -1}}function rl(){if(sx)return sb;sx=1;var e=sm?sy:(sm=1,sy=function(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e});return sb=function(t,n){var r=t.__data__;return e(n)?r["string"==typeof n?"string":"hash"]:r.map}}function ru(){if(sL)return sN;sL=1;var e=function(){if(sI)return sR;sI=1;var e=function(){if(sM)return sA;sM=1;var e=function(){if(s_)return sD;s_=1;var e=function(){if(sv)return sg;sv=1;var e=function(){if(o9)return o4;o9=1;var e=function(){if(oZ)return oK;oZ=1;var e=ra();return oK=function(){this.__data__=e?e(null):{},this.size=0}}(),t=oQ?o$:(oQ=1,o$=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=!!t,t}),n=function(){if(o0)return oJ;o0=1;var e=ra(),t=Object.prototype.hasOwnProperty;return oJ=function(n){var r=this.__data__;if(e){var i=r[n];return"__lodash_hash_undefined__"===i?void 0:i}return t.call(r,n)?r[n]:void 0}}(),r=function(){if(o2)return o1;o2=1;var e=ra(),t=Object.prototype.hasOwnProperty;return o1=function(n){var r=this.__data__;return e?void 0!==r[n]:t.call(r,n)}}(),i=function(){if(o3)return o5;o3=1;var e=ra();return o5=function(t,n){var r=this.__data__;return this.size+=+!this.has(t),r[t]=e&&void 0===n?"__lodash_hash_undefined__":n,this}}();function a(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}return a.prototype.clear=e,a.prototype.delete=t,a.prototype.get=n,a.prototype.has=r,a.prototype.set=i,o4=a}(),t=function(){if(sh)return sd;sh=1;var e=o8?o6:(o8=1,o6=function(){this.__data__=[],this.size=0}),t=function(){if(si)return sr;si=1;var e=rs(),t=Array.prototype.splice;return sr=function(n){var r=this.__data__,i=e(r,n);return!(i<0)&&(i==r.length-1?r.pop():t.call(r,i,1),--this.size,!0)}}(),n=function(){if(so)return sa;so=1;var e=rs();return sa=function(t){var n=this.__data__,r=e(n,t);return r<0?void 0:n[r][1]}}(),r=function(){if(sl)return ss;sl=1;var e=rs();return ss=function(t){return e(this.__data__,t)>-1}}(),i=function(){if(sc)return su;sc=1;var e=rs();return su=function(t,n){var r=this.__data__,i=e(r,t);return i<0?(++this.size,r.push([t,n])):r[i][1]=n,this}}();function a(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}return a.prototype.clear=e,a.prototype.delete=t,a.prototype.get=n,a.prototype.has=r,a.prototype.set=i,sd=a}(),n=sp?sf:(sp=1,sf=ri()(ec(),"Map"));return sg=function(){this.size=0,this.__data__={hash:new e,map:new(n||t),string:new e}}}(),t=function(){if(sE)return sw;sE=1;var e=rl();return sw=function(t){var n=e(this,t).delete(t);return this.size-=!!n,n}}(),n=function(){if(sC)return sT;sC=1;var e=rl();return sT=function(t){return e(this,t).get(t)}}(),r=function(){if(sP)return sk;sP=1;var e=rl();return sk=function(t){return e(this,t).has(t)}}(),i=function(){if(sB)return sS;sB=1;var e=rl();return sS=function(t,n){var r=e(this,t),i=r.size;return r.set(t,n),this.size+=+(r.size!=i),this}}();function a(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}return a.prototype.clear=e,a.prototype.delete=t,a.prototype.get=n,a.prototype.has=r,a.prototype.set=i,sD=a}();function t(n,r){if("function"!=typeof n||null!=r&&"function"!=typeof r)throw TypeError("Expected a function");var i=function(){var e=arguments,t=r?r.apply(this,e):e[0],a=i.cache;if(a.has(t))return a.get(t);var o=n.apply(this,e);return i.cache=a.set(t,o)||a,o};return i.cache=new(t.Cache||e),i}return t.Cache=e,sA=t}();return sR=function(t){var n=e(t,function(e){return 500===r.size&&r.clear(),e}),r=n.cache;return n}}(),t=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,n=/\\(\\)?/g;return sN=e(function(e){var r=[];return 46===e.charCodeAt(0)&&r.push(""),e.replace(t,function(e,t,i,a){r.push(i?a.replace(n,"$1"):t||e)}),r})}function rc(){return sz?sO:(sz=1,sO=function(e,t){for(var n=-1,r=null==e?0:e.length,i=Array(r);++n<r;)i[n]=t(e[n],n,e);return i})}function rd(){if(sj)return sX;sj=1;var e=function(){if(sF)return sV;sF=1;var e=ed(),t=rc(),n=rr(),r=ef(),i=e?e.prototype:void 0,a=i?i.toString:void 0;return sV=function e(i){if("string"==typeof i)return i;if(n(i))return t(i,e)+"";if(r(i))return a?a.call(i):"";var o=i+"";return"0"==o&&1/i==-1/0?"-0":o}}();return sX=function(t){return null==t?"":e(t)}}function rh(){if(sq)return sY;sq=1;var e=rr(),t=function(){if(oM)return oA;oM=1;var e=rr(),t=ef(),n=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,r=/^\w*$/;return oA=function(i,a){if(e(i))return!1;var o=typeof i;return!!("number"==o||"symbol"==o||"boolean"==o||null==i||t(i))||r.test(i)||!n.test(i)||null!=a&&i in Object(a)}}(),n=ru(),r=rd();return sY=function(i,a){return e(i)?i:t(i,a)?[i]:n(r(i))}}function rf(){if(sU)return sW;sU=1;var e=ef();return sW=function(t){if("string"==typeof t||e(t))return t;var n=t+"";return"0"==n&&1/t==-1/0?"-0":n}}J(rn,{instanceString:function(){return"animation"},hook:function(){var e=this._private;if(!e.hooked){var t=e.target._private.animation;(e.queue?t.queue:t.current).push(this),I(e.target)&&e.target.cy().addToAnimationPool(e.target),e.hooked=!0}return this},play:function(){var e=this._private;return 1===e.progress&&(e.progress=0),e.playing=!0,e.started=!1,e.stopped=!1,this.hook(),this},playing:function(){return this._private.playing},apply:function(){var e=this._private;return e.applying=!0,e.started=!1,e.stopped=!1,this.hook(),this},applying:function(){return this._private.applying},pause:function(){var e=this._private;return e.playing=!1,e.started=!1,this},stop:function(){var e=this._private;return e.playing=!1,e.started=!1,e.stopped=!0,this},rewind:function(){return this.progress(0)},fastforward:function(){return this.progress(1)},time:function(e){var t=this._private;return void 0===e?t.progress*t.duration:this.progress(e/t.duration)},progress:function(e){var t=this._private,n=t.playing;return void 0===e?t.progress:(n&&this.pause(),t.progress=e,t.started=!1,n&&this.play(),this)},completed:function(){return 1===this._private.progress},reverse:function(){var e=this._private,t=e.playing;t&&this.pause(),e.progress=1-e.progress,e.started=!1;var n=function(t,n){var r=e[t];null!=r&&(e[t]=e[n],e[n]=r)};if(n("zoom","startZoom"),n("pan","startPan"),n("position","startPosition"),e.style)for(var r=0;r<e.style.length;r++){var i=e.style[r],a=i.name,o=e.startStyle[a];e.startStyle[a]=i,e.style[r]=o}return t&&this.play(),this},promise:function(e){var t,n=this._private;return t="frame"===e?n.frames:n.completes,new re(function(e,n){t.push(function(){e()})})}}),rn.complete=rn.completed,rn.run=rn.play,rn.running=rn.playing;var rp=el(function(){if(sZ)return sK;sZ=1;var e=function(){if(sH)return sG;sH=1;var e=rh(),t=rf();return sG=function(n,r){r=e(r,n);for(var i=0,a=r.length;null!=n&&i<a;)n=n[t(r[i++])];return i&&i==a?n:void 0}}();return sK=function(t,n,r){var i=null==t?void 0:e(t,n);return void 0===i?r:i}}()),rg=el(function(){if(s8)return s6;s8=1;var e=function(){if(s9)return s4;s9=1;var e=function(){if(s2)return s1;s2=1;var e=function(){if(s0)return sJ;s0=1;var e=function(){if(sQ)return s$;sQ=1;var e=ri();return s$=function(){try{var t=e(Object,"defineProperty");return t({},"",{}),t}catch(e){}}()}();return sJ=function(t,n,r){"__proto__"==n&&e?e(t,n,{configurable:!0,enumerable:!0,value:r,writable:!0}):t[n]=r}}(),t=ro(),n=Object.prototype.hasOwnProperty;return s1=function(r,i,a){var o=r[i];n.call(r,i)&&t(o,a)&&(void 0!==a||i in r)||e(r,i,a)}}(),t=rh(),n=function(){if(s3)return s5;s3=1;var e=/^(?:0|[1-9]\d*)$/;return s5=function(t,n){var r=typeof t;return!!(n=null==n?0x1fffffffffffff:n)&&("number"==r||"symbol"!=r&&e.test(t))&&t>-1&&t%1==0&&t<n}}(),r=eu(),i=rf();return s4=function(a,o,s,l){if(!r(a))return a;o=t(o,a);for(var u=-1,c=o.length,d=c-1,h=a;null!=h&&++u<c;){var f=i(o[u]),p=s;if("__proto__"===f||"constructor"===f||"prototype"===f)break;if(u!=d){var g=h[f];void 0===(p=l?l(g,f,h):void 0)&&(p=r(g)?g:n(o[u+1])?[]:{})}e(h,f,p),h=h[f]}return a}}();return s6=function(t,n,r){return null==t?t:e(t,n,r)}}()),rv=el(function(){if(ln)return lt;ln=1;var e=rc(),t=le?s7:(le=1,s7=function(e,t){var n=-1,r=e.length;for(t||(t=Array(r));++n<r;)t[n]=e[n];return t}),n=rr(),r=ef(),i=ru(),a=rf(),o=rd();return lt=function(s){return n(s)?e(s,a):r(s)?[s]:t(i(o(s)))}}()),ry={};[{animated:function(){return function(){var e=void 0!==this.length;if(!(this._private.cy||this).styleEnabled())return!1;var t=(e?this:[this])[0];if(t)return t._private.animation.current.length>0}},clearQueue:function(){return function(){var e=void 0!==this.length?this:[this];if(!(this._private.cy||this).styleEnabled())return this;for(var t=0;t<e.length;t++)e[t]._private.animation.queue=[];return this}},delay:function(){return function(e,t){return(this._private.cy||this).styleEnabled()?this.animate({delay:e,duration:e,complete:t}):this}},delayAnimation:function(){return function(e,t){return(this._private.cy||this).styleEnabled()?this.animation({delay:e,duration:e,complete:t}):this}},animation:function(){return function(e,t){var n=void 0!==this.length,r=n?this:[this],i=this._private.cy||this,a=!n,o=!a;if(!i.styleEnabled())return this;var s=i.style();if(0===Object.keys(e=J({},e,t)).length)return new rt(r[0],e);switch(void 0===e.duration&&(e.duration=400),e.duration){case"slow":e.duration=600;break;case"fast":e.duration=200}if(o&&(e.style=s.getPropsList(e.style||e.css),e.css=void 0),o&&null!=e.renderedPosition){var l=e.renderedPosition,u=i.pan(),c=i.zoom();e.position=tr(l,c,u)}if(a&&null!=e.panBy){var d=e.panBy,h=i.pan();e.pan={x:h.x+d.x,y:h.y+d.y}}var f=e.center||e.centre;if(a&&null!=f){var p=i.getCenterPan(f.eles,e.zoom);null!=p&&(e.pan=p)}if(a&&null!=e.fit){var g=e.fit,v=i.getFitViewport(g.eles||g.boundingBox,g.padding);null!=v&&(e.pan=v.pan,e.zoom=v.zoom)}if(a&&A(e.zoom)){var y=i.getZoomedViewport(e.zoom);null!=y?(y.zoomed&&(e.zoom=y.zoom),y.panned&&(e.pan=y.pan)):e.zoom=null}return new rt(r[0],e)}},animate:function(){return function(e,t){var n=void 0!==this.length?this:[this];if(!(this._private.cy||this).styleEnabled())return this;t&&(e=J({},e,t));for(var r=0;r<n.length;r++){var i=n[r],a=i.animated()&&(void 0===e.queue||e.queue);i.animation(e,a?{queue:!0}:void 0).play()}return this}},stop:function(){return function(e,t){var n=void 0!==this.length?this:[this],r=this._private.cy||this;if(!r.styleEnabled())return this;for(var i=0;i<n.length;i++){for(var a=n[i]._private,o=a.animation.current,s=0;s<o.length;s++){var l=o[s]._private;t&&(l.duration=0)}e&&(a.animation.queue=[]),t||(a.animation.current=[])}return r.notify("draw"),this}}},{data:function(e){return e=J({},{field:"data",bindingEvent:"data",allowBinding:!1,allowSetting:!1,allowGetting:!1,settingEvent:"data",settingTriggersEvent:!1,triggerFnName:"trigger",immutableKeys:{},updateStyle:!1,beforeGet:function(e){},beforeSet:function(e,t){},onSet:function(e){},canSet:function(e){return!0}},e),function(t,n){var r,i=e,a=void 0!==this.length,o=a?this:[this],l=a?this[0]:this;if(B(t)){var u,c=-1!==t.indexOf(".")&&rv(t);if(i.allowGetting&&void 0===n)return l&&(i.beforeGet(l),u=c&&void 0===l._private[i.field][t]?rp(l._private[i.field],c):l._private[i.field][t]),u;if(i.allowSetting&&void 0!==n&&!i.immutableKeys[t]){var d=s({},t,n);i.beforeSet(this,d);for(var h=0,f=o.length;h<f;h++){var p=o[h];i.canSet(p)&&(c&&void 0===l._private[i.field][t]?rg(p._private[i.field],c,n):p._private[i.field][t]=n)}i.updateStyle&&this.updateStyle(),i.onSet(this),i.settingTriggersEvent&&this[i.triggerFnName](i.settingEvent)}}else if(i.allowSetting&&A(t)){var g,v,y=Object.keys(t);i.beforeSet(this,t);for(var b=0;b<y.length;b++)if(v=t[g=y[b]],!i.immutableKeys[g])for(var x=0;x<o.length;x++){var w=o[x];i.canSet(w)&&(w._private[i.field][g]=v)}i.updateStyle&&this.updateStyle(),i.onSet(this),i.settingTriggersEvent&&this[i.triggerFnName](i.settingEvent)}else if(i.allowBinding&&D(t))this.on(i.bindingEvent,t);else if(i.allowGetting&&void 0===t)return l&&(i.beforeGet(l),r=l._private[i.field]),r;return this}},removeData:function(e){return e=J({},{field:"data",event:"data",triggerFnName:"trigger",triggerEvent:!1,immutableKeys:{}},e),function(t){var n=e,r=void 0!==this.length?this:[this];if(B(t)){for(var i=t.split(/\s+/),a=i.length,o=0;o<a;o++){var s=i[o];if(!V(s)&&!n.immutableKeys[s])for(var l=0,u=r.length;l<u;l++)r[l]._private[n.field][s]=void 0}n.triggerEvent&&this[n.triggerFnName](n.event)}else if(void 0===t){for(var c=0,d=r.length;c<d;c++)for(var h=r[c]._private[n.field],f=Object.keys(h),p=0;p<f.length;p++){var g=f[p];n.immutableKeys[g]||(h[g]=void 0)}n.triggerEvent&&this[n.triggerFnName](n.event)}return this}}},{eventAliasesOn:function(e){e.addListener=e.listen=e.bind=e.on,e.unlisten=e.unbind=e.off=e.removeListener,e.trigger=e.emit,e.pon=e.promiseOn=function(e,t){var n=this,r=Array.prototype.slice.call(arguments,0);return new re(function(e,t){var i=r.concat([function(t){n.off.apply(n,a),e(t)}]),a=i.concat([]);n.on.apply(n,i)})}}}].forEach(function(e){J(ry,e)});var rm={animate:ry.animate(),animation:ry.animation(),animated:ry.animated(),clearQueue:ry.clearQueue(),delay:ry.delay(),delayAnimation:ry.delayAnimation(),stop:ry.stop()},rb={classes:function(e){if(void 0===e){var t=[];return this[0]._private.classes.forEach(function(e){return t.push(e)}),t}_(e)||(e=(e||"").match(/\S+/g)||[]);for(var n=[],r=new eQ(e),i=0;i<this.length;i++){for(var a=this[i],o=a._private,s=o.classes,l=!1,u=0;u<e.length;u++){var c=e[u];if(!s.has(c)){l=!0;break}}l||(l=s.size!==e.length),l&&(o.classes=r,n.push(a))}return n.length>0&&this.spawn(n).updateStyle().emit("class"),this},addClass:function(e){return this.toggleClass(e,!0)},hasClass:function(e){var t=this[0];return null!=t&&t._private.classes.has(e)},toggleClass:function(e,t){_(e)||(e=e.match(/\S+/g)||[]);for(var n=void 0===t,r=[],i=0,a=this.length;i<a;i++)for(var o=this[i],s=o._private.classes,l=!1,u=0;u<e.length;u++){var c=e[u],d=s.has(c),h=!1;t||n&&!d?(s.add(c),h=!0):(!t||n&&d)&&(s.delete(c),h=!0),!l&&h&&(r.push(o),l=!0)}return r.length>0&&this.spawn(r).updateStyle().emit("class"),this},removeClass:function(e){return this.toggleClass(e,!1)},flashClass:function(e,t){var n=this;if(null==t)t=250;else if(0===t)return n;return n.addClass(e),setTimeout(function(){n.removeClass(e)},t),n}};rb.className=rb.classNames=rb.classes;var rx={metaChar:"[\\!\\\"\\#\\$\\%\\&\\'\\(\\)\\*\\+\\,\\.\\/\\:\\;\\<\\=\\>\\?\\@\\[\\]\\^\\`\\{\\|\\}\\~]",comparatorOp:"=|\\!=|>|>=|<|<=|\\$=|\\^=|\\*=",boolOp:"\\?|\\!|\\^",string:"\"(?:\\\\\"|[^\"])*\"|'(?:\\\\'|[^'])*'",number:G,meta:"degree|indegree|outdegree",separator:"\\s*,\\s*",descendant:"\\s+",child:"\\s+>\\s+",subject:"\\$",group:"node|edge|\\*",directedEdge:"\\s+->\\s+",undirectedEdge:"\\s+<->\\s+"};rx.variable="(?:[\\w-.]|(?:\\\\"+rx.metaChar+"))+",rx.className="(?:[\\w-]|(?:\\\\"+rx.metaChar+"))+",rx.value=rx.string+"|"+rx.number,rx.id=rx.variable,function(){var e,t,n;for(n=0,e=rx.comparatorOp.split("|");n<e.length;n++)t=e[n],rx.comparatorOp+="|@"+t;for(n=0,e=rx.comparatorOp.split("|");n<e.length;n++)(t=e[n]).indexOf("!")>=0||"="!==t&&(rx.comparatorOp+="|\\!"+t)}();var rw=function(){return{checks:[]}},rE={GROUP:0,COLLECTION:1,FILTER:2,DATA_COMPARE:3,DATA_EXIST:4,DATA_BOOL:5,META_COMPARE:6,STATE:7,ID:8,CLASS:9,UNDIRECTED_EDGE:10,DIRECTED_EDGE:11,NODE_SOURCE:12,NODE_TARGET:13,NODE_NEIGHBOR:14,CHILD:15,DESCENDANT:16,PARENT:17,ANCESTOR:18,COMPOUND_SPLIT:19,TRUE:20},rT=[{selector:":selected",matches:function(e){return e.selected()}},{selector:":unselected",matches:function(e){return!e.selected()}},{selector:":selectable",matches:function(e){return e.selectable()}},{selector:":unselectable",matches:function(e){return!e.selectable()}},{selector:":locked",matches:function(e){return e.locked()}},{selector:":unlocked",matches:function(e){return!e.locked()}},{selector:":visible",matches:function(e){return e.visible()}},{selector:":hidden",matches:function(e){return!e.visible()}},{selector:":transparent",matches:function(e){return e.transparent()}},{selector:":grabbed",matches:function(e){return e.grabbed()}},{selector:":free",matches:function(e){return!e.grabbed()}},{selector:":removed",matches:function(e){return e.removed()}},{selector:":inside",matches:function(e){return!e.removed()}},{selector:":grabbable",matches:function(e){return e.grabbable()}},{selector:":ungrabbable",matches:function(e){return!e.grabbable()}},{selector:":animated",matches:function(e){return e.animated()}},{selector:":unanimated",matches:function(e){return!e.animated()}},{selector:":parent",matches:function(e){return e.isParent()}},{selector:":childless",matches:function(e){return e.isChildless()}},{selector:":child",matches:function(e){return e.isChild()}},{selector:":orphan",matches:function(e){return e.isOrphan()}},{selector:":nonorphan",matches:function(e){return e.isChild()}},{selector:":compound",matches:function(e){return e.isNode()?e.isParent():e.source().isParent()||e.target().isParent()}},{selector:":loop",matches:function(e){return e.isLoop()}},{selector:":simple",matches:function(e){return e.isSimple()}},{selector:":active",matches:function(e){return e.active()}},{selector:":inactive",matches:function(e){return!e.active()}},{selector:":backgrounding",matches:function(e){return e.backgrounding()}},{selector:":nonbackgrounding",matches:function(e){return!e.backgrounding()}}].sort(function(e,t){return -1*Q(e.selector,t.selector)}),rC=function(){for(var e,t={},n=0;n<rT.length;n++)t[(e=rT[n]).selector]=e.matches;return t}(),rk="("+rT.map(function(e){return e.selector}).join("|")+")",rP=function(e){return e.replace(RegExp("\\\\("+rx.metaChar+")","g"),function(e,t){return t})},rS=function(e,t,n){e[e.length-1]=n},rB=[{name:"group",query:!0,regex:"("+rx.group+")",populate:function(e,t,n){var r=l(n,1)[0];t.checks.push({type:rE.GROUP,value:"*"===r?r:r+"s"})}},{name:"state",query:!0,regex:rk,populate:function(e,t,n){var r=l(n,1)[0];t.checks.push({type:rE.STATE,value:r})}},{name:"id",query:!0,regex:"\\#("+rx.id+")",populate:function(e,t,n){var r=l(n,1)[0];t.checks.push({type:rE.ID,value:rP(r)})}},{name:"className",query:!0,regex:"\\.("+rx.className+")",populate:function(e,t,n){var r=l(n,1)[0];t.checks.push({type:rE.CLASS,value:rP(r)})}},{name:"dataExists",query:!0,regex:"\\[\\s*("+rx.variable+")\\s*\\]",populate:function(e,t,n){var r=l(n,1)[0];t.checks.push({type:rE.DATA_EXIST,field:rP(r)})}},{name:"dataCompare",query:!0,regex:"\\[\\s*("+rx.variable+")\\s*("+rx.comparatorOp+")\\s*("+rx.value+")\\s*\\]",populate:function(e,t,n){var r=l(n,3),i=r[0],a=r[1],o=r[2];o=null!=RegExp("^"+rx.string+"$").exec(o)?o.substring(1,o.length-1):parseFloat(o),t.checks.push({type:rE.DATA_COMPARE,field:rP(i),operator:a,value:o})}},{name:"dataBool",query:!0,regex:"\\[\\s*("+rx.boolOp+")\\s*("+rx.variable+")\\s*\\]",populate:function(e,t,n){var r=l(n,2),i=r[0],a=r[1];t.checks.push({type:rE.DATA_BOOL,field:rP(a),operator:i})}},{name:"metaCompare",query:!0,regex:"\\[\\[\\s*("+rx.meta+")\\s*("+rx.comparatorOp+")\\s*("+rx.number+")\\s*\\]\\]",populate:function(e,t,n){var r=l(n,3),i=r[0],a=r[1],o=r[2];t.checks.push({type:rE.META_COMPARE,field:rP(i),operator:a,value:parseFloat(o)})}},{name:"nextQuery",separator:!0,regex:rx.separator,populate:function(e,t){var n=e.currentSubject,r=e.edgeCount,i=e.compoundCount,a=e[e.length-1];return null!=n&&(a.subject=n,e.currentSubject=null),a.edgeCount=r,a.compoundCount=i,e.edgeCount=0,e.compoundCount=0,e[e.length++]=rw()}},{name:"directedEdge",separator:!0,regex:rx.directedEdge,populate:function(e,t){if(null==e.currentSubject){var n=rw(),r=rw();return n.checks.push({type:rE.DIRECTED_EDGE,source:t,target:r}),rS(e,t,n),e.edgeCount++,r}var i=rw(),a=rw();return i.checks.push({type:rE.NODE_SOURCE,source:t,target:a}),rS(e,t,i),e.edgeCount++,a}},{name:"undirectedEdge",separator:!0,regex:rx.undirectedEdge,populate:function(e,t){if(null==e.currentSubject){var n=rw(),r=rw();return n.checks.push({type:rE.UNDIRECTED_EDGE,nodes:[t,r]}),rS(e,t,n),e.edgeCount++,r}var i=rw(),a=rw();return i.checks.push({type:rE.NODE_NEIGHBOR,node:t,neighbor:a}),rS(e,t,i),a}},{name:"child",separator:!0,regex:rx.child,populate:function(e,t){if(null==e.currentSubject){var n=rw(),r=rw(),i=e[e.length-1];return n.checks.push({type:rE.CHILD,parent:i,child:r}),rS(e,t,n),e.compoundCount++,r}if(e.currentSubject===t){var a=rw(),o=e[e.length-1],s=rw(),l=rw(),u=rw(),c=rw();return a.checks.push({type:rE.COMPOUND_SPLIT,left:o,right:s,subject:l}),l.checks=t.checks,t.checks=[{type:rE.TRUE}],c.checks.push({type:rE.TRUE}),s.checks.push({type:rE.PARENT,parent:c,child:u}),rS(e,o,a),e.currentSubject=l,e.compoundCount++,u}var d=rw(),h=rw(),f=[{type:rE.PARENT,parent:d,child:h}];return d.checks=t.checks,t.checks=f,e.compoundCount++,h}},{name:"descendant",separator:!0,regex:rx.descendant,populate:function(e,t){if(null==e.currentSubject){var n=rw(),r=rw(),i=e[e.length-1];return n.checks.push({type:rE.DESCENDANT,ancestor:i,descendant:r}),rS(e,t,n),e.compoundCount++,r}if(e.currentSubject===t){var a=rw(),o=e[e.length-1],s=rw(),l=rw(),u=rw(),c=rw();return a.checks.push({type:rE.COMPOUND_SPLIT,left:o,right:s,subject:l}),l.checks=t.checks,t.checks=[{type:rE.TRUE}],c.checks.push({type:rE.TRUE}),s.checks.push({type:rE.ANCESTOR,ancestor:c,descendant:u}),rS(e,o,a),e.currentSubject=l,e.compoundCount++,u}var d=rw(),h=rw(),f=[{type:rE.ANCESTOR,ancestor:d,descendant:h}];return d.checks=t.checks,t.checks=f,e.compoundCount++,h}},{name:"subject",modifier:!0,regex:rx.subject,populate:function(e,t){if(null!=e.currentSubject&&e.currentSubject!==t)return ez("Redefinition of subject in selector `"+e.toString()+"`"),!1;e.currentSubject=t;var n=e[e.length-1].checks[0],r=null==n?null:n.type;r===rE.DIRECTED_EDGE?n.type=rE.NODE_TARGET:r===rE.UNDIRECTED_EDGE&&(n.type=rE.NODE_NEIGHBOR,n.node=n.nodes[1],n.neighbor=n.nodes[0],n.nodes=null)}}];rB.forEach(function(e){return e.regexObj=RegExp("^"+e.regex)});var rD=function(e){for(var t,n,r,i=0;i<rB.length;i++){var a=rB[i],o=a.name,s=e.match(a.regexObj);if(null!=s){n=s,t=a,r=o;var l=s[0];e=e.substring(l.length);break}}return{expr:t,match:n,name:r,remaining:e}},r_=function(e){var t=e.match(/^\s+/);if(t){var n=t[0];e=e.substring(n.length)}return e},rA=function(e,t,n){var r,i,a,o=B(e),s=M(e),l=B(n),u=!1,c=!1,d=!1;switch(t.indexOf("!")>=0&&(t=t.replace("!",""),c=!0),t.indexOf("@")>=0&&(t=t.replace("@",""),u=!0),(o||l||u)&&(i=o||s?""+e:"",a=""+n),u&&(e=i=i.toLowerCase(),n=a=a.toLowerCase()),t){case"*=":r=i.indexOf(a)>=0;break;case"$=":r=i.indexOf(a,i.length-a.length)>=0;break;case"^=":r=0===i.indexOf(a);break;case"=":r=e===n;break;case">":d=!0,r=e>n;break;case">=":d=!0,r=e>=n;break;case"<":d=!0,r=e<n;break;case"<=":d=!0,r=e<=n;break;default:r=!1}return c&&(null!=e||!d)&&(r=!r),r},rM=function(e,t){switch(t){case"?":return!!e;case"!":return!e;case"^":return void 0===e}},rR=function(e,t){return e.data(t)},rI=[],rN=function(e,t){return e.checks.every(function(e){return rI[e.type](e,t)})};rI[rE.GROUP]=function(e,t){var n=e.value;return"*"===n||n===t.group()},rI[rE.STATE]=function(e,t){var n;return n=e.value,rC[n](t)},rI[rE.ID]=function(e,t){var n=e.value;return t.id()===n},rI[rE.CLASS]=function(e,t){var n=e.value;return t.hasClass(n)},rI[rE.META_COMPARE]=function(e,t){var n=e.field,r=e.operator,i=e.value;return rA(t[n](),r,i)},rI[rE.DATA_COMPARE]=function(e,t){var n=e.field,r=e.operator,i=e.value;return rA(rR(t,n),r,i)},rI[rE.DATA_BOOL]=function(e,t){var n=e.field,r=e.operator;return rM(rR(t,n),r)},rI[rE.DATA_EXIST]=function(e,t){var n=e.field;return e.operator,void 0!==rR(t,n)},rI[rE.UNDIRECTED_EDGE]=function(e,t){var n=e.nodes[0],r=e.nodes[1],i=t.source(),a=t.target();return rN(n,i)&&rN(r,a)||rN(r,i)&&rN(n,a)},rI[rE.NODE_NEIGHBOR]=function(e,t){return rN(e.node,t)&&t.neighborhood().some(function(t){return t.isNode()&&rN(e.neighbor,t)})},rI[rE.DIRECTED_EDGE]=function(e,t){return rN(e.source,t.source())&&rN(e.target,t.target())},rI[rE.NODE_SOURCE]=function(e,t){return rN(e.source,t)&&t.outgoers().some(function(t){return t.isNode()&&rN(e.target,t)})},rI[rE.NODE_TARGET]=function(e,t){return rN(e.target,t)&&t.incomers().some(function(t){return t.isNode()&&rN(e.source,t)})},rI[rE.CHILD]=function(e,t){return rN(e.child,t)&&rN(e.parent,t.parent())},rI[rE.PARENT]=function(e,t){return rN(e.parent,t)&&t.children().some(function(t){return rN(e.child,t)})},rI[rE.DESCENDANT]=function(e,t){return rN(e.descendant,t)&&t.ancestors().some(function(t){return rN(e.ancestor,t)})},rI[rE.ANCESTOR]=function(e,t){return rN(e.ancestor,t)&&t.descendants().some(function(t){return rN(e.descendant,t)})},rI[rE.COMPOUND_SPLIT]=function(e,t){return rN(e.subject,t)&&rN(e.left,t)&&rN(e.right,t)},rI[rE.TRUE]=function(){return!0},rI[rE.COLLECTION]=function(e,t){return e.value.has(t)},rI[rE.FILTER]=function(e,t){return(0,e.value)(t)};var rL=function(e){this.inputText=e,this.currentSubject=null,this.compoundCount=0,this.edgeCount=0,this.length=0,null==e||B(e)&&e.match(/^\s*$/)||(I(e)?this.addQuery({checks:[{type:rE.COLLECTION,value:e.collection()}]}):D(e)?this.addQuery({checks:[{type:rE.FILTER,value:e}]}):B(e)?this.parse(e)||(this.invalid=!0):eL("A selector must be created from a string; found "))},rO=rL.prototype;[{parse:function(e){var t=this.inputText=e,n=this[0]=rw();for(this.length=1,t=r_(t);;){var r=rD(t);if(null==r.expr)return ez("The selector `"+e+"`is invalid"),!1;var i=r.match.slice(1),a=r.expr.populate(this,n,i);if(!1===a)return!1;if(null!=a&&(n=a),(t=r.remaining).match(/^\s*$/))break}var o=this[this.length-1];null!=this.currentSubject&&(o.subject=this.currentSubject),o.edgeCount=this.edgeCount,o.compoundCount=this.compoundCount;for(var s=0;s<this.length;s++){var l=this[s];if(l.compoundCount>0&&l.edgeCount>0)return ez("The selector `"+e+"` is invalid because it uses both a compound selector and an edge selector"),!1;if(l.edgeCount>1)return ez("The selector `"+e+"` is invalid because it uses multiple edge selectors"),!1;1===l.edgeCount&&ez("The selector `"+e+"` is deprecated.  Edge selectors do not take effect on changes to source and target nodes after an edge is added, for performance reasons.  Use a class or data selector on edges instead, updating the class or data of an edge when your app detects a change in source or target nodes.")}return!0},toString:function(){if(null!=this.toStringCache)return this.toStringCache;for(var e=function(e){return null==e?"":e},t=function(t){return B(t)?'"'+t+'"':e(t)},n=function(e){return" "+e+" "},r=function(r,a){var o=r.type,s=r.value;switch(o){case rE.GROUP:var l=e(s);return l.substring(0,l.length-1);case rE.DATA_COMPARE:return"["+r.field+n(e(r.operator))+t(s)+"]";case rE.DATA_BOOL:var u=r.operator,c=r.field;return"["+e(u)+c+"]";case rE.DATA_EXIST:return"["+r.field+"]";case rE.META_COMPARE:var d=r.operator;return"[["+r.field+n(e(d))+t(s)+"]]";case rE.STATE:return s;case rE.ID:return"#"+s;case rE.CLASS:return"."+s;case rE.PARENT:case rE.CHILD:return i(r.parent,a)+n(">")+i(r.child,a);case rE.ANCESTOR:case rE.DESCENDANT:return i(r.ancestor,a)+" "+i(r.descendant,a);case rE.COMPOUND_SPLIT:var h=i(r.left,a),f=i(r.subject,a),p=i(r.right,a);return h+(h.length>0?" ":"")+f+p;case rE.TRUE:return""}},i=function(e,t){return e.checks.reduce(function(n,i,a){return n+(t===e&&0===a?"$":"")+r(i,t)},"")},a="",o=0;o<this.length;o++){var s=this[o];a+=i(s,s.subject),this.length>1&&o<this.length-1&&(a+=", ")}return this.toStringCache=a,a}},{matches:function(e){for(var t=0;t<this.length;t++)if(rN(this[t],e))return!0;return!1},filter:function(e){var t=this;if(1===t.length&&1===t[0].checks.length&&t[0].checks[0].type===rE.ID)return e.getElementById(t[0].checks[0].value).collection();var n=function(e){for(var n=0;n<t.length;n++)if(rN(t[n],e))return!0;return!1};return null==t.text()&&(n=function(){return!0}),e.filter(n)}}].forEach(function(e){return J(rO,e)}),rO.text=function(){return this.inputText},rO.size=function(){return this.length},rO.eq=function(e){return this[e]},rO.sameText=function(e){return!this.invalid&&!e.invalid&&this.text()===e.text()},rO.addQuery=function(e){this[this.length++]=e},rO.selector=rO.toString;var rz={allAre:function(e){var t=new rL(e);return this.every(function(e){return t.matches(e)})},is:function(e){var t=new rL(e);return this.some(function(e){return t.matches(e)})},some:function(e,t){for(var n=0;n<this.length;n++)if(t?e.apply(t,[this[n],n,this]):e(this[n],n,this))return!0;return!1},every:function(e,t){for(var n=0;n<this.length;n++)if(!(t?e.apply(t,[this[n],n,this]):e(this[n],n,this)))return!1;return!0},same:function(e){if(this===e)return!0;e=this.cy().collection(e);var t=this.length;return t===e.length&&(1===t?this[0]===e[0]:this.every(function(t){return e.hasElementWithId(t.id())}))},anySame:function(e){return e=this.cy().collection(e),this.some(function(t){return e.hasElementWithId(t.id())})},allAreNeighbors:function(e){e=this.cy().collection(e);var t=this.neighborhood();return e.every(function(e){return t.hasElementWithId(e.id())})},contains:function(e){e=this.cy().collection(e);var t=this;return e.every(function(e){return t.hasElementWithId(e.id())})}};rz.allAreNeighbours=rz.allAreNeighbors,rz.has=rz.contains,rz.equal=rz.equals=rz.same;var rV=function(e,t){return function(n,r,i,a){if(null==n?o="":I(n)&&1===n.length&&(o=n.id()),1!==this.length||!o)return e.call(this,n,r,i,a);var o,s=this[0]._private,l=s.traversalCache=s.traversalCache||{},u=l[t]=l[t]||[],c=ek(o),d=u[c];return d||(u[c]=e.call(this,n,r,i,a))}},rF={parent:function(e){var t=[];if(1===this.length){var n=this[0]._private.parent;if(n)return n}for(var r=0;r<this.length;r++){var i=this[r]._private.parent;i&&t.push(i)}return this.spawn(t,!0).filter(e)},parents:function(e){for(var t=[],n=this.parent();n.nonempty();){for(var r=0;r<n.length;r++){var i=n[r];t.push(i)}n=n.parent()}return this.spawn(t,!0).filter(e)},commonAncestors:function(e){for(var t,n=0;n<this.length;n++){var r=this[n].parents();t=(t=t||r).intersect(r)}return t.filter(e)},orphans:function(e){return this.stdFilter(function(e){return e.isOrphan()}).filter(e)},nonorphans:function(e){return this.stdFilter(function(e){return e.isChild()}).filter(e)},children:rV(function(e){for(var t=[],n=0;n<this.length;n++)for(var r=this[n]._private.children,i=0;i<r.length;i++)t.push(r[i]);return this.spawn(t,!0).filter(e)},"children"),siblings:function(e){return this.parent().children().not(this).filter(e)},isParent:function(){var e=this[0];if(e)return e.isNode()&&0!==e._private.children.length},isChildless:function(){var e=this[0];if(e)return e.isNode()&&0===e._private.children.length},isChild:function(){var e=this[0];if(e)return e.isNode()&&null!=e._private.parent},isOrphan:function(){var e=this[0];if(e)return e.isNode()&&null==e._private.parent},descendants:function(e){var t=[];return!function e(n){for(var r=0;r<n.length;r++){var i=n[r];t.push(i),i.children().nonempty()&&e(i.children())}}(this.children()),this.spawn(t,!0).filter(e)}};function rX(e,t,n,r){for(var i=[],a=new eQ,o=e.cy().hasCompoundNodes(),s=0;s<e.length;s++){var l=e[s];n?i.push(l):o&&r(i,a,l)}for(;i.length>0;){var u=i.shift();t(u),a.add(u.id()),o&&r(i,a,u)}return e}function rj(e,t,n){if(n.isParent())for(var r=n._private.children,i=0;i<r.length;i++){var a=r[i];t.has(a.id())||e.push(a)}}function rY(e,t,n){if(n.isChild()){var r=n._private.parent;t.has(r.id())||e.push(r)}}function rq(e,t,n){rY(e,t,n),rj(e,t,n)}rF.forEachDown=function(e){var t=!(arguments.length>1)||void 0===arguments[1]||arguments[1];return rX(this,e,t,rj)},rF.forEachUp=function(e){var t=!(arguments.length>1)||void 0===arguments[1]||arguments[1];return rX(this,e,t,rY)},rF.forEachUpAndDown=function(e){var t=!(arguments.length>1)||void 0===arguments[1]||arguments[1];return rX(this,e,t,rq)},rF.ancestors=rF.parents,(lr=li={data:ry.data({field:"data",bindingEvent:"data",allowBinding:!0,allowSetting:!0,settingEvent:"data",settingTriggersEvent:!0,triggerFnName:"trigger",allowGetting:!0,immutableKeys:{id:!0,source:!0,target:!0,parent:!0},updateStyle:!0}),removeData:ry.removeData({field:"data",event:"data",triggerFnName:"trigger",triggerEvent:!0,immutableKeys:{id:!0,source:!0,target:!0,parent:!0},updateStyle:!0}),scratch:ry.data({field:"scratch",bindingEvent:"scratch",allowBinding:!0,allowSetting:!0,settingEvent:"scratch",settingTriggersEvent:!0,triggerFnName:"trigger",allowGetting:!0,updateStyle:!0}),removeScratch:ry.removeData({field:"scratch",event:"scratch",triggerFnName:"trigger",triggerEvent:!0,updateStyle:!0}),rscratch:ry.data({field:"rscratch",allowBinding:!1,allowSetting:!0,settingTriggersEvent:!1,allowGetting:!0}),removeRscratch:ry.removeData({field:"rscratch",triggerEvent:!1}),id:function(){var e=this[0];if(e)return e._private.data.id}}).attr=lr.data,lr.removeAttr=lr.removeData;var rW={};function rU(e){return function(t){if(void 0===t&&(t=!0),0!==this.length)if(!this.isNode()||this.removed())return;else{for(var n=0,r=this[0],i=r._private.edges,a=0;a<i.length;a++){var o=i[a];!t&&o.isLoop()||(n+=e(r,o))}return n}}}function rG(e,t){return function(n){for(var r,i=this.nodes(),a=0;a<i.length;a++){var o=i[a][e](n);void 0!==o&&(void 0===r||t(o,r))&&(r=o)}return r}}J(rW,{degree:rU(function(e,t){return t.source().same(t.target())?2:1}),indegree:rU(function(e,t){return+!!t.target().same(e)}),outdegree:rU(function(e,t){return+!!t.source().same(e)})}),J(rW,{minDegree:rG("degree",function(e,t){return e<t}),maxDegree:rG("degree",function(e,t){return e>t}),minIndegree:rG("indegree",function(e,t){return e<t}),maxIndegree:rG("indegree",function(e,t){return e>t}),minOutdegree:rG("outdegree",function(e,t){return e<t}),maxOutdegree:rG("outdegree",function(e,t){return e>t})}),J(rW,{totalDegree:function(e){for(var t=0,n=this.nodes(),r=0;r<n.length;r++)t+=n[r].degree(e);return t}});var rH=function(e,t,n){for(var r=0;r<e.length;r++){var i=e[r];if(!i.locked()){var a=i._private.position,o={x:null!=t.x?t.x-a.x:0,y:null!=t.y?t.y-a.y:0};i.isParent()&&(0!==o.x||0!==o.y)&&i.children().shift(o,n),i.dirtyBoundingBoxCache()}}},rK={field:"position",bindingEvent:"position",allowBinding:!0,allowSetting:!0,settingEvent:"position",settingTriggersEvent:!0,triggerFnName:"emitAndNotify",allowGetting:!0,validKeys:["x","y"],beforeGet:function(e){e.updateCompoundBounds()},beforeSet:function(e,t){rH(e,t,!1)},onSet:function(e){e.dirtyCompoundBoundsCache()},canSet:function(e){return!e.locked()}};(la=lo={position:ry.data(rK),silentPosition:ry.data(J({},rK,{allowBinding:!1,allowSetting:!0,settingTriggersEvent:!1,allowGetting:!1,beforeSet:function(e,t){rH(e,t,!0)},onSet:function(e){e.dirtyCompoundBoundsCache()}})),positions:function(e,t){if(A(e))t?this.silentPosition(e):this.position(e);else if(D(e)){var n=this.cy();n.startBatch();for(var r=0;r<this.length;r++){var i=this[r],a=void 0;(a=e(i,r))&&(t?i.silentPosition(a):i.position(a))}n.endBatch()}return this},silentPositions:function(e){return this.positions(e,!0)},shift:function(e,t,n){var r;if(A(e)?(r={x:M(e.x)?e.x:0,y:M(e.y)?e.y:0},n=t):B(e)&&M(t)&&((r={x:0,y:0})[e]=t),null!=r){var i=this.cy();i.startBatch();for(var a=0;a<this.length;a++){var o=this[a];if(!(i.hasCompoundNodes()&&o.isChild()&&o.ancestors().anySame(this))){var s=o.position(),l={x:s.x+r.x,y:s.y+r.y};n?o.silentPosition(l):o.position(l)}}i.endBatch()}return this},silentShift:function(e,t){return A(e)?this.shift(e,!0):B(e)&&M(t)&&this.shift(e,t,!0),this},renderedPosition:function(e,t){var n=this[0],r=this.cy(),i=r.zoom(),a=r.pan(),o=A(e)?e:void 0,s=void 0!==o||void 0!==t&&B(e);if(n&&n.isNode())if(!s)return(o=tn(n.position(),i,a),void 0===e)?o:o[e];else for(var l=0;l<this.length;l++){var u=this[l];void 0!==t?u.position(e,(t-a[e])/i):void 0!==o&&u.position(tr(o,i,a))}else if(!s)return;return this},relativePosition:function(e,t){var n=this[0],r=this.cy(),i=A(e)?e:void 0,a=void 0!==i||void 0!==t&&B(e),o=r.hasCompoundNodes();if(n&&n.isNode())if(a)for(var s=0;s<this.length;s++){var l=this[s],u=o?l.parent():null,c=u&&u.length>0;c&&(u=u[0]);var d=c?u.position():{x:0,y:0};void 0!==t?l.position(e,t+d[e]):void 0!==i&&l.position({x:i.x+d.x,y:i.y+d.y})}else{var h=n.position(),f=o?n.parent():null,p=f&&f.length>0;p&&(f=f[0]);var g=p?f.position():{x:0,y:0};return(i={x:h.x-g.x,y:h.y-g.y},void 0===e)?i:i[e]}else if(!a)return;return this}}).modelPosition=la.point=la.position,la.modelPositions=la.points=la.positions,la.renderedPoint=la.renderedPosition,la.relativePoint=la.relativePosition,ls=ll={},ll.renderedBoundingBox=function(e){var t=this.boundingBox(e),n=this.cy(),r=n.zoom(),i=n.pan(),a=t.x1*r+i.x,o=t.x2*r+i.x,s=t.y1*r+i.y,l=t.y2*r+i.y;return{x1:a,x2:o,y1:s,y2:l,w:o-a,h:l-s}},ll.dirtyCompoundBoundsCache=function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=this.cy();return t.styleEnabled()&&t.hasCompoundNodes()&&this.forEachUp(function(t){if(t.isParent()){var n=t._private;n.compoundBoundsClean=!1,n.bbCache=null,e||t.emitAndNotify("bounds")}}),this},ll.updateCompoundBounds=function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=this.cy();if(!t.styleEnabled()||!t.hasCompoundNodes()||!e&&t.batching())return this;for(var n=0;n<this.length;n++){var r=this[n],i=r._private;(!i.compoundBoundsClean||e)&&(!function(e){if(e.isParent()){var t=e._private,n=e.children(),r="include"===e.pstyle("compound-sizing-wrt-labels").value,i={width:{val:e.pstyle("min-width").pfValue,left:e.pstyle("min-width-bias-left"),right:e.pstyle("min-width-bias-right")},height:{val:e.pstyle("min-height").pfValue,top:e.pstyle("min-height-bias-top"),bottom:e.pstyle("min-height-bias-bottom")}},a=n.boundingBox({includeLabels:r,includeOverlays:!1,useCache:!1}),o=t.position;(0===a.w||0===a.h)&&((a={w:e.pstyle("width").pfValue,h:e.pstyle("height").pfValue}).x1=o.x-a.w/2,a.x2=o.x+a.w/2,a.y1=o.y-a.h/2,a.y2=o.y+a.h/2);var s=i.width.left.value;"px"===i.width.left.units&&i.width.val>0&&(s=100*s/i.width.val);var l=i.width.right.value;"px"===i.width.right.units&&i.width.val>0&&(l=100*l/i.width.val);var u=i.height.top.value;"px"===i.height.top.units&&i.height.val>0&&(u=100*u/i.height.val);var c=i.height.bottom.value;"px"===i.height.bottom.units&&i.height.val>0&&(c=100*c/i.height.val);var d=y(i.width.val-a.w,s,l),h=d.biasDiff,f=d.biasComplementDiff,p=y(i.height.val-a.h,u,c),g=p.biasDiff,v=p.biasComplementDiff;t.autoPadding=function(e,t,n,r){if("%"===n.units)switch(r){case"width":return e>0?n.pfValue*e:0;case"height":return t>0?n.pfValue*t:0;case"average":return e>0&&t>0?n.pfValue*(e+t)/2:0;case"min":return e>0&&t>0?e>t?n.pfValue*t:n.pfValue*e:0;case"max":return e>0&&t>0?e>t?n.pfValue*e:n.pfValue*t:0;default:return 0}return"px"===n.units?n.pfValue:0}(a.w,a.h,e.pstyle("padding"),e.pstyle("padding-relative-to").value),t.autoWidth=Math.max(a.w,i.width.val),o.x=(-h+a.x1+a.x2+f)/2,t.autoHeight=Math.max(a.h,i.height.val),o.y=(-g+a.y1+a.y2+v)/2}function y(e,t,n){var r=0,i=0,a=t+n;return e>0&&a>0&&(r=t/a*e,i=n/a*e),{biasDiff:r,biasComplementDiff:i}}}(r),t.batching()||(i.compoundBoundsClean=!0))}return this};var rZ=function(e){return e===1/0||e===-1/0?0:e},r$=function(e,t,n,r,i){r-t!=0&&i-n!=0&&null!=t&&null!=n&&null!=r&&null!=i&&(e.x1=t<e.x1?t:e.x1,e.x2=r>e.x2?r:e.x2,e.y1=n<e.y1?n:e.y1,e.y2=i>e.y2?i:e.y2,e.w=e.x2-e.x1,e.h=e.y2-e.y1)},rQ=function(e,t){return null==t?e:r$(e,t.x1,t.y1,t.x2,t.y2)},rJ=function(e,t,n){return eG(e,t,n)},r0=function(e,t,n){if(!t.cy().headless()){var r,i,a=t._private,o=a.rstyle,s=o.arrowWidth/2;if("none"!==t.pstyle(n+"-arrow-shape").value){"source"===n?(r=o.srcX,i=o.srcY):"target"===n?(r=o.tgtX,i=o.tgtY):(r=o.midX,i=o.midY);var l=a.arrowBounds=a.arrowBounds||{},u=l[n]=l[n]||{};u.x1=r-s,u.y1=i-s,u.x2=r+s,u.y2=i+s,u.w=u.x2-u.x1,u.h=u.y2-u.y1,tT(u,1),r$(e,u.x1,u.y1,u.x2,u.y2)}}},r1=function(e,t,n){if(!t.cy().headless()){a=n?n+"-":"";var r=t._private,i=r.rstyle;if(t.pstyle(a+"label").strValue){var a,o,s,l,u,c=t.pstyle("text-halign"),d=t.pstyle("text-valign"),h=rJ(i,"labelWidth",n),f=rJ(i,"labelHeight",n),p=rJ(i,"labelX",n),g=rJ(i,"labelY",n),v=t.pstyle(a+"text-margin-x").pfValue,y=t.pstyle(a+"text-margin-y").pfValue,b=t.isEdge(),x=t.pstyle(a+"text-rotation"),w=t.pstyle("text-outline-width").pfValue,E=t.pstyle("text-border-width").pfValue/2,T=t.pstyle("text-background-padding").pfValue,C=h/2,k=f/2;if(b)o=p-C,s=p+C,l=g-k,u=g+k;else{switch(c.value){case"left":o=p-h,s=p;break;case"center":o=p-C,s=p+C;break;case"right":o=p,s=p+h}switch(d.value){case"top":l=g-f,u=g;break;case"center":l=g-k,u=g+k;break;case"bottom":l=g,u=g+f}}var P=v-Math.max(w,E)-T-2,S=v+Math.max(w,E)+T+2,B=y-Math.max(w,E)-T-2,D=y+Math.max(w,E)+T+2;o+=P,s+=S,l+=B,u+=D;var _=n||"main",A=r.labelBounds,M=A[_]=A[_]||{};M.x1=o,M.y1=l,M.x2=s,M.y2=u,M.w=s-o,M.h=u-l,M.leftPad=P,M.rightPad=S,M.topPad=B,M.botPad=D;var R=b&&"autorotate"===x.strValue,I=null!=x.pfValue&&0!==x.pfValue;if(R||I){var N=R?rJ(r.rstyle,"labelAngle",n):x.pfValue,L=Math.cos(N),O=Math.sin(N),z=(o+s)/2,V=(l+u)/2;if(!b){switch(c.value){case"left":z=s;break;case"right":z=o}switch(d.value){case"top":V=u;break;case"bottom":V=l}}var F=function(e,t){return{x:(e-=z)*L-(t-=V)*O+z,y:e*O+t*L+V}},X=F(o,l),j=F(o,u),Y=F(s,l),q=F(s,u);o=Math.min(X.x,j.x,Y.x,q.x),s=Math.max(X.x,j.x,Y.x,q.x),l=Math.min(X.y,j.y,Y.y,q.y),u=Math.max(X.y,j.y,Y.y,q.y)}var W=_+"Rot",U=A[W]=A[W]||{};U.x1=o,U.y1=l,U.x2=s,U.y2=u,U.w=s-o,U.h=u-l,r$(e,o,l,s,u),r$(r.labelBounds.all,o,l,s,u)}return e}},r2=function(e,t){if(!t.cy().headless()){var n=t.pstyle("outline-opacity").value,r=t.pstyle("outline-width").value+t.pstyle("outline-offset").value;r5(e,t,n,r,"outside",r/2)}},r5=function(e,t,n,r,i,a){if(0!==n&&!(r<=0)&&"inside"!==i){var o=t.cy(),s=t.pstyle("shape").value,l=o.renderer().nodeShapes[s],u=t.position(),c=u.x,d=u.y,h=t.width(),f=t.height();l.hasMiterBounds?("center"===i&&(r/=2),rQ(e,l.miterBounds(c,d,h,f,r))):null!=a&&a>0&&tC(e,[a,a,a,a])}},r3=function(e,t){if(!t.cy().headless()){var n=t.pstyle("border-opacity").value,r=t.pstyle("border-width").pfValue,i=t.pstyle("border-position").value;r5(e,t,n,r,i)}},r4=function(e,t){var n=e._private.cy,r=n.styleEnabled(),i=n.headless(),a=tb(),o=e._private,s=e.isNode(),l=e.isEdge(),u=o.rstyle,c=s&&r?e.pstyle("bounds-expansion").pfValue:[0],d=function(e){return"none"!==e.pstyle("display").value},h=!r||d(e)&&(!l||d(e.source())&&d(e.target()));if(h){var f=0;r&&t.includeOverlays&&0!==e.pstyle("overlay-opacity").value&&(f=e.pstyle("overlay-padding").value);var p=0;r&&t.includeUnderlays&&0!==e.pstyle("underlay-opacity").value&&(p=e.pstyle("underlay-padding").value);var g=Math.max(f,p),v=0;if(r&&(v=e.pstyle("width").pfValue/2),s&&t.includeNodes){var y=e.position();k=y.x,P=y.y;var b=e.outerWidth()/2,x=e.outerHeight()/2;w=k-b,E=k+b,r$(a,w,T=P-x,E,C=P+x),r&&r2(a,e),r&&t.includeOutlines&&!i&&r2(a,e),r&&r3(a,e)}else if(l&&t.includeEdges)if(r&&!i){var w,E,T,C,k,P,S,B=e.pstyle("curve-style").strValue;if(w=Math.min(u.srcX,u.midX,u.tgtX),E=Math.max(u.srcX,u.midX,u.tgtX),T=Math.min(u.srcY,u.midY,u.tgtY),C=Math.max(u.srcY,u.midY,u.tgtY),w-=v,E+=v,r$(a,w,T-=v,E,C+=v),"haystack"===B){var D=u.haystackPts;if(D&&2===D.length){if(w=D[0].x,T=D[0].y,E=D[1].x,C=D[1].y,w>E){var _=w;w=E,E=_}if(T>C){var A=T;T=C,C=A}r$(a,w-v,T-v,E+v,C+v)}}else if("bezier"===B||"unbundled-bezier"===B||U(B,"segments")||U(B,"taxi")){switch(B){case"bezier":case"unbundled-bezier":S=u.bezierPts;break;case"segments":case"taxi":case"round-segments":case"round-taxi":S=u.linePts}if(null!=S)for(var M=0;M<S.length;M++){var R=S[M];w=R.x-v,E=R.x+v,r$(a,w,T=R.y-v,E,C=R.y+v)}}}else{var I=e.source().position(),N=e.target().position();if(w=I.x,E=N.x,T=I.y,C=N.y,w>E){var L=w;w=E,E=L}if(T>C){var O=T;T=C,C=O}w-=v,E+=v,r$(a,w,T-=v,E,C+=v)}if(r&&t.includeEdges&&l&&(r0(a,e,"mid-source"),r0(a,e,"mid-target"),r0(a,e,"source"),r0(a,e,"target")),r&&"yes"===e.pstyle("ghost").value){var z=e.pstyle("ghost-offset-x").pfValue,V=e.pstyle("ghost-offset-y").pfValue;r$(a,a.x1+z,a.y1+V,a.x2+z,a.y2+V)}var F=o.bodyBounds=o.bodyBounds||{};tk(F,a),tC(F,c),tT(F,1),r&&(w=a.x1,E=a.x2,T=a.y1,C=a.y2,r$(a,w-g,T-g,E+g,C+g));var X=o.overlayBounds=o.overlayBounds||{};tk(X,a),tC(X,c),tT(X,1);var j=o.labelBounds=o.labelBounds||{};null!=j.all?tx(j.all):j.all=tb(),r&&t.includeLabels&&(t.includeMainLabels&&r1(a,e,null),l&&(t.includeSourceLabels&&r1(a,e,"source"),t.includeTargetLabels&&r1(a,e,"target")))}return a.x1=rZ(a.x1),a.y1=rZ(a.y1),a.x2=rZ(a.x2),a.y2=rZ(a.y2),a.w=rZ(a.x2-a.x1),a.h=rZ(a.y2-a.y1),a.w>0&&a.h>0&&h&&(tC(a,c),tT(a,1)),a},r9=function(e){var t=0,n=function(e){return!!e<<t++};return 0+n(e.incudeNodes)+n(e.includeEdges)+n(e.includeLabels)+n(e.includeMainLabels)+n(e.includeSourceLabels)+n(e.includeTargetLabels)+n(e.includeOverlays)+n(e.includeOutlines)},r6=function(e){var t=function(e){return Math.round(e)};if(e.isEdge()){var n=e.source().position(),r=e.target().position();return eC([t(n.x),t(n.y),t(r.x),t(r.y)])}var i=e.position();return eC([t(i.x),t(i.y)])},r8=function(e,t){var n,r=e._private,i=e.isEdge(),a=null==t?ie:r9(t);if(null==r.bbCache?(r.bbCache=n=r4(e,r7),r.bbCachePosKey=r6(e)):n=r.bbCache,a!==ie){var o=e.isNode();n=tb(),(t.includeNodes&&o||t.includeEdges&&!o)&&(t.includeOverlays?rQ(n,r.overlayBounds):rQ(n,r.bodyBounds)),t.includeLabels&&(t.includeMainLabels&&(!i||t.includeSourceLabels&&t.includeTargetLabels)?rQ(n,r.labelBounds.all):(t.includeMainLabels&&rQ(n,r.labelBounds.mainRot),t.includeSourceLabels&&rQ(n,r.labelBounds.sourceRot),t.includeTargetLabels&&rQ(n,r.labelBounds.targetRot))),n.w=n.x2-n.x1,n.h=n.y2-n.y1}return n},r7={includeNodes:!0,includeEdges:!0,includeLabels:!0,includeMainLabels:!0,includeSourceLabels:!0,includeTargetLabels:!0,includeOverlays:!0,includeUnderlays:!0,includeOutlines:!0,useCache:!0},ie=r9(r7),it=eY(r7);ll.boundingBox=function(e){var t,n=void 0===e||void 0===e.useCache||!0===e.useCache,r=X(function(e){var t=e._private;return null==t.bbCache||t.styleDirty||t.bbCachePosKey!==r6(e)},function(e){return e.id()});if(n&&1===this.length&&!r(this[0]))e=void 0===e?r7:it(e),t=r8(this[0],e);else{t=tb();var i=it(e=e||r7),a=this.cy().styleEnabled();this.edges().forEach(r),this.nodes().forEach(r),a&&this.recalculateRenderedStyle(n),this.updateCompoundBounds(!n);for(var o=0;o<this.length;o++){var s=this[o];r(s)&&s.dirtyBoundingBoxCache(),rQ(t,r8(s,i))}}return t.x1=rZ(t.x1),t.y1=rZ(t.y1),t.x2=rZ(t.x2),t.y2=rZ(t.y2),t.w=rZ(t.x2-t.x1),t.h=rZ(t.y2-t.y1),t},ll.dirtyBoundingBoxCache=function(){for(var e=0;e<this.length;e++){var t=this[e]._private;t.bbCache=null,t.bbCachePosKey=null,t.bodyBounds=null,t.overlayBounds=null,t.labelBounds.all=null,t.labelBounds.source=null,t.labelBounds.target=null,t.labelBounds.main=null,t.labelBounds.sourceRot=null,t.labelBounds.targetRot=null,t.labelBounds.mainRot=null,t.arrowBounds.source=null,t.arrowBounds.target=null,t.arrowBounds["mid-source"]=null,t.arrowBounds["mid-target"]=null}return this.emitAndNotify("bounds"),this},ll.boundingBoxAt=function(e){var t,n=this.nodes(),r=this.cy(),i=r.hasCompoundNodes(),a=r.collection();if(i&&(a=n.filter(function(e){return e.isParent()}),n=n.not(a)),A(e)){var o=e;e=function(){return o}}r.startBatch(),n.forEach(function(t,n){return t._private.bbAtOldPos=e(t,n)}).silentPositions(e),i&&(a.dirtyCompoundBoundsCache(),a.dirtyBoundingBoxCache(),a.updateCompoundBounds(!0));var s={x1:(t=this.boundingBox({useCache:!1})).x1,x2:t.x2,w:t.w,y1:t.y1,y2:t.y2,h:t.h};return n.silentPositions(function(e){return e._private.bbAtOldPos}),i&&(a.dirtyCompoundBoundsCache(),a.dirtyBoundingBoxCache(),a.updateCompoundBounds(!0)),r.endBatch(),s},ls.boundingbox=ls.bb=ls.boundingBox,ls.renderedBoundingbox=ls.renderedBoundingBox,lu=lc={};var ir=function(e){e.uppercaseName=W(e.name),e.autoName="auto"+e.uppercaseName,e.labelName="label"+e.uppercaseName,e.outerName="outer"+e.uppercaseName,e.uppercaseOuterName=W(e.outerName),lu[e.name]=function(){var t=this[0],n=t._private,r=n.cy._private.styleEnabled;if(t)if(!r)return 1;else{if(t.isParent())return t.updateCompoundBounds(),n[e.autoName]||0;var i=t.pstyle(e.name);return"label"===i.strValue?(t.recalculateRenderedStyle(),n.rstyle[e.labelName]||0):i.pfValue}},lu["outer"+e.uppercaseName]=function(){var t=this[0],n=t._private.cy._private.styleEnabled;if(t)if(!n)return 1;else{var r=t[e.name](),i=t.pstyle("border-position").value;return r+("center"===i?t.pstyle("border-width").pfValue:"outside"===i?2*t.pstyle("border-width").pfValue:0)+2*t.padding()}},lu["rendered"+e.uppercaseName]=function(){var t=this[0];if(t)return t[e.name]()*this.cy().zoom()},lu["rendered"+e.uppercaseOuterName]=function(){var t=this[0];if(t)return t[e.outerName]()*this.cy().zoom()}};ir({name:"width"}),ir({name:"height"}),lc.padding=function(){var e=this[0],t=e._private;return e.isParent()&&(e.updateCompoundBounds(),void 0!==t.autoPadding)?t.autoPadding:e.pstyle("padding").pfValue},lc.paddedHeight=function(){var e=this[0];return e.height()+2*e.padding()},lc.paddedWidth=function(){var e=this[0];return e.width()+2*e.padding()};var ii=function(e,t){if(e.isEdge()&&e.takesUpSpace())return t(e)},ia=function(e,t){if(e.isEdge()&&e.takesUpSpace()){var n=e.cy();return tn(t(e),n.zoom(),n.pan())}},io=function(e,t){if(e.isEdge()&&e.takesUpSpace()){var n=e.cy(),r=n.pan(),i=n.zoom();return t(e).map(function(e){return tn(e,i,r)})}},is={controlPoints:{get:function(e){return e.renderer().getControlPoints(e)},mult:!0},segmentPoints:{get:function(e){return e.renderer().getSegmentPoints(e)},mult:!0},sourceEndpoint:{get:function(e){return e.renderer().getSourceEndpoint(e)}},targetEndpoint:{get:function(e){return e.renderer().getTargetEndpoint(e)}},midpoint:{get:function(e){return e.renderer().getEdgeMidpoint(e)}}},il=J({},lo,ll,lc,Object.keys(is).reduce(function(e,t){var n=is[t],r="rendered"+t[0].toUpperCase()+t.substr(1);return e[t]=function(){return ii(this,n.get)},n.mult?e[r]=function(){return io(this,n.get)}:e[r]=function(){return ia(this,n.get)},e},{})),iu=function(e,t){this.recycle(e,t)};function ic(){return!1}function id(){return!0}iu.prototype={instanceString:function(){return"event"},recycle:function(e,t){if(this.isImmediatePropagationStopped=this.isPropagationStopped=this.isDefaultPrevented=ic,null!=e&&e.preventDefault?(this.type=e.type,this.isDefaultPrevented=e.defaultPrevented?id:ic):null!=e&&e.type?t=e:this.type=e,null!=t&&(this.originalEvent=t.originalEvent,this.type=null!=t.type?t.type:this.type,this.cy=t.cy,this.target=t.target,this.position=t.position,this.renderedPosition=t.renderedPosition,this.namespace=t.namespace,this.layout=t.layout),null!=this.cy&&null!=this.position&&null==this.renderedPosition){var n=this.position,r=this.cy.zoom(),i=this.cy.pan();this.renderedPosition={x:n.x*r+i.x,y:n.y*r+i.y}}this.timeStamp=e&&e.timeStamp||Date.now()},preventDefault:function(){this.isDefaultPrevented=id;var e=this.originalEvent;e&&e.preventDefault&&e.preventDefault()},stopPropagation:function(){this.isPropagationStopped=id;var e=this.originalEvent;e&&e.stopPropagation&&e.stopPropagation()},stopImmediatePropagation:function(){this.isImmediatePropagationStopped=id,this.stopPropagation()},isDefaultPrevented:ic,isPropagationStopped:ic,isImmediatePropagationStopped:ic};var ih=/^([^.]+)(\.(?:[^.]+))?$/,ip={qualifierCompare:function(e,t){return e===t},eventMatches:function(){return!0},addEventFields:function(){},callbackContext:function(e){return e},beforeEmit:function(){},afterEmit:function(){},bubble:function(){return!1},parent:function(){return null},context:null},ig=Object.keys(ip),iv={};function iy(){for(var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:iv,t=arguments.length>1?arguments[1]:void 0,n=0;n<ig.length;n++){var r=ig[n];this[r]=e[r]||ip[r]}this.context=t||this.context,this.listeners=[],this.emitting=0}var im=iy.prototype,ib=function(e,t,n,r,i,a,o){D(r)&&(i=r,r=null),o&&(a=null==a?o:J({},a,o));for(var s=_(n)?n:n.split(/\s+/),l=0;l<s.length;l++){var u=s[l];if(!V(u)){var c=u.match(ih);if(c&&!1===t(e,u,c[1],c[2]?c[2]:null,r,i,a))break}}},ix=function(e,t){return e.addEventFields(e.context,t),new iu(t.type,t)},iw=function(e,t,n){if("event"===S(n))return void t(e,n);if(A(n))return void t(e,ix(e,n));for(var r=_(n)?n:n.split(/\s+/),i=0;i<r.length;i++){var a=r[i];if(!V(a)){var o=a.match(ih);if(o){var s=ix(e,{type:o[1],namespace:o[2]?o[2]:null,target:e.context});t(e,s)}}}};im.on=im.addListener=function(e,t,n,r,i){return ib(this,function(e,t,n,r,i,a,o){D(a)&&e.listeners.push({event:t,callback:a,type:n,namespace:r,qualifier:i,conf:o})},e,t,n,r,i),this},im.one=function(e,t,n,r){return this.on(e,t,n,r,{one:!0})},im.removeListener=im.off=function(e,t,n,r){var i=this;0!==this.emitting&&(this.listeners=this.listeners.slice());for(var a=this.listeners,o=function(o){var s=a[o];ib(i,function(t,n,r,i,l,u){if((s.type===r||"*"===e)&&(!i&&".*"!==s.namespace||s.namespace===i)&&(!l||t.qualifierCompare(s.qualifier,l))&&(!u||s.callback===u))return a.splice(o,1),!1},e,t,n,r)},s=a.length-1;s>=0;s--)o(s);return this},im.removeAllListeners=function(){return this.removeListener("*")},im.emit=im.trigger=function(e,t,n){var r=this.listeners,i=r.length;return this.emitting++,_(t)||(t=[t]),iw(this,function(e,a){null!=n&&(i=(r=[{event:a.event,type:a.type,namespace:a.namespace,callback:n}]).length);for(var o=0;o<i;o++)!function(){var n=r[o];if(n.type===a.type&&(!n.namespace||n.namespace===a.namespace||".*"===n.namespace)&&e.eventMatches(e.context,n,a)){var i=[a];null!=t&&eU(i,t),e.beforeEmit(e.context,n,a),n.conf&&n.conf.one&&(e.listeners=e.listeners.filter(function(e){return e!==n}));var s=e.callbackContext(e.context,n,a),l=n.callback.apply(s,i);e.afterEmit(e.context,n,a),!1===l&&(a.stopPropagation(),a.preventDefault())}}();e.bubble(e.context)&&!a.isPropagationStopped()&&e.parent(e.context).emit(a,t)},e),this.emitting--,this};var iE={qualifierCompare:function(e,t){return null==e||null==t?null==e&&null==t:e.sameText(t)},eventMatches:function(e,t,n){var r=t.qualifier;return null==r||e!==n.target&&N(n.target)&&r.matches(n.target)},addEventFields:function(e,t){t.cy=e.cy(),t.target=e},callbackContext:function(e,t,n){return null!=t.qualifier?n.target:e},beforeEmit:function(e,t){t.conf&&t.conf.once&&t.conf.onceCollection.removeListener(t.event,t.qualifier,t.callback)},bubble:function(){return!0},parent:function(e){return e.isChild()?e.parent():e.cy()}},iT=function(e){return B(e)?new rL(e):e},iC={createEmitter:function(){for(var e=0;e<this.length;e++){var t=this[e],n=t._private;n.emitter||(n.emitter=new iy(iE,t))}return this},emitter:function(){return this._private.emitter},on:function(e,t,n){for(var r=iT(t),i=0;i<this.length;i++)this[i].emitter().on(e,r,n);return this},removeListener:function(e,t,n){for(var r=iT(t),i=0;i<this.length;i++)this[i].emitter().removeListener(e,r,n);return this},removeAllListeners:function(){for(var e=0;e<this.length;e++)this[e].emitter().removeAllListeners();return this},one:function(e,t,n){for(var r=iT(t),i=0;i<this.length;i++)this[i].emitter().one(e,r,n);return this},once:function(e,t,n){for(var r=iT(t),i=0;i<this.length;i++)this[i].emitter().on(e,r,n,{once:!0,onceCollection:this})},emit:function(e,t){for(var n=0;n<this.length;n++)this[n].emitter().emit(e,t);return this},emitAndNotify:function(e,t){if(0!==this.length)return this.cy().notify(e,this),this.emit(e,t),this}};ry.eventAliasesOn(iC);var ik={nodes:function(e){return this.filter(function(e){return e.isNode()}).filter(e)},edges:function(e){return this.filter(function(e){return e.isEdge()}).filter(e)},byGroup:function(){for(var e=this.spawn(),t=this.spawn(),n=0;n<this.length;n++){var r=this[n];r.isNode()?e.push(r):t.push(r)}return{nodes:e,edges:t}},filter:function(e,t){if(void 0===e)return this;if(B(e)||I(e))return new rL(e).filter(this);if(D(e)){for(var n=this.spawn(),r=0;r<this.length;r++){var i=this[r];(t?e.apply(t,[i,r,this]):e(i,r,this))&&n.push(i)}return n}return this.spawn()},not:function(e){if(!e)return this;B(e)&&(e=this.filter(e));for(var t=this.spawn(),n=0;n<this.length;n++){var r=this[n];e.has(r)||t.push(r)}return t},absoluteComplement:function(){return this.cy().mutableElements().not(this)},intersect:function(e){if(B(e))return this.filter(e);for(var t=this.spawn(),n=this.length<e.length,r=n?this:e,i=n?e:this,a=0;a<r.length;a++){var o=r[a];i.has(o)&&t.push(o)}return t},xor:function(e){var t=this._private.cy;B(e)&&(e=t.$(e));var n=this.spawn(),r=e,i=function(e,t){for(var r=0;r<e.length;r++){var i=e[r],a=i._private.data.id;t.hasElementWithId(a)||n.push(i)}};return i(this,r),i(r,this),n},diff:function(e){var t=this._private.cy;B(e)&&(e=t.$(e));var n=this.spawn(),r=this.spawn(),i=this.spawn(),a=e,o=function(e,t,n){for(var r=0;r<e.length;r++){var a=e[r],o=a._private.data.id;t.hasElementWithId(o)?i.merge(a):n.push(a)}};return o(this,a,n),o(a,this,r),{left:n,right:r,both:i}},add:function(e){var t=this._private.cy;if(!e)return this;if(B(e)){var n=e;e=t.mutableElements().filter(n)}for(var r=this.spawnSelf(),i=0;i<e.length;i++){var a=e[i];this.has(a)||r.push(a)}return r},merge:function(e){var t=this._private,n=t.cy;if(!e)return this;if(e&&B(e)){var r=e;e=n.mutableElements().filter(r)}for(var i=t.map,a=0;a<e.length;a++){var o=e[a],s=o._private.data.id;if(!i.has(s)){var l=this.length++;this[l]=o,i.set(s,{ele:o,index:l})}}return this},unmergeAt:function(e){var t=this[e].id(),n=this._private.map;this[e]=void 0,n.delete(t);var r=e===this.length-1;if(this.length>1&&!r){var i=this.length-1,a=this[i],o=a._private.data.id;this[i]=void 0,this[e]=a,n.set(o,{ele:a,index:e})}return this.length--,this},unmergeOne:function(e){e=e[0];var t=this._private,n=e._private.data.id,r=t.map.get(n);if(!r)return this;var i=r.index;return this.unmergeAt(i),this},unmerge:function(e){var t=this._private.cy;if(!e)return this;if(e&&B(e)){var n=e;e=t.mutableElements().filter(n)}for(var r=0;r<e.length;r++)this.unmergeOne(e[r]);return this},unmergeBy:function(e){for(var t=this.length-1;t>=0;t--)e(this[t])&&this.unmergeAt(t);return this},map:function(e,t){for(var n=[],r=0;r<this.length;r++){var i=this[r],a=t?e.apply(t,[i,r,this]):e(i,r,this);n.push(a)}return n},reduce:function(e,t){for(var n=t,r=0;r<this.length;r++)n=e(n,this[r],r,this);return n},max:function(e,t){for(var n,r=-1/0,i=0;i<this.length;i++){var a=this[i],o=t?e.apply(t,[a,i,this]):e(a,i,this);o>r&&(r=o,n=a)}return{value:r,ele:n}},min:function(e,t){for(var n,r=1/0,i=0;i<this.length;i++){var a=this[i],o=t?e.apply(t,[a,i,this]):e(a,i,this);o<r&&(r=o,n=a)}return{value:r,ele:n}}};ik.u=ik["|"]=ik["+"]=ik.union=ik.or=ik.add,ik["\\"]=ik["!"]=ik["-"]=ik.difference=ik.relativeComplement=ik.subtract=ik.not,ik.n=ik["&"]=ik["."]=ik.and=ik.intersection=ik.intersect,ik["^"]=ik["(+)"]=ik["(-)"]=ik.symmetricDifference=ik.symdiff=ik.xor,ik.fnFilter=ik.filterFn=ik.stdFilter=ik.filter,ik.complement=ik.abscomp=ik.absoluteComplement;var iP=function(e,t){var n=e.cy().hasCompoundNodes();function r(e){var t=e.pstyle("z-compound-depth");return"auto"===t.value?n?e.zDepth():0:"bottom"===t.value?-1:"top"===t.value?eA:0}var i=r(e)-r(t);if(0!==i)return i;function a(e){return"auto"===e.pstyle("z-index-compare").value?+!!e.isNode():0}var o=a(e)-a(t);if(0!==o)return o;var s=e.pstyle("z-index").value-t.pstyle("z-index").value;return 0!==s?s:e.poolIndex()-t.poolIndex()},iS={forEach:function(e,t){if(D(e))for(var n=this.length,r=0;r<n;r++){var i=this[r];if(!1===(t?e.apply(t,[i,r,this]):e(i,r,this)))break}return this},toArray:function(){for(var e=[],t=0;t<this.length;t++)e.push(this[t]);return e},slice:function(e,t){var n=[],r=this.length;null==t&&(t=r),null==e&&(e=0),e<0&&(e=r+e),t<0&&(t=r+t);for(var i=e;i>=0&&i<t&&i<r;i++)n.push(this[i]);return this.spawn(n)},size:function(){return this.length},eq:function(e){return this[e]||this.spawn()},first:function(){return this[0]||this.spawn()},last:function(){return this[this.length-1]||this.spawn()},empty:function(){return 0===this.length},nonempty:function(){return!this.empty()},sort:function(e){if(!D(e))return this;var t=this.toArray().sort(e);return this.spawn(t)},sortByZIndex:function(){return this.sort(iP)},zDepth:function(){var e=this[0];if(e){var t=e._private;if("nodes"===t.group){var n=t.data.parent?e.parents().size():0;return e.isParent()?n:eA-1}var r=t.source,i=t.target;return Math.max(r.zDepth(),i.zDepth(),0)}}};iS.each=iS.forEach,!function(){var e="undefined";("undefined"==typeof Symbol?"undefined":d(Symbol))!=e&&d(Symbol.iterator)!=e&&(iS[Symbol.iterator]=function(){var e=this,t={value:void 0,done:!1},n=0,r=this.length;return s({next:function(){return n<r?t.value=e[n++]:(t.value=void 0,t.done=!0),t}},Symbol.iterator,function(){return this})})}();var iB=eY({nodeDimensionsIncludeLabels:!1}),iD={layoutDimensions:function(e){if(e=iB(e),this.takesUpSpace())if(e.nodeDimensionsIncludeLabels){var t,n=this.boundingBox();t={w:n.w,h:n.h}}else t={w:this.outerWidth(),h:this.outerHeight()};else t={w:0,h:0};return(0===t.w||0===t.h)&&(t.w=t.h=1),t},layoutPositions:function(e,t,n){var r=this.nodes().filter(function(e){return!e.isParent()}),i=this.cy(),a=t.eles,o=function(e){return e.id()},s=X(n,o);e.emit({type:"layoutstart",layout:e}),e.animations=[];var l=function(e,t,n){var r={x:t.x1+t.w/2,y:t.y1+t.h/2},i={x:(n.x-r.x)*e,y:(n.y-r.y)*e};return{x:r.x+i.x,y:r.y+i.y}},u=t.spacingFactor&&1!==t.spacingFactor,c=function(){if(!u)return null;for(var e=tb(),t=0;t<r.length;t++){var n=s(r[t],t);tE(e,n.x,n.y)}return e}(),d=X(function(e,n){var r=s(e,n);return u&&(r=l(Math.abs(t.spacingFactor),c,r)),null!=t.transform&&(r=t.transform(e,r)),r},o);if(t.animate){for(var h=0;h<r.length;h++){var f=r[h],p=d(f,h);if(null==t.animateFilter||t.animateFilter(f,h)){var g=f.animation({position:p,duration:t.animationDuration,easing:t.animationEasing});e.animations.push(g)}else f.position(p)}if(t.fit){var v=i.animation({fit:{boundingBox:a.boundingBoxAt(d),padding:t.padding},duration:t.animationDuration,easing:t.animationEasing});e.animations.push(v)}else if(void 0!==t.zoom&&void 0!==t.pan){var y=i.animation({zoom:t.zoom,pan:t.pan,duration:t.animationDuration,easing:t.animationEasing});e.animations.push(y)}e.animations.forEach(function(e){return e.play()}),e.one("layoutready",t.ready),e.emit({type:"layoutready",layout:e}),re.all(e.animations.map(function(e){return e.promise()})).then(function(){e.one("layoutstop",t.stop),e.emit({type:"layoutstop",layout:e})})}else r.positions(d),t.fit&&i.fit(t.eles,t.padding),null!=t.zoom&&i.zoom(t.zoom),t.pan&&i.pan(t.pan),e.one("layoutready",t.ready),e.emit({type:"layoutready",layout:e}),e.one("layoutstop",t.stop),e.emit({type:"layoutstop",layout:e});return this},layout:function(e){return this.cy().makeLayout(J({},e,{eles:this}))}};function i_(e,t,n){var r,i=n._private,a=i.styleCache=i.styleCache||[];return null!=(r=a[e])?r:r=a[e]=t(n)}function iA(e,t){return e=ek(e),function(n){return i_(e,t,n)}}function iM(e,t){e=ek(e);var n=function(e){return t.call(e)};return function(){var t=this[0];if(t)return i_(e,n,t)}}iD.createLayout=iD.makeLayout=iD.layout;var iR={recalculateRenderedStyle:function(e){var t=this.cy(),n=t.renderer(),r=t.styleEnabled();return n&&r&&n.recalculateRenderedStyle(this,e),this},dirtyStyleCache:function(){var e,t=this.cy(),n=function(e){return e._private.styleCache=null};return t.hasCompoundNodes()?((e=this.spawnSelf().merge(this.descendants()).merge(this.parents())).merge(e.connectedEdges()),e.forEach(n)):this.forEach(function(e){n(e),e.connectedEdges().forEach(n)}),this},updateStyle:function(e){var t=this._private.cy;if(!t.styleEnabled())return this;if(t.batching())return t._private.batchStyleEles.merge(this),this;var n=t.hasCompoundNodes(),r=this;e=!!e||void 0===e,n&&(r=this.spawnSelf().merge(this.descendants()).merge(this.parents()));var i=r;return e?i.emitAndNotify("style"):i.emit("style"),r.forEach(function(e){return e._private.styleDirty=!0}),this},cleanStyle:function(){var e=this.cy();if(e.styleEnabled())for(var t=0;t<this.length;t++){var n=this[t];n._private.styleDirty&&(n._private.styleDirty=!1,e.style().apply(n))}},parsedStyle:function(e){var t=!(arguments.length>1)||void 0===arguments[1]||arguments[1],n=this[0],r=n.cy();if(r.styleEnabled()&&n){n._private.styleDirty&&(n._private.styleDirty=!1,r.style().apply(n));var i=n._private.style[e];return null!=i?i:t?r.style().getDefaultProperty(e):null}},numericStyle:function(e){var t=this[0];if(t.cy().styleEnabled()&&t){var n=t.pstyle(e);return void 0!==n.pfValue?n.pfValue:n.value}},numericStyleUnits:function(e){var t=this[0];if(t.cy().styleEnabled()&&t)return t.pstyle(e).units},renderedStyle:function(e){var t=this.cy();if(!t.styleEnabled())return this;var n=this[0];if(n)return t.style().getRenderedStyle(n,e)},style:function(e,t){var n=this.cy();if(!n.styleEnabled())return this;var r=n.style();if(A(e))r.applyBypass(this,e,!1),this.emitAndNotify("style");else if(B(e))if(void 0===t){var i=this[0];return i?r.getStylePropertyValue(i,e):void 0}else r.applyBypass(this,e,t,!1),this.emitAndNotify("style");else if(void 0===e){var a=this[0];return a?r.getRawStyle(a):void 0}return this},removeStyle:function(e){var t=this.cy();if(!t.styleEnabled())return this;var n=t.style();if(void 0===e)for(var r=0;r<this.length;r++){var i=this[r];n.removeAllBypasses(i,!1)}else{e=e.split(/\s+/);for(var a=0;a<this.length;a++){var o=this[a];n.removeBypasses(o,e,!1)}}return this.emitAndNotify("style"),this},show:function(){return this.css("display","element"),this},hide:function(){return this.css("display","none"),this},effectiveOpacity:function(){var e=this.cy();if(!e.styleEnabled())return 1;var t=e.hasCompoundNodes(),n=this[0];if(n){var r=n._private,i=n.pstyle("opacity").value;if(!t)return i;var a=r.data.parent?n.parents():null;if(a)for(var o=0;o<a.length;o++)i*=a[o].pstyle("opacity").value;return i}},transparent:function(){if(!this.cy().styleEnabled())return!1;var e=this[0],t=e.cy().hasCompoundNodes();if(e)if(!t)return 0===e.pstyle("opacity").value;else return 0===e.effectiveOpacity()},backgrounding:function(){return!!this.cy().styleEnabled()&&!!this[0]._private.backgrounding}};function iI(e,t){var n=e._private.data.parent?e.parents():null;if(n){for(var r=0;r<n.length;r++)if(!t(n[r]))return!1}return!0}function iN(e){var t=e.ok,n=e.edgeOkViaNode||e.ok,r=e.parentOk||e.ok;return function(){var e=this.cy();if(!e.styleEnabled())return!0;var i=this[0],a=e.hasCompoundNodes();if(i){var o=i._private;if(!t(i))return!1;if(i.isNode())return!a||iI(i,r);var s=o.source,l=o.target;return n(s)&&(!a||iI(s,n))&&(s===l||n(l)&&(!a||iI(l,n)))}}}var iL=iA("eleTakesUpSpace",function(e){return"element"===e.pstyle("display").value&&0!==e.width()&&(!e.isNode()||0!==e.height())});iR.takesUpSpace=iM("takesUpSpace",iN({ok:iL})),iR.interactive=iM("interactive",iN({ok:iA("eleInteractive",function(e){return"yes"===e.pstyle("events").value&&"visible"===e.pstyle("visibility").value&&iL(e)}),parentOk:iA("parentInteractive",function(e){return"visible"===e.pstyle("visibility").value&&iL(e)}),edgeOkViaNode:iL})),iR.noninteractive=function(){var e=this[0];if(e)return!e.interactive()},iR.visible=iM("visible",iN({ok:iA("eleVisible",function(e){return"visible"===e.pstyle("visibility").value&&0!==e.pstyle("opacity").pfValue&&iL(e)}),edgeOkViaNode:iL})),iR.hidden=function(){var e=this[0];if(e)return!e.visible()},iR.isBundledBezier=iM("isBundledBezier",function(){return!!this.cy().styleEnabled()&&!this.removed()&&"bezier"===this.pstyle("curve-style").value&&this.takesUpSpace()}),iR.bypass=iR.css=iR.style,iR.renderedCss=iR.renderedStyle,iR.removeBypass=iR.removeCss=iR.removeStyle,iR.pstyle=iR.parsedStyle;var iO={};function iz(e){return function(){var t=arguments,n=[];if(2===t.length){var r=t[0],i=t[1];this.on(e.event,r,i)}else if(1===t.length&&D(t[0])){var a=t[0];this.on(e.event,a)}else if(0===t.length||1===t.length&&_(t[0])){for(var o=1===t.length?t[0]:null,s=0;s<this.length;s++){var l=this[s],u=!e.ableField||l._private[e.ableField],c=l._private[e.field]!=e.value;if(e.overrideAble){var d=e.overrideAble(l);if(void 0!==d&&(u=d,!d))return this}u&&(l._private[e.field]=e.value,c&&n.push(l))}var h=this.spawn(n);h.updateStyle(),h.emit(e.event),o&&h.emit(o)}return this}}function iV(e){iO[e.field]=function(){var t=this[0];if(t){if(e.overrideField){var n=e.overrideField(t);if(void 0!==n)return n}return t._private[e.field]}},iO[e.on]=iz({event:e.on,field:e.field,ableField:e.ableField,overrideAble:e.overrideAble,value:!0}),iO[e.off]=iz({event:e.off,field:e.field,ableField:e.ableField,overrideAble:e.overrideAble,value:!1})}iV({field:"locked",overrideField:function(e){return!!e.cy().autolock()||void 0},on:"lock",off:"unlock"}),iV({field:"grabbable",overrideField:function(e){return!(e.cy().autoungrabify()||e.pannable())&&void 0},on:"grabify",off:"ungrabify"}),iV({field:"selected",ableField:"selectable",overrideAble:function(e){return!e.cy().autounselectify()&&void 0},on:"select",off:"unselect"}),iV({field:"selectable",overrideField:function(e){return!e.cy().autounselectify()&&void 0},on:"selectify",off:"unselectify"}),iO.deselect=iO.unselect,iO.grabbed=function(){var e=this[0];if(e)return e._private.grabbed},iV({field:"active",on:"activate",off:"unactivate"}),iV({field:"pannable",on:"panify",off:"unpanify"}),iO.inactive=function(){var e=this[0];if(e)return!e._private.active};var iF={},iX=function(e){return function(t){for(var n=[],r=0;r<this.length;r++){var i=this[r];if(i.isNode()){for(var a=!1,o=i.connectedEdges(),s=0;s<o.length;s++){var l=o[s],u=l.source(),c=l.target();if(e.noIncomingEdges&&c===i&&u!==i||e.noOutgoingEdges&&u===i&&c!==i){a=!0;break}}a||n.push(i)}}return this.spawn(n,!0).filter(t)}},ij=function(e){return function(t){for(var n=[],r=0;r<this.length;r++){var i=this[r];if(i.isNode())for(var a=i.connectedEdges(),o=0;o<a.length;o++){var s=a[o],l=s.source(),u=s.target();e.outgoing&&l===i?(n.push(s),n.push(u)):e.incoming&&u===i&&(n.push(s),n.push(l))}}return this.spawn(n,!0).filter(t)}},iY=function(e){return function(t){for(var n=this,r=[],i={};;){var a=e.outgoing?n.outgoers():n.incomers();if(0===a.length)break;for(var o=!1,s=0;s<a.length;s++){var l=a[s],u=l.id();i[u]||(i[u]=!0,r.push(l),o=!0)}if(!o)break;n=a}return this.spawn(r,!0).filter(t)}};function iq(e){return function(t){for(var n=[],r=0;r<this.length;r++){var i=this[r]._private[e.attr];i&&n.push(i)}return this.spawn(n,!0).filter(t)}}function iW(e){return function(t){var n=[],r=this._private.cy,i=e||{};B(t)&&(t=r.$(t));for(var a=0;a<t.length;a++)for(var o=t[a]._private.edges,s=0;s<o.length;s++){var l=o[s],u=l._private.data,c=this.hasElementWithId(u.source)&&t.hasElementWithId(u.target),d=t.hasElementWithId(u.source)&&this.hasElementWithId(u.target);if(c||d){if((i.thisIsSrc||i.thisIsTgt)&&(i.thisIsSrc&&!c||i.thisIsTgt&&!d))continue;n.push(l)}}return this.spawn(n,!0)}}function iU(e){return e=J({},{codirected:!1},e),function(t){for(var n=[],r=this.edges(),i=e,a=0;a<r.length;a++)for(var o=r[a]._private,s=o.source,l=s._private.data.id,u=o.data.target,c=s._private.edges,d=0;d<c.length;d++){var h=c[d],f=h._private.data,p=f.target,g=f.source,v=p===u&&g===l,y=l===p&&u===g;(i.codirected&&v||!i.codirected&&(v||y))&&n.push(h)}return this.spawn(n,!0).filter(t)}}iF.clearTraversalCache=function(){for(var e=0;e<this.length;e++)this[e]._private.traversalCache=null},J(iF,{roots:iX({noIncomingEdges:!0}),leaves:iX({noOutgoingEdges:!0}),outgoers:rV(ij({outgoing:!0}),"outgoers"),successors:iY({outgoing:!0}),incomers:rV(ij({incoming:!0}),"incomers"),predecessors:iY({})}),J(iF,{neighborhood:rV(function(e){for(var t=[],n=this.nodes(),r=0;r<n.length;r++)for(var i=n[r],a=i.connectedEdges(),o=0;o<a.length;o++){var s=a[o],l=s.source(),u=s.target(),c=i===l?u:l;c.length>0&&t.push(c[0]),t.push(s[0])}return this.spawn(t,!0).filter(e)},"neighborhood"),closedNeighborhood:function(e){return this.neighborhood().add(this).filter(e)},openNeighborhood:function(e){return this.neighborhood(e)}}),iF.neighbourhood=iF.neighborhood,iF.closedNeighbourhood=iF.closedNeighborhood,iF.openNeighbourhood=iF.openNeighborhood,J(iF,{source:rV(function(e){var t,n=this[0];return n&&(t=n._private.source||n.cy().collection()),t&&e?t.filter(e):t},"source"),target:rV(function(e){var t,n=this[0];return n&&(t=n._private.target||n.cy().collection()),t&&e?t.filter(e):t},"target"),sources:iq({attr:"source"}),targets:iq({attr:"target"})}),J(iF,{edgesWith:rV(iW(),"edgesWith"),edgesTo:rV(iW({thisIsSrc:!0}),"edgesTo")}),J(iF,{connectedEdges:rV(function(e){for(var t=[],n=0;n<this.length;n++){var r=this[n];if(r.isNode())for(var i=r._private.edges,a=0;a<i.length;a++){var o=i[a];t.push(o)}}return this.spawn(t,!0).filter(e)},"connectedEdges"),connectedNodes:rV(function(e){for(var t=[],n=0;n<this.length;n++){var r=this[n];r.isEdge()&&(t.push(r.source()[0]),t.push(r.target()[0]))}return this.spawn(t,!0).filter(e)},"connectedNodes"),parallelEdges:rV(iU(),"parallelEdges"),codirectedEdges:rV(iU({codirected:!0}),"codirectedEdges")}),J(iF,{components:function(e){var t=this,n=t.cy(),r=n.collection(),i=null==e?t.nodes():e.nodes(),a=[];null!=e&&i.empty()&&(i=e.sources());var o=function(e,t){r.merge(e),i.unmerge(e),t.merge(e)};if(i.empty())return t.spawn();do!function(){var e=n.collection();a.push(e);var r=i[0];o(r,e),t.bfs({directed:!1,roots:r,visit:function(t){return o(t,e)}}),e.forEach(function(n){n.connectedEdges().forEach(function(n){t.has(n)&&e.has(n.source())&&e.has(n.target())&&e.merge(n)})})}();while(i.length>0);return a},component:function(){var e=this[0];return e.cy().mutableElements().components(e)[0]}}),iF.componentsOf=iF.components;var iG=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(void 0===e)return void eL("A collection must have a reference to the core");var i=new eZ,a=!1;if(t){if(t.length>0&&A(t[0])&&!N(t[0])){a=!0;for(var o=[],s=new eQ,l=0,u=t.length;l<u;l++){var c=t[l];null==c.data&&(c.data={});var d=c.data;if(null==d.id)d.id=eF();else if(e.hasElementWithId(d.id)||s.has(d.id))continue;var h=new eJ(e,c,!1);o.push(h),s.add(d.id)}t=o}}else t=[];this.length=0;for(var f=0,p=t.length;f<p;f++){var g=t[f][0];if(null!=g){var v=g._private.data.id;(!n||!i.has(v))&&(n&&i.set(v,{index:this.length,ele:g}),this[this.length]=g,this.length++)}}this._private={eles:this,cy:e,get map(){return null==this.lazyMap&&this.rebuildMap(),this.lazyMap},set map(m){this.lazyMap=m},rebuildMap:function(){for(var e=this.lazyMap=new eZ,t=this.eles,n=0;n<t.length;n++){var r=t[n];e.set(r.id(),{index:n,ele:r})}}},n&&(this._private.map=i),a&&!r&&this.restore()},iH=eJ.prototype=iG.prototype=Object.create(Array.prototype);iH.instanceString=function(){return"collection"},iH.spawn=function(e,t){return new iG(this.cy(),e,t)},iH.spawnSelf=function(){return this.spawn(this)},iH.cy=function(){return this._private.cy},iH.renderer=function(){return this._private.cy.renderer()},iH.element=function(){return this[0]},iH.collection=function(){return L(this)?this:new iG(this._private.cy,[this])},iH.unique=function(){return new iG(this._private.cy,this,!0)},iH.hasElementWithId=function(e){return e=""+e,this._private.map.has(e)},iH.getElementById=function(e){e=""+e;var t=this._private.cy,n=this._private.map.get(e);return n?n.ele:new iG(t)},iH.$id=iH.getElementById,iH.poolIndex=function(){var e=this._private.cy._private.elements,t=this[0]._private.data.id;return e._private.map.get(t).index},iH.indexOf=function(e){var t=e[0]._private.data.id;return this._private.map.get(t).index},iH.indexOfId=function(e){return e=""+e,this._private.map.get(e).index},iH.json=function(e){var t=this.element(),n=this.cy();if(null==t&&e)return this;if(null!=t){var r=t._private;if(A(e)){if(n.startBatch(),e.data){t.data(e.data);var i=r.data;if(t.isEdge()){var a=!1,o={},s=e.data.source,l=e.data.target;null!=s&&s!=i.source&&(o.source=""+s,a=!0),null!=l&&l!=i.target&&(o.target=""+l,a=!0),a&&(t=t.move(o))}else{var u="parent"in e.data,c=e.data.parent;u&&(null!=c||null!=i.parent)&&c!=i.parent&&(void 0===c&&(c=null),null!=c&&(c=""+c),t=t.move({parent:c}))}}e.position&&t.position(e.position);var d=function(n,i,a){var o=e[n];null!=o&&o!==r[n]&&(o?t[i]():t[a]())};return d("removed","remove","restore"),d("selected","select","unselect"),d("selectable","selectify","unselectify"),d("locked","lock","unlock"),d("grabbable","grabify","ungrabify"),d("pannable","panify","unpanify"),null!=e.classes&&t.classes(e.classes),n.endBatch(),this}if(void 0===e){var h={data:eV(r.data),position:eV(r.position),group:r.group,removed:r.removed,selected:r.selected,selectable:r.selectable,locked:r.locked,grabbable:r.grabbable,pannable:r.pannable,classes:null};h.classes="";var f=0;return r.classes.forEach(function(e){return h.classes+=0==f++?e:" "+e}),h}}},iH.jsons=function(){for(var e=[],t=0;t<this.length;t++){var n=this[t].json();e.push(n)}return e},iH.clone=function(){for(var e=this.cy(),t=[],n=0;n<this.length;n++){var r=new eJ(e,this[n].json(),!1);t.push(r)}return new iG(e,t)},iH.copy=iH.clone,iH.restore=function(){for(var e=!(arguments.length>0)||void 0===arguments[0]||arguments[0],t=!(arguments.length>1)||void 0===arguments[1]||arguments[1],n=this.cy(),r=n._private,i=[],a=[],o=0,s=this.length;o<s;o++){var l=this[o];(!t||l.removed())&&(l.isNode()?i.push(l):a.push(l))}R=i.concat(a);var u=function(){R.splice(I,1),I--};for(I=0;I<R.length;I++){var c=R[I],d=c._private,h=d.data;if(c.clearTraversalCache(),t||d.removed){if(void 0===h.id)h.id=eF();else if(M(h.id))h.id=""+h.id;else if(V(h.id)||!B(h.id)){eL("Can not create element with invalid string ID `"+h.id+"`"),u();continue}else if(n.hasElementWithId(h.id)){eL("Can not create second element with ID `"+h.id+"`"),u();continue}}var f=h.id;if(c.isNode()){var p=d.position;null==p.x&&(p.x=0),null==p.y&&(p.y=0)}if(c.isEdge()){for(var g=["source","target"],v=g.length,y=!1,b=0;b<v;b++){var x=g[b],w=h[x];M(w)&&(w=h[x]=""+h[x]),null==w||""===w?(eL("Can not create edge `"+f+"` with unspecified "+x),y=!0):n.hasElementWithId(w)||(eL("Can not create edge `"+f+"` with nonexistant "+x+" `"+w+"`"),y=!0)}if(y){u();continue}var E=n.getElementById(h.source),T=n.getElementById(h.target);E.same(T)?E._private.edges.push(c):(E._private.edges.push(c),T._private.edges.push(c)),c._private.source=E,c._private.target=T}d.map=new eZ,d.map.set(f,{ele:c,index:0}),d.removed=!1,t&&n.addToPool(c)}for(var C=0;C<i.length;C++){var k=i[C],P=k._private.data;M(P.parent)&&(P.parent=""+P.parent);var S=P.parent;if(null!=S||k._private.parent){var D=k._private.parent?n.collection().merge(k._private.parent):n.getElementById(S);if(D.empty())P.parent=void 0;else if(D[0].removed())ez("Node added with missing parent, reference to parent removed"),P.parent=void 0,k._private.parent=null;else{for(var _=!1,A=D;!A.empty();){if(k.same(A)){_=!0,P.parent=void 0;break}A=A.parent()}_||(D[0]._private.children.push(k),k._private.parent=D[0],r.hasCompoundNodes=!0)}}}if(R.length>0){for(var R,I,N,L=R.length===this.length?this:new iG(n,R),O=0;O<L.length;O++){var z=L[O];z.isNode()||(z.parallelEdges().clearTraversalCache(),z.source().clearTraversalCache(),z.target().clearTraversalCache())}(r.hasCompoundNodes?n.collection().merge(L).merge(L.connectedNodes()).merge(L.parent()):L).dirtyCompoundBoundsCache().dirtyBoundingBoxCache().updateStyle(e),e?L.emitAndNotify("add"):t&&L.emit("add")}return this},iH.removed=function(){var e=this[0];return e&&e._private.removed},iH.inside=function(){var e=this[0];return e&&!e._private.removed},iH.remove=function(){for(var e=!(arguments.length>0)||void 0===arguments[0]||arguments[0],t=!(arguments.length>1)||void 0===arguments[1]||arguments[1],n=[],r={},i=this._private.cy,a=0,o=this.length;a<o;a++)!function e(i){var a=r[i.id()];if(!(t&&i.removed())&&!a)if(r[i.id()]=!0,i.isNode()){n.push(i);for(var o=i._private.edges,s=0;s<o.length;s++)e(o[s]);for(var l=i._private.children,u=0;u<l.length;u++)e(l[u])}else n.unshift(i)}(this[a]);function s(e,t){eq(e._private.edges,t),e.clearTraversalCache()}var l=[];l.ids={},this.dirtyCompoundBoundsCache(),t&&i.removeFromPool(n);for(var u=0;u<n.length;u++){var c=n[u];if(c.isEdge()){var d=c.source()[0],h=c.target()[0];s(d,c),s(h,c);for(var f=c.parallelEdges(),p=0;p<f.length;p++){var g=f[p];g.clearTraversalCache(),g.isBundledBezier()&&g.dirtyBoundingBoxCache()}}else{var v=c.parent();0!==v.length&&function(e,t){t=t[0];var n=(e=e[0])._private.children,r=e.id();eq(n,t),t._private.parent=null,l.ids[r]||(l.ids[r]=!0,l.push(e))}(v,c)}t&&(c._private.removed=!0)}var y=i._private.elements;i._private.hasCompoundNodes=!1;for(var b=0;b<y.length;b++)if(y[b].isParent()){i._private.hasCompoundNodes=!0;break}var x=new iG(this.cy(),n);x.size()>0&&(e?x.emitAndNotify("remove"):t&&x.emit("remove"));for(var w=0;w<l.length;w++){var E=l[w];t&&E.removed()||E.updateStyle()}return x},iH.move=function(e){var t=this._private.cy,n=this,r=function(e){return null==e?e:""+e};if(void 0!==e.source||void 0!==e.target){var i=r(e.source),a=r(e.target),o=null!=i&&t.hasElementWithId(i),s=null!=a&&t.hasElementWithId(a);(o||s)&&(t.batch(function(){n.remove(!1,!1),n.emitAndNotify("moveout");for(var e=0;e<n.length;e++){var t=n[e],r=t._private.data;t.isEdge()&&(o&&(r.source=i),s&&(r.target=a))}n.restore(!1,!1)}),n.emitAndNotify("move"))}else if(void 0!==e.parent){var l=r(e.parent);if(null===l||t.hasElementWithId(l)){var u=null===l?void 0:l;t.batch(function(){var e=n.remove(!1,!1);e.emitAndNotify("moveout");for(var t=0;t<n.length;t++){var r=n[t],i=r._private.data;r.isNode()&&(i.parent=u)}e.restore(!1,!1)}),n.emitAndNotify("move")}}return this},[n5,rm,rb,rz,rF,li,rW,il,iC,ik,{isNode:function(){return"nodes"===this.group()},isEdge:function(){return"edges"===this.group()},isLoop:function(){return this.isEdge()&&this.source()[0]===this.target()[0]},isSimple:function(){return this.isEdge()&&this.source()[0]!==this.target()[0]},group:function(){var e=this[0];if(e)return e._private.group}},iS,iD,iR,iO,iF].forEach(function(e){J(iH,e)});var iK=function(){function e(e){return-e.tension*e.x-e.friction*e.v}function t(t,n,r){var i={x:t.x+r.dx*n,v:t.v+r.dv*n,tension:t.tension,friction:t.friction};return{dx:i.v,dv:e(i)}}return function n(r,i,a){var o,s,l,u,c,d,h,f,p,g,v,y={x:-1,v:0,tension:null,friction:null},b=[0],x=0;for(r=parseFloat(r)||500,i=parseFloat(i)||20,a=a||null,y.tension=r,y.friction=i,g=(p=null!==a)?(x=n(r,i))/a*.016:.016;o=v||y,s=g,l={dx:o.v,dv:e(o)},u=t(o,.5*s,l),c=t(o,.5*s,u),d=t(o,s,c),h=1/6*(l.dx+2*(u.dx+c.dx)+d.dx),f=1/6*(l.dv+2*(u.dv+c.dv)+d.dv),o.x=o.x+h*s,o.v=o.v+f*s,v=o,b.push(1+v.x),x+=16,Math.abs(v.x)>1e-4&&Math.abs(v.v)>1e-4;);return p?function(e){return b[e*(b.length-1)|0]}:x}}(),iZ=function(e,t,n,r){var i=function(e,t,n,r){var i="undefined"!=typeof Float32Array;if(4!=arguments.length)return!1;for(var a=0;a<4;++a)if("number"!=typeof arguments[a]||isNaN(arguments[a])||!isFinite(arguments[a]))return!1;e=Math.min(e,1),n=Math.min(n,1),e=Math.max(e,0),n=Math.max(n,0);var o=i?new Float32Array(11):Array(11);function s(e,t,n){return(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e}function l(e,t,n){return 3*(1-3*n+3*t)*e*e+2*(3*n-6*t)*e+3*t}var u=!1,c=function(i){return(u||(u=!0,(e!==t||n!==r)&&function(){for(var t=0;t<11;++t)o[t]=s(.1*t,e,n)}()),e===t&&n===r)?i:0===i?0:1===i?1:s(function(t){for(var r=0,i=1;10!==i&&o[i]<=t;++i)r+=.1;var a=r+(t-o[--i])/(o[i+1]-o[i])*.1,u=l(a,e,n);if(u>=.001){for(var c=a,d=0;d<4;++d){var h=l(c,e,n);if(0===h)break;var f=s(c,e,n)-t;c-=f/h}return c}return 0===u?a:function(t,r,i){var a,o,l=0;do(a=s(o=r+(i-r)/2,e,n)-t)>0?i=o:r=o;while(Math.abs(a)>1e-7&&++l<10);return o}(t,r,r+.1)}(i),t,r)};c.getControlPoints=function(){return[{x:e,y:t},{x:n,y:r}]};var d="generateBezier("+[e,t,n,r]+")";return c.toString=function(){return d},c}(e,t,n,r);return function(e,t,n){return e+(t-e)*i(n)}},i$={linear:function(e,t,n){return e+(t-e)*n},ease:iZ(.25,.1,.25,1),"ease-in":iZ(.42,0,1,1),"ease-out":iZ(0,0,.58,1),"ease-in-out":iZ(.42,0,.58,1),"ease-in-sine":iZ(.47,0,.745,.715),"ease-out-sine":iZ(.39,.575,.565,1),"ease-in-out-sine":iZ(.445,.05,.55,.95),"ease-in-quad":iZ(.55,.085,.68,.53),"ease-out-quad":iZ(.25,.46,.45,.94),"ease-in-out-quad":iZ(.455,.03,.515,.955),"ease-in-cubic":iZ(.55,.055,.675,.19),"ease-out-cubic":iZ(.215,.61,.355,1),"ease-in-out-cubic":iZ(.645,.045,.355,1),"ease-in-quart":iZ(.895,.03,.685,.22),"ease-out-quart":iZ(.165,.84,.44,1),"ease-in-out-quart":iZ(.77,0,.175,1),"ease-in-quint":iZ(.755,.05,.855,.06),"ease-out-quint":iZ(.23,1,.32,1),"ease-in-out-quint":iZ(.86,0,.07,1),"ease-in-expo":iZ(.95,.05,.795,.035),"ease-out-expo":iZ(.19,1,.22,1),"ease-in-out-expo":iZ(1,0,0,1),"ease-in-circ":iZ(.6,.04,.98,.335),"ease-out-circ":iZ(.075,.82,.165,1),"ease-in-out-circ":iZ(.785,.135,.15,.86),spring:function(e,t,n){if(0===n)return i$.linear;var r=iK(e,t,n);return function(e,t,n){return e+(t-e)*r(n)}},"cubic-bezier":iZ};function iQ(e,t,n,r,i){if(1===r||t===n)return n;var a=i(t,n,r);return null==e||((e.roundValue||e.color)&&(a=Math.round(a)),void 0!==e.min&&(a=Math.max(a,e.min)),void 0!==e.max&&(a=Math.min(a,e.max))),a}function iJ(e,t){return null==e.pfValue&&null==e.value?e:null!=e.pfValue&&(null==t||"%"!==t.type.units)?e.pfValue:e.value}function i0(e,t,n,r,i){var a=null!=i?i.type:null;n<0?n=0:n>1&&(n=1);var o=iJ(e,i),s=iJ(t,i);if(M(o)&&M(s))return iQ(a,o,s,n,r);if(_(o)&&_(s)){for(var l=[],u=0;u<s.length;u++){var c=o[u],d=s[u];if(null!=c&&null!=d){var h=iQ(a,c,d,n,r);l.push(h)}else l.push(d)}return l}}function i1(e,t){return null!=e&&null!=t&&(!!(M(e)&&M(t))||!!e&&!!t)}function i2(e,t){var n=t._private.aniEles,r=[];function i(t,n){var i=t._private,a=i.animation.current,o=i.animation.queue,s=!1;if(0===a.length){var l=o.shift();l&&a.push(l)}for(var u=function(e){for(var t=e.length-1;t>=0;t--)(0,e[t])();e.splice(0,e.length)},c=a.length-1;c>=0;c--){var d=a[c],h=d._private;if(h.stopped){a.splice(c,1),h.hooked=!1,h.playing=!1,h.started=!1,u(h.frames);continue}(h.playing||h.applying)&&(h.playing&&h.applying&&(h.applying=!1),h.started||function(e,t,n,r){var i=t._private;i.started=!0,i.startTime=n-i.progress*i.duration}(0,d,e),function(e,t,n,r){var i,a,o,s,l=!r,u=e._private,c=t._private,d=c.easing,h=c.startTime,f=(r?e:e.cy()).style();c.easingImpl||(null==d?c.easingImpl=i$.linear:(i=B(d)?f.parse("transition-timing-function",d).value:d,B(i)?(a=i,o=[]):(a=i[1],o=i.slice(2).map(function(e){return+e})),o.length>0?("spring"===a&&o.push(c.duration),c.easingImpl=i$[a].apply(null,o)):c.easingImpl=i$[a]));var p=c.easingImpl;if(s=0===c.duration?1:(n-h)/c.duration,c.applying&&(s=c.progress),s<0?s=0:s>1&&(s=1),null==c.delay){var g=c.startPosition,v=c.position;if(v&&l&&!e.locked()){var y={};i1(g.x,v.x)&&(y.x=i0(g.x,v.x,s,p)),i1(g.y,v.y)&&(y.y=i0(g.y,v.y,s,p)),e.position(y)}var b=c.startPan,x=c.pan,w=u.pan,E=null!=x&&r;E&&(i1(b.x,x.x)&&(w.x=i0(b.x,x.x,s,p)),i1(b.y,x.y)&&(w.y=i0(b.y,x.y,s,p)),e.emit("pan"));var T=c.startZoom,C=c.zoom,k=null!=C&&r;k&&(i1(T,C)&&(u.zoom=tm(u.minZoom,i0(T,C,s,p),u.maxZoom)),e.emit("zoom")),(E||k)&&e.emit("viewport");var P=c.style;if(P&&P.length>0&&l){for(var S=0;S<P.length;S++){var D=P[S],_=D.name,A=c.startStyle[_],M=f.properties[A.name],R=i0(A,D,s,p,M);f.overrideBypass(e,_,R)}e.emit("style")}}c.progress=s}(t,d,e,n),h.applying&&(h.applying=!1),u(h.frames),null!=h.step&&h.step(e),d.completed()&&(a.splice(c,1),h.hooked=!1,h.playing=!1,h.started=!1,u(h.completes)),s=!0)}return n||0!==a.length||0!==o.length||r.push(t),s}for(var a=!1,o=0;o<n.length;o++){var s=i(n[o]);a=a||s}var l=i(t,!0);(a||l)&&(n.length>0?t.notify("draw",n):t.notify("draw")),n.unmerge(r),t.emit("step")}var i5={animate:ry.animate(),animation:ry.animation(),animated:ry.animated(),clearQueue:ry.clearQueue(),delay:ry.delay(),delayAnimation:ry.delayAnimation(),stop:ry.stop(),addToAnimationPool:function(e){this.styleEnabled()&&this._private.aniEles.merge(e)},stopAnimationLoop:function(){this._private.animationsRunning=!1},startAnimationLoop:function(){var e=this;if(e._private.animationsRunning=!0,e.styleEnabled()){var t=e.renderer();t&&t.beforeRender?t.beforeRender(function(t,n){i2(n,e)},t.beforeRenderPriorities.animations):function t(){e._private.animationsRunning&&em(function(n){i2(n,e),t()})}()}}},i3={qualifierCompare:function(e,t){return null==e||null==t?null==e&&null==t:e.sameText(t)},eventMatches:function(e,t,n){var r=t.qualifier;return null==r||e!==n.target&&N(n.target)&&r.matches(n.target)},addEventFields:function(e,t){t.cy=e,t.target=e},callbackContext:function(e,t,n){return null!=t.qualifier?n.target:e}},i4=function(e){return B(e)?new rL(e):e},i9={createEmitter:function(){var e=this._private;return e.emitter||(e.emitter=new iy(i3,this)),this},emitter:function(){return this._private.emitter},on:function(e,t,n){return this.emitter().on(e,i4(t),n),this},removeListener:function(e,t,n){return this.emitter().removeListener(e,i4(t),n),this},removeAllListeners:function(){return this.emitter().removeAllListeners(),this},one:function(e,t,n){return this.emitter().one(e,i4(t),n),this},once:function(e,t,n){return this.emitter().one(e,i4(t),n),this},emit:function(e,t){return this.emitter().emit(e,t),this},emitAndNotify:function(e,t){return this.emit(e),this.notify(e,t),this}};ry.eventAliasesOn(i9);var i6={png:function(e){return e=e||{},this._private.renderer.png(e)},jpg:function(e){var t=this._private.renderer;return(e=e||{}).bg=e.bg||"#fff",t.jpg(e)}};i6.jpeg=i6.jpg;var i8={layout:function(e){if(null==e)return void eL("Layout options must be specified to make a layout");if(null==e.name)return void eL("A `name` must be specified to make a layout");var t,n=e.name,r=this.extension("layout",n);return null==r?void eL("No such layout `"+n+"` found.  Did you forget to import it and `cytoscape.use()` it?"):(t=B(e.eles)?this.$(e.eles):null!=e.eles?e.eles:this.$(),new r(J({},e,{cy:this,eles:t})))}};i8.createLayout=i8.makeLayout=i8.layout;var i7=eY({hideEdgesOnViewport:!1,textureOnViewport:!1,motionBlur:!1,motionBlurOpacity:.05,pixelRatio:void 0,desktopTapThreshold:4,touchTapThreshold:8,wheelSensitivity:1,debug:!1,showFps:!1,webgl:!1,webglDebug:!1,webglDebugShowAtlases:!1,webglTexSize:2048,webglTexRows:36,webglTexRowsNodes:18,webglBatchSize:2048,webglTexPerBatch:14,webglBgColor:[255,255,255]}),ae={renderTo:function(e,t,n,r){return this._private.renderer.renderTo(e,t,n,r),this},renderer:function(){return this._private.renderer},forceRender:function(){return this.notify("draw"),this},resize:function(){return this.invalidateSize(),this.emitAndNotify("resize"),this},initRenderer:function(e){var t=this.extension("renderer",e.name);if(null==t)return void eL("Can not initialise: No such renderer `".concat(e.name,"` found. Did you forget to import it and `cytoscape.use()` it?"));void 0!==e.wheelSensitivity&&ez("You have set a custom wheel sensitivity.  This will make your app zoom unnaturally when using mainstream mice.  You should change this value from the default only if you can guarantee that all your users will use the same hardware and OS configuration as your current machine.");var n=i7(e);n.cy=this,this._private.renderer=new t(n),this.notify("init")},destroyRenderer:function(){this.notify("destroy");var e=this.container();if(e)for(e._cyreg=null;e.childNodes.length>0;)e.removeChild(e.childNodes[0]);this._private.renderer=null,this.mutableElements().forEach(function(e){var t=e._private;t.rscratch={},t.rstyle={},t.animation.current=[],t.animation.queue=[]})},onRender:function(e){return this.on("render",e)},offRender:function(e){return this.off("render",e)}};ae.invalidateDimensions=ae.resize;var at={collection:function(e,t){return B(e)?this.$(e):I(e)?e.collection():_(e)?(t||(t={}),new iG(this,e,t.unique,t.removed)):new iG(this)},nodes:function(e){var t=this.$(function(e){return e.isNode()});return e?t.filter(e):t},edges:function(e){var t=this.$(function(e){return e.isEdge()});return e?t.filter(e):t},$:function(e){var t=this._private.elements;return e?t.filter(e):t.spawnSelf()},mutableElements:function(){return this._private.elements}};at.elements=at.filter=at.$;var an={};an.apply=function(e){for(var t=this._private.cy.collection(),n=0;n<e.length;n++){var r=e[n],i=this.getContextMeta(r);if(!i.empty){var a=this.getContextStyle(i),o=this.applyContextStyle(i,a,r);r._private.appliedInitStyle?this.updateTransitions(r,o.diffProps):r._private.appliedInitStyle=!0,this.updateStyleHints(r)&&t.push(r)}}return t},an.getPropertiesDiff=function(e,t){var n=this._private.propDiffs=this._private.propDiffs||{},r=e+"-"+t,i=n[r];if(i)return i;for(var a=[],o={},s=0;s<this.length;s++){var l=this[s],u="t"===e[s],c="t"===t[s],d=u!==c,h=l.mappedProperties.length>0;if(d||c&&h){var f=void 0;d&&h||d?f=l.properties:h&&(f=l.mappedProperties);for(var p=0;p<f.length;p++){for(var g=f[p],v=g.name,y=!1,b=s+1;b<this.length;b++){var x=this[b];if("t"===t[b]&&(y=null!=x.properties[g.name]))break}o[v]||y||(o[v]=!0,a.push(v))}}}return n[r]=a,a},an.getContextMeta=function(e){for(var t,n="",r=e._private.styleCxtKey||"",i=0;i<this.length;i++){var a=this[i];a.selector&&a.selector.matches(e)?n+="t":n+="f"}return t=this.getPropertiesDiff(r,n),e._private.styleCxtKey=n,{key:n,diffPropNames:t,empty:0===t.length}},an.getContextStyle=function(e){var t=e.key,n=this._private.contextStyles=this._private.contextStyles||{};if(n[t])return n[t];for(var r={_private:{key:t}},i=0;i<this.length;i++){var a=this[i];if("t"===t[i])for(var o=0;o<a.properties.length;o++){var s=a.properties[o];r[s.name]=s}}return n[t]=r,r},an.applyContextStyle=function(e,t,n){for(var r=e.diffPropNames,i={},a=this.types,o=0;o<r.length;o++){var s=r[o],l=t[s],u=n.pstyle(s);if(!l)if(!u)continue;else l=u.bypass?{name:s,deleteBypassed:!0}:{name:s,delete:!0};if(u!==l){if(l.mapped===a.fn&&null!=u&&null!=u.mapping&&u.mapping.value===l.value){var c=u.mapping;if((c.fnValue=l.value(n))===c.prevFnValue)continue}var d=i[s]={prev:u};this.applyParsedProperty(n,l),d.next=n.pstyle(s),d.next&&d.next.bypass&&(d.next=d.next.bypassed)}}return{diffProps:i}},an.updateStyleHints=function(e){var t=e._private,n=this,r=n.propertyGroupNames,i=n.propertyGroupKeys,a=function(e,t,r){return n.getPropertiesHash(e,t,r)},o=t.styleKey;if(e.removed())return!1;var s="nodes"===t.group,l=e._private.style;r=Object.keys(l);for(var u=0;u<i.length;u++){var c=i[u];t.styleKeys[c]=[9261,5381]}for(var d=function(e,n){return t.styleKeys[n][0]=ex(e,t.styleKeys[n][0])},h=function(e,n){return t.styleKeys[n][1]=ew(e,t.styleKeys[n][1])},f=function(e,t){for(var n=0;n<e.length;n++){var r=e.charCodeAt(n);d(r,t),h(r,t)}},p=0;p<r.length;p++){var g=r[p],v=l[g];if(null!=v){var y,b,x=this.properties[g],w=x.type,E=x.groupKey,T=void 0;null!=x.hashOverride?T=x.hashOverride(e,v):null!=v.pfValue&&(T=v.pfValue);var C=null==x.enums?v.value:null,k=null!=T,P=null!=C,S=k||P,B=v.units;w.number&&S&&!w.multiple?(d(b=-128<(y=k?T:C)&&y<128&&Math.floor(y)!==y?2e9-(1024*y|0):y,E),h(b,E),k||null==B||f(B,E)):f(v.strValue,E)}}for(var D=[9261,5381],_=0;_<i.length;_++){var A=i[_],M=t.styleKeys[A];D[0]=ex(M[0],D[0]),D[1]=ew(M[1],D[1])}t.styleKey=2097152*D[0]+D[1];var R=t.styleKeys;t.labelDimsKey=eE(R.labelDimensions);var I=a(e,["label"],R.labelDimensions);if(t.labelKey=eE(I),t.labelStyleKey=eE(eT(R.commonLabel,I)),!s){var N=a(e,["source-label"],R.labelDimensions);t.sourceLabelKey=eE(N),t.sourceLabelStyleKey=eE(eT(R.commonLabel,N));var L=a(e,["target-label"],R.labelDimensions);t.targetLabelKey=eE(L),t.targetLabelStyleKey=eE(eT(R.commonLabel,L))}if(s){var O=t.styleKeys,z=O.nodeBody,V=O.nodeBorder,F=O.nodeOutline,X=O.backgroundImage,j=O.compound,Y=O.pie,q=O.stripe;t.nodeKey=eE([z,V,F,X,j,Y,q].filter(function(e){return null!=e}).reduce(eT,[9261,5381])),t.hasPie=null!=Y&&9261!==Y[0]&&5381!==Y[1],t.hasStripe=null!=q&&9261!==q[0]&&5381!==q[1]}return o!==t.styleKey},an.clearStyleHints=function(e){var t=e._private;t.styleCxtKey="",t.styleKeys={},t.styleKey=null,t.labelKey=null,t.labelStyleKey=null,t.sourceLabelKey=null,t.sourceLabelStyleKey=null,t.targetLabelKey=null,t.targetLabelStyleKey=null,t.nodeKey=null,t.hasPie=null,t.hasStripe=null},an.applyParsedProperty=function(e,t){var n=this,r=t,i=e._private.style,a=n.types,o=n.properties[r.name].type,s=r.bypass,l=i[r.name],u=l&&l.bypass,c=e._private,d="mapping",h=function(e){return null==e?null:null!=e.pfValue?e.pfValue:e.value},f=function(){var t=h(l),i=h(r);n.checkTriggers(e,r.name,t,i)};if("curve-style"===t.name&&e.isEdge()&&("bezier"!==t.value&&e.isLoop()||"haystack"===t.value&&(e.source().isParent()||e.target().isParent()))&&(r=t=this.parse(t.name,"bezier",s)),r.delete)return i[r.name]=void 0,f(),!0;if(r.deleteBypassed)if(!l)return f(),!0;else if(l.bypass)return l.bypassed=void 0,f(),!0;else return!1;if(r.deleteBypass)if(!l)return f(),!0;else if(l.bypass)return i[r.name]=l.bypassed,f(),!0;else return!1;var p=function(){ez("Do not assign mappings to elements without corresponding data (i.e. ele `"+e.id()+"` has no mapping for property `"+r.name+"` with data field `"+r.field+"`); try a `["+r.field+"]` selector to limit scope to elements with `"+r.field+"` defined")};switch(r.mapped){case a.mapData:for(var g,v,y=r.field.split("."),b=c.data,x=0;x<y.length&&b;x++)b=b[y[x]];if(null==b)return p(),!1;if(!M(b))return ez("Do not use continuous mappers without specifying numeric data (i.e. `"+r.field+": "+b+"` for `"+e.id()+"` is non-numeric)"),!1;var w=r.fieldMax-r.fieldMin;if((v=0===w?0:(b-r.fieldMin)/w)<0?v=0:v>1&&(v=1),o.color){var E=r.valueMin[0],T=r.valueMax[0],C=r.valueMin[1],k=r.valueMax[1],P=r.valueMin[2],S=r.valueMax[2],B=null==r.valueMin[3]?1:r.valueMin[3],D=[Math.round(E+(T-E)*v),Math.round(C+(k-C)*v),Math.round(P+(S-P)*v),Math.round(B+((null==r.valueMax[3]?1:r.valueMax[3])-B)*v)];g={bypass:r.bypass,name:r.name,value:D,strValue:"rgb("+D[0]+", "+D[1]+", "+D[2]+")"}}else{if(!o.number)return!1;var _=r.valueMin+(r.valueMax-r.valueMin)*v;g=this.parse(r.name,_,r.bypass,d)}if(!g)return p(),!1;g.mapping=r,r=g;break;case a.data:for(var A=r.field.split("."),R=c.data,I=0;I<A.length&&R;I++)R=R[A[I]];if(null!=R&&(g=this.parse(r.name,R,r.bypass,d)),!g)return p(),!1;g.mapping=r,r=g;break;case a.fn:var N=r.value,L=null!=r.fnValue?r.fnValue:N(e);if(r.prevFnValue=L,null==L)return ez("Custom function mappers may not return null (i.e. `"+r.name+"` for ele `"+e.id()+"` is null)"),!1;if(!(g=this.parse(r.name,L,r.bypass,d)))return ez("Custom function mappers may not return invalid values for the property type (i.e. `"+r.name+"` for ele `"+e.id()+"` is invalid)"),!1;g.mapping=eV(r),r=g;break;case void 0:break;default:return!1}return s?(u?r.bypassed=l.bypassed:r.bypassed=l,i[r.name]=r):u?l.bypassed=r:i[r.name]=r,f(),!0},an.cleanElements=function(e,t){for(var n=0;n<e.length;n++){var r=e[n];if(this.clearStyleHints(r),r.dirtyCompoundBoundsCache(),r.dirtyBoundingBoxCache(),t)for(var i=r._private.style,a=Object.keys(i),o=0;o<a.length;o++){var s=a[o],l=i[s];null!=l&&(l.bypass?l.bypassed=null:i[s]=null)}else r._private.style={}}},an.update=function(){this._private.cy.mutableElements().updateStyle()},an.updateTransitions=function(e,t){var n=this,r=e._private,i=e.pstyle("transition-property").value,a=e.pstyle("transition-duration").pfValue,o=e.pstyle("transition-delay").pfValue;if(i.length>0&&a>0){for(var s={},l=!1,u=0;u<i.length;u++){var c=i[u],d=e.pstyle(c),h=t[c];if(h){var f=h.prev,p=null!=h.next?h.next:d,g=!1,v=void 0;f&&(M(f.pfValue)&&M(p.pfValue)?(g=p.pfValue-f.pfValue,v=f.pfValue+1e-6*g):M(f.value)&&M(p.value)?(g=p.value-f.value,v=f.value+1e-6*g):_(f.value)&&_(p.value)&&(g=f.value[0]!==p.value[0]||f.value[1]!==p.value[1]||f.value[2]!==p.value[2],v=f.strValue),g&&(s[c]=p.strValue,this.applyBypass(e,c,v),l=!0))}}if(!l)return;r.transitioning=!0,new re(function(t){o>0?e.delayAnimation(o).play().promise().then(t):t()}).then(function(){return e.animation({style:s,duration:a,easing:e.pstyle("transition-timing-function").value,queue:!1}).play().promise()}).then(function(){n.removeBypasses(e,i),e.emitAndNotify("style"),r.transitioning=!1})}else r.transitioning&&(this.removeBypasses(e,i),e.emitAndNotify("style"),r.transitioning=!1)},an.checkTrigger=function(e,t,n,r,i,a){var o=this.properties[t],s=i(o);!e.removed()&&null!=s&&s(n,r,e)&&a(o)},an.checkZOrderTrigger=function(e,t,n,r){var i=this;this.checkTrigger(e,t,n,r,function(e){return e.triggersZOrder},function(){i._private.cy.notify("zorder",e)})},an.checkBoundsTrigger=function(e,t,n,r){this.checkTrigger(e,t,n,r,function(e){return e.triggersBounds},function(t){e.dirtyCompoundBoundsCache(),e.dirtyBoundingBoxCache()})},an.checkConnectedEdgesBoundsTrigger=function(e,t,n,r){this.checkTrigger(e,t,n,r,function(e){return e.triggersBoundsOfConnectedEdges},function(t){e.connectedEdges().forEach(function(e){e.dirtyBoundingBoxCache()})})},an.checkParallelEdgesBoundsTrigger=function(e,t,n,r){this.checkTrigger(e,t,n,r,function(e){return e.triggersBoundsOfParallelEdges},function(t){e.parallelEdges().forEach(function(e){e.dirtyBoundingBoxCache()})})},an.checkTriggers=function(e,t,n,r){e.dirtyStyleCache(),this.checkZOrderTrigger(e,t,n,r),this.checkBoundsTrigger(e,t,n,r),this.checkConnectedEdgesBoundsTrigger(e,t,n,r),this.checkParallelEdgesBoundsTrigger(e,t,n,r)};var ar={};ar.applyBypass=function(e,t,n,r){var i=[];if("*"===t||"**"===t){if(void 0!==n)for(var a=0;a<this.properties.length;a++){var o=this.properties[a].name,s=this.parse(o,n,!0);s&&i.push(s)}}else if(B(t)){var l=this.parse(t,n,!0);l&&i.push(l)}else{if(!A(t))return!1;r=n;for(var u=Object.keys(t),c=0;c<u.length;c++){var d=u[c],h=t[d];if(void 0===h&&(h=t[Y(d)]),void 0!==h){var f=this.parse(d,h,!0);f&&i.push(f)}}}if(0===i.length)return!1;for(var p=!1,g=0;g<e.length;g++){for(var v=e[g],y={},b=void 0,x=0;x<i.length;x++){var w=i[x];if(r){var E=v.pstyle(w.name);b=y[w.name]={prev:E}}p=this.applyParsedProperty(v,eV(w))||p,r&&(b.next=v.pstyle(w.name))}p&&this.updateStyleHints(v),r&&this.updateTransitions(v,y,!0)}return p},ar.overrideBypass=function(e,t,n){t=j(t);for(var r=0;r<e.length;r++){var i=e[r],a=i._private.style[t],o=this.properties[t].type,s=o.color,l=o.mutiple,u=a?null!=a.pfValue?a.pfValue:a.value:null;a&&a.bypass?(a.value=n,null!=a.pfValue&&(a.pfValue=n),s?a.strValue="rgb("+n.join(",")+")":l?a.strValue=n.join(" "):a.strValue=""+n,this.updateStyleHints(i)):this.applyBypass(i,t,n),this.checkTriggers(i,t,u,n)}},ar.removeAllBypasses=function(e,t){return this.removeBypasses(e,this.propertyNames,t)},ar.removeBypasses=function(e,t,n){for(var r=0;r<e.length;r++){for(var i=e[r],a={},o=0;o<t.length;o++){var s=t[o],l=this.properties[s],u=i.pstyle(l.name);if(u&&u.bypass){var c=this.parse(s,"",!0),d=a[l.name]={prev:u};this.applyParsedProperty(i,c),d.next=i.pstyle(l.name)}}this.updateStyleHints(i),n&&this.updateTransitions(i,a,!0)}};var ai={};ai.getEmSizeInPixels=function(){var e=this.containerCss("font-size");return null!=e?parseFloat(e):1},ai.containerCss=function(e){var t=this._private.cy,n=t.container(),r=t.window();if(r&&n&&r.getComputedStyle)return r.getComputedStyle(n).getPropertyValue(e)};var aa={};aa.getRenderedStyle=function(e,t){return t?this.getStylePropertyValue(e,t,!0):this.getRawStyle(e,!0)},aa.getRawStyle=function(e,t){if(e=e[0]){for(var n={},r=0;r<this.properties.length;r++){var i=this.properties[r],a=this.getStylePropertyValue(e,i.name,t);null!=a&&(n[i.name]=a,n[Y(i.name)]=a)}return n}},aa.getIndexedStyle=function(e,t,n,r){var i=e.pstyle(t)[n][r];return null!=i?i:e.cy().style().getDefaultProperty(t)[n][0]},aa.getStylePropertyValue=function(e,t,n){if(e=e[0]){var r=this.properties[t];r.alias&&(r=r.pointsTo);var i=r.type,a=e.pstyle(r.name);if(a){var o=a.value,s=a.units,l=a.strValue;if(n&&i.number&&null!=o&&M(o)){var u=e.cy().zoom(),c=function(e){return e*u},d=function(e,t){return c(e)+t},h=_(o);if(h?s.every(function(e){return null!=e}):null!=s)if(h)return o.map(function(e,t){return d(e,s[t])}).join(" ");else return d(o,s);return h?o.map(function(e){return B(e)?e:""+c(e)}).join(" "):""+c(o)}if(null!=l)return l}return null}},aa.getAnimationStartStyle=function(e,t){for(var n={},r=0;r<t.length;r++){var i=t[r].name,a=e.pstyle(i);void 0!==a&&(a=A(a)?this.parse(i,a.strValue):this.parse(i,a)),a&&(n[i]=a)}return n},aa.getPropsList=function(e){var t=[],n=this.properties;if(e)for(var r=Object.keys(e),i=0;i<r.length;i++){var a=r[i],o=e[a],s=n[a]||n[j(a)],l=this.parse(s.name,o);l&&t.push(l)}return t},aa.getNonDefaultPropertiesHash=function(e,t,n){var r,i,a,o,s,l,u=n.slice();for(s=0;s<t.length;s++)if(r=t[s],null!=(i=e.pstyle(r,!1)))if(null!=i.pfValue)u[0]=ex(o,u[0]),u[1]=ew(o,u[1]);else for(l=0,a=i.strValue;l<a.length;l++)o=a.charCodeAt(l),u[0]=ex(o,u[0]),u[1]=ew(o,u[1]);return u},aa.getPropertiesHash=aa.getNonDefaultPropertiesHash;var ao={};ao.appendFromJson=function(e){for(var t=0;t<e.length;t++){var n=e[t],r=n.selector,i=n.style||n.css,a=Object.keys(i);this.selector(r);for(var o=0;o<a.length;o++){var s=a[o],l=i[s];this.css(s,l)}}return this},ao.fromJson=function(e){return this.resetToDefault(),this.appendFromJson(e),this},ao.json=function(){for(var e=[],t=this.defaultLength;t<this.length;t++){for(var n=this[t],r=n.selector,i=n.properties,a={},o=0;o<i.length;o++){var s=i[o];a[s.name]=s.strValue}e.push({selector:r?r.toString():"core",style:a})}return e};var as={};as.appendFromString=function(e){var t,n,r,i=""+e;function a(){i=i.length>t.length?i.substr(t.length):""}function o(){n=n.length>r.length?n.substr(r.length):""}for(i=i.replace(/[/][*](\s|.)+?[*][/]/g,"");!i.match(/^\s*$/);){var s=i.match(/^\s*((?:.|\s)+?)\s*\{((?:.|\s)+?)\}/);if(!s){ez("Halting stylesheet parsing: String stylesheet contains more to parse but no selector and block found in: "+i);break}t=s[0];var l=s[1];if("core"!==l&&new rL(l).invalid){ez("Skipping parsing of block: Invalid selector found in string stylesheet: "+l),a();continue}var u=s[2],c=!1;n=u;for(var d=[];!n.match(/^\s*$/);){var h=n.match(/^\s*(.+?)\s*:\s*(.+?)(?:\s*;|\s*$)/);if(!h){ez("Skipping parsing of block: Invalid formatting of style property and value definitions found in:"+u),c=!0;break}r=h[0];var f=h[1],p=h[2];if(!this.properties[f]){ez("Skipping property: Invalid property name in: "+r),o();continue}if(!this.parse(f,p)){ez("Skipping property: Invalid property definition in: "+r),o();continue}d.push({name:f,val:p}),o()}if(c){a();break}this.selector(l);for(var g=0;g<d.length;g++){var v=d[g];this.css(v.name,v.val)}a()}return this},as.fromString=function(e){return this.resetToDefault(),this.appendFromString(e),this};var al={};!function(){var e=function(e){return"^"+e+"\\s*\\(\\s*([\\w\\.]+)\\s*\\)$"},t=function(e){var t=G+"|\\w+|"+K+"|"+$+"|\\#[0-9a-fA-F]{3}|\\#[0-9a-fA-F]{6}";return"^"+e+"\\s*\\(([\\w\\.]+)\\s*\\,\\s*("+G+")\\s*\\,\\s*("+G+")\\s*,\\s*("+t+")\\s*\\,\\s*("+t+")\\)$"},n=["^url\\s*\\(\\s*['\"]?(.+?)['\"]?\\s*\\)$","^(none)$","^(.+)$"];al.types={time:{number:!0,min:0,units:"s|ms",implicitUnits:"ms"},percent:{number:!0,min:0,max:100,units:"%",implicitUnits:"%"},percentages:{number:!0,min:0,max:100,units:"%",implicitUnits:"%",multiple:!0},zeroOneNumber:{number:!0,min:0,max:1,unitless:!0},zeroOneNumbers:{number:!0,min:0,max:1,unitless:!0,multiple:!0},nOneOneNumber:{number:!0,min:-1,max:1,unitless:!0},nonNegativeInt:{number:!0,min:0,integer:!0,unitless:!0},nonNegativeNumber:{number:!0,min:0,unitless:!0},position:{enums:["parent","origin"]},nodeSize:{number:!0,min:0,enums:["label"]},number:{number:!0,unitless:!0},numbers:{number:!0,unitless:!0,multiple:!0},positiveNumber:{number:!0,unitless:!0,min:0,strictMin:!0},size:{number:!0,min:0},bidirectionalSize:{number:!0},bidirectionalSizeMaybePercent:{number:!0,allowPercent:!0},bidirectionalSizes:{number:!0,multiple:!0},sizeMaybePercent:{number:!0,min:0,allowPercent:!0},axisDirection:{enums:["horizontal","leftward","rightward","vertical","upward","downward","auto"]},axisDirectionExplicit:{enums:["leftward","rightward","upward","downward"]},axisDirectionPrimary:{enums:["horizontal","vertical"]},paddingRelativeTo:{enums:["width","height","average","min","max"]},bgWH:{number:!0,min:0,allowPercent:!0,enums:["auto"],multiple:!0},bgPos:{number:!0,allowPercent:!0,multiple:!0},bgRelativeTo:{enums:["inner","include-padding"],multiple:!0},bgRepeat:{enums:["repeat","repeat-x","repeat-y","no-repeat"],multiple:!0},bgFit:{enums:["none","contain","cover"],multiple:!0},bgCrossOrigin:{enums:["anonymous","use-credentials","null"],multiple:!0},bgClip:{enums:["none","node"],multiple:!0},bgContainment:{enums:["inside","over"],multiple:!0},boxSelection:{enums:["contain","overlap","none"]},color:{color:!0},colors:{color:!0,multiple:!0},fill:{enums:["solid","linear-gradient","radial-gradient"]},bool:{enums:["yes","no"]},bools:{enums:["yes","no"],multiple:!0},lineStyle:{enums:["solid","dotted","dashed"]},lineCap:{enums:["butt","round","square"]},linePosition:{enums:["center","inside","outside"]},lineJoin:{enums:["round","bevel","miter"]},borderStyle:{enums:["solid","dotted","dashed","double"]},curveStyle:{enums:["bezier","unbundled-bezier","haystack","segments","straight","straight-triangle","taxi","round-segments","round-taxi"]},radiusType:{enums:["arc-radius","influence-radius"],multiple:!0},fontFamily:{regex:'^([\\w- \\"]+(?:\\s*,\\s*[\\w- \\"]+)*)$'},fontStyle:{enums:["italic","normal","oblique"]},fontWeight:{enums:["normal","bold","bolder","lighter","100","200","300","400","500","600","800","900",100,200,300,400,500,600,700,800,900]},textDecoration:{enums:["none","underline","overline","line-through"]},textTransform:{enums:["none","uppercase","lowercase"]},textWrap:{enums:["none","wrap","ellipsis"]},textOverflowWrap:{enums:["whitespace","anywhere"]},textBackgroundShape:{enums:["rectangle","roundrectangle","round-rectangle","circle"]},nodeShape:{enums:["rectangle","roundrectangle","round-rectangle","cutrectangle","cut-rectangle","bottomroundrectangle","bottom-round-rectangle","barrel","ellipse","triangle","round-triangle","square","pentagon","round-pentagon","hexagon","round-hexagon","concavehexagon","concave-hexagon","heptagon","round-heptagon","octagon","round-octagon","tag","round-tag","star","diamond","round-diamond","vee","rhomboid","right-rhomboid","polygon"]},overlayShape:{enums:["roundrectangle","round-rectangle","ellipse"]},cornerRadius:{number:!0,min:0,units:"px|em",implicitUnits:"px",enums:["auto"]},compoundIncludeLabels:{enums:["include","exclude"]},arrowShape:{enums:["tee","triangle","triangle-tee","circle-triangle","triangle-cross","triangle-backcurve","vee","square","circle","diamond","chevron","none"]},arrowFill:{enums:["filled","hollow"]},arrowWidth:{number:!0,units:"%|px|em",implicitUnits:"px",enums:["match-line"]},display:{enums:["element","none"]},visibility:{enums:["hidden","visible"]},zCompoundDepth:{enums:["bottom","orphan","auto","top"]},zIndexCompare:{enums:["auto","manual"]},valign:{enums:["top","center","bottom"]},halign:{enums:["left","center","right"]},justification:{enums:["left","center","right","auto"]},text:{string:!0},data:{mapping:!0,regex:e("data")},layoutData:{mapping:!0,regex:e("layoutData")},scratch:{mapping:!0,regex:e("scratch")},mapData:{mapping:!0,regex:t("mapData")},mapLayoutData:{mapping:!0,regex:t("mapLayoutData")},mapScratch:{mapping:!0,regex:t("mapScratch")},fn:{mapping:!0,fn:!0},url:{regexes:n,singleRegexMatchValue:!0},urls:{regexes:n,singleRegexMatchValue:!0,multiple:!0},propList:{propList:!0},angle:{number:!0,units:"deg|rad",implicitUnits:"rad"},textRotation:{number:!0,units:"deg|rad",implicitUnits:"rad",enums:["none","autorotate"]},polygonPointList:{number:!0,multiple:!0,evenMultiple:!0,min:-1,max:1,unitless:!0},edgeDistances:{enums:["intersection","node-position","endpoints"]},edgeEndpoint:{number:!0,multiple:!0,units:"%|px|em|deg|rad",implicitUnits:"px",enums:["inside-to-node","outside-to-node","outside-to-node-or-label","outside-to-line","outside-to-line-or-label"],singleEnum:!0,validate:function(e,t){switch(e.length){case 2:return"deg"!==t[0]&&"rad"!==t[0]&&"deg"!==t[1]&&"rad"!==t[1];case 1:return B(e[0])||"deg"===t[0]||"rad"===t[0];default:return!1}}},easing:{regexes:["^(spring)\\s*\\(\\s*("+G+")\\s*,\\s*("+G+")\\s*\\)$","^(cubic-bezier)\\s*\\(\\s*("+G+")\\s*,\\s*("+G+")\\s*,\\s*("+G+")\\s*,\\s*("+G+")\\s*\\)$"],enums:["linear","ease","ease-in","ease-out","ease-in-out","ease-in-sine","ease-out-sine","ease-in-out-sine","ease-in-quad","ease-out-quad","ease-in-out-quad","ease-in-cubic","ease-out-cubic","ease-in-out-cubic","ease-in-quart","ease-out-quart","ease-in-out-quart","ease-in-quint","ease-out-quint","ease-in-out-quint","ease-in-expo","ease-out-expo","ease-in-out-expo","ease-in-circ","ease-out-circ","ease-in-out-circ"]},gradientDirection:{enums:["to-bottom","to-top","to-left","to-right","to-bottom-right","to-bottom-left","to-top-right","to-top-left","to-right-bottom","to-left-bottom","to-right-top","to-left-top"]},boundsExpansion:{number:!0,multiple:!0,min:0,validate:function(e){var t=e.length;return 1===t||2===t||4===t}}};var r=function(e,t){return(null==e||null==t)&&e!==t||0==e&&0!=t||0!=e&&0==t},i=function(e,t){return e!=t},a=al.types,o=[{name:"label",type:a.text,triggersBounds:i,triggersZOrder:function(e,t){var n=V(e),r=V(t);return n&&!r||!n&&r}},{name:"text-rotation",type:a.textRotation,triggersBounds:i},{name:"text-margin-x",type:a.bidirectionalSize,triggersBounds:i},{name:"text-margin-y",type:a.bidirectionalSize,triggersBounds:i}],s=[{name:"source-label",type:a.text,triggersBounds:i},{name:"source-text-rotation",type:a.textRotation,triggersBounds:i},{name:"source-text-margin-x",type:a.bidirectionalSize,triggersBounds:i},{name:"source-text-margin-y",type:a.bidirectionalSize,triggersBounds:i},{name:"source-text-offset",type:a.size,triggersBounds:i}],l=[{name:"target-label",type:a.text,triggersBounds:i},{name:"target-text-rotation",type:a.textRotation,triggersBounds:i},{name:"target-text-margin-x",type:a.bidirectionalSize,triggersBounds:i},{name:"target-text-margin-y",type:a.bidirectionalSize,triggersBounds:i},{name:"target-text-offset",type:a.size,triggersBounds:i}],u=[{name:"font-family",type:a.fontFamily,triggersBounds:i},{name:"font-style",type:a.fontStyle,triggersBounds:i},{name:"font-weight",type:a.fontWeight,triggersBounds:i},{name:"font-size",type:a.size,triggersBounds:i},{name:"text-transform",type:a.textTransform,triggersBounds:i},{name:"text-wrap",type:a.textWrap,triggersBounds:i},{name:"text-overflow-wrap",type:a.textOverflowWrap,triggersBounds:i},{name:"text-max-width",type:a.size,triggersBounds:i},{name:"text-outline-width",type:a.size,triggersBounds:i},{name:"line-height",type:a.positiveNumber,triggersBounds:i}],c=[{name:"text-valign",type:a.valign,triggersBounds:i},{name:"text-halign",type:a.halign,triggersBounds:i},{name:"color",type:a.color},{name:"text-outline-color",type:a.color},{name:"text-outline-opacity",type:a.zeroOneNumber},{name:"text-background-color",type:a.color},{name:"text-background-opacity",type:a.zeroOneNumber},{name:"text-background-padding",type:a.size,triggersBounds:i},{name:"text-border-opacity",type:a.zeroOneNumber},{name:"text-border-color",type:a.color},{name:"text-border-width",type:a.size,triggersBounds:i},{name:"text-border-style",type:a.borderStyle,triggersBounds:i},{name:"text-background-shape",type:a.textBackgroundShape,triggersBounds:i},{name:"text-justification",type:a.justification},{name:"box-select-labels",type:a.bool,triggersBounds:i}],d=[{name:"events",type:a.bool,triggersZOrder:i},{name:"text-events",type:a.bool,triggersZOrder:i},{name:"box-selection",type:a.boxSelection,triggersZOrder:i}],h=[{name:"display",type:a.display,triggersZOrder:i,triggersBounds:i,triggersBoundsOfConnectedEdges:i,triggersBoundsOfParallelEdges:function(e,t,n){return e!==t&&"bezier"===n.pstyle("curve-style").value}},{name:"visibility",type:a.visibility,triggersZOrder:i},{name:"opacity",type:a.zeroOneNumber,triggersZOrder:r},{name:"text-opacity",type:a.zeroOneNumber},{name:"min-zoomed-font-size",type:a.size},{name:"z-compound-depth",type:a.zCompoundDepth,triggersZOrder:i},{name:"z-index-compare",type:a.zIndexCompare,triggersZOrder:i},{name:"z-index",type:a.number,triggersZOrder:i}],f=[{name:"overlay-padding",type:a.size,triggersBounds:i},{name:"overlay-color",type:a.color},{name:"overlay-opacity",type:a.zeroOneNumber,triggersBounds:r},{name:"overlay-shape",type:a.overlayShape,triggersBounds:i},{name:"overlay-corner-radius",type:a.cornerRadius}],p=[{name:"underlay-padding",type:a.size,triggersBounds:i},{name:"underlay-color",type:a.color},{name:"underlay-opacity",type:a.zeroOneNumber,triggersBounds:r},{name:"underlay-shape",type:a.overlayShape,triggersBounds:i},{name:"underlay-corner-radius",type:a.cornerRadius}],g=[{name:"transition-property",type:a.propList},{name:"transition-duration",type:a.time},{name:"transition-delay",type:a.time},{name:"transition-timing-function",type:a.easing}],v=function(e,t){return"label"===t.value?-e.poolIndex():t.pfValue},y=[{name:"height",type:a.nodeSize,triggersBounds:i,hashOverride:v},{name:"width",type:a.nodeSize,triggersBounds:i,hashOverride:v},{name:"shape",type:a.nodeShape,triggersBounds:i},{name:"shape-polygon-points",type:a.polygonPointList,triggersBounds:i},{name:"corner-radius",type:a.cornerRadius},{name:"background-color",type:a.color},{name:"background-fill",type:a.fill},{name:"background-opacity",type:a.zeroOneNumber},{name:"background-blacken",type:a.nOneOneNumber},{name:"background-gradient-stop-colors",type:a.colors},{name:"background-gradient-stop-positions",type:a.percentages},{name:"background-gradient-direction",type:a.gradientDirection},{name:"padding",type:a.sizeMaybePercent,triggersBounds:i},{name:"padding-relative-to",type:a.paddingRelativeTo,triggersBounds:i},{name:"bounds-expansion",type:a.boundsExpansion,triggersBounds:i}],b=[{name:"border-color",type:a.color},{name:"border-opacity",type:a.zeroOneNumber},{name:"border-width",type:a.size,triggersBounds:i},{name:"border-style",type:a.borderStyle},{name:"border-cap",type:a.lineCap},{name:"border-join",type:a.lineJoin},{name:"border-dash-pattern",type:a.numbers},{name:"border-dash-offset",type:a.number},{name:"border-position",type:a.linePosition}],x=[{name:"outline-color",type:a.color},{name:"outline-opacity",type:a.zeroOneNumber},{name:"outline-width",type:a.size,triggersBounds:i},{name:"outline-style",type:a.borderStyle},{name:"outline-offset",type:a.size,triggersBounds:i}],w=[{name:"background-image",type:a.urls},{name:"background-image-crossorigin",type:a.bgCrossOrigin},{name:"background-image-opacity",type:a.zeroOneNumbers},{name:"background-image-containment",type:a.bgContainment},{name:"background-image-smoothing",type:a.bools},{name:"background-position-x",type:a.bgPos},{name:"background-position-y",type:a.bgPos},{name:"background-width-relative-to",type:a.bgRelativeTo},{name:"background-height-relative-to",type:a.bgRelativeTo},{name:"background-repeat",type:a.bgRepeat},{name:"background-fit",type:a.bgFit},{name:"background-clip",type:a.bgClip},{name:"background-width",type:a.bgWH},{name:"background-height",type:a.bgWH},{name:"background-offset-x",type:a.bgPos},{name:"background-offset-y",type:a.bgPos}],E=[{name:"position",type:a.position,triggersBounds:i},{name:"compound-sizing-wrt-labels",type:a.compoundIncludeLabels,triggersBounds:i},{name:"min-width",type:a.size,triggersBounds:i},{name:"min-width-bias-left",type:a.sizeMaybePercent,triggersBounds:i},{name:"min-width-bias-right",type:a.sizeMaybePercent,triggersBounds:i},{name:"min-height",type:a.size,triggersBounds:i},{name:"min-height-bias-top",type:a.sizeMaybePercent,triggersBounds:i},{name:"min-height-bias-bottom",type:a.sizeMaybePercent,triggersBounds:i}],T=[{name:"line-style",type:a.lineStyle},{name:"line-color",type:a.color},{name:"line-fill",type:a.fill},{name:"line-cap",type:a.lineCap},{name:"line-opacity",type:a.zeroOneNumber},{name:"line-dash-pattern",type:a.numbers},{name:"line-dash-offset",type:a.number},{name:"line-outline-width",type:a.size},{name:"line-outline-color",type:a.color},{name:"line-gradient-stop-colors",type:a.colors},{name:"line-gradient-stop-positions",type:a.percentages},{name:"curve-style",type:a.curveStyle,triggersBounds:i,triggersBoundsOfParallelEdges:function(e,t){return e!==t&&("bezier"===e||"bezier"===t)}},{name:"haystack-radius",type:a.zeroOneNumber,triggersBounds:i},{name:"source-endpoint",type:a.edgeEndpoint,triggersBounds:i},{name:"target-endpoint",type:a.edgeEndpoint,triggersBounds:i},{name:"control-point-step-size",type:a.size,triggersBounds:i},{name:"control-point-distances",type:a.bidirectionalSizes,triggersBounds:i},{name:"control-point-weights",type:a.numbers,triggersBounds:i},{name:"segment-distances",type:a.bidirectionalSizes,triggersBounds:i},{name:"segment-weights",type:a.numbers,triggersBounds:i},{name:"segment-radii",type:a.numbers,triggersBounds:i},{name:"radius-type",type:a.radiusType,triggersBounds:i},{name:"taxi-turn",type:a.bidirectionalSizeMaybePercent,triggersBounds:i},{name:"taxi-turn-min-distance",type:a.size,triggersBounds:i},{name:"taxi-direction",type:a.axisDirection,triggersBounds:i},{name:"taxi-radius",type:a.number,triggersBounds:i},{name:"edge-distances",type:a.edgeDistances,triggersBounds:i},{name:"arrow-scale",type:a.positiveNumber,triggersBounds:i},{name:"loop-direction",type:a.angle,triggersBounds:i},{name:"loop-sweep",type:a.angle,triggersBounds:i},{name:"source-distance-from-node",type:a.size,triggersBounds:i},{name:"target-distance-from-node",type:a.size,triggersBounds:i}],C=[{name:"ghost",type:a.bool,triggersBounds:i},{name:"ghost-offset-x",type:a.bidirectionalSize,triggersBounds:i},{name:"ghost-offset-y",type:a.bidirectionalSize,triggersBounds:i},{name:"ghost-opacity",type:a.zeroOneNumber}],k=[{name:"selection-box-color",type:a.color},{name:"selection-box-opacity",type:a.zeroOneNumber},{name:"selection-box-border-color",type:a.color},{name:"selection-box-border-width",type:a.size},{name:"active-bg-color",type:a.color},{name:"active-bg-opacity",type:a.zeroOneNumber},{name:"active-bg-size",type:a.size},{name:"outside-texture-bg-color",type:a.color},{name:"outside-texture-bg-opacity",type:a.zeroOneNumber}],P=[];al.pieBackgroundN=16,P.push({name:"pie-size",type:a.sizeMaybePercent}),P.push({name:"pie-hole",type:a.sizeMaybePercent}),P.push({name:"pie-start-angle",type:a.angle});for(var S=1;S<=al.pieBackgroundN;S++)P.push({name:"pie-"+S+"-background-color",type:a.color}),P.push({name:"pie-"+S+"-background-size",type:a.percent}),P.push({name:"pie-"+S+"-background-opacity",type:a.zeroOneNumber});var D=[];al.stripeBackgroundN=16,D.push({name:"stripe-size",type:a.sizeMaybePercent}),D.push({name:"stripe-direction",type:a.axisDirectionPrimary});for(var _=1;_<=al.stripeBackgroundN;_++)D.push({name:"stripe-"+_+"-background-color",type:a.color}),D.push({name:"stripe-"+_+"-background-size",type:a.percent}),D.push({name:"stripe-"+_+"-background-opacity",type:a.zeroOneNumber});var A=[],M=al.arrowPrefixes=["source","mid-source","target","mid-target"];[{name:"arrow-shape",type:a.arrowShape,triggersBounds:i},{name:"arrow-color",type:a.color},{name:"arrow-fill",type:a.arrowFill},{name:"arrow-width",type:a.arrowWidth}].forEach(function(e){M.forEach(function(t){var n=t+"-"+e.name,r=e.type,i=e.triggersBounds;A.push({name:n,type:r,triggersBounds:i})})},{});var R=al.properties=[].concat(d,g,h,f,p,C,c,u,o,s,l,y,b,x,w,P,D,E,T,A,k),I=al.propertyGroups={behavior:d,transition:g,visibility:h,overlay:f,underlay:p,ghost:C,commonLabel:c,labelDimensions:u,mainLabel:o,sourceLabel:s,targetLabel:l,nodeBody:y,nodeBorder:b,nodeOutline:x,backgroundImage:w,pie:P,stripe:D,compound:E,edgeLine:T,edgeArrow:A,core:k},N=al.propertyGroupNames={};(al.propertyGroupKeys=Object.keys(I)).forEach(function(e){N[e]=I[e].map(function(e){return e.name}),I[e].forEach(function(t){return t.groupKey=e})});var L=al.aliases=[{name:"content",pointsTo:"label"},{name:"control-point-distance",pointsTo:"control-point-distances"},{name:"control-point-weight",pointsTo:"control-point-weights"},{name:"segment-distance",pointsTo:"segment-distances"},{name:"segment-weight",pointsTo:"segment-weights"},{name:"segment-radius",pointsTo:"segment-radii"},{name:"edge-text-rotation",pointsTo:"text-rotation"},{name:"padding-left",pointsTo:"padding"},{name:"padding-right",pointsTo:"padding"},{name:"padding-top",pointsTo:"padding"},{name:"padding-bottom",pointsTo:"padding"}];al.propertyNames=R.map(function(e){return e.name});for(var O=0;O<R.length;O++){var z=R[O];R[z.name]=z}for(var F=0;F<L.length;F++){var X=L[F],j=R[X.pointsTo],Y={name:X.name,alias:!0,pointsTo:j};R.push(Y),R[X.name]=Y}}(),al.getDefaultProperty=function(e){return this.getDefaultProperties()[e]},al.getDefaultProperties=function(){var e=this._private;if(null!=e.defaultProperties)return e.defaultProperties;for(var t=J({"selection-box-color":"#ddd","selection-box-opacity":.65,"selection-box-border-color":"#aaa","selection-box-border-width":1,"active-bg-color":"black","active-bg-opacity":.15,"active-bg-size":30,"outside-texture-bg-color":"#000","outside-texture-bg-opacity":.125,events:"yes","text-events":"no","text-valign":"top","text-halign":"center","text-justification":"auto","line-height":1,color:"#000","box-selection":"contain","text-outline-color":"#000","text-outline-width":0,"text-outline-opacity":1,"text-opacity":1,"text-decoration":"none","text-transform":"none","text-wrap":"none","text-overflow-wrap":"whitespace","text-max-width":9999,"text-background-color":"#000","text-background-opacity":0,"text-background-shape":"rectangle","text-background-padding":0,"text-border-opacity":0,"text-border-width":0,"text-border-style":"solid","text-border-color":"#000","font-family":"Helvetica Neue, Helvetica, sans-serif","font-style":"normal","font-weight":"normal","font-size":16,"min-zoomed-font-size":0,"text-rotation":"none","source-text-rotation":"none","target-text-rotation":"none",visibility:"visible",display:"element",opacity:1,"z-compound-depth":"auto","z-index-compare":"auto","z-index":0,label:"","text-margin-x":0,"text-margin-y":0,"source-label":"","source-text-offset":0,"source-text-margin-x":0,"source-text-margin-y":0,"target-label":"","target-text-offset":0,"target-text-margin-x":0,"target-text-margin-y":0,"overlay-opacity":0,"overlay-color":"#000","overlay-padding":10,"overlay-shape":"round-rectangle","overlay-corner-radius":"auto","underlay-opacity":0,"underlay-color":"#000","underlay-padding":10,"underlay-shape":"round-rectangle","underlay-corner-radius":"auto","transition-property":"none","transition-duration":0,"transition-delay":0,"transition-timing-function":"linear","box-select-labels":"no","background-blacken":0,"background-color":"#999","background-fill":"solid","background-opacity":1,"background-image":"none","background-image-crossorigin":"anonymous","background-image-opacity":1,"background-image-containment":"inside","background-image-smoothing":"yes","background-position-x":"50%","background-position-y":"50%","background-offset-x":0,"background-offset-y":0,"background-width-relative-to":"include-padding","background-height-relative-to":"include-padding","background-repeat":"no-repeat","background-fit":"none","background-clip":"node","background-width":"auto","background-height":"auto","border-color":"#000","border-opacity":1,"border-width":0,"border-style":"solid","border-dash-pattern":[4,2],"border-dash-offset":0,"border-cap":"butt","border-join":"miter","border-position":"center","outline-color":"#999","outline-opacity":1,"outline-width":0,"outline-offset":0,"outline-style":"solid",height:30,width:30,shape:"ellipse","shape-polygon-points":"-1, -1,   1, -1,   1, 1,   -1, 1","corner-radius":"auto","bounds-expansion":0,"background-gradient-direction":"to-bottom","background-gradient-stop-colors":"#999","background-gradient-stop-positions":"0%",ghost:"no","ghost-offset-y":0,"ghost-offset-x":0,"ghost-opacity":0,padding:0,"padding-relative-to":"width",position:"origin","compound-sizing-wrt-labels":"include","min-width":0,"min-width-bias-left":0,"min-width-bias-right":0,"min-height":0,"min-height-bias-top":0,"min-height-bias-bottom":0},{"pie-size":"100%","pie-hole":0,"pie-start-angle":"0deg"},[{name:"pie-{{i}}-background-color",value:"black"},{name:"pie-{{i}}-background-size",value:"0%"},{name:"pie-{{i}}-background-opacity",value:1}].reduce(function(e,t){for(var n=1;n<=al.pieBackgroundN;n++){var r=t.name.replace("{{i}}",n),i=t.value;e[r]=i}return e},{}),{"stripe-size":"100%","stripe-direction":"horizontal"},[{name:"stripe-{{i}}-background-color",value:"black"},{name:"stripe-{{i}}-background-size",value:"0%"},{name:"stripe-{{i}}-background-opacity",value:1}].reduce(function(e,t){for(var n=1;n<=al.stripeBackgroundN;n++){var r=t.name.replace("{{i}}",n),i=t.value;e[r]=i}return e},{}),{"line-style":"solid","line-color":"#999","line-fill":"solid","line-cap":"butt","line-opacity":1,"line-outline-width":0,"line-outline-color":"#000","line-gradient-stop-colors":"#999","line-gradient-stop-positions":"0%","control-point-step-size":40,"control-point-weights":.5,"segment-weights":.5,"segment-distances":20,"segment-radii":15,"radius-type":"arc-radius","taxi-turn":"50%","taxi-radius":15,"taxi-turn-min-distance":10,"taxi-direction":"auto","edge-distances":"intersection","curve-style":"haystack","haystack-radius":0,"arrow-scale":1,"loop-direction":"-45deg","loop-sweep":"-90deg","source-distance-from-node":0,"target-distance-from-node":0,"source-endpoint":"outside-to-node","target-endpoint":"outside-to-node","line-dash-pattern":[6,3],"line-dash-offset":0},[{name:"arrow-shape",value:"none"},{name:"arrow-color",value:"#999"},{name:"arrow-fill",value:"filled"},{name:"arrow-width",value:1}].reduce(function(e,t){return al.arrowPrefixes.forEach(function(n){var r=n+"-"+t.name,i=t.value;e[r]=i}),e},{})),n={},r=0;r<this.properties.length;r++){var i=this.properties[r];if(!i.pointsTo){var a=i.name,o=t[a],s=this.parse(a,o);n[a]=s}}return e.defaultProperties=n,e.defaultProperties},al.addDefaultStylesheet=function(){this.selector(":parent").css({shape:"rectangle",padding:10,"background-color":"#eee","border-color":"#ccc","border-width":1}).selector("edge").css({width:3}).selector(":loop").css({"curve-style":"bezier"}).selector("edge:compound").css({"curve-style":"bezier","source-endpoint":"outside-to-line","target-endpoint":"outside-to-line"}).selector(":selected").css({"background-color":"#0169D9","line-color":"#0169D9","source-arrow-color":"#0169D9","target-arrow-color":"#0169D9","mid-source-arrow-color":"#0169D9","mid-target-arrow-color":"#0169D9"}).selector(":parent:selected").css({"background-color":"#CCE1F9","border-color":"#aec8e5"}).selector(":active").css({"overlay-color":"black","overlay-padding":10,"overlay-opacity":.25}),this.defaultLength=this.length};var au={};au.parse=function(e,t,n,r){if(D(t))return this.parseImplWarn(e,t,n,r);var i,a=eP(e,""+t,n?"t":"f","mapping"===r||!0===r||!1===r||null==r?"dontcare":r),o=this.propCache=this.propCache||[];return(i=o[a])||(i=o[a]=this.parseImplWarn(e,t,n,r)),(n||"mapping"===r)&&(i=eV(i))&&(i.value=eV(i.value)),i},au.parseImplWarn=function(e,t,n,r){var i=this.parseImpl(e,t,n,r);return i||null==t||ez("The style property `".concat(e,": ").concat(t,"` is invalid")),i&&("width"===i.name||"height"===i.name)&&"label"===t&&ez("The style value of `label` is deprecated for `"+i.name+"`"),i},au.parseImpl=function(e,t,n,r){e=j(e);var i=this.properties[e],a=t,o=this.types;if(!i||void 0===t)return null;i.alias&&(e=(i=i.pointsTo).name);var s=B(t);s&&(t=t.trim());var l=i.type;if(!l)return null;if(n&&(""===t||null===t))return{name:e,value:t,bypass:!0,deleteBypass:!0};if(D(t))return{name:e,value:t,strValue:"fn",mapped:o.fn,bypass:n};if(!s||r||t.length<7||"a"!==t[1]);else if(t.length>=7&&"d"===t[0]&&(g=new RegExp(o.data.regex).exec(t))){if(n)return!1;var u=o.data;return{name:e,value:g,strValue:""+t,mapped:u,field:g[1],bypass:n}}else if(t.length>=10&&"m"===t[0]&&(v=new RegExp(o.mapData.regex).exec(t))){if(n||l.multiple)return!1;var c=o.mapData;if(!(l.color||l.number))return!1;var d=this.parse(e,v[4]);if(!d||d.mapped)return!1;var h=this.parse(e,v[5]);if(!h||h.mapped)return!1;if(d.pfValue===h.pfValue||d.strValue===h.strValue)return ez("`"+e+": "+t+"` is not a valid mapper because the output range is zero; converting to `"+e+": "+d.strValue+"`"),this.parse(e,d.strValue);if(l.color){var f=d.value,p=h.value;if(f[0]===p[0]&&f[1]===p[1]&&f[2]===p[2]&&(f[3]===p[3]||(null==f[3]||1===f[3])&&(null==p[3]||1===p[3])))return!1}return{name:e,value:v,strValue:""+t,mapped:c,field:v[1],fieldMin:parseFloat(v[2]),fieldMax:parseFloat(v[3]),valueMin:d.value,valueMax:h.value,bypass:n}}if(l.multiple&&"multiple"!==r){if(y=s?t.split(/\s+/):_(t)?t:[t],l.evenMultiple&&y.length%2!=0)return null;for(var g,v,y,b=[],x=[],w=[],E="",T=!1,C=0;C<y.length;C++){var k=this.parse(e,y[C],n,"multiple");T=T||B(k.value),b.push(k.value),w.push(null!=k.pfValue?k.pfValue:k.value),x.push(k.units),E+=(C>0?" ":"")+k.strValue}if(l.validate&&!l.validate(b,x))return null;if(l.singleEnum&&T)if(1===b.length&&B(b[0]))return{name:e,value:b[0],strValue:b[0],bypass:n};else return null;return{name:e,value:b,pfValue:w,strValue:E,bypass:n,units:x}}var P=function(){for(var r=0;r<l.enums.length;r++)if(l.enums[r]===t)return{name:e,value:t,strValue:""+t,bypass:n};return null};if(l.number){var S,A,R="px";if(l.units&&(A=l.units),l.implicitUnits&&(R=l.implicitUnits),!l.unitless)if(s){var I="px|em"+(l.allowPercent?"|\\%":"");A&&(I=A);var N=t.match("^("+G+")("+I+")?$");N&&(t=N[1],A=N[2]||R)}else(!A||l.implicitUnits)&&(A=R);if(isNaN(t=parseFloat(t))&&void 0===l.enums)return null;if(isNaN(t)&&void 0!==l.enums)return t=a,P();if(l.integer&&!(M(S=t)&&Math.floor(S)===S)||void 0!==l.min&&(t<l.min||l.strictMin&&t===l.min)||void 0!==l.max&&(t>l.max||l.strictMax&&t===l.max))return null;var L={name:e,value:t,strValue:""+t+(A||""),units:A,bypass:n};return l.unitless||"px"!==A&&"em"!==A?L.pfValue=t:L.pfValue="px"!==A&&A?this.getEmSizeInPixels()*t:t,("ms"===A||"s"===A)&&(L.pfValue="ms"===A?t:1e3*t),("deg"===A||"rad"===A)&&(L.pfValue="rad"===A?t:Math.PI*t/180),"%"===A&&(L.pfValue=t/100),L}if(l.propList){var O=[],z=""+t;if("none"===z);else{for(var V=z.split(/\s*,\s*|\s+/),F=0;F<V.length;F++){var X=V[F].trim();this.properties[X]?O.push(X):ez("`"+X+"` is not a valid property name")}if(0===O.length)return null}return{name:e,value:O,strValue:0===O.length?"none":O.join(" "),bypass:n}}if(l.color){var Y=er(t);return Y?{name:e,value:Y,pfValue:Y,strValue:"rgb("+Y[0]+","+Y[1]+","+Y[2]+")",bypass:n}:null}if(l.regex||l.regexes){if(l.enums){var q=P();if(q)return q}for(var W=l.regexes?l.regexes:[l.regex],U=0;U<W.length;U++){var H=new RegExp(W[U]).exec(t);if(H)return{name:e,value:l.singleRegexMatchValue?H[1]:H,strValue:""+t,bypass:n}}return null}if(l.string)return{name:e,value:""+t,strValue:""+t,bypass:n};else if(l.enums)return P();else return null};var ac=function(e){return this instanceof ac?O(e)?void(this._private={cy:e,coreStyle:{}},this.length=0,this.resetToDefault()):void eL("A style must have a core reference"):new ac(e)},ad=ac.prototype;ad.instanceString=function(){return"style"},ad.clear=function(){for(var e=this._private,t=e.cy.elements(),n=0;n<this.length;n++)this[n]=void 0;return this.length=0,e.contextStyles={},e.propDiffs={},this.cleanElements(t,!0),t.forEach(function(e){var t=e[0]._private;t.styleDirty=!0,t.appliedInitStyle=!1}),this},ad.resetToDefault=function(){return this.clear(),this.addDefaultStylesheet(),this},ad.core=function(e){return this._private.coreStyle[e]||this.getDefaultProperty(e)},ad.selector=function(e){var t="core"===e?null:new rL(e),n=this.length++;return this[n]={selector:t,properties:[],mappedProperties:[],index:n},this},ad.css=function(){var e=arguments;if(1===e.length)for(var t=e[0],n=0;n<this.properties.length;n++){var r=this.properties[n],i=t[r.name];void 0===i&&(i=t[Y(r.name)]),void 0!==i&&this.cssRule(r.name,i)}else 2===e.length&&this.cssRule(e[0],e[1]);return this},ad.style=ad.css,ad.cssRule=function(e,t){var n=this.parse(e,t);if(n){var r=this.length-1;this[r].properties.push(n),this[r].properties[n.name]=n,n.name.match(/pie-(\d+)-background-size/)&&n.value&&(this._private.hasPie=!0),n.name.match(/stripe-(\d+)-background-size/)&&n.value&&(this._private.hasStripe=!0),n.mapped&&this[r].mappedProperties.push(n),this[r].selector||(this._private.coreStyle[n.name]=n)}return this},ad.append=function(e){return z(e)?e.appendToStyle(this):_(e)?this.appendFromJson(e):B(e)&&this.appendFromString(e),this},ac.fromJson=function(e,t){var n=new ac(e);return n.fromJson(t),n},ac.fromString=function(e,t){return new ac(e).fromString(t)},[an,ar,ai,aa,ao,as,al,au].forEach(function(e){J(ad,e)}),ac.types=ad.types,ac.properties=ad.properties,ac.propertyGroups=ad.propertyGroups,ac.propertyGroupNames=ad.propertyGroupNames,ac.propertyGroupKeys=ad.propertyGroupKeys;var ah={autolock:function(e){return void 0===e?this._private.autolock:(this._private.autolock=!!e,this)},autoungrabify:function(e){return void 0===e?this._private.autoungrabify:(this._private.autoungrabify=!!e,this)},autounselectify:function(e){return void 0===e?this._private.autounselectify:(this._private.autounselectify=!!e,this)},selectionType:function(e){var t=this._private;return(null==t.selectionType&&(t.selectionType="single"),void 0===e)?t.selectionType:(("additive"===e||"single"===e)&&(t.selectionType=e),this)},panningEnabled:function(e){return void 0===e?this._private.panningEnabled:(this._private.panningEnabled=!!e,this)},userPanningEnabled:function(e){return void 0===e?this._private.userPanningEnabled:(this._private.userPanningEnabled=!!e,this)},zoomingEnabled:function(e){return void 0===e?this._private.zoomingEnabled:(this._private.zoomingEnabled=!!e,this)},userZoomingEnabled:function(e){return void 0===e?this._private.userZoomingEnabled:(this._private.userZoomingEnabled=!!e,this)},boxSelectionEnabled:function(e){return void 0===e?this._private.boxSelectionEnabled:(this._private.boxSelectionEnabled=!!e,this)},pan:function(){var e,t,n,r,i,a=arguments,o=this._private.pan;switch(a.length){case 0:return o;case 1:if(B(a[0]))return o[e=a[0]];if(A(a[0])){if(!this._private.panningEnabled)return this;r=(n=a[0]).x,i=n.y,M(r)&&(o.x=r),M(i)&&(o.y=i),this.emit("pan viewport")}break;case 2:if(!this._private.panningEnabled)return this;e=a[0],t=a[1],("x"===e||"y"===e)&&M(t)&&(o[e]=t),this.emit("pan viewport")}return this.notify("viewport"),this},panBy:function(e,t){var n,r,i,a=arguments,o=this._private.pan;if(!this._private.panningEnabled)return this;switch(a.length){case 1:A(e)&&(r=(n=a[0]).x,i=n.y,M(r)&&(o.x+=r),M(i)&&(o.y+=i),this.emit("pan viewport"));break;case 2:("x"===e||"y"===e)&&M(t)&&(o[e]+=t),this.emit("pan viewport")}return this.notify("viewport"),this},gc:function(){this.notify("gc")},fit:function(e,t){var n=this.getFitViewport(e,t);if(n){var r=this._private;r.zoom=n.zoom,r.pan=n.pan,this.emit("pan zoom viewport"),this.notify("viewport")}return this},getFitViewport:function(e,t){if(M(e)&&void 0===t&&(t=e,e=void 0),this._private.panningEnabled&&this._private.zoomingEnabled){if(B(e)){var n,r,i,a=e;e=this.$(a)}else if(A(n=e)&&M(n.x1)&&M(n.x2)&&M(n.y1)&&M(n.y2)){var o=e;(r={x1:o.x1,y1:o.y1,x2:o.x2,y2:o.y2}).w=r.x2-r.x1,r.h=r.y2-r.y1}else I(e)||(e=this.mutableElements());if(!(I(e)&&e.empty())){r=r||e.boundingBox();var s=this.width(),l=this.height();if(t=M(t)?t:0,!isNaN(s)&&!isNaN(l)&&s>0&&l>0&&!isNaN(r.w)&&!isNaN(r.h)&&r.w>0&&r.h>0){i=(i=(i=Math.min((s-2*t)/r.w,(l-2*t)/r.h))>this._private.maxZoom?this._private.maxZoom:i)<this._private.minZoom?this._private.minZoom:i;var u={x:(s-i*(r.x1+r.x2))/2,y:(l-i*(r.y1+r.y2))/2};return{zoom:i,pan:u}}}}},zoomRange:function(e,t){var n=this._private;if(null==t){var r=e;e=r.min,t=r.max}return M(e)&&M(t)&&e<=t?(n.minZoom=e,n.maxZoom=t):M(e)&&void 0===t&&e<=n.maxZoom?n.minZoom=e:M(t)&&void 0===e&&t>=n.minZoom&&(n.maxZoom=t),this},minZoom:function(e){return void 0===e?this._private.minZoom:this.zoomRange({min:e})},maxZoom:function(e){return void 0===e?this._private.maxZoom:this.zoomRange({max:e})},getZoomedViewport:function(e){var t,n,r=this._private,i=r.pan,a=r.zoom,o=!1;if(r.zoomingEnabled||(o=!0),M(e)?n=e:A(e)&&(n=e.level,null!=e.position?t=tn(e.position,a,i):null!=e.renderedPosition&&(t=e.renderedPosition),null==t||r.panningEnabled||(o=!0)),n=(n=n>r.maxZoom?r.maxZoom:n)<r.minZoom?r.minZoom:n,o||!M(n)||n===a||null!=t&&(!M(t.x)||!M(t.y)))return null;if(null==t)return{zoomed:!0,panned:!1,zoom:n,pan:i};var s=n,l={x:-s/a*(t.x-i.x)+t.x,y:-s/a*(t.y-i.y)+t.y};return{zoomed:!0,panned:!0,zoom:s,pan:l}},zoom:function(e){if(void 0===e)return this._private.zoom;var t=this.getZoomedViewport(e),n=this._private;return null!=t&&t.zoomed&&(n.zoom=t.zoom,t.panned&&(n.pan.x=t.pan.x,n.pan.y=t.pan.y),this.emit("zoom"+(t.panned?" pan":"")+" viewport"),this.notify("viewport")),this},viewport:function(e){var t=this._private,n=!0,r=!0,i=[],a=!1,o=!1;if(!e||(M(e.zoom)||(n=!1),A(e.pan)||(r=!1),!n&&!r))return this;if(n){var s=e.zoom;s<t.minZoom||s>t.maxZoom||!t.zoomingEnabled?a=!0:(t.zoom=s,i.push("zoom"))}if(r&&(!a||!e.cancelOnFailedZoom)&&t.panningEnabled){var l=e.pan;M(l.x)&&(t.pan.x=l.x,o=!1),M(l.y)&&(t.pan.y=l.y,o=!1),o||i.push("pan")}return i.length>0&&(i.push("viewport"),this.emit(i.join(" ")),this.notify("viewport")),this},center:function(e){var t=this.getCenterPan(e);return t&&(this._private.pan=t,this.emit("pan viewport"),this.notify("viewport")),this},getCenterPan:function(e,t){if(this._private.panningEnabled){if(B(e)){var n=e;e=this.mutableElements().filter(n)}else I(e)||(e=this.mutableElements());if(0!==e.length){var r=e.boundingBox(),i=this.width(),a=this.height();return{x:(i-(t=void 0===t?this._private.zoom:t)*(r.x1+r.x2))/2,y:(a-t*(r.y1+r.y2))/2}}}},reset:function(){return this._private.panningEnabled&&this._private.zoomingEnabled&&this.viewport({pan:{x:0,y:0},zoom:1}),this},invalidateSize:function(){this._private.sizeCache=null},size:function(){var e,t,n=this._private,r=n.container;return n.sizeCache=n.sizeCache||(r?(e=this.window().getComputedStyle(r),t=function(t){return parseFloat(e.getPropertyValue(t))},{width:r.clientWidth-t("padding-left")-t("padding-right"),height:r.clientHeight-t("padding-top")-t("padding-bottom")}):{width:1,height:1})},width:function(){return this.size().width},height:function(){return this.size().height},extent:function(){var e=this._private.pan,t=this._private.zoom,n=this.renderedExtent(),r={x1:(n.x1-e.x)/t,x2:(n.x2-e.x)/t,y1:(n.y1-e.y)/t,y2:(n.y2-e.y)/t};return r.w=r.x2-r.x1,r.h=r.y2-r.y1,r},renderedExtent:function(){var e=this.width(),t=this.height();return{x1:0,y1:0,x2:e,y2:t,w:e,h:t}},multiClickDebounceTime:function(e){return e?(this._private.multiClickDebounceTime=e,this):this._private.multiClickDebounceTime}};ah.centre=ah.center,ah.autolockNodes=ah.autolock,ah.autoungrabifyNodes=ah.autoungrabify;var af={data:ry.data({field:"data",bindingEvent:"data",allowBinding:!0,allowSetting:!0,settingEvent:"data",settingTriggersEvent:!0,triggerFnName:"trigger",allowGetting:!0,updateStyle:!0}),removeData:ry.removeData({field:"data",event:"data",triggerFnName:"trigger",triggerEvent:!0,updateStyle:!0}),scratch:ry.data({field:"scratch",bindingEvent:"scratch",allowBinding:!0,allowSetting:!0,settingEvent:"scratch",settingTriggersEvent:!0,triggerFnName:"trigger",allowGetting:!0,updateStyle:!0}),removeScratch:ry.removeData({field:"scratch",event:"scratch",triggerFnName:"trigger",triggerEvent:!0,updateStyle:!0})};af.attr=af.data,af.removeAttr=af.removeData;var ap=function(e){var t=this,n=(e=J({},e)).container;n&&!R(n)&&R(n[0])&&(n=n[0]);var r=n?n._cyreg:null;(r=r||{}).cy&&(r.cy.destroy(),r={});var i=r.readies=r.readies||[];n&&(n._cyreg=r),r.cy=t;var a=void 0!==w&&void 0!==n&&!e.headless,o=e;o.layout=J({name:a?"grid":"null"},o.layout),o.renderer=J({name:a?"canvas":"null"},o.renderer);var s=function(e,t,n){return void 0!==t?t:void 0!==n?n:e},l=this._private={container:n,ready:!1,options:o,elements:new iG(this),listeners:[],aniEles:new iG(this),data:o.data||{},scratch:{},layout:null,renderer:null,destroyed:!1,notificationsEnabled:!0,minZoom:1e-50,maxZoom:1e50,zoomingEnabled:s(!0,o.zoomingEnabled),userZoomingEnabled:s(!0,o.userZoomingEnabled),panningEnabled:s(!0,o.panningEnabled),userPanningEnabled:s(!0,o.userPanningEnabled),boxSelectionEnabled:s(!0,o.boxSelectionEnabled),autolock:s(!1,o.autolock,o.autolockNodes),autoungrabify:s(!1,o.autoungrabify,o.autoungrabifyNodes),autounselectify:s(!1,o.autounselectify),styleEnabled:void 0===o.styleEnabled?a:o.styleEnabled,zoom:M(o.zoom)?o.zoom:1,pan:{x:A(o.pan)&&M(o.pan.x)?o.pan.x:0,y:A(o.pan)&&M(o.pan.y)?o.pan.y:0},animation:{current:[],queue:[]},hasCompoundNodes:!1,multiClickDebounceTime:s(250,o.multiClickDebounceTime)};this.createEmitter(),this.selectionType(o.selectionType),this.zoomRange({min:o.minZoom,max:o.maxZoom}),l.styleEnabled&&t.setStyle([]);var u=J({},o,o.renderer);t.initRenderer(u);var c=function(e,n,r){t.notifications(!1);var i=t.mutableElements();i.length>0&&i.remove(),null!=e&&(A(e)||_(e))&&t.add(e),t.one("layoutready",function(e){t.notifications(!0),t.emit(e),t.one("load",n),t.emitAndNotify("load")}).one("layoutstop",function(){t.one("done",r),t.emit("done")});var a=J({},t._private.options.layout);a.eles=t.elements(),t.layout(a).run()};!function(e,t){if(e.some(F))return re.all(e).then(t);t(e)}([o.style,o.elements],function(e){var n=e[0],a=e[1];l.styleEnabled&&t.style().append(n),c(a,function(){t.startAnimationLoop(),l.ready=!0,D(o.ready)&&t.on("ready",o.ready);for(var e=0;e<i.length;e++){var n=i[e];t.on("ready",n)}r&&(r.readies=[]),t.emit("ready")},o.done)})},ag=ap.prototype;J(ag,{instanceString:function(){return"core"},isReady:function(){return this._private.ready},destroyed:function(){return this._private.destroyed},ready:function(e){return this.isReady()?this.emitter().emit("ready",[],e):this.on("ready",e),this},destroy:function(){if(!this.destroyed())return this.stopAnimationLoop(),this.destroyRenderer(),this.emit("destroy"),this._private.destroyed=!0,this},hasElementWithId:function(e){return this._private.elements.hasElementWithId(e)},getElementById:function(e){return this._private.elements.getElementById(e)},hasCompoundNodes:function(){return this._private.hasCompoundNodes},headless:function(){return this._private.renderer.isHeadless()},styleEnabled:function(){return this._private.styleEnabled},addToPool:function(e){return this._private.elements.merge(e),this},removeFromPool:function(e){return this._private.elements.unmerge(e),this},container:function(){return this._private.container||null},window:function(){if(null==this._private.container)return w;var e=this._private.container.ownerDocument;return void 0===e||null==e?w:e.defaultView||w},mount:function(e){if(null!=e){var t=this._private,n=t.options;return!R(e)&&R(e[0])&&(e=e[0]),this.stopAnimationLoop(),this.destroyRenderer(),t.container=e,t.styleEnabled=!0,this.invalidateSize(),this.initRenderer(J({},n,n.renderer,{name:"null"===n.renderer.name?"canvas":n.renderer.name})),this.startAnimationLoop(),this.style(n.style),this.emit("mount"),this}},unmount:function(){return this.stopAnimationLoop(),this.destroyRenderer(),this.initRenderer({name:"null"}),this.emit("unmount"),this},options:function(){return eV(this._private.options)},json:function(e){var t=this,n=t._private,r=t.mutableElements();if(A(e)){if(t.startBatch(),e.elements){var i={},a=function(e,n){for(var r=[],a=[],o=0;o<e.length;o++){var s=e[o];if(!s.data.id){ez("cy.json() cannot handle elements without an ID attribute");continue}var l=""+s.data.id,u=t.getElementById(l);i[l]=!0,0!==u.length?a.push({ele:u,json:s}):(n&&(s.group=n),r.push(s))}t.add(r);for(var c=0;c<a.length;c++){var d=a[c],h=d.ele,f=d.json;h.json(f)}};if(_(e.elements))a(e.elements);else for(var o=["nodes","edges"],s=0;s<o.length;s++){var l=o[s],u=e.elements[l];_(u)&&a(u,l)}var c=t.collection();r.filter(function(e){return!i[e.id()]}).forEach(function(e){e.isParent()?c.merge(e):e.remove()}),c.forEach(function(e){return e.children().move({parent:null})}),c.forEach(function(e){return t.getElementById(e.id()).remove()})}e.style&&t.style(e.style),null!=e.zoom&&e.zoom!==n.zoom&&t.zoom(e.zoom),e.pan&&(e.pan.x!==n.pan.x||e.pan.y!==n.pan.y)&&t.pan(e.pan),e.data&&t.data(e.data);for(var d=["minZoom","maxZoom","zoomingEnabled","userZoomingEnabled","panningEnabled","userPanningEnabled","boxSelectionEnabled","autolock","autoungrabify","autounselectify","multiClickDebounceTime"],h=0;h<d.length;h++){var f=d[h];null!=e[f]&&t[f](e[f])}return t.endBatch(),this}var p={};e?p.elements=this.elements().map(function(e){return e.json()}):(p.elements={},r.forEach(function(e){var t=e.group();p.elements[t]||(p.elements[t]=[]),p.elements[t].push(e.json())})),this._private.styleEnabled&&(p.style=t.style().json()),p.data=eV(t.data());var g=n.options;return p.zoomingEnabled=n.zoomingEnabled,p.userZoomingEnabled=n.userZoomingEnabled,p.zoom=n.zoom,p.minZoom=n.minZoom,p.maxZoom=n.maxZoom,p.panningEnabled=n.panningEnabled,p.userPanningEnabled=n.userPanningEnabled,p.pan=eV(n.pan),p.boxSelectionEnabled=n.boxSelectionEnabled,p.renderer=eV(g.renderer),p.hideEdgesOnViewport=g.hideEdgesOnViewport,p.textureOnViewport=g.textureOnViewport,p.wheelSensitivity=g.wheelSensitivity,p.motionBlur=g.motionBlur,p.multiClickDebounceTime=g.multiClickDebounceTime,p}}),ag.$id=ag.getElementById,[{add:function(e){var t;if(I(e))if(e._private.cy===this)t=e.restore();else{for(var n=[],r=0;r<e.length;r++){var i=e[r];n.push(i.json())}t=new iG(this,n)}else if(_(e))t=new iG(this,e);else if(A(e)&&(_(e.nodes)||_(e.edges))){for(var a=[],o=["nodes","edges"],s=0,l=o.length;s<l;s++){var u=o[s],c=e[u];if(_(c))for(var d=0,h=c.length;d<h;d++){var f=J({group:u},c[d]);a.push(f)}}t=new iG(this,a)}else t=new eJ(this,e).collection();return t},remove:function(e){if(I(e));else if(B(e)){var t=e;e=this.$(t)}return e.remove()}},i5,i9,i6,i8,{notify:function(e,t){var n=this._private;if(this.batching()){n.batchNotifications=n.batchNotifications||{};var r=n.batchNotifications[e]=n.batchNotifications[e]||this.collection();null!=t&&r.merge(t);return}if(n.notificationsEnabled){var i=this.renderer();!this.destroyed()&&i&&i.notify(e,t)}},notifications:function(e){var t=this._private;return void 0===e?t.notificationsEnabled:(t.notificationsEnabled=!!e,this)},noNotifications:function(e){this.notifications(!1),e(),this.notifications(!0)},batching:function(){return this._private.batchCount>0},startBatch:function(){var e=this._private;return null==e.batchCount&&(e.batchCount=0),0===e.batchCount&&(e.batchStyleEles=this.collection(),e.batchNotifications={}),e.batchCount++,this},endBatch:function(){var e=this._private;if(0===e.batchCount)return this;if(e.batchCount--,0===e.batchCount){e.batchStyleEles.updateStyle();var t=this.renderer();Object.keys(e.batchNotifications).forEach(function(n){var r=e.batchNotifications[n];r.empty()?t.notify(n):t.notify(n,r)})}return this},batch:function(e){return this.startBatch(),e(),this.endBatch(),this},batchData:function(e){var t=this;return this.batch(function(){for(var n=Object.keys(e),r=0;r<n.length;r++){var i=n[r],a=e[i];t.getElementById(i).data(a)}})}},ae,at,{style:function(e){return e&&this.setStyle(e).update(),this._private.style},setStyle:function(e){var t=this._private;return z(e)?t.style=e.generateStyle(this):_(e)?t.style=ac.fromJson(this,e):B(e)?t.style=ac.fromString(this,e):t.style=ac(this),t.style},updateStyle:function(){this.mutableElements().updateStyle()}},ah,af].forEach(function(e){J(ag,e)});var av={fit:!0,directed:!1,direction:"downward",padding:30,circle:!1,grid:!1,spacingFactor:1.75,boundingBox:void 0,avoidOverlap:!0,nodeDimensionsIncludeLabels:!1,roots:void 0,depthSort:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,t){return!0},ready:void 0,stop:void 0,transform:function(e,t){return t}},ay={maximal:!1,acyclic:!1},am=function(e){return e.scratch("breadthfirst")},ab=function(e,t){return e.scratch("breadthfirst",t)};function ax(e){this.options=J({},av,ay,e)}ax.prototype.run=function(){var e,t=this.options,n=t.cy,r=t.eles,i=r.nodes().filter(function(e){return e.isChildless()}),a=t.directed,o=t.acyclic||t.maximal||t.maximalAdjustments>0,s=!!t.boundingBox,l=tb(s?t.boundingBox:structuredClone(n.extent()));if(I(t.roots))e=t.roots;else if(_(t.roots)){for(var u=[],c=0;c<t.roots.length;c++){var d=t.roots[c],h=n.getElementById(d);u.push(h)}e=n.collection(u)}else if(B(t.roots))e=n.$(t.roots);else if(a)e=i.roots();else{var f=r.components();e=n.collection();for(var p=0;p<f.length;p++)!function(){var t=f[p],n=t.maxDegree(!1),r=t.filter(function(e){return e.degree(!1)===n});e=e.add(r)}()}var g=[],v={},y=function(e,t){null==g[t]&&(g[t]=[]);var n=g[t].length;g[t].push(e),ab(e,{index:n,depth:t})},b=function(e,t){var n=am(e),r=n.depth,i=n.index;g[r][i]=null,e.isChildless()&&y(e,t)};r.bfs({roots:e,directed:t.directed,visit:function(e,t,n,r,i){var a=e[0],o=a.id();a.isChildless()&&y(a,i),v[o]=!0}});for(var x=[],w=0;w<i.length;w++){var E=i[w];v[E.id()]||x.push(E)}var T=function(e){for(var t=g[e],n=0;n<t.length;n++){var r=t[n];if(null==r){t.splice(n,1),n--;continue}ab(r,{depth:e,index:n})}};if(a&&o){var C=[],k={},P=function(e){return C.push(e)};for(i.forEach(function(e){return C.push(e)});C.length>0;){var S=C.shift(),D=function(e,n){for(var i=am(e),a=e.incomers().filter(function(e){return e.isNode()&&r.has(e)}),o=-1,s=e.id(),l=0;l<a.length;l++)o=Math.max(o,am(a[l]).depth);if(i.depth<=o){if(!t.acyclic&&n[s])return null;var u=o+1;return b(e,u),n[s]=u,!0}return!1}(S,k);if(D)S.outgoers().filter(function(e){return e.isNode()&&r.has(e)}).forEach(P);else if(null===D){ez("Detected double maximal shift for node `"+S.id()+"`.  Bailing maximal adjustment due to cycle.  Use `options.maximal: true` only on DAGs.");break}}}var A=0;if(t.avoidOverlap)for(var M=0;M<i.length;M++){var R=i[M].layoutDimensions(t),N=R.w,L=R.h;A=Math.max(A,N,L)}var O={},z=function(e){if(O[e.id()])return O[e.id()];for(var t=am(e).depth,n=e.neighborhood(),r=0,a=0,o=0;o<n.length;o++){var s=n[o];if(!(s.isEdge()||s.isParent())&&i.has(s)){var l=am(s);if(null!=l){var u=l.index,c=l.depth;if(null!=u&&null!=c){var d=g[c].length;c<t&&(r+=u/d,a++)}}}}return r/=a=Math.max(1,a),0===a&&(r=0),O[e.id()]=r,r},V=function(e,t){var n=z(e)-z(t);return 0===n?Q(e.id(),t.id()):n};void 0!==t.depthSort&&(V=t.depthSort);for(var F=g.length,X=0;X<F;X++)g[X].sort(V),T(X);for(var j=[],Y=0;Y<x.length;Y++)j.push(x[Y]);if(j.length){g.unshift(j),F=g.length;for(var q=0;q<F;q++)T(q)}for(var W=0,U=0;U<F;U++)W=Math.max(g[U].length,W);var G={x:l.x1+l.w/2,y:l.y1+l.h/2},H=i.reduce(function(e,n){var r;return r=n.boundingBox({includeLabels:t.nodeDimensionsIncludeLabels}),{w:-1===e.w?r.w:(e.w+r.w)/2,h:-1===e.h?r.h:(e.h+r.h)/2}},{w:-1,h:-1}),K=Math.max(1===F?0:s?(l.h-2*t.padding-H.h)/(F-1):(l.h-2*t.padding-H.h)/(F+1),A),Z=g.reduce(function(e,t){return Math.max(e,t.length)},0),$=function(e){var n=am(e),r=n.depth,i=n.index;if(t.circle){var a=Math.min(l.w/2/F,l.h/2/F),o=(a=Math.max(a,A))*r+a-(F>0&&g[0].length<=3?a/2:0),u=2*Math.PI/g[r].length*i;return 0===r&&1===g[0].length&&(o=1),{x:G.x+o*Math.cos(u),y:G.y+o*Math.sin(u)}}var c=g[r].length,d=Math.max(1===c?0:s?(l.w-2*t.padding-H.w)/((t.grid?Z:c)-1):(l.w-2*t.padding-H.w)/((t.grid?Z:c)+1),A);return{x:G.x+(i+1-(c+1)/2)*d,y:G.y+(r+1-(F+1)/2)*K}},J={downward:0,leftward:90,upward:180,rightward:-90};return -1===Object.keys(J).indexOf(t.direction)&&eL("Invalid direction '".concat(t.direction,"' specified for breadthfirst layout. Valid values are: ").concat(Object.keys(J).join(", "))),r.nodes().layoutPositions(this,t,function(e){return function(e,t,n){if(0===n)return e;var r,i,a,o,s=(t.x1+t.x2)/2,l=(t.y1+t.y2)/2,u=t.w/t.h,c=(r=e.x,i=e.y,{x:Math.cos(a=n*Math.PI/180)*(r-s)-Math.sin(a)*(i-l)+s,y:Math.sin(a)*(r-s)+Math.cos(a)*(i-l)+l}),d=(o=c.x,{x:(o-s)*u+s,y:(c.y-l)*(1/u)+l});return{x:d.x,y:d.y}}($(e),l,J[t.direction])}),this};var aw={fit:!0,padding:30,boundingBox:void 0,avoidOverlap:!0,nodeDimensionsIncludeLabels:!1,spacingFactor:void 0,radius:void 0,startAngle:1.5*Math.PI,sweep:void 0,clockwise:!0,sort:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,t){return!0},ready:void 0,stop:void 0,transform:function(e,t){return t}};function aE(e){this.options=J({},aw,e)}aE.prototype.run=function(){var e,t=this.options,n=t.cy,r=t.eles,i=void 0!==t.counterclockwise?!t.counterclockwise:t.clockwise,a=r.nodes().not(":parent");t.sort&&(a=a.sort(t.sort));for(var o=tb(t.boundingBox?t.boundingBox:{x1:0,y1:0,w:n.width(),h:n.height()}),s={x:o.x1+o.w/2,y:o.y1+o.h/2},l=(void 0===t.sweep?2*Math.PI-2*Math.PI/a.length:t.sweep)/Math.max(1,a.length-1),u=0,c=0;c<a.length;c++){var d=a[c].layoutDimensions(t);u=Math.max(u,d.w,d.h)}if(e=M(t.radius)?t.radius:a.length<=1?0:Math.min(o.h,o.w)/2-u,a.length>1&&t.avoidOverlap){var h=Math.cos(l)-1,f=Math.sin(l)-0;e=Math.max(Math.sqrt((u*=1.75)*u/(h*h+f*f)),e)}return r.nodes().layoutPositions(this,t,function(n,r){var a=t.startAngle+r*l*(i?1:-1),o=e*Math.cos(a),u=e*Math.sin(a);return{x:s.x+o,y:s.y+u}}),this};var aT={fit:!0,padding:30,startAngle:1.5*Math.PI,sweep:void 0,clockwise:!0,equidistant:!1,minNodeSpacing:10,boundingBox:void 0,avoidOverlap:!0,nodeDimensionsIncludeLabels:!1,height:void 0,width:void 0,spacingFactor:void 0,concentric:function(e){return e.degree()},levelWidth:function(e){return e.maxDegree()/4},animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,t){return!0},ready:void 0,stop:void 0,transform:function(e,t){return t}};function aC(e){this.options=J({},aT,e)}aC.prototype.run=function(){for(var e=this.options,t=void 0!==e.counterclockwise?!e.counterclockwise:e.clockwise,n=e.cy,r=e.eles,i=r.nodes().not(":parent"),a=tb(e.boundingBox?e.boundingBox:{x1:0,y1:0,w:n.width(),h:n.height()}),o={x:a.x1+a.w/2,y:a.y1+a.h/2},s=[],l=0,u=0;u<i.length;u++){var c=i[u],d=void 0;d=e.concentric(c),s.push({value:d,node:c}),c._private.scratch.concentric=d}i.updateStyle();for(var h=0;h<i.length;h++){var f=i[h].layoutDimensions(e);l=Math.max(l,f.w,f.h)}s.sort(function(e,t){return t.value-e.value});for(var p=e.levelWidth(i),g=[[]],v=g[0],y=0;y<s.length;y++){var b=s[y];v.length>0&&Math.abs(v[0].value-b.value)>=p&&(v=[],g.push(v)),v.push(b)}var x=l+e.minNodeSpacing;if(!e.avoidOverlap){var w=g.length>0&&g[0].length>1,E=(Math.min(a.w,a.h)/2-x)/(g.length+w?1:0);x=Math.min(x,E)}for(var T=0,C=0;C<g.length;C++){var k=g[C],P=void 0===e.sweep?2*Math.PI-2*Math.PI/k.length:e.sweep,S=k.dTheta=P/Math.max(1,k.length-1);if(k.length>1&&e.avoidOverlap){var B=Math.cos(S)-1,D=Math.sin(S)-0;T=Math.max(Math.sqrt(x*x/(B*B+D*D)),T)}k.r=T,T+=x}if(e.equidistant){for(var _=0,A=0,M=0;M<g.length;M++)_=Math.max(_,g[M].r-A);A=0;for(var R=0;R<g.length;R++){var I=g[R];0===R&&(A=I.r),I.r=A,A+=_}}for(var N={},L=0;L<g.length;L++)for(var O=g[L],z=O.dTheta,V=O.r,F=0;F<O.length;F++){var X=O[F],j=e.startAngle+(t?1:-1)*z*F,Y={x:o.x+V*Math.cos(j),y:o.y+V*Math.sin(j)};N[X.node.id()]=Y}return r.nodes().layoutPositions(this,e,function(e){return N[e.id()]}),this};var ak={ready:function(){},stop:function(){},animate:!0,animationEasing:void 0,animationDuration:void 0,animateFilter:function(e,t){return!0},animationThreshold:250,refresh:20,fit:!0,padding:30,boundingBox:void 0,nodeDimensionsIncludeLabels:!1,randomize:!1,componentSpacing:40,nodeRepulsion:function(e){return 2048},nodeOverlap:4,idealEdgeLength:function(e){return 32},edgeElasticity:function(e){return 32},nestingFactor:1.2,gravity:1,numIter:1e3,initialTemp:1e3,coolingFactor:.99,minTemp:1};function aP(e){this.options=J({},ak,e),this.options.layout=this;var t=this.options.eles.nodes(),n=this.options.eles.edges().filter(function(e){var n=e.source().data("id"),r=e.target().data("id"),i=t.some(function(e){return e.data("id")===n}),a=t.some(function(e){return e.data("id")===r});return!i||!a});this.options.eles=this.options.eles.not(n)}aP.prototype.run=function(){var e=this.options,t=e.cy,n=this;n.stopped=!1,(!0===e.animate||!1===e.animate)&&n.emit({type:"layoutstart",layout:n}),ld=!0===e.debug;var r=aS(t,n,e);ld&&lh(r),e.randomize&&a_(r);var i=ev(),a=function(){aM(r,t,e),!0===e.fit&&t.fit(e.padding)},o=function(t){return!n.stopped&&!(t>=e.numIter)&&(aR(r,e),r.temperature=r.temperature*e.coolingFactor,!(r.temperature<e.minTemp))},s=function(){if(!0===e.animate||!1===e.animate)a(),n.one("layoutstop",e.stop),n.emit({type:"layoutstop",layout:n});else{var t=e.eles.nodes(),i=aA(r,e,t);t.layoutPositions(n,e,i)}},l=0,u=!0;if(!0===e.animate){var c=function(){for(var t=0;u&&t<e.refresh;)u=o(l),l++,t++;u?(ev()-i>=e.animationThreshold&&a(),em(c)):(aW(r,e),s())};c()}else{for(;u;)u=o(l),l++;aW(r,e),s()}return this},aP.prototype.stop=function(){return this.stopped=!0,this.thread&&this.thread.stop(),this.emit("layoutstop"),this},aP.prototype.destroy=function(){return this.thread&&this.thread.stop(),this};var aS=function(e,t,n){for(var r=n.eles.edges(),i=n.eles.nodes(),a=tb(n.boundingBox?n.boundingBox:{x1:0,y1:0,w:e.width(),h:e.height()}),o={isCompound:e.hasCompoundNodes(),layoutNodes:[],idToIndex:{},nodeSize:i.size(),graphSet:[],indexToGraph:[],layoutEdges:[],edgeSize:r.size(),temperature:n.initialTemp,clientWidth:a.w,clientHeight:a.h,boundingBox:a},s=n.eles.components(),l={},u=0;u<s.length;u++)for(var c=s[u],d=0;d<c.length;d++){var h=c[d];l[h.id()]=u}for(var u=0;u<o.nodeSize;u++){var f=i[u],p=f.layoutDimensions(n),g={};g.isLocked=f.locked(),g.id=f.data("id"),g.parentId=f.data("parent"),g.cmptId=l[f.id()],g.children=[],g.positionX=f.position("x"),g.positionY=f.position("y"),g.offsetX=0,g.offsetY=0,g.height=p.w,g.width=p.h,g.maxX=g.positionX+g.width/2,g.minX=g.positionX-g.width/2,g.maxY=g.positionY+g.height/2,g.minY=g.positionY-g.height/2,g.padLeft=parseFloat(f.style("padding")),g.padRight=parseFloat(f.style("padding")),g.padTop=parseFloat(f.style("padding")),g.padBottom=parseFloat(f.style("padding")),g.nodeRepulsion=D(n.nodeRepulsion)?n.nodeRepulsion(f):n.nodeRepulsion,o.layoutNodes.push(g),o.idToIndex[g.id]=u}for(var v=[],y=0,b=-1,x=[],u=0;u<o.nodeSize;u++){var f=o.layoutNodes[u],w=f.parentId;null!=w?o.layoutNodes[o.idToIndex[w]].children.push(f.id):(v[++b]=f.id,x.push(f.id))}for(o.graphSet.push(x);y<=b;){var E=v[y++],T=o.idToIndex[E],h=o.layoutNodes[T],C=h.children;if(C.length>0){o.graphSet.push(C);for(var u=0;u<C.length;u++)v[++b]=C[u]}}for(var u=0;u<o.graphSet.length;u++)for(var k=o.graphSet[u],d=0;d<k.length;d++){var P=o.idToIndex[k[d]];o.indexToGraph[P]=u}for(var u=0;u<o.edgeSize;u++){var S=r[u],B={};B.id=S.data("id"),B.sourceId=S.data("source"),B.targetId=S.data("target");var _=D(n.idealEdgeLength)?n.idealEdgeLength(S):n.idealEdgeLength,A=D(n.edgeElasticity)?n.edgeElasticity(S):n.edgeElasticity,M=o.idToIndex[B.sourceId],R=o.idToIndex[B.targetId];if(o.indexToGraph[M]!=o.indexToGraph[R]){for(var I=aB(B.sourceId,B.targetId,o),N=o.graphSet[I],L=0,g=o.layoutNodes[M];-1===N.indexOf(g.id);)g=o.layoutNodes[o.idToIndex[g.parentId]],L++;for(g=o.layoutNodes[R];-1===N.indexOf(g.id);)g=o.layoutNodes[o.idToIndex[g.parentId]],L++;_*=L*n.nestingFactor}B.idealLength=_,B.elasticity=A,o.layoutEdges.push(B)}return o},aB=function(e,t,n){var r=aD(e,t,0,n);return 2>r.count?0:r.graph},aD=function(e,t,n,r){var i=r.graphSet[n];if(-1<i.indexOf(e)&&-1<i.indexOf(t))return{count:2,graph:n};for(var a=0,o=0;o<i.length;o++){var s=i[o],l=r.idToIndex[s],u=r.layoutNodes[l].children;if(0!==u.length){var c=aD(e,t,r.indexToGraph[r.idToIndex[u[0]]],r);if(0===c.count)continue;if(1!==c.count)return c;if(2==++a)break}}return{count:a,graph:n}},a_=function(e,t){for(var n=e.clientWidth,r=e.clientHeight,i=0;i<e.nodeSize;i++){var a=e.layoutNodes[i];0!==a.children.length||a.isLocked||(a.positionX=Math.random()*n,a.positionY=Math.random()*r)}},aA=function(e,t,n){var r=e.boundingBox,i={x1:1/0,x2:-1/0,y1:1/0,y2:-1/0};return t.boundingBox&&(n.forEach(function(t){var n=e.layoutNodes[e.idToIndex[t.data("id")]];i.x1=Math.min(i.x1,n.positionX),i.x2=Math.max(i.x2,n.positionX),i.y1=Math.min(i.y1,n.positionY),i.y2=Math.max(i.y2,n.positionY)}),i.w=i.x2-i.x1,i.h=i.y2-i.y1),function(n,a){var o=e.layoutNodes[e.idToIndex[n.data("id")]];if(!t.boundingBox)return{x:o.positionX,y:o.positionY};var s=0===i.w?.5:(o.positionX-i.x1)/i.w,l=0===i.h?.5:(o.positionY-i.y1)/i.h;return{x:r.x1+s*r.w,y:r.y1+l*r.h}}},aM=function(e,t,n){var r=n.layout,i=n.eles.nodes(),a=aA(e,n,i);i.positions(a),!0!==e.ready&&(e.ready=!0,r.one("layoutready",n.ready),r.emit({type:"layoutready",layout:this}))},aR=function(e,t,n){aI(e,t),aV(e),aF(e,t),aX(e),aj(e)},aI=function(e,t){for(var n=0;n<e.graphSet.length;n++)for(var r=e.graphSet[n],i=r.length,a=0;a<i;a++)for(var o=e.layoutNodes[e.idToIndex[r[a]]],s=a+1;s<i;s++)aL(o,e.layoutNodes[e.idToIndex[r[s]]],e,t)},aN=function(e){return -1+2*e*Math.random()},aL=function(e,t,n,r){if(e.cmptId===t.cmptId||n.isCompound){var i=t.positionX-e.positionX,a=t.positionY-e.positionY;0===i&&0===a&&(i=aN(1),a=aN(1));var o=aO(e,t,i,a);if(o>0)var s=r.nodeOverlap*o,l=Math.sqrt(i*i+a*a),u=s*i/l,c=s*a/l;else var d=az(e,i,a),h=az(t,-1*i,-1*a),f=h.x-d.x,p=h.y-d.y,g=f*f+p*p,l=Math.sqrt(g),s=(e.nodeRepulsion+t.nodeRepulsion)/g,u=s*f/l,c=s*p/l;e.isLocked||(e.offsetX-=u,e.offsetY-=c),t.isLocked||(t.offsetX+=u,t.offsetY+=c)}},aO=function(e,t,n,r){if(n>0)var i=e.maxX-t.minX;else var i=t.maxX-e.minX;if(r>0)var a=e.maxY-t.minY;else var a=t.maxY-e.minY;return i>=0&&a>=0?Math.sqrt(i*i+a*a):0},az=function(e,t,n){var r=e.positionX,i=e.positionY,a=e.height||1,o=e.width||1,s=n/t,l=a/o,u={};return 0===t&&0<n||0===t&&0>n?(u.x=r,u.y=i+a/2):0<t&&-1*l<=s&&s<=l?(u.x=r+o/2,u.y=i+o*n/2/t):0>t&&-1*l<=s&&s<=l?(u.x=r-o/2,u.y=i-o*n/2/t):0<n&&(s<=-1*l||s>=l)?(u.x=r+a*t/2/n,u.y=i+a/2):0>n&&(s<=-1*l||s>=l)&&(u.x=r-a*t/2/n,u.y=i-a/2),u},aV=function(e,t){for(var n=0;n<e.edgeSize;n++){var r=e.layoutEdges[n],i=e.idToIndex[r.sourceId],a=e.layoutNodes[i],o=e.idToIndex[r.targetId],s=e.layoutNodes[o],l=s.positionX-a.positionX,u=s.positionY-a.positionY;if(0!==l||0!==u){var c=az(a,l,u),d=az(s,-1*l,-1*u),h=d.x-c.x,f=d.y-c.y,p=Math.sqrt(h*h+f*f),g=Math.pow(r.idealLength-p,2)/r.elasticity;if(0!==p)var v=g*h/p,y=g*f/p;else var v=0,y=0;a.isLocked||(a.offsetX+=v,a.offsetY+=y),s.isLocked||(s.offsetX-=v,s.offsetY-=y)}}},aF=function(e,t){if(0!==t.gravity)for(var n=0;n<e.graphSet.length;n++){var r=e.graphSet[n],i=r.length;if(0===n)var a=e.clientHeight/2,o=e.clientWidth/2;else var s=e.layoutNodes[e.idToIndex[r[0]]],l=e.layoutNodes[e.idToIndex[s.parentId]],a=l.positionX,o=l.positionY;for(var u=0;u<i;u++){var c=e.layoutNodes[e.idToIndex[r[u]]];if(!c.isLocked){var d=a-c.positionX,h=o-c.positionY,f=Math.sqrt(d*d+h*h);if(f>1){var p=t.gravity*d/f,g=t.gravity*h/f;c.offsetX+=p,c.offsetY+=g}}}}},aX=function(e,t){var n=[],r=0,i=-1;for(n.push.apply(n,e.graphSet[0]),i+=e.graphSet[0].length;r<=i;){var a=n[r++],o=e.idToIndex[a],s=e.layoutNodes[o],l=s.children;if(0<l.length&&!s.isLocked){for(var u=s.offsetX,c=s.offsetY,d=0;d<l.length;d++){var h=e.layoutNodes[e.idToIndex[l[d]]];h.offsetX+=u,h.offsetY+=c,n[++i]=l[d]}s.offsetX=0,s.offsetY=0}}},aj=function(e,t){for(var n=0;n<e.nodeSize;n++){var r=e.layoutNodes[n];0<r.children.length&&(r.maxX=void 0,r.minX=void 0,r.maxY=void 0,r.minY=void 0)}for(var n=0;n<e.nodeSize;n++){var r=e.layoutNodes[n];if(!(0<r.children.length)&&!r.isLocked){var i=aY(r.offsetX,r.offsetY,e.temperature);r.positionX+=i.x,r.positionY+=i.y,r.offsetX=0,r.offsetY=0,r.minX=r.positionX-r.width,r.maxX=r.positionX+r.width,r.minY=r.positionY-r.height,r.maxY=r.positionY+r.height,aq(r,e)}}for(var n=0;n<e.nodeSize;n++){var r=e.layoutNodes[n];0<r.children.length&&!r.isLocked&&(r.positionX=(r.maxX+r.minX)/2,r.positionY=(r.maxY+r.minY)/2,r.width=r.maxX-r.minX,r.height=r.maxY-r.minY)}},aY=function(e,t,n){var r=Math.sqrt(e*e+t*t);if(r>n)var i={x:n*e/r,y:n*t/r};else var i={x:e,y:t};return i},aq=function(e,t){var n=e.parentId;if(null!=n){var r=t.layoutNodes[t.idToIndex[n]],i=!1;if((null==r.maxX||e.maxX+r.padRight>r.maxX)&&(r.maxX=e.maxX+r.padRight,i=!0),(null==r.minX||e.minX-r.padLeft<r.minX)&&(r.minX=e.minX-r.padLeft,i=!0),(null==r.maxY||e.maxY+r.padBottom>r.maxY)&&(r.maxY=e.maxY+r.padBottom,i=!0),(null==r.minY||e.minY-r.padTop<r.minY)&&(r.minY=e.minY-r.padTop,i=!0),i)return aq(r,t)}},aW=function(e,t){for(var n=e.layoutNodes,r=[],i=0;i<n.length;i++){var a=n[i],o=a.cmptId;(r[o]=r[o]||[]).push(a)}for(var s=0,i=0;i<r.length;i++){var l=r[i];if(l){l.x1=1/0,l.x2=-1/0,l.y1=1/0,l.y2=-1/0;for(var u=0;u<l.length;u++){var c=l[u];l.x1=Math.min(l.x1,c.positionX-c.width/2),l.x2=Math.max(l.x2,c.positionX+c.width/2),l.y1=Math.min(l.y1,c.positionY-c.height/2),l.y2=Math.max(l.y2,c.positionY+c.height/2)}l.w=l.x2-l.x1,l.h=l.y2-l.y1,s+=l.w*l.h}}r.sort(function(e,t){return t.w*t.h-e.w*e.h});for(var d=0,h=0,f=0,p=0,g=Math.sqrt(s)*e.clientWidth/e.clientHeight,i=0;i<r.length;i++){var l=r[i];if(l){for(var u=0;u<l.length;u++){var c=l[u];c.isLocked||(c.positionX+=d-l.x1,c.positionY+=h-l.y1)}d+=l.w+t.componentSpacing,f+=l.w+t.componentSpacing,p=Math.max(p,l.h),f>g&&(h+=p+t.componentSpacing,d=0,f=0,p=0)}}},aU={fit:!0,padding:30,boundingBox:void 0,avoidOverlap:!0,avoidOverlapPadding:10,nodeDimensionsIncludeLabels:!1,spacingFactor:void 0,condense:!1,rows:void 0,cols:void 0,position:function(e){},sort:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,t){return!0},ready:void 0,stop:void 0,transform:function(e,t){return t}};function aG(e){this.options=J({},aU,e)}aG.prototype.run=function(){var e=this.options,t=e.cy,n=e.eles,r=n.nodes().not(":parent");e.sort&&(r=r.sort(e.sort));var i=tb(e.boundingBox?e.boundingBox:{x1:0,y1:0,w:t.width(),h:t.height()});if(0===i.h||0===i.w)n.nodes().layoutPositions(this,e,function(e){return{x:i.x1,y:i.y1}});else{var a=r.size(),o=Math.sqrt(a*i.h/i.w),s=Math.round(o),l=Math.round(i.w/i.h*o),u=function(e){if(null==e)return Math.min(s,l);Math.min(s,l)==s?s=e:l=e},c=function(e){if(null==e)return Math.max(s,l);Math.max(s,l)==s?s=e:l=e},d=e.rows,h=null!=e.cols?e.cols:e.columns;if(null!=d&&null!=h)s=d,l=h;else if(null!=d&&null==h)l=Math.ceil(a/(s=d));else if(null==d&&null!=h)s=Math.ceil(a/(l=h));else if(l*s>a){var f=u(),p=c();(f-1)*p>=a?u(f-1):(p-1)*f>=a&&c(p-1)}else for(;l*s<a;){var g=u(),v=c();(v+1)*g>=a?c(v+1):u(g+1)}var y=i.w/l,b=i.h/s;if(e.condense&&(y=0,b=0),e.avoidOverlap)for(var x=0;x<r.length;x++){var w=r[x],E=w._private.position;(null==E.x||null==E.y)&&(E.x=0,E.y=0);var T=w.layoutDimensions(e),C=e.avoidOverlapPadding,k=T.w+C,P=T.h+C;y=Math.max(y,k),b=Math.max(b,P)}for(var S={},B=function(e,t){return!!S["c-"+e+"-"+t]},D=function(e,t){S["c-"+e+"-"+t]=!0},_=0,A=0,M=function(){++A>=l&&(A=0,_++)},R={},I=0;I<r.length;I++){var N=r[I],L=e.position(N);if(L&&(void 0!==L.row||void 0!==L.col)){var O={row:L.row,col:L.col};if(void 0===O.col)for(O.col=0;B(O.row,O.col);)O.col++;else if(void 0===O.row)for(O.row=0;B(O.row,O.col);)O.row++;R[N.id()]=O,D(O.row,O.col)}}r.layoutPositions(this,e,function(e,t){if(e.locked()||e.isParent())return!1;var n,r,a=R[e.id()];if(a)n=a.col*y+y/2+i.x1,r=a.row*b+b/2+i.y1;else{for(;B(_,A);)M();n=A*y+y/2+i.x1,r=_*b+b/2+i.y1,D(_,A),M()}return{x:n,y:r}})}return this};var aH={ready:function(){},stop:function(){}};function aK(e){this.options=J({},aH,e)}aK.prototype.run=function(){var e=this.options,t=e.eles;return e.cy,this.emit("layoutstart"),t.nodes().positions(function(){return{x:0,y:0}}),this.one("layoutready",e.ready),this.emit("layoutready"),this.one("layoutstop",e.stop),this.emit("layoutstop"),this},aK.prototype.stop=function(){return this};var aZ={positions:void 0,zoom:void 0,pan:void 0,fit:!0,padding:30,spacingFactor:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,t){return!0},ready:void 0,stop:void 0,transform:function(e,t){return t}};function a$(e){this.options=J({},aZ,e)}a$.prototype.run=function(){var e=this.options,t=e.eles.nodes(),n=D(e.positions);return t.layoutPositions(this,e,function(t,r){var i=function(t){if(null==e.positions){var r;return{x:(r=t.position()).x,y:r.y}}if(n)return e.positions(t);var i=e.positions[t._private.data.id];return null==i?null:i}(t);return!t.locked()&&null!=i&&i}),this};var aQ={fit:!0,padding:30,boundingBox:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,t){return!0},ready:void 0,stop:void 0,transform:function(e,t){return t}};function aJ(e){this.options=J({},aQ,e)}aJ.prototype.run=function(){var e=this.options,t=e.cy,n=e.eles,r=tb(e.boundingBox?e.boundingBox:{x1:0,y1:0,w:t.width(),h:t.height()});return n.nodes().layoutPositions(this,e,function(e,t){return{x:r.x1+Math.round(Math.random()*r.w),y:r.y1+Math.round(Math.random()*r.h)}}),this};var a0=[{name:"breadthfirst",impl:ax},{name:"circle",impl:aE},{name:"concentric",impl:aC},{name:"cose",impl:aP},{name:"grid",impl:aG},{name:"null",impl:aK},{name:"preset",impl:a$},{name:"random",impl:aJ}];function a1(e){this.options=e,this.notifications=0}var a2=function(){},a5=function(){throw Error("A headless instance can not render images")};a1.prototype={recalculateRenderedStyle:a2,notify:function(){this.notifications++},init:a2,isHeadless:function(){return!0},png:a5,jpg:a5};var a3={};a3.arrowShapeWidth=.3,a3.registerArrowShapes=function(){var e=this.arrowShapes={},t=this,n=function(e,t,n,r,i,a,o){var s=i.x-n/2-o,l=i.x+n/2+o,u=i.y-n/2-o,c=i.y+n/2+o;return s<=e&&e<=l&&u<=t&&t<=c},r=function(e,t,n,r,i){var a=e*Math.cos(r)-t*Math.sin(r),o=e*Math.sin(r)+t*Math.cos(r);return{x:a*n+i.x,y:o*n+i.y}},i=function(e,t,n,i){for(var a=[],o=0;o<e.length;o+=2){var s=e[o],l=e[o+1];a.push(r(s,l,t,n,i))}return a},a=function(e){for(var t=[],n=0;n<e.length;n++){var r=e[n];t.push(r.x,r.y)}return t},o=function(e){return e.pstyle("width").pfValue*e.pstyle("arrow-scale").pfValue*2},s=function(r,s){B(s)&&(s=e[s]),e[r]=J({name:r,points:[-.15,-.3,.15,-.3,.15,.3,-.15,.3],collide:function(e,t,n,r,o,s){return tz(e,t,a(i(this.points,n+2*s,r,o)))},roughCollide:n,draw:function(e,n,r,a){var o=i(this.points,n,r,a);t.arrowShapeImpl("polygon")(e,o)},spacing:function(e){return 0},gap:o},s)};s("none",{collide:eR,roughCollide:eR,draw:eN,spacing:eI,gap:eI}),s("triangle",{points:[-.15,-.3,0,0,.15,-.3]}),s("arrow","triangle"),s("triangle-backcurve",{points:e.triangle.points,controlPoint:[0,-.15],roughCollide:n,draw:function(e,n,a,o,s){var l=i(this.points,n,a,o),u=this.controlPoint,c=r(u[0],u[1],n,a,o);t.arrowShapeImpl(this.name)(e,l,c)},gap:function(e){return .8*o(e)}}),s("triangle-tee",{points:[0,0,.15,-.3,-.15,-.3,0,0],pointsTee:[-.15,-.4,-.15,-.5,.15,-.5,.15,-.4],collide:function(e,t,n,r,o,s,l){var u=a(i(this.points,n+2*l,r,o)),c=a(i(this.pointsTee,n+2*l,r,o));return tz(e,t,u)||tz(e,t,c)},draw:function(e,n,r,a,o){var s=i(this.points,n,r,a),l=i(this.pointsTee,n,r,a);t.arrowShapeImpl(this.name)(e,s,l)}}),s("circle-triangle",{radius:.15,pointsTr:[0,-.15,.15,-.45,-.15,-.45,0,-.15],collide:function(e,t,n,r,o,s,l){var u=Math.pow(o.x-e,2)+Math.pow(o.y-t,2)<=Math.pow((n+2*l)*this.radius,2);return tz(e,t,a(i(this.points,n+2*l,r,o)))||u},draw:function(e,n,r,a,o){var s=i(this.pointsTr,n,r,a);t.arrowShapeImpl(this.name)(e,s,a.x,a.y,this.radius*n)},spacing:function(e){return t.getArrowWidth(e.pstyle("width").pfValue,e.pstyle("arrow-scale").value)*this.radius}}),s("triangle-cross",{points:[0,0,.15,-.3,-.15,-.3,0,0],baseCrossLinePts:[-.15,-.4,-.15,-.4,.15,-.4,.15,-.4],crossLinePts:function(e,t){var n=this.baseCrossLinePts.slice(),r=t/e;return n[3]=n[3]-r,n[5]=n[5]-r,n},collide:function(e,t,n,r,o,s,l){var u=a(i(this.points,n+2*l,r,o)),c=a(i(this.crossLinePts(n,s),n+2*l,r,o));return tz(e,t,u)||tz(e,t,c)},draw:function(e,n,r,a,o){var s=i(this.points,n,r,a),l=i(this.crossLinePts(n,o),n,r,a);t.arrowShapeImpl(this.name)(e,s,l)}}),s("vee",{points:[-.15,-.3,0,0,.15,-.3,0,-.15],gap:function(e){return .525*o(e)}}),s("circle",{radius:.15,collide:function(e,t,n,r,i,a,o){return Math.pow(i.x-e,2)+Math.pow(i.y-t,2)<=Math.pow((n+2*o)*this.radius,2)},draw:function(e,n,r,i,a){t.arrowShapeImpl(this.name)(e,i.x,i.y,this.radius*n)},spacing:function(e){return t.getArrowWidth(e.pstyle("width").pfValue,e.pstyle("arrow-scale").value)*this.radius}}),s("tee",{points:[-.15,0,-.15,-.1,.15,-.1,.15,0],spacing:function(e){return 1},gap:function(e){return 1}}),s("square",{points:[-.15,0,.15,0,.15,-.3,-.15,-.3]}),s("diamond",{points:[-.15,-.15,0,-.3,.15,-.15,0,0],gap:function(e){return e.pstyle("width").pfValue*e.pstyle("arrow-scale").value}}),s("chevron",{points:[0,0,-.15,-.15,-.1,-.2,0,-.1,.1,-.2,.15,-.15],gap:function(e){return .95*e.pstyle("width").pfValue*e.pstyle("arrow-scale").value}})};var a4={};a4.projectIntoViewport=function(e,t){var n=this.cy,r=this.findContainerClientCoords(),i=r[0],a=r[1],o=r[4],s=n.pan(),l=n.zoom();return[((e-i)/o-s.x)/l,((t-a)/o-s.y)/l]},a4.findContainerClientCoords=function(){if(this.containerBB)return this.containerBB;var e=this.container,t=e.getBoundingClientRect(),n=this.cy.window().getComputedStyle(e),r=function(e){return parseFloat(n.getPropertyValue(e))},i={left:r("padding-left"),right:r("padding-right"),top:r("padding-top"),bottom:r("padding-bottom")},a={left:r("border-left-width"),right:r("border-right-width"),top:r("border-top-width"),bottom:r("border-bottom-width")},o=e.clientWidth,s=e.clientHeight,l=i.left+i.right,u=i.top+i.bottom,c=a.left+a.right,d=t.width/(o+c),h=t.left+i.left+a.left,f=t.top+i.top+a.top;return this.containerBB=[h,f,o-l,s-u,d]},a4.invalidateContainerClientCoordsCache=function(){this.containerBB=null},a4.findNearestElement=function(e,t,n,r){return this.findNearestElements(e,t,n,r)[0]},a4.findNearestElements=function(e,t,n,r){var i,a,o=this,s=this,l=s.getCachedZSortedEles(),u=[],c=s.cy.zoom(),d=s.cy.hasCompoundNodes(),h=(r?24:8)/c,f=(r?8:2)/c,p=(r?8:2)/c,g=1/0;function v(e,t){if(e.isNode())if(a)return;else a=e,u.push(e);if(e.isEdge()&&(null==t||t<g))if(i){if(i.pstyle("z-compound-depth").value===e.pstyle("z-compound-depth").value&&i.pstyle("z-compound-depth").value===e.pstyle("z-compound-depth").value){for(var n=0;n<u.length;n++)if(u[n].isEdge()){u[n]=e,i=e,g=null!=t?t:g;break}}}else u.push(e),i=e,g=null!=t?t:g}function y(n){var r=n.outerWidth()+2*f,i=n.outerHeight()+2*f,a=r/2,l=i/2,u=n.position(),c="auto"===n.pstyle("corner-radius").value?"auto":n.pstyle("corner-radius").pfValue,d=n._private.rscratch;if(u.x-a<=e&&e<=u.x+a&&u.y-l<=t&&t<=u.y+l&&s.nodeShapes[o.getNodeShape(n)].checkPoint(e,t,0,r,i,u.x,u.y,c,d))return v(n,0),!0}n&&(l=l.interactive);function b(n,r){var i,a=n._private;i=r?r+"-":"",n.boundingBox();var o=a.labelBounds[r||"main"],s=n.pstyle(i+"label").value;if("yes"===n.pstyle("text-events").strValue&&s){var l=eG(a.rscratch,"labelX",r),u=eG(a.rscratch,"labelY",r),c=eG(a.rscratch,"labelAngle",r),d=n.pstyle(i+"text-margin-x").pfValue,h=n.pstyle(i+"text-margin-y").pfValue,f=o.x1-p-d,g=o.x2+p-d,y=o.y1-p-h,b=o.y2+p-h;if(c){var x=Math.cos(c),w=Math.sin(c),E=function(e,t){return{x:(e-=l)*x-(t-=u)*w+l,y:e*w+t*x+u}},T=E(f,y),C=E(f,b),k=E(g,y),P=E(g,b);if(tz(e,t,[T.x+d,T.y+h,k.x+d,k.y+h,P.x+d,P.y+h,C.x+d,C.y+h]))return v(n),!0}else if(tS(o,e,t))return v(n),!0}}for(var x=l.length-1;x>=0;x--){var w=l[x];w.isNode()?y(w)||b(w):function(n){var r,i=n._private,a=i.rscratch,l=n.pstyle("width").pfValue,c=n.pstyle("arrow-scale").value,f=l/2+h,p=f*f,g=2*f,b=i.source,x=i.target;if("segments"===a.edgeType||"straight"===a.edgeType||"haystack"===a.edgeType){for(var w=a.allpts,E=0;E+3<w.length;E+=2)if(tM(e,t,w[E],w[E+1],w[E+2],w[E+3],g)&&p>(r=tO(e,t,w[E],w[E+1],w[E+2],w[E+3])))return v(n,r),!0}else if("bezier"===a.edgeType||"multibezier"===a.edgeType||"self"===a.edgeType||"compound"===a.edgeType){for(var w=a.allpts,E=0;E+5<a.allpts.length;E+=4)if(tR(e,t,w[E],w[E+1],w[E+2],w[E+3],w[E+4],w[E+5],g)&&p>(r=tL(e,t,w[E],w[E+1],w[E+2],w[E+3],w[E+4],w[E+5])))return v(n,r),!0}for(var b=b||i.source,x=x||i.target,T=o.getArrowWidth(l,c),C=[{name:"source",x:a.arrowStartX,y:a.arrowStartY,angle:a.srcArrowAngle},{name:"target",x:a.arrowEndX,y:a.arrowEndY,angle:a.tgtArrowAngle},{name:"mid-source",x:a.midX,y:a.midY,angle:a.midsrcArrowAngle},{name:"mid-target",x:a.midX,y:a.midY,angle:a.midtgtArrowAngle}],E=0;E<C.length;E++){var k=C[E],P=s.arrowShapes[n.pstyle(k.name+"-arrow-shape").value],S=n.pstyle("width").pfValue;if(P.roughCollide(e,t,T,k.angle,{x:k.x,y:k.y},S,h)&&P.collide(e,t,T,k.angle,{x:k.x,y:k.y},S,h))return v(n),!0}d&&u.length>0&&(y(b),y(x))}(w)||b(w)||b(w,"source")||b(w,"target")}return u},a4.getAllInBox=function(e,t,n,r){var i=this.getCachedZSortedEles().interactive,a=2/this.cy.zoom(),o=[],s=Math.min(e,n),u=Math.max(e,n),c=Math.min(t,r),d=Math.max(t,r);e=s,n=u;var h=tb({x1:e,y1:t=c,x2:n,y2:r=d}),f=[{x:h.x1,y:h.y1},{x:h.x2,y:h.y1},{x:h.x2,y:h.y2},{x:h.x1,y:h.y2}],p=[[f[0],f[1]],[f[1],f[2]],[f[2],f[3]],[f[3],f[0]]];function g(e,t){var n=e._private;e.boundingBox();var r=n.labelBounds.main;if(!r)return null;var i=eG(n.rscratch,"labelX",t),o=eG(n.rscratch,"labelY",t),s=eG(n.rscratch,"labelAngle",t),l=e.pstyle("text-margin-x").pfValue,u=e.pstyle("text-margin-y").pfValue,c=r.x1-a-l,d=r.x2+a-l,h=r.y1-a-u,f=r.y2+a-u;if(!s)return[{x:c,y:h},{x:d,y:h},{x:d,y:f},{x:c,y:f}];var p=Math.cos(s),g=Math.sin(s),v=function(e,t){return{x:(e-=i)*p-(t-=o)*g+i,y:e*g+t*p+o}};return[v(c,h),v(d,h),v(d,f),v(c,f)]}for(var v=0;v<i.length;v++){var y=i[v];if(y.isNode()){var b="yes"===y.pstyle("text-events").strValue,x=y.pstyle("box-selection").strValue,w="yes"===y.pstyle("box-select-labels").strValue;if("none"===x)continue;var E=("overlap"===x||w)&&b,T=y.boundingBox({includeNodes:!0,includeEdges:!1,includeLabels:E});if("contain"===x){var C=!1;if(w&&b){var k=g(y);k&&t4(k,f)&&(o.push(y),C=!0)}!C&&tD(h,T)&&o.push(y)}else if("overlap"===x&&tP(h,T)){var P=y.boundingBox({includeNodes:!0,includeEdges:!0,includeLabels:!1,includeMainLabels:!1,includeSourceLabels:!1,includeTargetLabels:!1});if(t4([{x:P.x1,y:P.y1},{x:P.x2,y:P.y1},{x:P.x2,y:P.y2},{x:P.x1,y:P.y2}],f))o.push(y);else{var S=g(y);S&&t4(S,f)&&o.push(y)}}}else{var B=y._private,D=B.rscratch,_=y.pstyle("box-selection").strValue;if("none"===_)continue;if("contain"===_){if(null!=D.startX&&null!=D.startY&&!tS(h,D.startX,D.startY)||null!=D.endX&&null!=D.endY&&!tS(h,D.endX,D.endY))continue;if("bezier"===D.edgeType||"multibezier"===D.edgeType||"self"===D.edgeType||"compound"===D.edgeType||"segments"===D.edgeType||"haystack"===D.edgeType){for(var A=B.rstyle.bezierPts||B.rstyle.linePts||B.rstyle.haystackPts,M=!0,R=0;R<A.length;R++)if(!tB(h,A[R])){M=!1;break}M&&o.push(y)}else"straight"===D.edgeType&&o.push(y)}else if("overlap"===_){var I=!1;if(null!=D.startX&&null!=D.startY&&null!=D.endX&&null!=D.endY&&(tS(h,D.startX,D.startY)||tS(h,D.endX,D.endY)))o.push(y),I=!0;else if(!I&&"haystack"===D.edgeType){for(var N=B.rstyle.haystackPts,L=0;L<N.length;L++)if(tB(h,N[L])){o.push(y),I=!0;break}}if(!I){var O=B.rstyle.bezierPts||B.rstyle.linePts||B.rstyle.haystackPts;if((!O||O.length<2)&&"straight"===D.edgeType&&null!=D.startX&&null!=D.startY&&null!=D.endX&&null!=D.endY&&(O=[{x:D.startX,y:D.startY},{x:D.endX,y:D.endY}]),!O||O.length<2)continue;for(var z=0;z<O.length-1;z++){for(var V=O[z],F=O[z+1],X=0;X<p.length;X++){var j=l(p[X],2);if(function(e,t,n,r){function i(e,t,n){return(n.y-e.y)*(t.x-e.x)>(t.y-e.y)*(n.x-e.x)}return i(e,n,r)!==i(t,n,r)&&i(e,t,n)!==i(e,t,r)}(V,F,j[0],j[1])){o.push(y),I=!0;break}}if(I)break}}}}}return o};var a9={};a9.calculateArrowAngles=function(e){var t=e._private.rscratch,n="haystack"===t.edgeType,r="bezier"===t.edgeType,i="multibezier"===t.edgeType,a="segments"===t.edgeType,o="compound"===t.edgeType,s="self"===t.edgeType;if(n?(y=t.haystackPts[0],b=t.haystackPts[1],x=t.haystackPts[2],w=t.haystackPts[3]):(y=t.arrowStartX,b=t.arrowStartY,x=t.arrowEndX,w=t.arrowEndY),d=t.midX,h=t.midY,a)g=y-t.segpts[0],v=b-t.segpts[1];else if(i||o||s||r){var l=t.allpts,u=tg(l[0],l[2],l[4],.1),c=tg(l[1],l[3],l[5],.1);g=y-u,v=b-c}else g=y-d,v=b-h;t.srcArrowAngle=tu(g,v);var d=t.midX,h=t.midY;if(n&&(d=(y+x)/2,h=(b+w)/2),g=x-y,v=w-b,a){var l=t.allpts;if(l.length/2%2==0){var f=l.length/2,p=f-2;g=l[f]-l[p],v=l[f+1]-l[p+1]}else if(t.isRound)g=t.midVector[1],v=-t.midVector[0];else{var f=l.length/2-1,p=f-2;g=l[f]-l[p],v=l[f+1]-l[p+1]}}else if(i||o||s){var g,v,y,b,x,w,d,h,E,T,C,k,l=t.allpts;if(t.ctrlpts.length/2%2==0){var P=l.length/2-1,S=P+2,B=S+2;E=tg(l[P],l[S],l[B],0),T=tg(l[P+1],l[S+1],l[B+1],0),C=tg(l[P],l[S],l[B],1e-4),k=tg(l[P+1],l[S+1],l[B+1],1e-4)}else{var S=l.length/2-1,P=S-2,B=S+2;E=tg(l[P],l[S],l[B],.4999),T=tg(l[P+1],l[S+1],l[B+1],.4999),C=tg(l[P],l[S],l[B],.5),k=tg(l[P+1],l[S+1],l[B+1],.5)}g=C-E,v=k-T}if(t.midtgtArrowAngle=tu(g,v),t.midDispX=g,t.midDispY=v,g*=-1,v*=-1,a){var l=t.allpts;if(l.length/2%2==0);else if(!t.isRound){var f=l.length/2-1,D=f+2;g=-(l[D]-l[f]),v=-(l[D+1]-l[f+1])}}if(t.midsrcArrowAngle=tu(g,v),a)g=x-t.segpts[t.segpts.length-2],v=w-t.segpts[t.segpts.length-1];else if(i||o||s||r){var l=t.allpts,_=l.length,u=tg(l[_-6],l[_-4],l[_-2],.9),c=tg(l[_-5],l[_-3],l[_-1],.9);g=x-u,v=w-c}else g=x-d,v=w-h;t.tgtArrowAngle=tu(g,v)},a9.getArrowWidth=a9.getArrowHeight=function(e,t){var n=this.arrowWidthCache=this.arrowWidthCache||{},r=n[e+", "+t];return r||(r=Math.max(Math.pow(13.37*e,.9),29)*t,n[e+", "+t]=r),r};var a6,a8,a7,oe,ot,on,or,oi,oa,oo,os,ol,ou,oc,od,oh,of,op,og,ov,oy,om,ob,ox,ow,oE,oT,oC,ok,oP,oS,oB,oD,o_,oA,oM,oR,oI,oN,oL,oO,oz,oV,oF,oX,oj,oY,oq,oW,oU,oG,oH,oK,oZ,o$,oQ,oJ,o0,o1,o2,o5,o3,o4,o9,o6,o8,o7,se,st,sn,sr,si,sa,so,ss,sl,su,sc,sd,sh,sf,sp,sg,sv,sy,sm,sb,sx,sw,sE,sT,sC,sk,sP,sS,sB,sD,s_,sA,sM,sR,sI,sN,sL,sO,sz,sV,sF,sX,sj,sY,sq,sW,sU,sG,sH,sK,sZ,s$,sQ,sJ,s0,s1,s2,s5,s3,s4,s9,s6,s8,s7,le,lt,ln,lr,li,la,lo,ls,ll,lu,lc,ld,lh,lf,lp,lg,lv,ly,lm,lb,lx,lw,lE,lT,lC,lk={},lP={},lS=function(e,t,n){n.x=t.x-e.x,n.y=t.y-e.y,n.len=Math.sqrt(n.x*n.x+n.y*n.y),n.nx=n.x/n.len,n.ny=n.y/n.len,n.ang=Math.atan2(n.ny,n.nx)},lB=function(e,t){t.x=-1*e.x,t.y=-1*e.y,t.nx=-1*e.nx,t.ny=-1*e.ny,t.ang=e.ang>0?-(Math.PI-e.ang):Math.PI+e.ang},lD=function(e,t,n,r,i){if(e!==y?lS(t,e,lk):lB(lP,lk),lS(t,n,lP),lg=lk.nx*lP.ny-lk.ny*lP.nx,lv=lk.nx*lP.nx- -(lk.ny*lP.ny),1e-6>Math.abs(lb=Math.asin(Math.max(-1,Math.min(1,lg))))){lf=t.x,lp=t.y,lw=lT=0;return}ly=1,lm=!1,lv<0?lb<0?lb=Math.PI+lb:(lb=Math.PI-lb,ly=-1,lm=!0):lb>0&&(ly=-1,lm=!0),lT=void 0!==t.radius?t.radius:r,lx=lb/2,lC=Math.min(lk.len/2,lP.len/2),lw=i?(lE=Math.abs(Math.cos(lx)*lT/Math.sin(lx)))>lC?Math.abs((lE=lC)*Math.sin(lx)/Math.cos(lx)):lT:Math.abs((lE=Math.min(lC,lT))*Math.sin(lx)/Math.cos(lx)),g=t.x+lP.nx*lE,v=t.y+lP.ny*lE,lf=g-lP.ny*lw*ly,lp=v+lP.nx*lw*ly,f=t.x+lk.nx*lE,p=t.y+lk.ny*lE,y=t};function l_(e,t){0===t.radius?e.lineTo(t.cx,t.cy):e.arc(t.cx,t.cy,t.radius,t.startAngle,t.endAngle,t.counterClockwise)}function lA(e,t,n,r){var i=!(arguments.length>4)||void 0===arguments[4]||arguments[4];return 0===r||0===t.radius?{cx:t.x,cy:t.y,radius:0,startX:t.x,startY:t.y,stopX:t.x,stopY:t.y,startAngle:void 0,endAngle:void 0,counterClockwise:void 0}:(lD(e,t,n,r,i),{cx:lf,cy:lp,radius:lw,startX:f,startY:p,stopX:g,stopY:v,startAngle:lk.ang+Math.PI/2*ly,endAngle:lP.ang-Math.PI/2*ly,counterClockwise:lm})}var lM=Math.sqrt(.02),lR={};function lI(e){var t=[];if(null!=e){for(var n=0;n<e.length;n+=2){var r=e[n],i=e[n+1];t.push({x:r,y:i})}return t}}lR.findMidptPtsEtc=function(e,t){var n,r=t.posPts,i=t.intersectionPts,a=t.vectorNormInverse,o=e.pstyle("source-endpoint"),s=e.pstyle("target-endpoint"),u=null!=o.units&&null!=s.units;switch(e.pstyle("edge-distances").value){case"node-position":n=r;break;case"intersection":n=i;break;case"endpoints":if(u){var c,d,h,f=l(this.manualEndptToPx(e.source()[0],o),2),p=f[0],g=f[1],v=l(this.manualEndptToPx(e.target()[0],s),2),y=v[0],b=v[1];c=b-g,h=Math.sqrt((d=y-p)*d+c*c),a={x:-c/h,y:d/h},n={x1:p,y1:g,x2:y,y2:b}}else ez("Edge ".concat(e.id()," has edge-distances:endpoints specified without manual endpoints specified via source-endpoint and target-endpoint.  Falling back on edge-distances:intersection (default).")),n=i}return{midptPts:n,vectorNormInverse:a}},lR.findHaystackPoints=function(e){for(var t=0;t<e.length;t++){var n=e[t],r=n._private,i=r.rscratch;if(!i.haystack){var a=2*Math.random()*Math.PI;i.source={x:Math.cos(a),y:Math.sin(a)},i.target={x:Math.cos(a=2*Math.random()*Math.PI),y:Math.sin(a)}}var o=r.source,s=r.target,l=o.position(),u=s.position(),c=o.width(),d=s.width(),h=o.height(),f=s.height(),p=n.pstyle("haystack-radius").value/2;i.haystackPts=i.allpts=[i.source.x*c*p+l.x,i.source.y*h*p+l.y,i.target.x*d*p+u.x,i.target.y*f*p+u.y],i.midX=(i.allpts[0]+i.allpts[2])/2,i.midY=(i.allpts[1]+i.allpts[3])/2,i.edgeType="haystack",i.haystack=!0,this.storeEdgeProjections(n),this.calculateArrowAngles(n),this.recalculateEdgeLabelProjections(n),this.calculateLabelAngles(n)}},lR.findSegmentsPoints=function(e,t){var n=e._private.rscratch,r=e.pstyle("segment-weights"),i=e.pstyle("segment-distances"),a=e.pstyle("segment-radii"),o=e.pstyle("radius-type"),s=Math.min(r.pfValue.length,i.pfValue.length),l=a.pfValue[a.pfValue.length-1],u=o.pfValue[o.pfValue.length-1];n.edgeType="segments",n.segpts=[],n.radii=[],n.isArcRadius=[];for(var c=0;c<s;c++){var d=r.pfValue[c],h=i.pfValue[c],f=1-d,p=this.findMidptPtsEtc(e,t),g=p.midptPts,v=p.vectorNormInverse,y={x:g.x1*f+g.x2*d,y:g.y1*f+g.y2*d};n.segpts.push(y.x+v.x*h,y.y+v.y*h),n.radii.push(void 0!==a.pfValue[c]?a.pfValue[c]:l),n.isArcRadius.push((void 0!==o.pfValue[c]?o.pfValue[c]:u)==="arc-radius")}},lR.findLoopPoints=function(e,t,n,r){var i=e._private.rscratch,a=t.dirCounts,o=t.srcPos,s=e.pstyle("control-point-distances"),l=s?s.pfValue[0]:void 0,u=e.pstyle("loop-direction").pfValue,c=e.pstyle("loop-sweep").pfValue,d=e.pstyle("control-point-step-size").pfValue;i.edgeType="self";var h=n,f=d;r&&(h=0,f=l);var p=u-Math.PI/2,g=p-c/2,v=p+c/2,y=String(u+"_"+c);h=void 0===a[y]?a[y]=0:++a[y],i.ctrlpts=[o.x+1.4*Math.cos(g)*f*(h/3+1),o.y+1.4*Math.sin(g)*f*(h/3+1),o.x+1.4*Math.cos(v)*f*(h/3+1),o.y+1.4*Math.sin(v)*f*(h/3+1)]},lR.findCompoundLoopPoints=function(e,t,n,r){var i=e._private.rscratch;i.edgeType="compound";var a=t.srcPos,o=t.tgtPos,s=t.srcW,l=t.srcH,u=t.tgtW,c=t.tgtH,d=e.pstyle("control-point-step-size").pfValue,h=e.pstyle("control-point-distances"),f=h?h.pfValue[0]:void 0,p=n,g=d;r&&(p=0,g=f);var v={x:a.x-s/2,y:a.y-l/2},y={x:o.x-u/2,y:o.y-c/2},b={x:Math.min(v.x,y.x),y:Math.min(v.y,y.y)},x=Math.max(.5,Math.log(.01*s)),w=Math.max(.5,Math.log(.01*u));i.ctrlpts=[b.x,b.y-1.7995514309304248*g*(p/3+1)*x,b.x-1.7995514309304248*g*(p/3+1)*w,b.y]},lR.findStraightEdgePoints=function(e){e._private.rscratch.edgeType="straight"},lR.findBezierPoints=function(e,t,n,r,i){var a=e._private.rscratch,o=e.pstyle("control-point-step-size").pfValue,s=e.pstyle("control-point-distances"),l=e.pstyle("control-point-weights"),u=s&&l?Math.min(s.value.length,l.value.length):1,c=s?s.pfValue[0]:void 0,d=l.value[0];a.edgeType=r?"multibezier":"bezier",a.ctrlpts=[];for(var h=0;h<u;h++){var f=(.5-t.eles.length/2+n)*o*(i?-1:1),p=void 0,g=td(f);r&&(c=s?s.pfValue[h]:o,d=l.value[h]);var v=void 0!==(p=r?c:void 0!==c?g*c:void 0)?p:f,y=1-d,b=d,x=this.findMidptPtsEtc(e,t),w=x.midptPts,E=x.vectorNormInverse,T={x:w.x1*y+w.x2*b,y:w.y1*y+w.y2*b};a.ctrlpts.push(T.x+E.x*v,T.y+E.y*v)}},lR.findTaxiPoints=function(e,t){var n,r=e._private.rscratch;r.edgeType="segments";var i="vertical",a="horizontal",o="leftward",s="rightward",l="downward",u="upward",c=t.posPts,d=t.srcW,h=t.srcH,f=t.tgtW,p=t.tgtH,g="node-position"!==e.pstyle("edge-distances").value,v=e.pstyle("taxi-direction").value,y=v,b=e.pstyle("taxi-turn"),x="%"===b.units,w=b.pfValue,E=e.pstyle("taxi-turn-min-distance").pfValue,T=c.x2-c.x1,C=c.y2-c.y1,k=function(e,t){return e>0?Math.max(e-t,0):Math.min(e+t,0)},P=k(T,g?(d+f)/2:0),S=k(C,g?(h+p)/2:0),B=!1;"auto"===y?v=Math.abs(P)>Math.abs(S)?a:i:y===u||y===l?(v=i,B=!0):(y===o||y===s)&&(v=a,B=!0);var D=v===i,_=D?S:P,A=D?C:T,M=td(A),R=!1;!(B&&(x||w<0))&&(y===l&&A<0||y===u&&A>0||y===o&&A>0||y===s&&A<0)&&(M*=-1,_=M*Math.abs(_),R=!0);var I=function(e){return Math.abs(e)<E||Math.abs(e)>=Math.abs(_)},N=I(n=x?(w<0?1+w:w)*_:(w<0?_:0)+w*M),L=I(Math.abs(_)-Math.abs(n));if((N||L)&&!R)if(D){var O=Math.abs(T)<=f/2;if(Math.abs(A)<=h/2){var z=(c.x1+c.x2)/2;r.segpts=[z,c.y1,z,c.y2]}else if(O){var V=(c.y1+c.y2)/2;r.segpts=[c.x1,V,c.x2,V]}else r.segpts=[c.x1,c.y2]}else{var F=Math.abs(C)<=p/2;if(Math.abs(A)<=d/2){var X=(c.y1+c.y2)/2;r.segpts=[c.x1,X,c.x2,X]}else if(F){var j=(c.x1+c.x2)/2;r.segpts=[j,c.y1,j,c.y2]}else r.segpts=[c.x2,c.y1]}else if(D){var Y=c.y1+n+(g?h/2*M:0);r.segpts=[c.x1,Y,c.x2,Y]}else{var q=c.x1+n+(g?d/2*M:0);r.segpts=[q,c.y1,q,c.y2]}if(r.isRound){var W=e.pstyle("taxi-radius").value,U="arc-radius"===e.pstyle("radius-type").value[0];r.radii=Array(r.segpts.length/2).fill(W),r.isArcRadius=Array(r.segpts.length/2).fill(U)}},lR.tryToCorrectInvalidPoints=function(e,t){var n=e._private.rscratch;if("bezier"===n.edgeType){var r=t.srcPos,i=t.tgtPos,a=t.srcW,o=t.srcH,s=t.tgtW,l=t.tgtH,u=t.srcShape,c=t.tgtShape,d=t.srcCornerRadius,h=t.tgtCornerRadius,f=t.srcRs,p=t.tgtRs,g=!M(n.startX)||!M(n.startY),v=!M(n.arrowStartX)||!M(n.arrowStartY),y=!M(n.endX)||!M(n.endY),b=!M(n.arrowEndX)||!M(n.arrowEndY),x=3*(this.getArrowWidth(e.pstyle("width").pfValue,e.pstyle("arrow-scale").value)*this.arrowShapeWidth),w=th({x:n.ctrlpts[0],y:n.ctrlpts[1]},{x:n.startX,y:n.startY}),E=w<x,T=th({x:n.ctrlpts[0],y:n.ctrlpts[1]},{x:n.endX,y:n.endY}),C=T<x,k=!1;if(g||v||E){k=!0;var P={x:n.ctrlpts[0]-r.x,y:n.ctrlpts[1]-r.y},S=Math.sqrt(P.x*P.x+P.y*P.y),B={x:P.x/S,y:P.y/S},D=Math.max(a,o),_={x:n.ctrlpts[0]+2*B.x*D,y:n.ctrlpts[1]+2*B.y*D},A=u.intersectLine(r.x,r.y,a,o,_.x,_.y,0,d,f);E?(n.ctrlpts[0]=n.ctrlpts[0]+B.x*(x-w),n.ctrlpts[1]=n.ctrlpts[1]+B.y*(x-w)):(n.ctrlpts[0]=A[0]+B.x*x,n.ctrlpts[1]=A[1]+B.y*x)}if(y||b||C){k=!0;var R={x:n.ctrlpts[0]-i.x,y:n.ctrlpts[1]-i.y},I=Math.sqrt(R.x*R.x+R.y*R.y),N={x:R.x/I,y:R.y/I},L=Math.max(a,o),O={x:n.ctrlpts[0]+2*N.x*L,y:n.ctrlpts[1]+2*N.y*L},z=c.intersectLine(i.x,i.y,s,l,O.x,O.y,0,h,p);C?(n.ctrlpts[0]=n.ctrlpts[0]+N.x*(x-T),n.ctrlpts[1]=n.ctrlpts[1]+N.y*(x-T)):(n.ctrlpts[0]=z[0]+N.x*x,n.ctrlpts[1]=z[1]+N.y*x)}k&&this.findEndpoints(e)}},lR.storeAllpts=function(e){var t=e._private.rscratch;if("multibezier"===t.edgeType||"bezier"===t.edgeType||"self"===t.edgeType||"compound"===t.edgeType){t.allpts=[],t.allpts.push(t.startX,t.startY);for(var n,r=0;r+1<t.ctrlpts.length;r+=2)t.allpts.push(t.ctrlpts[r],t.ctrlpts[r+1]),r+3<t.ctrlpts.length&&t.allpts.push((t.ctrlpts[r]+t.ctrlpts[r+2])/2,(t.ctrlpts[r+1]+t.ctrlpts[r+3])/2);(t.allpts.push(t.endX,t.endY),t.ctrlpts.length/2%2==0)?(n=t.allpts.length/2-1,t.midX=t.allpts[n],t.midY=t.allpts[n+1]):(n=t.allpts.length/2-3,t.midX=tg(t.allpts[n],t.allpts[n+2],t.allpts[n+4],.5),t.midY=tg(t.allpts[n+1],t.allpts[n+3],t.allpts[n+5],.5))}else if("straight"===t.edgeType)t.allpts=[t.startX,t.startY,t.endX,t.endY],t.midX=(t.startX+t.endX+t.arrowStartX+t.arrowEndX)/4,t.midY=(t.startY+t.endY+t.arrowStartY+t.arrowEndY)/4;else if("segments"===t.edgeType){if(t.allpts=[],t.allpts.push(t.startX,t.startY),t.allpts.push.apply(t.allpts,t.segpts),t.allpts.push(t.endX,t.endY),t.isRound){t.roundCorners=[];for(var i=2;i+3<t.allpts.length;i+=2){var a=t.radii[i/2-1],o=t.isArcRadius[i/2-1];t.roundCorners.push(lA({x:t.allpts[i-2],y:t.allpts[i-1]},{x:t.allpts[i],y:t.allpts[i+1],radius:a},{x:t.allpts[i+2],y:t.allpts[i+3]},a,o))}}if(t.segpts.length%4==0){var s=t.segpts.length/2,l=s-2;t.midX=(t.segpts[l]+t.segpts[s])/2,t.midY=(t.segpts[l+1]+t.segpts[s+1])/2}else{var u=t.segpts.length/2-1;if(t.isRound){var c={x:t.segpts[u],y:t.segpts[u+1]},d=t.roundCorners[u/2];if(0===d.radius){var h={x:t.segpts[u+2],y:t.segpts[u+3]};t.midX=c.x,t.midY=c.y,t.midVector=[c.y-h.y,h.x-c.x]}else{var f=[c.x-d.cx,c.y-d.cy],p=d.radius/Math.sqrt(Math.pow(f[0],2)+Math.pow(f[1],2));f=f.map(function(e){return e*p}),t.midX=d.cx+f[0],t.midY=d.cy+f[1],t.midVector=f}}else t.midX=t.segpts[u],t.midY=t.segpts[u+1]}}},lR.checkForInvalidEdgeWarning=function(e){var t=e[0]._private.rscratch;t.nodesOverlap||M(t.startX)&&M(t.startY)&&M(t.endX)&&M(t.endY)?t.loggedErr=!1:t.loggedErr||(t.loggedErr=!0,ez("Edge `"+e.id()+"` has invalid endpoints and so it is impossible to draw.  Adjust your edge style (e.g. control points) accordingly or use an alternative edge type.  This is expected behaviour when the source node and the target node overlap."))},lR.findEdgeControlPoints=function(e){var t=this;if(e&&0!==e.length){for(var n=this,r=n.cy.hasCompoundNodes(),i=new eZ,a=function(e,t){return[].concat(u(e),[+!!t]).join("-")},o=[],s=[],l=0;l<e.length;l++){var c=e[l],d=c._private,h=c.pstyle("curve-style").value;if(!c.removed()&&c.takesUpSpace()){if("haystack"===h){s.push(c);continue}var f="unbundled-bezier"===h||U(h,"segments")||"straight"===h||"straight-triangle"===h||U(h,"taxi"),p="unbundled-bezier"===h||"bezier"===h,g=d.source,v=d.target,y=[g.poolIndex(),v.poolIndex()].sort(),b=a(y,f),x=i.get(b);null==x&&(x={eles:[]},o.push({pairId:y,edgeIsUnbundled:f}),i.set(b,x)),x.eles.push(c),f&&(x.hasUnbundled=!0),p&&(x.hasBezier=!0)}}for(var w=0;w<o.length;w++)!function(){var e,s=o[w],l=a(s.pairId,s.edgeIsUnbundled),u=i.get(l);if(!u.hasUnbundled){var c=u.eles[0].parallelEdges().filter(function(e){return e.isBundledBezier()});eW(u.eles),c.forEach(function(e){return u.eles.push(e)}),u.eles.sort(function(e,t){return e.poolIndex()-t.poolIndex()})}var d=u.eles[0],h=d.source(),f=d.target();if(h.poolIndex()>f.poolIndex()){var p=h;h=f,f=p}var g=u.srcPos=h.position(),v=u.tgtPos=f.position(),y=u.srcW=h.outerWidth(),b=u.srcH=h.outerHeight(),x=u.tgtW=f.outerWidth(),E=u.tgtH=f.outerHeight(),T=u.srcShape=n.nodeShapes[t.getNodeShape(h)],C=u.tgtShape=n.nodeShapes[t.getNodeShape(f)],k=u.srcCornerRadius="auto"===h.pstyle("corner-radius").value?"auto":h.pstyle("corner-radius").pfValue,P=u.tgtCornerRadius="auto"===f.pstyle("corner-radius").value?"auto":f.pstyle("corner-radius").pfValue,S=u.tgtRs=f._private.rscratch,B=u.srcRs=h._private.rscratch;u.dirCounts={north:0,west:0,south:0,east:0,northwest:0,southwest:0,northeast:0,southeast:0};for(var D=0;D<u.eles.length;D++){var _=u.eles[D],A=_[0]._private.rscratch,R=_.pstyle("curve-style").value,I="unbundled-bezier"===R||U(R,"segments")||U(R,"taxi"),N=!h.same(_.source());if(!u.calculatedIntersection&&h!==f&&(u.hasBezier||u.hasUnbundled)){u.calculatedIntersection=!0;var L=T.intersectLine(g.x,g.y,y,b,v.x,v.y,0,k,B),O=u.srcIntn=L,z=C.intersectLine(v.x,v.y,x,E,g.x,g.y,0,P,S),V=u.tgtIntn=z,F=u.intersectionPts={x1:L[0],x2:z[0],y1:L[1],y2:z[1]},X=u.posPts={x1:g.x,x2:v.x,y1:g.y,y2:v.y},j=z[1]-L[1],Y=z[0]-L[0],q=Math.sqrt(Y*Y+j*j);M(q)&&q>=lM||(q=Math.sqrt(Math.max(Y*Y,.01)+Math.max(j*j,.01)));var W=u.vector={x:Y,y:j},G=u.vectorNorm={x:W.x/q,y:W.y/q},H={x:-G.y,y:G.x};u.nodesOverlap=!M(q)||C.checkPoint(L[0],L[1],0,x,E,v.x,v.y,P,S)||T.checkPoint(z[0],z[1],0,y,b,g.x,g.y,k,B),u.vectorNormInverse=H,e={nodesOverlap:u.nodesOverlap,dirCounts:u.dirCounts,calculatedIntersection:!0,hasBezier:u.hasBezier,hasUnbundled:u.hasUnbundled,eles:u.eles,srcPos:v,srcRs:S,tgtPos:g,tgtRs:B,srcW:x,srcH:E,tgtW:y,tgtH:b,srcIntn:V,tgtIntn:O,srcShape:C,tgtShape:T,posPts:{x1:X.x2,y1:X.y2,x2:X.x1,y2:X.y1},intersectionPts:{x1:F.x2,y1:F.y2,x2:F.x1,y2:F.y1},vector:{x:-W.x,y:-W.y},vectorNorm:{x:-G.x,y:-G.y},vectorNormInverse:{x:-H.x,y:-H.y}}}var K=N?e:u;A.nodesOverlap=K.nodesOverlap,A.srcIntn=K.srcIntn,A.tgtIntn=K.tgtIntn,A.isRound=R.startsWith("round"),r&&(h.isParent()||h.isChild()||f.isParent()||f.isChild())&&(h.parents().anySame(f)||f.parents().anySame(h)||h.same(f)&&h.isParent())?t.findCompoundLoopPoints(_,K,D,I):h===f?t.findLoopPoints(_,K,D,I):R.endsWith("segments")?t.findSegmentsPoints(_,K):R.endsWith("taxi")?t.findTaxiPoints(_,K):"straight"!==R&&(I||u.eles.length%2!=1||D!==Math.floor(u.eles.length/2))?t.findBezierPoints(_,K,D,I,N):t.findStraightEdgePoints(_),t.findEndpoints(_),t.tryToCorrectInvalidPoints(_,K),t.checkForInvalidEdgeWarning(_),t.storeAllpts(_),t.storeEdgeProjections(_),t.calculateArrowAngles(_),t.recalculateEdgeLabelProjections(_),t.calculateLabelAngles(_)}}();this.findHaystackPoints(s)}},lR.getSegmentPoints=function(e){var t=e[0]._private.rscratch;if(this.recalculateRenderedStyle(e),"segments"===t.edgeType)return lI(t.segpts)},lR.getControlPoints=function(e){var t=e[0]._private.rscratch;this.recalculateRenderedStyle(e);var n=t.edgeType;if("bezier"===n||"multibezier"===n||"self"===n||"compound"===n)return lI(t.ctrlpts)},lR.getEdgeMidpoint=function(e){var t=e[0]._private.rscratch;return this.recalculateRenderedStyle(e),{x:t.midX,y:t.midY}};var lN={};lN.manualEndptToPx=function(e,t){var n=e.position(),r=e.outerWidth(),i=e.outerHeight(),a=e._private.rscratch;if(2===t.value.length){var o=[t.pfValue[0],t.pfValue[1]];return"%"===t.units[0]&&(o[0]=o[0]*r),"%"===t.units[1]&&(o[1]=o[1]*i),o[0]+=n.x,o[1]+=n.y,o}var s=t.pfValue[0];s=-Math.PI/2+s;var l=2*Math.max(r,i),u=[n.x+Math.cos(s)*l,n.y+Math.sin(s)*l];return this.nodeShapes[this.getNodeShape(e)].intersectLine(n.x,n.y,r,i,u[0],u[1],0,"auto"===e.pstyle("corner-radius").value?"auto":e.pstyle("corner-radius").pfValue,a)},lN.findEndpoints=function(e){var t,n,r,i,a,o,s,l,u,c=e.source()[0],d=e.target()[0],h=c.position(),f=d.position(),p=e.pstyle("target-arrow-shape").value,g=e.pstyle("source-arrow-shape").value,v=e.pstyle("target-distance-from-node").pfValue,y=e.pstyle("source-distance-from-node").pfValue,b=c._private.rscratch,x=d._private.rscratch,w=e.pstyle("curve-style").value,E=e._private.rscratch,T=E.edgeType,C=U(w,"taxi"),k="self"===T||"compound"===T,P="bezier"===T||"multibezier"===T||k,S="bezier"!==T,B="straight"===T||"segments"===T,D="segments"===T,_=k||C,A=e.pstyle("source-endpoint"),R=_?"outside-to-node":A.value,I="auto"===c.pstyle("corner-radius").value?"auto":c.pstyle("corner-radius").pfValue,N=e.pstyle("target-endpoint"),L=_?"outside-to-node":N.value,O="auto"===d.pstyle("corner-radius").value?"auto":d.pstyle("corner-radius").pfValue;E.srcManEndpt=A,E.tgtManEndpt=N;var z=null!=(t=(null==N||null==(n=N.pfValue)?void 0:n.length)===2?N.pfValue:null)?t:[0,0],V=null!=(r=(null==A||null==(i=A.pfValue)?void 0:i.length)===2?A.pfValue:null)?r:[0,0];if(P){var F=[E.ctrlpts[0],E.ctrlpts[1]];o=S?[E.ctrlpts[E.ctrlpts.length-2],E.ctrlpts[E.ctrlpts.length-1]]:F,s=F}else if(B){var X=D?E.segpts.slice(0,2):[f.x+z[0],f.y+z[1]];o=D?E.segpts.slice(E.segpts.length-2):[h.x+V[0],h.y+V[1]],s=X}if("inside-to-node"===L)a=[f.x,f.y];else if(N.units)a=this.manualEndptToPx(d,N);else if("outside-to-line"===L)a=E.tgtIntn;else if("outside-to-node"===L||"outside-to-node-or-label"===L?l=o:("outside-to-line"===L||"outside-to-line-or-label"===L)&&(l=[h.x,h.y]),a=this.nodeShapes[this.getNodeShape(d)].intersectLine(f.x,f.y,d.outerWidth(),d.outerHeight(),l[0],l[1],0,O,x),"outside-to-node-or-label"===L||"outside-to-line-or-label"===L){var j=d._private.rscratch,Y=j.labelWidth,q=j.labelHeight,W=j.labelX,G=j.labelY,H=Y/2,K=q/2,Z=d.pstyle("text-valign").value;"top"===Z?G-=K:"bottom"===Z&&(G+=K);var $=d.pstyle("text-halign").value;"left"===$?W-=H:"right"===$&&(W+=H);var Q=tK(l[0],l[1],[W-H,G-K,W+H,G-K,W+H,G+K,W-H,G+K],f.x,f.y);if(Q.length>0){var J=tf(h,ti(a)),ee=tf(h,ti(Q)),et=J;ee<J&&(a=Q,et=ee),Q.length>2&&tf(h,{x:Q[2],y:Q[3]})<et&&(a=[Q[2],Q[3]])}}var en=t$(a,o,this.arrowShapes[p].spacing(e)+v),er=t$(a,o,this.arrowShapes[p].gap(e)+v);if(E.endX=er[0],E.endY=er[1],E.arrowEndX=en[0],E.arrowEndY=en[1],"inside-to-node"===R)a=[h.x,h.y];else if(A.units)a=this.manualEndptToPx(c,A);else if("outside-to-line"===R)a=E.srcIntn;else if("outside-to-node"===R||"outside-to-node-or-label"===R?u=s:("outside-to-line"===R||"outside-to-line-or-label"===R)&&(u=[f.x,f.y]),a=this.nodeShapes[this.getNodeShape(c)].intersectLine(h.x,h.y,c.outerWidth(),c.outerHeight(),u[0],u[1],0,I,b),"outside-to-node-or-label"===R||"outside-to-line-or-label"===R){var ei=c._private.rscratch,ea=ei.labelWidth,eo=ei.labelHeight,es=ei.labelX,el=ei.labelY,eu=ea/2,ec=eo/2,ed=c.pstyle("text-valign").value;"top"===ed?el-=ec:"bottom"===ed&&(el+=ec);var eh=c.pstyle("text-halign").value;"left"===eh?es-=eu:"right"===eh&&(es+=eu);var ef=tK(u[0],u[1],[es-eu,el-ec,es+eu,el-ec,es+eu,el+ec,es-eu,el+ec],h.x,h.y);if(ef.length>0){var ep=tf(f,ti(a)),eg=tf(f,ti(ef)),ev=ep;eg<ep&&(a=[ef[0],ef[1]],ev=eg),ef.length>2&&tf(f,{x:ef[2],y:ef[3]})<ev&&(a=[ef[2],ef[3]])}}var ey=t$(a,s,this.arrowShapes[g].spacing(e)+y),em=t$(a,s,this.arrowShapes[g].gap(e)+y);E.startX=em[0],E.startY=em[1],E.arrowStartX=ey[0],E.arrowStartY=ey[1],(P||S||B)&&(M(E.startX)&&M(E.startY)&&M(E.endX)&&M(E.endY)?E.badLine=!1:E.badLine=!0)},lN.getSourceEndpoint=function(e){var t=e[0]._private.rscratch;return(this.recalculateRenderedStyle(e),"haystack"===t.edgeType)?{x:t.haystackPts[0],y:t.haystackPts[1]}:{x:t.arrowStartX,y:t.arrowStartY}},lN.getTargetEndpoint=function(e){var t=e[0]._private.rscratch;return(this.recalculateRenderedStyle(e),"haystack"===t.edgeType)?{x:t.haystackPts[2],y:t.haystackPts[3]}:{x:t.arrowEndX,y:t.arrowEndY}};var lL={};lL.storeEdgeProjections=function(e){var t=e._private,n=t.rscratch,r=n.edgeType;if(t.rstyle.bezierPts=null,t.rstyle.linePts=null,t.rstyle.haystackPts=null,"multibezier"===r||"bezier"===r||"self"===r||"compound"===r){t.rstyle.bezierPts=[];for(var i=0;i+5<n.allpts.length;i+=4)!function(e,t,n){for(var r=function(e,t,n,r){return tg(e,t,n,r)},i=t._private.rstyle.bezierPts,a=0;a<e.bezierProjPcts.length;a++){var o=e.bezierProjPcts[a];i.push({x:r(n[0],n[2],n[4],o),y:r(n[1],n[3],n[5],o)})}}(this,e,n.allpts.slice(i,i+6))}else if("segments"===r)for(var a=t.rstyle.linePts=[],i=0;i+1<n.allpts.length;i+=2)a.push({x:n.allpts[i],y:n.allpts[i+1]});else if("haystack"===r){var o=n.haystackPts;t.rstyle.haystackPts=[{x:o[0],y:o[1]},{x:o[2],y:o[3]}]}t.rstyle.arrowWidth=this.getArrowWidth(e.pstyle("width").pfValue,e.pstyle("arrow-scale").value)*this.arrowShapeWidth},lL.recalculateEdgeProjections=function(e){this.findEdgeControlPoints(e)};var lO={};lO.recalculateNodeLabelProjection=function(e){var t,n;if(!V(e.pstyle("label").strValue)){var r=e._private,i=e.width(),a=e.height(),o=e.padding(),s=e.position(),l=e.pstyle("text-halign").strValue,u=e.pstyle("text-valign").strValue,c=r.rscratch,d=r.rstyle;switch(l){case"left":t=s.x-i/2-o;break;case"right":t=s.x+i/2+o;break;default:t=s.x}switch(u){case"top":n=s.y-a/2-o;break;case"bottom":n=s.y+a/2+o;break;default:n=s.y}c.labelX=t,c.labelY=n,d.labelX=t,d.labelY=n,this.calculateLabelAngles(e),this.applyLabelDimensions(e)}};var lz=function(e,t){var n=Math.atan(t/e);return 0===e&&n<0&&(n*=-1),n},lV=function(e,t){return lz(t.x-e.x,t.y-e.y)},lF=function(e,t,n,r){var i=tm(0,r-.001,1),a=tm(0,r+.001,1);return lV(tv(e,t,n,i),tv(e,t,n,a))};lO.recalculateEdgeLabelProjections=function(e){var t,n=e._private,r=n.rscratch,i=this,a={mid:e.pstyle("label").strValue,source:e.pstyle("source-label").strValue,target:e.pstyle("target-label").strValue};if(a.mid||a.source||a.target){t={x:r.midX,y:r.midY};var o=function(e,t,r){eH(n.rscratch,e,t,r),eH(n.rstyle,e,t,r)};o("labelX",null,t.x),o("labelY",null,t.y),o("labelAutoAngle",null,lz(r.midDispX,r.midDispY));var s=function(){if(s.cache)return s.cache;for(var e=[],t=0;t+5<r.allpts.length;t+=4){var a={x:r.allpts[t],y:r.allpts[t+1]},o={x:r.allpts[t+2],y:r.allpts[t+3]},l={x:r.allpts[t+4],y:r.allpts[t+5]};e.push({p0:a,p1:o,p2:l,startDist:0,length:0,segments:[]})}var u=n.rstyle.bezierPts,c=i.bezierProjPcts.length;function d(e,t,n,r,i){var a=th(t,n),o=e.segments[e.segments.length-1],s={p0:t,p1:n,t0:r,t1:i,startDist:o?o.startDist+o.length:0,length:a};e.segments.push(s),e.length+=a}for(var h=0;h<e.length;h++){var f=e[h],p=e[h-1];p&&(f.startDist=p.startDist+p.length),d(f,f.p0,u[h*c],0,i.bezierProjPcts[0]);for(var g=0;g<c-1;g++)d(f,u[h*c+g],u[h*c+g+1],i.bezierProjPcts[g],i.bezierProjPcts[g+1]);d(f,u[h*c+c-1],f.p2,i.bezierProjPcts[c-1],1)}return s.cache=e},l=function(n){var i="source"===n;if(a[n]){var l=e.pstyle(n+"-text-offset").pfValue;switch(r.edgeType){case"self":case"compound":case"bezier":case"multibezier":for(var u,c,d=s(),h=0,f=0,p=0;p<d.length;p++){for(var g=d[i?p:d.length-1-p],v=0;v<g.segments.length;v++){var y=g.segments[i?v:g.segments.length-1-v],b=p===d.length-1&&v===g.segments.length-1;if(h=f,(f+=y.length)>=l||b){c={cp:g,segment:y};break}}if(c)break}var x=c.cp,w=c.segment,E=(l-h)/w.length,T=w.t1-w.t0,C=i?w.t0+T*E:w.t1-T*E;C=tm(0,C,1),t=tv(x.p0,x.p1,x.p2,C),u=lF(x.p0,x.p1,x.p2,C);break;case"straight":case"segments":case"haystack":for(var k,P,S,B,D=0,_=r.allpts.length,A=0;A+3<_&&(i?(k={x:r.allpts[A],y:r.allpts[A+1]},P={x:r.allpts[A+2],y:r.allpts[A+3]}):(k={x:r.allpts[_-2-A],y:r.allpts[_-1-A]},P={x:r.allpts[_-4-A],y:r.allpts[_-3-A]}),S=th(k,P),B=D,!((D+=S)>=l));A+=2);var M=(l-B)/S;t=ty(k,P,M=tm(0,M,1)),u=lV(k,P)}o("labelX",n,t.x),o("labelY",n,t.y),o("labelAutoAngle",n,u)}};l("source"),l("target"),this.applyLabelDimensions(e)}},lO.applyLabelDimensions=function(e){this.applyPrefixedLabelDimensions(e),e.isEdge()&&(this.applyPrefixedLabelDimensions(e,"source"),this.applyPrefixedLabelDimensions(e,"target"))},lO.applyPrefixedLabelDimensions=function(e,t){var n=e._private,r=this.getLabelText(e,t),i=ek(r,e._private.labelDimsKey);if(eG(n.rscratch,"prefixedLabelDimsKey",t)!==i){eH(n.rscratch,"prefixedLabelDimsKey",t,i);var a=this.calculateLabelDimensions(e,r),o=e.pstyle("line-height").pfValue,s=e.pstyle("text-wrap").strValue,l=eG(n.rscratch,"labelWrapCachedLines",t)||[],u="wrap"!==s?1:Math.max(l.length,1),c=a.height/u,d=a.width,h=a.height+(u-1)*(o-1)*c;eH(n.rstyle,"labelWidth",t,d),eH(n.rscratch,"labelWidth",t,d),eH(n.rstyle,"labelHeight",t,h),eH(n.rscratch,"labelHeight",t,h),eH(n.rscratch,"labelLineHeight",t,c*o)}},lO.getLabelText=function(e,t){var n=e._private,r=t?t+"-":"",i=e.pstyle(r+"label").strValue,a=e.pstyle("text-transform").value,s=function(e,r){return r?(eH(n.rscratch,e,t,r),r):eG(n.rscratch,e,t)};if(!i)return"";"none"==a||("uppercase"==a?i=i.toUpperCase():"lowercase"==a&&(i=i.toLowerCase()));var l=e.pstyle("text-wrap").value;if("wrap"===l){var u=s("labelKey");if(null!=u&&s("labelWrapKey")===u)return s("labelWrapCachedText");for(var c=i.split("\n"),d=e.pstyle("text-max-width").pfValue,h="anywhere"===e.pstyle("text-overflow-wrap").value,f=[],p=/[\s\u200b]+|$/g,g=0;g<c.length;g++){var v=c[g],y=this.calculateLabelDimensions(e,v).width;if(h&&(v=v.split("").join("​")),y>d){var b,x=v.matchAll(p),w="",E=0,T=o(x);try{for(T.s();!(b=T.n()).done;){var C=b.value,k=C[0],P=v.substring(E,C.index);E=C.index+k.length;var S=0===w.length?P:w+P+k;this.calculateLabelDimensions(e,S).width<=d?w+=P+k:(w&&f.push(w),w=P+k)}}catch(e){T.e(e)}finally{T.f()}w.match(/^[\s\u200b]+$/)||f.push(w)}else f.push(v)}s("labelWrapCachedLines",f),i=s("labelWrapCachedText",f.join("\n")),s("labelWrapKey",u)}else if("ellipsis"===l){var B=e.pstyle("text-max-width").pfValue,D="",_=!1;if(this.calculateLabelDimensions(e,i).width<B)return i;for(var A=0;A<i.length&&!(this.calculateLabelDimensions(e,D+i[A]+"…").width>B);A++)D+=i[A],A===i.length-1&&(_=!0);return _||(D+="…"),D}return i},lO.getLabelJustification=function(e){var t=e.pstyle("text-justification").strValue,n=e.pstyle("text-halign").strValue;if("auto"!==t)return t;if(!e.isNode())return"center";switch(n){case"left":return"right";case"right":return"left";default:return"center"}},lO.calculateLabelDimensions=function(e,t){var n=this.cy.window().document,r=e.pstyle("font-style").strValue,i=e.pstyle("font-size").pfValue,a=e.pstyle("font-family").strValue,o=e.pstyle("font-weight").strValue,s=this.labelCalcCanvas,l=this.labelCalcCanvasContext;if(!s){s=this.labelCalcCanvas=n.createElement("canvas"),l=this.labelCalcCanvasContext=s.getContext("2d");var u=s.style;u.position="absolute",u.left="-9999px",u.top="-9999px",u.zIndex="-1",u.visibility="hidden",u.pointerEvents="none"}l.font="".concat(r," ").concat(o," ").concat(i,"px ").concat(a);for(var c=0,d=0,h=t.split("\n"),f=0;f<h.length;f++){var p=h[f];c=Math.max(Math.ceil(l.measureText(p).width),c),d+=i}return{width:c+=0,height:d+=0}},lO.calculateLabelAngle=function(e,t){var n=e._private.rscratch,r=e.isEdge(),i=e.pstyle((t?t+"-":"")+"text-rotation"),a=i.strValue;return"none"===a?0:r&&"autorotate"===a?n.labelAutoAngle:"autorotate"===a?0:i.pfValue},lO.calculateLabelAngles=function(e){var t=e.isEdge(),n=e._private.rscratch;n.labelAngle=this.calculateLabelAngle(e),t&&(n.sourceLabelAngle=this.calculateLabelAngle(e,"source"),n.targetLabelAngle=this.calculateLabelAngle(e,"target"))};var lX={},lj=!1;lX.getNodeShape=function(e){var t=e.pstyle("shape").value;if("cutrectangle"===t&&(28>e.width()||28>e.height()))return lj||(ez("The `cutrectangle` node shape can not be used at small sizes so `rectangle` is used instead"),lj=!0),"rectangle";if(e.isParent())if("rectangle"===t||"roundrectangle"===t||"round-rectangle"===t||"cutrectangle"===t||"cut-rectangle"===t||"barrel"===t)return t;else return"rectangle";if("polygon"===t){var n=e.pstyle("shape-polygon-points").value;return this.nodeShapes.makePolygon(n).name}return t};var lY={};lY.registerCalculationListeners=function(){var e=this.cy,t=e.collection(),n=this,r=function(e){var n=!(arguments.length>1)||void 0===arguments[1]||arguments[1];if(t.merge(e),n)for(var r=0;r<e.length;r++){var i=e[r]._private.rstyle;i.clean=!1,i.cleanConnected=!1}};n.binder(e).on("bounds.* dirty.*",function(e){r(e.target)}).on("style.* background.*",function(e){r(e.target,!1)});var i=function(i){if(i){var a=n.onUpdateEleCalcsFns;t.cleanStyle();for(var o=0;o<t.length;o++){var s=t[o],l=s._private.rstyle;s.isNode()&&!l.cleanConnected&&(r(s.connectedEdges()),l.cleanConnected=!0)}if(a)for(var u=0;u<a.length;u++)(0,a[u])(i,t);n.recalculateRenderedStyle(t),t=e.collection()}};n.flushRenderedStyleQueue=function(){i(!0)},n.beforeRender(i,n.beforeRenderPriorities.eleCalcs)},lY.onUpdateEleCalcs=function(e){(this.onUpdateEleCalcsFns=this.onUpdateEleCalcsFns||[]).push(e)},lY.recalculateRenderedStyle=function(e,t){var n=function(e){return e._private.rstyle.cleanConnected};if(0!==e.length){var r=[],i=[];if(!this.destroyed){void 0===t&&(t=!0);for(var a=0;a<e.length;a++){var o=e[a],s=o._private,l=s.rstyle;!o.isEdge()||n(o.source())&&n(o.target())||(l.clean=!1),o.isEdge()&&o.isBundledBezier()&&o.parallelEdges().some(function(e){return!e._private.rstyle.clean&&e.isBundledBezier()})&&(l.clean=!1),t&&l.clean||o.removed()||"none"!==o.pstyle("display").value&&("nodes"===s.group?i.push(o):r.push(o),l.clean=!0)}for(var u=0;u<i.length;u++){var c=i[u],d=c._private.rstyle,h=c.position();this.recalculateNodeLabelProjection(c),d.nodeX=h.x,d.nodeY=h.y,d.nodeW=c.pstyle("width").pfValue,d.nodeH=c.pstyle("height").pfValue}this.recalculateEdgeProjections(r);for(var f=0;f<r.length;f++){var p=r[f]._private,g=p.rstyle,v=p.rscratch;g.srcX=v.arrowStartX,g.srcY=v.arrowStartY,g.tgtX=v.arrowEndX,g.tgtY=v.arrowEndY,g.midX=v.midX,g.midY=v.midY,g.labelAngle=v.labelAngle,g.sourceLabelAngle=v.sourceLabelAngle,g.targetLabelAngle=v.targetLabelAngle}}}};var lq={};lq.updateCachedGrabbedEles=function(){var e=this.cachedZSortedEles;if(e){e.drag=[],e.nondrag=[];for(var t=[],n=0;n<e.length;n++){var r=e[n],i=r._private.rscratch;r.grabbed()&&!r.isParent()?t.push(r):i.inDragLayer?e.drag.push(r):e.nondrag.push(r)}for(var n=0;n<t.length;n++){var r=t[n];e.drag.push(r)}}},lq.invalidateCachedZSortedEles=function(){this.cachedZSortedEles=null},lq.getCachedZSortedEles=function(e){if(e||!this.cachedZSortedEles){var t=this.cy.mutableElements().toArray();t.sort(iP),t.interactive=t.filter(function(e){return e.interactive()}),this.cachedZSortedEles=t,this.updateCachedGrabbedEles()}else t=this.cachedZSortedEles;return t};var lW={};[a4,a9,lR,lN,lL,lO,lX,lY,lq].forEach(function(e){J(lW,e)});var lU={};lU.getCachedImage=function(e,t,n){var r=this.imageCache=this.imageCache||{},i=r[e];if(i)return i.image.complete||i.image.addEventListener("load",n),i.image;var a=(i=r[e]=r[e]||{}).image=new Image;a.addEventListener("load",n),a.addEventListener("error",function(){a.error=!0});var o="data:";return e.substring(0,o.length).toLowerCase()!==o&&(a.crossOrigin=t="null"===t?null:t),a.src=e,a};var lG={};lG.registerBinding=function(e,t,n,r){var i=Array.prototype.slice.apply(arguments,[1]);if(Array.isArray(e)){for(var a=[],o=0;o<e.length;o++){var s=e[o];if(void 0!==s){var l=this.binder(s);a.push(l.on.apply(l,i))}}return a}var l=this.binder(e);return l.on.apply(l,i)},lG.binder=function(e){var t=this,n=t.cy.window(),r=e===n||e===n.document||e===n.document.body||"undefined"!=typeof HTMLElement&&e instanceof HTMLElement;if(null==t.supportsPassiveEvents){var i=!1;try{var a=Object.defineProperty({},"passive",{get:function(){return i=!0,!0}});n.addEventListener("test",null,a)}catch(e){}t.supportsPassiveEvents=i}var o=function(n,i,a){var o=Array.prototype.slice.call(arguments);return r&&t.supportsPassiveEvents&&(o[2]={capture:null!=a&&a,passive:!1,once:!1}),t.bindings.push({target:e,args:o}),(e.addEventListener||e.on).apply(e,o),this};return{on:o,addEventListener:o,addListener:o,bind:o}},lG.nodeIsDraggable=function(e){return e&&e.isNode()&&!e.locked()&&e.grabbable()},lG.nodeIsGrabbable=function(e){return this.nodeIsDraggable(e)&&e.interactive()},lG.load=function(){var e,t,n,r,i,a,o,s,l,u,c,d,h,f,p,g,v,y,b,x,w,E,T,C,k,P=this,S=P.cy.window(),B=function(e){return e.selected()},D=function(e,t,n,r){null==e&&(e=P.cy);for(var i=0;i<t.length;i++){var a=t[i];e.emit({originalEvent:n,type:a,position:r})}},_=function(e){return e.shiftKey||e.metaKey||e.ctrlKey},A=function(e,t){var n=!0;if(P.cy.hasCompoundNodes()&&e&&e.pannable())for(var r=0;t&&r<t.length;r++){var e=t[r];if(e.isNode()&&e.isParent()&&!e.pannable()){n=!1;break}}else n=!0;return n},R=function(e){e[0]._private.grabbed=!0},I=function(e){e[0]._private.grabbed=!1},N=function(e){e[0]._private.rscratch.inDragLayer=!0},L=function(e){e[0]._private.rscratch.inDragLayer=!1},O=function(e){e[0]._private.rscratch.isGrabTarget=!0},z=function(e){e[0]._private.rscratch.isGrabTarget=!1},V=function(e,t){var n=t.addToList;!n.has(e)&&e.grabbable()&&!e.locked()&&(n.merge(e),R(e))},F=function(e,t){if(e.cy().hasCompoundNodes()&&(null!=t.inDragLayer||null!=t.addToList)){var n=e.descendants();t.inDragLayer&&(n.forEach(N),n.connectedEdges().forEach(N)),t.addToList&&V(n,t)}},X=function(e,t){t=t||{};var n=e.cy().hasCompoundNodes();t.inDragLayer&&(e.forEach(N),e.neighborhood().stdFilter(function(e){return!n||e.isEdge()}).forEach(N)),t.addToList&&e.forEach(function(e){V(e,t)}),F(e,t),Y(e,{inDragLayer:t.inDragLayer}),P.updateCachedGrabbedEles()},j=function(e){e&&(P.getCachedZSortedEles().forEach(function(e){I(e),L(e),z(e)}),P.updateCachedGrabbedEles())},Y=function(e,t){if((null!=t.inDragLayer||null!=t.addToList)&&e.cy().hasCompoundNodes()){var n=e.ancestors().orphans();if(!n.same(e)){var r=n.descendants().spawnSelf().merge(n).unmerge(e).unmerge(e.descendants()),i=r.connectedEdges();t.inDragLayer&&(i.forEach(N),r.forEach(N)),t.addToList&&r.forEach(function(e){V(e,t)})}}},q=function(){null!=document.activeElement&&null!=document.activeElement.blur&&document.activeElement.blur()},W="undefined"!=typeof MutationObserver,U="undefined"!=typeof ResizeObserver;W?(P.removeObserver=new MutationObserver(function(e){for(var t=0;t<e.length;t++){var n=e[t].removedNodes;if(n){for(var r=0;r<n.length;r++)if(n[r]===P.container){P.destroy();break}}}}),P.container.parentNode&&P.removeObserver.observe(P.container.parentNode,{childList:!0})):P.registerBinding(P.container,"DOMNodeRemoved",function(e){P.destroy()});var G=ep(function(){P.cy.resize()},100);W&&(P.styleObserver=new MutationObserver(G),P.styleObserver.observe(P.container,{attributes:!0})),P.registerBinding(S,"resize",G),U&&(P.resizeObserver=new ResizeObserver(G),P.resizeObserver.observe(P.container));for(var H=function(){P.invalidateContainerClientCoordsCache()},K=P.container;null!=K;){e=K,P.registerBinding(e,"transitionend",H),P.registerBinding(e,"animationend",H),P.registerBinding(e,"scroll",H),K=K.parentNode}P.registerBinding(P.container,"contextmenu",function(e){e.preventDefault()});var Z=function(e){for(var t=P.findContainerClientCoords(),n=t[0],r=t[1],i=t[2],a=t[3],o=e.touches?e.touches:[e],s=!1,l=0;l<o.length;l++){var u=o[l];if(n<=u.clientX&&u.clientX<=n+i&&r<=u.clientY&&u.clientY<=r+a){s=!0;break}}if(!s)return!1;for(var c=P.container,d=e.target.parentNode,h=!1;d;){if(d===c){h=!0;break}d=d.parentNode}return!!h};P.registerBinding(P.container,"mousedown",function(e){if(Z(e)&&(1!==P.hoverData.which||1===e.which)){e.preventDefault(),q(),P.hoverData.capture=!0,P.hoverData.which=e.which;var t=P.cy,n=[e.clientX,e.clientY],r=P.projectIntoViewport(n[0],n[1]),i=P.selection,a=P.findNearestElements(r[0],r[1],!0,!1),o=a[0],s=P.dragData.possibleDragElements;P.hoverData.mdownPos=r,P.hoverData.mdownGPos=n;var l=function(t){return{originalEvent:e,type:t,position:{x:r[0],y:r[1]}}};if(3==e.which){P.hoverData.cxtStarted=!0;var u={originalEvent:e,type:"cxttapstart",position:{x:r[0],y:r[1]}};o?(o.activate(),o.emit(u),P.hoverData.down=o):t.emit(u),P.hoverData.downTime=new Date().getTime(),P.hoverData.cxtDragged=!1}else if(1==e.which){if(o&&o.activate(),null!=o&&P.nodeIsGrabbable(o)){if(O(o),o.selected()){s=P.dragData.possibleDragElements=t.collection();var c=t.$(function(e){return e.isNode()&&e.selected()&&P.nodeIsGrabbable(e)});X(c,{addToList:s}),o.emit(l("grabon")),c.forEach(function(e){e.emit(l("grab"))})}else X(o,{addToList:s=P.dragData.possibleDragElements=t.collection()}),o.emit(l("grabon")).emit(l("grab"));P.redrawHint("eles",!0),P.redrawHint("drag",!0)}P.hoverData.down=o,P.hoverData.downs=a,P.hoverData.downTime=new Date().getTime(),D(o,["mousedown","tapstart","vmousedown"],e,{x:r[0],y:r[1]}),null==o?(i[4]=1,P.data.bgActivePosistion={x:r[0],y:r[1]},P.redrawHint("select",!0),P.redraw()):o.pannable()&&(i[4]=1),P.hoverData.tapholdCancelled=!1,clearTimeout(P.hoverData.tapholdTimeout),P.hoverData.tapholdTimeout=setTimeout(function(){if(!P.hoverData.tapholdCancelled){var e=P.hoverData.down;e?e.emit(l("taphold")):t.emit(l("taphold"))}},P.tapholdDuration)}i[0]=i[2]=r[0],i[1]=i[3]=r[1]}},!1);var $=function(e){var t=e.getRootNode();if(t&&11===t.nodeType&&void 0!==t.host)return t}(P.container);P.registerBinding([S,$],"mousemove",function(e){var t,n;if(P.hoverData.capture||Z(e)){var r=!1,i=P.cy,a=i.zoom(),o=[e.clientX,e.clientY],s=P.projectIntoViewport(o[0],o[1]),l=P.hoverData.mdownPos,u=P.hoverData.mdownGPos,c=P.selection,d=null;P.hoverData.draggingEles||P.hoverData.dragging||P.hoverData.selecting||(d=P.findNearestElement(s[0],s[1],!0,!1));var h=P.hoverData.last,f=P.hoverData.down,p=[s[0]-c[2],s[1]-c[3]],g=P.dragData.possibleDragElements;if(u){var v=o[0]-u[0],y=o[1]-u[1];P.hoverData.isOverThresholdDrag=t=v*v+y*y>=P.desktopTapThreshold2}var b=_(e);t&&(P.hoverData.tapholdCancelled=!0),r=!0,D(d,["mousemove","vmousemove","tapdrag"],e,{x:s[0],y:s[1]});var x=function(t){return{originalEvent:e,type:t,position:{x:s[0],y:s[1]}}},w=function(){P.data.bgActivePosistion=void 0,P.hoverData.selecting||i.emit(x("boxstart")),c[4]=1,P.hoverData.selecting=!0,P.redrawHint("select",!0),P.redraw()};if(3===P.hoverData.which){if(t){var E=x("cxtdrag");f?f.emit(E):i.emit(E),P.hoverData.cxtDragged=!0,(!P.hoverData.cxtOver||d!==P.hoverData.cxtOver)&&(P.hoverData.cxtOver&&P.hoverData.cxtOver.emit(x("cxtdragout")),P.hoverData.cxtOver=d,d&&d.emit(x("cxtdragover")))}}else if(P.hoverData.dragging){if(r=!0,i.panningEnabled()&&i.userPanningEnabled()){if(P.hoverData.justStartedPan){var T=P.hoverData.mdownPos;n={x:(s[0]-T[0])*a,y:(s[1]-T[1])*a},P.hoverData.justStartedPan=!1}else n={x:p[0]*a,y:p[1]*a};i.panBy(n),i.emit(x("dragpan")),P.hoverData.dragged=!0}s=P.projectIntoViewport(e.clientX,e.clientY)}else if(1==c[4]&&(null==f||f.pannable()))t&&(!P.hoverData.dragging&&i.boxSelectionEnabled()&&(b||!i.panningEnabled()||!i.userPanningEnabled())?w():!P.hoverData.selecting&&i.panningEnabled()&&i.userPanningEnabled()&&A(f,P.hoverData.downs)&&(P.hoverData.dragging=!0,P.hoverData.justStartedPan=!0,c[4]=0,P.data.bgActivePosistion=ti(l),P.redrawHint("select",!0),P.redraw()),f&&f.pannable()&&f.active()&&f.unactivate());else{if(f&&f.pannable()&&f.active()&&f.unactivate(),f&&f.grabbed()||d==h||(h&&D(h,["mouseout","tapdragout"],e,{x:s[0],y:s[1]}),d&&D(d,["mouseover","tapdragover"],e,{x:s[0],y:s[1]}),P.hoverData.last=d),f)if(t){if(i.boxSelectionEnabled()&&b)f&&f.grabbed()&&(j(g),f.emit(x("freeon")),g.emit(x("free")),P.dragData.didDrag&&(f.emit(x("dragfreeon")),g.emit(x("dragfree")))),w();else if(f&&f.grabbed()&&P.nodeIsDraggable(f)){var C,k=!P.dragData.didDrag;k&&P.redrawHint("eles",!0),P.dragData.didDrag=!0,P.hoverData.draggingEles||X(g,{inDragLayer:!0});var S={x:0,y:0};if(M(p[0])&&M(p[1])&&(S.x+=p[0],S.y+=p[1],k)){var B=P.hoverData.dragDelta;B&&M(B[0])&&M(B[1])&&(S.x+=B[0],S.y+=B[1])}P.hoverData.draggingEles=!0,g.silentShift(S).emit(x("position")).emit(x("drag")),P.redrawHint("drag",!0),P.redraw()}}else 0===(C=P.hoverData.dragDelta=P.hoverData.dragDelta||[]).length?(C.push(p[0]),C.push(p[1])):(C[0]+=p[0],C[1]+=p[1]);r=!0}if(c[2]=s[0],c[3]=s[1],r)return e.stopPropagation&&e.stopPropagation(),e.preventDefault&&e.preventDefault(),!1}},!1),P.registerBinding(S,"mouseup",function(e){if((1!==P.hoverData.which||1===e.which||!P.hoverData.capture)&&P.hoverData.capture){P.hoverData.capture=!1;var i=P.cy,a=P.projectIntoViewport(e.clientX,e.clientY),o=P.selection,s=P.findNearestElement(a[0],a[1],!0,!1),l=P.dragData.possibleDragElements,u=P.hoverData.down,c=_(e);P.data.bgActivePosistion&&(P.redrawHint("select",!0),P.redraw()),P.hoverData.tapholdCancelled=!0,P.data.bgActivePosistion=void 0,u&&u.unactivate();var d=function(t){return{originalEvent:e,type:t,position:{x:a[0],y:a[1]}}};if(3===P.hoverData.which){var h=d("cxttapend");if(u?u.emit(h):i.emit(h),!P.hoverData.cxtDragged){var f=d("cxttap");u?u.emit(f):i.emit(f)}P.hoverData.cxtDragged=!1,P.hoverData.which=null}else if(1===P.hoverData.which){if(D(s,["mouseup","tapend","vmouseup"],e,{x:a[0],y:a[1]}),P.dragData.didDrag||P.hoverData.dragged||P.hoverData.selecting||P.hoverData.isOverThresholdDrag||(D(u,["click","tap","vclick"],e,{x:a[0],y:a[1]}),n=!1,e.timeStamp-r<=i.multiClickDebounceTime()?(t&&clearTimeout(t),n=!0,r=null,D(u,["dblclick","dbltap","vdblclick"],e,{x:a[0],y:a[1]})):(t=setTimeout(function(){n||D(u,["oneclick","onetap","voneclick"],e,{x:a[0],y:a[1]})},i.multiClickDebounceTime()),r=e.timeStamp)),null!=u||P.dragData.didDrag||P.hoverData.selecting||P.hoverData.dragged||_(e)||(i.$(B).unselect(["tapunselect"]),l.length>0&&P.redrawHint("eles",!0),P.dragData.possibleDragElements=l=i.collection()),s!=u||P.dragData.didDrag||P.hoverData.selecting||null==s||!s._private.selectable||(P.hoverData.dragging||("additive"===i.selectionType()||c?s.selected()?s.unselect(["tapunselect"]):s.select(["tapselect"]):c||(i.$(B).unmerge(s).unselect(["tapunselect"]),s.select(["tapselect"]))),P.redrawHint("eles",!0)),P.hoverData.selecting){var p=i.collection(P.getAllInBox(o[0],o[1],o[2],o[3]));P.redrawHint("select",!0),p.length>0&&P.redrawHint("eles",!0),i.emit(d("boxend"));"additive"===i.selectionType()||c||i.$(B).unmerge(p).unselect(),p.emit(d("box")).stdFilter(function(e){return e.selectable()&&!e.selected()}).select().emit(d("boxselect")),P.redraw()}if(P.hoverData.dragging&&(P.hoverData.dragging=!1,P.redrawHint("select",!0),P.redrawHint("eles",!0),P.redraw()),!o[4]){P.redrawHint("drag",!0),P.redrawHint("eles",!0);var g=u&&u.grabbed();j(l),g&&(u.emit(d("freeon")),l.emit(d("free")),P.dragData.didDrag&&(u.emit(d("dragfreeon")),l.emit(d("dragfree"))))}}o[4]=0,P.hoverData.down=null,P.hoverData.cxtStarted=!1,P.hoverData.draggingEles=!1,P.hoverData.selecting=!1,P.hoverData.isOverThresholdDrag=!1,P.dragData.didDrag=!1,P.hoverData.dragged=!1,P.hoverData.dragDelta=[],P.hoverData.mdownPos=null,P.hoverData.mdownGPos=null,P.hoverData.which=null}},!1);var Q=[],J=1e5,ee=function(e,t){for(var n=0;n<e.length;n++)if(e[n]%t!=0)return!1;return!0},et=function(e){for(var t=Math.abs(e[0]),n=1;n<e.length;n++)if(Math.abs(e[n])!==t)return!1;return!0},en=function(e){var t=!1,n=e.deltaY;if(null==n&&(null!=e.wheelDeltaY?n=e.wheelDeltaY/4:null!=e.wheelDelta&&(n=e.wheelDelta/4)),0!==n){if(null==i)if(Q.length>=4){if(!(i=ee(Q,5))){var r=Math.abs(Q[0]);i=et(Q)&&r>5}if(i)for(var a=0;a<Q.length;a++)J=Math.min(Math.abs(Q[a]),J)}else Q.push(n),t=!0;else i&&(J=Math.min(Math.abs(n),J));if(!P.scrollingPage){var o=P.cy,s=o.zoom(),l=o.pan(),u=P.projectIntoViewport(e.clientX,e.clientY),c=[u[0]*s+l.x,u[1]*s+l.y];if(P.hoverData.draggingEles||P.hoverData.dragging||P.hoverData.cxtStarted||0!==P.selection[4])return void e.preventDefault();if(o.panningEnabled()&&o.userPanningEnabled()&&o.zoomingEnabled()&&o.userZoomingEnabled()){e.preventDefault(),P.data.wheelZooming=!0,clearTimeout(P.data.wheelTimeout),P.data.wheelTimeout=setTimeout(function(){P.data.wheelZooming=!1,P.redrawHint("eles",!0),P.redraw()},150),t&&Math.abs(n)>5&&(n=5*td(n)),d=-(n/250),i&&(d/=J,d*=3),d*=P.wheelSensitivity,1===e.deltaMode&&(d*=33);var d,h=o.zoom()*Math.pow(10,d);"gesturechange"===e.type&&(h=P.gestureStartZoom*e.scale),o.zoom({level:h,renderedPosition:{x:c[0],y:c[1]}}),o.emit({type:"gesturechange"===e.type?"pinchzoom":"scrollzoom",originalEvent:e,position:{x:u[0],y:u[1]}})}}}};P.registerBinding(P.container,"wheel",en,!0),P.registerBinding(S,"scroll",function(e){P.scrollingPage=!0,clearTimeout(P.scrollingPageTimeout),P.scrollingPageTimeout=setTimeout(function(){P.scrollingPage=!1},250)},!0),P.registerBinding(P.container,"gesturestart",function(e){P.gestureStartZoom=P.cy.zoom(),P.hasTouchStarted||e.preventDefault()},!0),P.registerBinding(P.container,"gesturechange",function(e){P.hasTouchStarted||en(e)},!0),P.registerBinding(P.container,"mouseout",function(e){var t=P.projectIntoViewport(e.clientX,e.clientY);P.cy.emit({originalEvent:e,type:"mouseout",position:{x:t[0],y:t[1]}})},!1),P.registerBinding(P.container,"mouseover",function(e){var t=P.projectIntoViewport(e.clientX,e.clientY);P.cy.emit({originalEvent:e,type:"mouseover",position:{x:t[0],y:t[1]}})},!1);var er=function(e,t,n,r){return Math.sqrt((n-e)*(n-e)+(r-t)*(r-t))},ei=function(e,t,n,r){return(n-e)*(n-e)+(r-t)*(r-t)};if(P.registerBinding(P.container,"touchstart",b=function(e){if(P.hasTouchStarted=!0,Z(e)){q(),P.touchData.capture=!0,P.data.bgActivePosistion=void 0;var t=P.cy,n=P.touchData.now,r=P.touchData.earlier;if(e.touches[0]){var i=P.projectIntoViewport(e.touches[0].clientX,e.touches[0].clientY);n[0]=i[0],n[1]=i[1]}if(e.touches[1]){var i=P.projectIntoViewport(e.touches[1].clientX,e.touches[1].clientY);n[2]=i[0],n[3]=i[1]}if(e.touches[2]){var i=P.projectIntoViewport(e.touches[2].clientX,e.touches[2].clientY);n[4]=i[0],n[5]=i[1]}var b=function(t){return{originalEvent:e,type:t,position:{x:n[0],y:n[1]}}};if(e.touches[1]){P.touchData.singleTouchMoved=!0,j(P.dragData.touchDragEles);var x=P.findContainerClientCoords();f=x[0],p=x[1],g=x[2],v=x[3],a=e.touches[0].clientX-f,o=e.touches[0].clientY-p,s=e.touches[1].clientX-f,l=e.touches[1].clientY-p,y=0<=a&&a<=g&&0<=s&&s<=g&&0<=o&&o<=v&&0<=l&&l<=v;var w=t.pan(),E=t.zoom();if(u=er(a,o,s,l),c=ei(a,o,s,l),h=[((d=[(a+s)/2,(o+l)/2])[0]-w.x)/E,(d[1]-w.y)/E],c<4e4&&!e.touches[2]){var T=P.findNearestElement(n[0],n[1],!0,!0),C=P.findNearestElement(n[2],n[3],!0,!0);T&&T.isNode()?(T.activate().emit(b("cxttapstart")),P.touchData.start=T):C&&C.isNode()?(C.activate().emit(b("cxttapstart")),P.touchData.start=C):t.emit(b("cxttapstart")),P.touchData.start&&(P.touchData.start._private.grabbed=!1),P.touchData.cxt=!0,P.touchData.cxtDragged=!1,P.data.bgActivePosistion=void 0,P.redraw();return}}if(e.touches[2])t.boxSelectionEnabled()&&e.preventDefault();else if(e.touches[1]);else if(e.touches[0]){var k=P.findNearestElements(n[0],n[1],!0,!0),S=k[0];if(null!=S&&(S.activate(),P.touchData.start=S,P.touchData.starts=k,P.nodeIsGrabbable(S))){var B=P.dragData.touchDragEles=t.collection(),_=null;P.redrawHint("eles",!0),P.redrawHint("drag",!0),S.selected()?X(_=t.$(function(e){return e.selected()&&P.nodeIsGrabbable(e)}),{addToList:B}):X(S,{addToList:B}),O(S),S.emit(b("grabon")),_?_.forEach(function(e){e.emit(b("grab"))}):S.emit(b("grab"))}D(S,["touchstart","tapstart","vmousedown"],e,{x:n[0],y:n[1]}),null==S&&(P.data.bgActivePosistion={x:i[0],y:i[1]},P.redrawHint("select",!0),P.redraw()),P.touchData.singleTouchMoved=!1,P.touchData.singleTouchStartTime=+new Date,clearTimeout(P.touchData.tapholdTimeout),P.touchData.tapholdTimeout=setTimeout(function(){!1!==P.touchData.singleTouchMoved||P.pinching||P.touchData.selecting||D(P.touchData.start,["taphold"],e,{x:n[0],y:n[1]})},P.tapholdDuration)}if(e.touches.length>=1){for(var A=P.touchData.startPosition=[null,null,null,null,null,null],M=0;M<n.length;M++)A[M]=r[M]=n[M];var R=e.touches[0];P.touchData.startGPosition=[R.clientX,R.clientY]}}},!1),P.registerBinding(S,"touchmove",x=function(e){var t=P.touchData.capture;if(t||Z(e)){var n=P.selection,r=P.cy,i=P.touchData.now,d=P.touchData.earlier,g=r.zoom();if(e.touches[0]){var v=P.projectIntoViewport(e.touches[0].clientX,e.touches[0].clientY);i[0]=v[0],i[1]=v[1]}if(e.touches[1]){var v=P.projectIntoViewport(e.touches[1].clientX,e.touches[1].clientY);i[2]=v[0],i[3]=v[1]}if(e.touches[2]){var v=P.projectIntoViewport(e.touches[2].clientX,e.touches[2].clientY);i[4]=v[0],i[5]=v[1]}var b=function(t){return{originalEvent:e,type:t,position:{x:i[0],y:i[1]}}},x=P.touchData.startGPosition;if(t&&e.touches[0]&&x){for(var w=[],E=0;E<i.length;E++)w[E]=i[E]-d[E];var T=e.touches[0].clientX-x[0],C=T*T,k=e.touches[0].clientY-x[1];en=C+k*k>=P.touchTapThreshold2}if(t&&P.touchData.cxt){e.preventDefault();var S=e.touches[0].clientX-f,B=e.touches[0].clientY-p,_=e.touches[1].clientX-f,R=e.touches[1].clientY-p,I=ei(S,B,_,R);if(I/c>=2.25||I>=22500){P.touchData.cxt=!1,P.data.bgActivePosistion=void 0,P.redrawHint("select",!0);var N=b("cxttapend");P.touchData.start?(P.touchData.start.unactivate().emit(N),P.touchData.start=null):r.emit(N)}}if(t&&P.touchData.cxt){var N=b("cxtdrag");P.data.bgActivePosistion=void 0,P.redrawHint("select",!0),P.touchData.start?P.touchData.start.emit(N):r.emit(N),P.touchData.start&&(P.touchData.start._private.grabbed=!1),P.touchData.cxtDragged=!0;var L=P.findNearestElement(i[0],i[1],!0,!0);(!P.touchData.cxtOver||L!==P.touchData.cxtOver)&&(P.touchData.cxtOver&&P.touchData.cxtOver.emit(b("cxtdragout")),P.touchData.cxtOver=L,L&&L.emit(b("cxtdragover")))}else if(t&&e.touches[2]&&r.boxSelectionEnabled())e.preventDefault(),P.data.bgActivePosistion=void 0,this.lastThreeTouch=+new Date,P.touchData.selecting||r.emit(b("boxstart")),P.touchData.selecting=!0,P.touchData.didSelect=!0,n[4]=1,n&&0!==n.length&&void 0!==n[0]?(n[2]=(i[0]+i[2]+i[4])/3,n[3]=(i[1]+i[3]+i[5])/3):(n[0]=(i[0]+i[2]+i[4])/3,n[1]=(i[1]+i[3]+i[5])/3,n[2]=(i[0]+i[2]+i[4])/3+1,n[3]=(i[1]+i[3]+i[5])/3+1),P.redrawHint("select",!0),P.redraw();else if(t&&e.touches[1]&&!P.touchData.didSelect&&r.zoomingEnabled()&&r.panningEnabled()&&r.userZoomingEnabled()&&r.userPanningEnabled()){e.preventDefault(),P.data.bgActivePosistion=void 0,P.redrawHint("select",!0);var O=P.dragData.touchDragEles;if(O){P.redrawHint("drag",!0);for(var z=0;z<O.length;z++){var V=O[z]._private;V.grabbed=!1,V.rscratch.inDragLayer=!1}}var F=P.touchData.start,S=e.touches[0].clientX-f,B=e.touches[0].clientY-p,_=e.touches[1].clientX-f,R=e.touches[1].clientY-p,Y=er(S,B,_,R),q=Y/u;if(y){var W=S-a,U=B-o,G=_-s,H=R-l,K=r.zoom(),$=K*q,Q=r.pan(),J=h[0]*K+Q.x,ee=h[1]*K+Q.y,et={x:-$/K*(J-Q.x-(W+G)/2)+J,y:-$/K*(ee-Q.y-(U+H)/2)+ee};if(F&&F.active()){var O=P.dragData.touchDragEles;j(O),P.redrawHint("drag",!0),P.redrawHint("eles",!0),F.unactivate().emit(b("freeon")),O.emit(b("free")),P.dragData.didDrag&&(F.emit(b("dragfreeon")),O.emit(b("dragfree")))}r.viewport({zoom:$,pan:et,cancelOnFailedZoom:!0}),r.emit(b("pinchzoom")),u=Y,a=S,o=B,s=_,l=R,P.pinching=!0}if(e.touches[0]){var v=P.projectIntoViewport(e.touches[0].clientX,e.touches[0].clientY);i[0]=v[0],i[1]=v[1]}if(e.touches[1]){var v=P.projectIntoViewport(e.touches[1].clientX,e.touches[1].clientY);i[2]=v[0],i[3]=v[1]}if(e.touches[2]){var v=P.projectIntoViewport(e.touches[2].clientX,e.touches[2].clientY);i[4]=v[0],i[5]=v[1]}}else if(e.touches[0]&&!P.touchData.didSelect){var en,L,ea=P.touchData.start,eo=P.touchData.last;if(P.hoverData.draggingEles||P.swipePanning||(L=P.findNearestElement(i[0],i[1],!0,!0)),t&&null!=ea&&e.preventDefault(),t&&null!=ea&&P.nodeIsDraggable(ea))if(en){var O=P.dragData.touchDragEles,es=!P.dragData.didDrag;es&&X(O,{inDragLayer:!0}),P.dragData.didDrag=!0;var el={x:0,y:0};if(M(w[0])&&M(w[1])&&(el.x+=w[0],el.y+=w[1],es)){P.redrawHint("eles",!0);var eu=P.touchData.dragDelta;eu&&M(eu[0])&&M(eu[1])&&(el.x+=eu[0],el.y+=eu[1])}P.hoverData.draggingEles=!0,O.silentShift(el).emit(b("position")).emit(b("drag")),P.redrawHint("drag",!0),P.touchData.startPosition[0]==d[0]&&P.touchData.startPosition[1]==d[1]&&P.redrawHint("eles",!0),P.redraw()}else{var eu=P.touchData.dragDelta=P.touchData.dragDelta||[];0===eu.length?(eu.push(w[0]),eu.push(w[1])):(eu[0]+=w[0],eu[1]+=w[1])}if(D(ea||L,["touchmove","tapdrag","vmousemove"],e,{x:i[0],y:i[1]}),ea&&ea.grabbed()||L==eo||(eo&&eo.emit(b("tapdragout")),L&&L.emit(b("tapdragover"))),P.touchData.last=L,t)for(var z=0;z<i.length;z++)i[z]&&P.touchData.startPosition[z]&&en&&(P.touchData.singleTouchMoved=!0);if(t&&(null==ea||ea.pannable())&&r.panningEnabled()&&r.userPanningEnabled()){A(ea,P.touchData.starts)&&(e.preventDefault(),P.data.bgActivePosistion||(P.data.bgActivePosistion=ti(P.touchData.startPosition)),P.swipePanning?(r.panBy({x:w[0]*g,y:w[1]*g}),r.emit(b("dragpan"))):en&&(P.swipePanning=!0,r.panBy({x:T*g,y:k*g}),r.emit(b("dragpan")),ea&&(ea.unactivate(),P.redrawHint("select",!0),P.touchData.start=null)));var v=P.projectIntoViewport(e.touches[0].clientX,e.touches[0].clientY);i[0]=v[0],i[1]=v[1]}}for(var E=0;E<i.length;E++)d[E]=i[E];t&&e.touches.length>0&&!P.hoverData.draggingEles&&!P.swipePanning&&null!=P.data.bgActivePosistion&&(P.data.bgActivePosistion=void 0,P.redrawHint("select",!0),P.redraw())}},!1),P.registerBinding(S,"touchcancel",w=function(e){var t=P.touchData.start;P.touchData.capture=!1,t&&t.unactivate()}),P.registerBinding(S,"touchend",E=function(e){var t,n=P.touchData.start;if(P.touchData.capture){0===e.touches.length&&(P.touchData.capture=!1),e.preventDefault();var r=P.selection;P.swipePanning=!1,P.hoverData.draggingEles=!1;var i=P.cy,a=i.zoom(),o=P.touchData.now,s=P.touchData.earlier;if(e.touches[0]){var l=P.projectIntoViewport(e.touches[0].clientX,e.touches[0].clientY);o[0]=l[0],o[1]=l[1]}if(e.touches[1]){var l=P.projectIntoViewport(e.touches[1].clientX,e.touches[1].clientY);o[2]=l[0],o[3]=l[1]}if(e.touches[2]){var l=P.projectIntoViewport(e.touches[2].clientX,e.touches[2].clientY);o[4]=l[0],o[5]=l[1]}var u=function(t){return{originalEvent:e,type:t,position:{x:o[0],y:o[1]}}};if(n&&n.unactivate(),P.touchData.cxt){if(t=u("cxttapend"),n?n.emit(t):i.emit(t),!P.touchData.cxtDragged){var c=u("cxttap");n?n.emit(c):i.emit(c)}P.touchData.start&&(P.touchData.start._private.grabbed=!1),P.touchData.cxt=!1,P.touchData.start=null,P.redraw();return}if(!e.touches[2]&&i.boxSelectionEnabled()&&P.touchData.selecting){P.touchData.selecting=!1;var d=i.collection(P.getAllInBox(r[0],r[1],r[2],r[3]));r[0]=void 0,r[1]=void 0,r[2]=void 0,r[3]=void 0,r[4]=0,P.redrawHint("select",!0),i.emit(u("boxend")),d.emit(u("box")).stdFilter(function(e){return e.selectable()&&!e.selected()}).select().emit(u("boxselect")),d.nonempty()&&P.redrawHint("eles",!0),P.redraw()}if(null!=n&&n.unactivate(),e.touches[2])P.data.bgActivePosistion=void 0,P.redrawHint("select",!0);else if(e.touches[1]);else if(e.touches[0]);else if(!e.touches[0]){P.data.bgActivePosistion=void 0,P.redrawHint("select",!0);var h=P.dragData.touchDragEles;if(null!=n){var f=n._private.grabbed;j(h),P.redrawHint("drag",!0),P.redrawHint("eles",!0),f&&(n.emit(u("freeon")),h.emit(u("free")),P.dragData.didDrag&&(n.emit(u("dragfreeon")),h.emit(u("dragfree")))),D(n,["touchend","tapend","vmouseup","tapdragout"],e,{x:o[0],y:o[1]}),n.unactivate(),P.touchData.start=null}else D(P.findNearestElement(o[0],o[1],!0,!0),["touchend","tapend","vmouseup","tapdragout"],e,{x:o[0],y:o[1]});var p=P.touchData.startPosition[0]-o[0],g=P.touchData.startPosition[1]-o[1];P.touchData.singleTouchMoved||(n||i.$(":selected").unselect(["tapunselect"]),D(n,["tap","vclick"],e,{x:o[0],y:o[1]}),T=!1,e.timeStamp-k<=i.multiClickDebounceTime()?(C&&clearTimeout(C),T=!0,k=null,D(n,["dbltap","vdblclick"],e,{x:o[0],y:o[1]})):(C=setTimeout(function(){T||D(n,["onetap","voneclick"],e,{x:o[0],y:o[1]})},i.multiClickDebounceTime()),k=e.timeStamp)),null!=n&&!P.dragData.didDrag&&n._private.selectable&&(p*p+g*g)*a*a<P.touchTapThreshold2&&!P.pinching&&("single"===i.selectionType()?(i.$(B).unmerge(n).unselect(["tapunselect"]),n.select(["tapselect"])):n.selected()?n.unselect(["tapunselect"]):n.select(["tapselect"]),P.redrawHint("eles",!0)),P.touchData.singleTouchMoved=!0}for(var v=0;v<o.length;v++)s[v]=o[v];P.dragData.didDrag=!1,0===e.touches.length&&(P.touchData.dragDelta=[],P.touchData.startPosition=[null,null,null,null,null,null],P.touchData.startGPosition=null,P.touchData.didSelect=!1),e.touches.length<2&&(1===e.touches.length&&(P.touchData.startGPosition=[e.touches[0].clientX,e.touches[0].clientY]),P.pinching=!1,P.redrawHint("eles",!0),P.redraw())}},!1),"undefined"==typeof TouchEvent){var ea=[],eo=function(e){return{clientX:e.clientX,clientY:e.clientY,force:1,identifier:e.pointerId,pageX:e.pageX,pageY:e.pageY,radiusX:e.width/2,radiusY:e.height/2,screenX:e.screenX,screenY:e.screenY,target:e.target}},es=function(e){ea.push({event:e,touch:eo(e)})},el=function(e){for(var t=0;t<ea.length;t++)if(ea[t].event.pointerId===e.pointerId)return void ea.splice(t,1)},eu=function(e){var t=ea.filter(function(t){return t.event.pointerId===e.pointerId})[0];t.event=e,t.touch=eo(e)},ec=function(e){e.touches=ea.map(function(e){return e.touch})},ed=function(e){return"mouse"===e.pointerType||4===e.pointerType};P.registerBinding(P.container,"pointerdown",function(e){ed(e)||(e.preventDefault(),es(e),ec(e),b(e))}),P.registerBinding(P.container,"pointerup",function(e){ed(e)||(el(e),ec(e),E(e))}),P.registerBinding(P.container,"pointercancel",function(e){ed(e)||(el(e),ec(e),w(e))}),P.registerBinding(P.container,"pointermove",function(e){ed(e)||(e.preventDefault(),eu(e),ec(e),x(e))})}};var lH={};lH.generatePolygon=function(e,t){return this.nodeShapes[e]={renderer:this,name:e,points:t,draw:function(e,t,n,r,i,a){this.renderer.nodeShapeImpl("polygon",e,t,n,r,i,this.points)},intersectLine:function(e,t,n,r,i,a,o,s){return tK(i,a,this.points,e,t,n/2,r/2,o)},checkPoint:function(e,t,n,r,i,a,o,s){return tV(e,t,this.points,a,o,r,i,[0,-1],n)},hasMiterBounds:"rectangle"!==e,miterBounds:function(e,t,n,r,i,a){var o,s,l;return o=this.points,s=function(e,t){if(e.length<3)throw Error("Need at least 3 vertices");var n=function(e,t){return{x:e.x+t.x,y:e.y+t.y}},r=function(e,t){return{x:e.x-t.x,y:e.y-t.y}},i=function(e,t){return{x:e.x*t,y:e.y*t}},a=function(e,t){return e.x*t.y-e.y*t.x},o=e.map(function(e){return{x:e.x,y:e.y}});0>function(e){for(var t=0,n=0;n<e.length;n++){var r=e[n],i=e[(n+1)%e.length];t+=r.x*i.y-i.x*r.y}return t/2}(o)&&o.reverse();for(var s=o.length,l=[],u=0;u<s;u++){var c=o[u],d=r(o[(u+1)%s],c),h=function(e){var t=t_(e.x,e.y);return 0===t?{x:0,y:0}:{x:e.x/t,y:e.y/t}}({x:d.y,y:-d.x});l.push(h)}for(var f=l.map(function(e,r){return{p1:n(o[r],i(e,t)),p2:n(o[(r+1)%s],i(e,t))}}),p=[],g=0;g<s;g++){var v=f[(g-1+s)%s],y=f[g],b=function(e,t,o,s){var l=r(t,e),u=r(s,o),c=a(l,u);if(1e-9>Math.abs(c))return n(e,i(l,.5));var d=a(r(o,e),u)/c;return n(e,i(l,d))}(v.p1,v.p2,y.p1,y.p2);p.push(b)}return p}(tH(o,e,t,n,r),i),l=tb(),s.forEach(function(e){return tE(l,e.x,e.y)}),l}}},lH.generateEllipse=function(){return this.nodeShapes.ellipse={renderer:this,name:"ellipse",draw:function(e,t,n,r,i,a){this.renderer.nodeShapeImpl(this.name,e,t,n,r,i)},intersectLine:function(e,t,n,r,i,a,o,s){return tY(i,a,e,t,n/2+o,r/2+o)},checkPoint:function(e,t,n,r,i,a,o,s){return tq(e,t,r,i,a,o,n)}}},lH.generateRoundPolygon=function(e,t){return this.nodeShapes[e]={renderer:this,name:e,points:t,getOrCreateCorners:function(e,n,r,i,a,o,s){if(void 0!==o[s]&&o[s+"-cx"]===e&&o[s+"-cy"]===n)return o[s];o[s]=Array(t.length/2),o[s+"-cx"]=e,o[s+"-cy"]=n;var l=r/2,u=i/2;a="auto"===a?t2(r,i):a;for(var c=Array(t.length/2),d=0;d<t.length/2;d++)c[d]={x:e+l*t[2*d],y:n+u*t[2*d+1]};var h,f,p,g,v=c.length;for(h=0,f=c[v-1];h<v;h++)p=c[h%v],g=c[(h+1)%v],o[s][h]=lA(f,p,g,a),f=p,p=g;return o[s]},draw:function(e,t,n,r,i,a,o){this.renderer.nodeShapeImpl("round-polygon",e,t,n,r,i,this.points,this.getOrCreateCorners(t,n,r,i,a,o,"drawCorners"))},intersectLine:function(e,t,n,r,i,a,o,s,l){return tZ(i,a,this.points,e,t,n,r,o,this.getOrCreateCorners(e,t,n,r,s,l,"corners"))},checkPoint:function(e,t,n,r,i,a,o,s,l){return tF(e,t,this.points,a,o,r,i,this.getOrCreateCorners(a,o,r,i,s,l,"corners"))}}},lH.generateRoundRectangle=function(){return this.nodeShapes["round-rectangle"]=this.nodeShapes.roundrectangle={renderer:this,name:"round-rectangle",points:tQ(4,0),draw:function(e,t,n,r,i,a){this.renderer.nodeShapeImpl(this.name,e,t,n,r,i,this.points,a)},intersectLine:function(e,t,n,r,i,a,o,s){return tA(i,a,e,t,n,r,o,s)},checkPoint:function(e,t,n,r,i,a,o,s){var l=r/2,u=i/2,c=2*(s=Math.min(l,u,s="auto"===s?t1(r,i):s));return!!(tV(e,t,this.points,a,o,r,i-c,[0,-1],n)||tV(e,t,this.points,a,o,r-c,i,[0,-1],n)||tq(e,t,c,c,a-l+s,o-u+s,n)||tq(e,t,c,c,a+l-s,o-u+s,n)||tq(e,t,c,c,a+l-s,o+u-s,n)||tq(e,t,c,c,a-l+s,o+u-s,n))}}},lH.generateCutRectangle=function(){return this.nodeShapes["cut-rectangle"]=this.nodeShapes.cutrectangle={renderer:this,name:"cut-rectangle",cornerLength:t5(),points:tQ(4,0),draw:function(e,t,n,r,i,a){this.renderer.nodeShapeImpl(this.name,e,t,n,r,i,null,a)},generateCutTrianglePts:function(e,t,n,r,i){var a="auto"===i?this.cornerLength:i,o=t/2,s=e/2,l=n-s,u=n+s,c=r-o,d=r+o;return{topLeft:[l,c+a,l+a,c,l+a,c+a],topRight:[u-a,c,u,c+a,u-a,c+a],bottomRight:[u,d-a,u-a,d,u-a,d-a],bottomLeft:[l+a,d,l,d-a,l+a,d-a]}},intersectLine:function(e,t,n,r,i,a,o,s){var l=this.generateCutTrianglePts(n+2*o,r+2*o,e,t,s);return tK(i,a,[].concat.apply([],[l.topLeft.splice(0,4),l.topRight.splice(0,4),l.bottomRight.splice(0,4),l.bottomLeft.splice(0,4)]),e,t)},checkPoint:function(e,t,n,r,i,a,o,s){var l="auto"===s?this.cornerLength:s;if(tV(e,t,this.points,a,o,r,i-2*l,[0,-1],n)||tV(e,t,this.points,a,o,r-2*l,i,[0,-1],n))return!0;var u=this.generateCutTrianglePts(r,i,a,o);return tz(e,t,u.topLeft)||tz(e,t,u.topRight)||tz(e,t,u.bottomRight)||tz(e,t,u.bottomLeft)}}},lH.generateBarrel=function(){return this.nodeShapes.barrel={renderer:this,name:"barrel",points:tQ(4,0),draw:function(e,t,n,r,i,a){this.renderer.nodeShapeImpl(this.name,e,t,n,r,i)},intersectLine:function(e,t,n,r,i,a,o,s){var l=this.generateBarrelBezierPts(n+2*o,r+2*o,e,t),u=function(e){var t=tv({x:e[0],y:e[1]},{x:e[2],y:e[3]},{x:e[4],y:e[5]},.15),n=tv({x:e[0],y:e[1]},{x:e[2],y:e[3]},{x:e[4],y:e[5]},.5),r=tv({x:e[0],y:e[1]},{x:e[2],y:e[3]},{x:e[4],y:e[5]},.85);return[e[0],e[1],t.x,t.y,n.x,n.y,r.x,r.y,e[4],e[5]]};return tK(i,a,[].concat(u(l.topLeft),u(l.topRight),u(l.bottomRight),u(l.bottomLeft)),e,t)},generateBarrelBezierPts:function(e,t,n,r){var i=t/2,a=e/2,o=n-a,s=n+a,l=r-i,u=r+i,c=t3(e,t),d=c.heightOffset,h=c.widthOffset,f=c.ctrlPtOffsetPct*e,p={topLeft:[o,l+d,o+f,l,o+h,l],topRight:[s-h,l,s-f,l,s,l+d],bottomRight:[s,u-d,s-f,u,s-h,u],bottomLeft:[o+h,u,o+f,u,o,u-d]};return p.topLeft.isTop=!0,p.topRight.isTop=!0,p.bottomLeft.isBottom=!0,p.bottomRight.isBottom=!0,p},checkPoint:function(e,t,n,r,i,a,o,s){var l=t3(r,i),u=l.heightOffset,c=l.widthOffset;if(tV(e,t,this.points,a,o,r,i-2*u,[0,-1],n)||tV(e,t,this.points,a,o,r-2*c,i,[0,-1],n))return!0;for(var d=this.generateBarrelBezierPts(r,i,a,o),h=Object.keys(d),f=0;f<h.length;f++){var p=d[h[f]],g=function(e,t,n){var r=n[4],i=n[2],a=n[0],o=n[5],s=n[1],l=Math.min(r,a),u=Math.max(r,a),c=Math.min(o,s),d=Math.max(o,s);if(l<=e&&e<=u&&c<=t&&t<=d){var h=[r-2*i+a,2*(i-r),r],f=tI(h[0],h[1],h[2],e).filter(function(e){return 0<=e&&e<=1});if(f.length>0)return f[0]}return null}(e,t,p);if(null!=g){var v=tg(p[5],p[3],p[1],g);if(p.isTop&&v<=t||p.isBottom&&t<=v)return!0}}return!1}}},lH.generateBottomRoundrectangle=function(){return this.nodeShapes["bottom-round-rectangle"]=this.nodeShapes.bottomroundrectangle={renderer:this,name:"bottom-round-rectangle",points:tQ(4,0),draw:function(e,t,n,r,i,a){this.renderer.nodeShapeImpl(this.name,e,t,n,r,i,this.points,a)},intersectLine:function(e,t,n,r,i,a,o,s){var l=e-(n/2+o),u=t-(r/2+o),c=e+(n/2+o),d=tG(i,a,e,t,l,u,c,u,!1);return d.length>0?d:tA(i,a,e,t,n,r,o,s)},checkPoint:function(e,t,n,r,i,a,o,s){var l=2*(s="auto"===s?t1(r,i):s);if(tV(e,t,this.points,a,o,r,i-l,[0,-1],n)||tV(e,t,this.points,a,o,r-l,i,[0,-1],n))return!0;var u=r/2+2*n,c=i/2+2*n;return!!(tz(e,t,[a-u,o-c,a-u,o,a+u,o,a+u,o-c])||tq(e,t,l,l,a+r/2-s,o+i/2-s,n)||tq(e,t,l,l,a-r/2+s,o+i/2-s,n))}}},lH.registerNodeShapes=function(){var e=this.nodeShapes={},t=this;this.generateEllipse(),this.generatePolygon("triangle",tQ(3,0)),this.generateRoundPolygon("round-triangle",tQ(3,0)),this.generatePolygon("rectangle",tQ(4,0)),e.square=e.rectangle,this.generateRoundRectangle(),this.generateCutRectangle(),this.generateBarrel(),this.generateBottomRoundrectangle();var n=[0,1,1,0,0,-1,-1,0];this.generatePolygon("diamond",n),this.generateRoundPolygon("round-diamond",n),this.generatePolygon("pentagon",tQ(5,0)),this.generateRoundPolygon("round-pentagon",tQ(5,0)),this.generatePolygon("hexagon",tQ(6,0)),this.generateRoundPolygon("round-hexagon",tQ(6,0)),this.generatePolygon("heptagon",tQ(7,0)),this.generateRoundPolygon("round-heptagon",tQ(7,0)),this.generatePolygon("octagon",tQ(8,0)),this.generateRoundPolygon("round-octagon",tQ(8,0));var r=Array(20),i=t0(5,0),a=t0(5,Math.PI/5),o=.5*(3-Math.sqrt(5));o*=1.57;for(var s=0;s<a.length/2;s++)a[2*s]*=o,a[2*s+1]*=o;for(var s=0;s<5;s++)r[4*s]=i[2*s],r[4*s+1]=i[2*s+1],r[4*s+2]=a[2*s],r[4*s+3]=a[2*s+1];r=tJ(r),this.generatePolygon("star",r),this.generatePolygon("vee",[-1,-1,0,-.333,1,-1,0,1]),this.generatePolygon("rhomboid",[-1,-1,.333,-1,1,1,-.333,1]),this.generatePolygon("right-rhomboid",[-.333,-1,1,-1,.333,1,-1,1]),this.nodeShapes.concavehexagon=this.generatePolygon("concave-hexagon",[-1,-.95,-.75,0,-1,.95,1,.95,.75,0,1,-.95]);var l=[-1,-1,.25,-1,1,0,.25,1,-1,1];this.generatePolygon("tag",l),this.generateRoundPolygon("round-tag",l),e.makePolygon=function(e){var n,r="polygon-"+e.join("$");return(n=this[r])?n:t.generatePolygon(r,e)}};var lK={};lK.timeToRender=function(){return this.redrawTotalTime/this.redrawCount},lK.redraw=function(e){e=e||ej(),void 0===this.averageRedrawTime&&(this.averageRedrawTime=0),void 0===this.lastRedrawTime&&(this.lastRedrawTime=0),void 0===this.lastDrawTime&&(this.lastDrawTime=0),this.requestedFrame=!0,this.renderOptions=e},lK.beforeRender=function(e,t){if(!this.destroyed){null==t&&eL("Priority is not optional for beforeRender");var n=this.beforeRenderCallbacks;n.push({fn:e,priority:t}),n.sort(function(e,t){return t.priority-e.priority})}};var lZ=function(e,t,n){for(var r=e.beforeRenderCallbacks,i=0;i<r.length;i++)r[i].fn(t,n)};lK.startRenderLoop=function(){var e=this,t=e.cy;if(!e.renderLoopStarted){e.renderLoopStarted=!0;var n=function(r){if(!e.destroyed){if(t.batching());else if(e.requestedFrame&&!e.skipFrame){lZ(e,!0,r);var i=ev();e.render(e.renderOptions);var a=e.lastDrawTime=ev();void 0===e.averageRedrawTime&&(e.averageRedrawTime=a-i),void 0===e.redrawCount&&(e.redrawCount=0),e.redrawCount++,void 0===e.redrawTotalTime&&(e.redrawTotalTime=0);var o=a-i;e.redrawTotalTime+=o,e.lastRedrawTime=o,e.averageRedrawTime=e.averageRedrawTime/2+o/2,e.requestedFrame=!1}else lZ(e,!1,r);e.skipFrame=!1,em(n)}};em(n)}};var l$=function(e){this.init(e)},lQ=l$.prototype;lQ.clientFunctions=["redrawHint","render","renderTo","matchCanvasSize","nodeShapeImpl","arrowShapeImpl"],lQ.init=function(e){this.options=e,this.cy=e.cy;var t=this.container=e.cy.container(),n=this.cy.window();if(n){var r=n.document,i=r.head,a="__________cytoscape_stylesheet",o="__________cytoscape_container",s=null!=r.getElementById(a);if(0>t.className.indexOf(o)&&(t.className=(t.className||"")+" "+o),!s){var l=r.createElement("style");l.id=a,l.textContent="."+o+" { position: relative; }",i.insertBefore(l,i.children[0])}"static"===n.getComputedStyle(t).getPropertyValue("position")&&ez("A Cytoscape container has style position:static and so can not use UI extensions properly")}this.selection=[void 0,void 0,void 0,void 0,0],this.bezierProjPcts=[.05,.225,.4,.5,.6,.775,.95],this.hoverData={down:null,last:null,downTime:null,triggerMode:null,dragging:!1,initialPan:[null,null],capture:!1},this.dragData={possibleDragElements:[]},this.touchData={start:null,capture:!1,startPosition:[null,null,null,null,null,null],singleTouchStartTime:null,singleTouchMoved:!0,now:[null,null,null,null,null,null],earlier:[null,null,null,null,null,null]},this.redraws=0,this.showFps=e.showFps,this.debug=e.debug,this.webgl=e.webgl,this.hideEdgesOnViewport=e.hideEdgesOnViewport,this.textureOnViewport=e.textureOnViewport,this.wheelSensitivity=e.wheelSensitivity,this.motionBlurEnabled=e.motionBlur,this.forcedPixelRatio=M(e.pixelRatio)?e.pixelRatio:null,this.motionBlur=e.motionBlur,this.motionBlurOpacity=e.motionBlurOpacity,this.motionBlurTransparency=1-this.motionBlurOpacity,this.motionBlurPxRatio=1,this.mbPxRBlurry=1,this.minMbLowQualFrames=4,this.fullQualityMb=!1,this.clearedForMotionBlur=[],this.desktopTapThreshold=e.desktopTapThreshold,this.desktopTapThreshold2=e.desktopTapThreshold*e.desktopTapThreshold,this.touchTapThreshold=e.touchTapThreshold,this.touchTapThreshold2=e.touchTapThreshold*e.touchTapThreshold,this.tapholdDuration=500,this.bindings=[],this.beforeRenderCallbacks=[],this.beforeRenderPriorities={animations:400,eleCalcs:300,eleTxrDeq:200,lyrTxrDeq:150,lyrTxrSkip:100},this.registerNodeShapes(),this.registerArrowShapes(),this.registerCalculationListeners()},lQ.notify=function(e,t){var n=this.cy;if(!this.destroyed){if("init"===e)return void this.load();if("destroy"===e)return void this.destroy();("add"===e||"remove"===e||"move"===e&&n.hasCompoundNodes()||"load"===e||"zorder"===e||"mount"===e)&&this.invalidateCachedZSortedEles(),"viewport"===e&&this.redrawHint("select",!0),"gc"===e&&this.redrawHint("gc",!0),("load"===e||"resize"===e||"mount"===e)&&(this.invalidateContainerClientCoordsCache(),this.matchCanvasSize(this.container)),this.redrawHint("eles",!0),this.redrawHint("drag",!0),this.startRenderLoop(),this.redraw()}},lQ.destroy=function(){this.destroyed=!0,this.cy.stopAnimationLoop();for(var e=0;e<this.bindings.length;e++){var t=this.bindings[e],n=t.target;(n.off||n.removeEventListener).apply(n,t.args)}if(this.bindings=[],this.beforeRenderCallbacks=[],this.onUpdateEleCalcsFns=[],this.removeObserver&&this.removeObserver.disconnect(),this.styleObserver&&this.styleObserver.disconnect(),this.resizeObserver&&this.resizeObserver.disconnect(),this.labelCalcDiv)try{document.body.removeChild(this.labelCalcDiv)}catch(e){}},lQ.isHeadless=function(){return!1},[a3,lW,lU,lG,lH,lK].forEach(function(e){J(lQ,e)});var lJ=1e3/60,l0={setupDequeueing:function(e){return function(){var t=this,n=this.renderer;if(!t.dequeueingSetup){t.dequeueingSetup=!0;var r=ep(function(){n.redrawHint("eles",!0),n.redrawHint("drag",!0),n.redraw()},e.deqRedrawThreshold),i=e.priority||eN;n.beforeRender(function(i,a){var o=ev(),s=n.averageRedrawTime,l=n.lastRedrawTime,u=[],c=n.cy.extent(),d=n.getPixelRatio();for(i||n.flushRenderedStyleQueue();;){var h=ev(),f=h-o,p=h-a;if(l<lJ){var g=lJ-(i?s:0);if(p>=e.deqFastCost*g)break}else if(i){if(f>=e.deqCost*l||f>=e.deqAvgCost*s)break}else if(p>=e.deqNoDrawCost*lJ)break;var v=e.deq(t,d,c);if(v.length>0)for(var y=0;y<v.length;y++)u.push(v[y]);else break}u.length>0&&(e.onDeqd(t,u),!i&&e.shouldRedraw(t,u,d,c)&&r())},i(t))}}}},l1=a(function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:eR;i(this,e),this.idsByKey=new eZ,this.keyForId=new eZ,this.cachesByLvl=new eZ,this.lvls=[],this.getKey=t,this.doesEleInvalidateKey=n},[{key:"getIdsFor",value:function(e){null==e&&eL("Can not get id list for null key");var t=this.idsByKey,n=this.idsByKey.get(e);return n||(n=new eQ,t.set(e,n)),n}},{key:"addIdForKey",value:function(e,t){null!=e&&this.getIdsFor(e).add(t)}},{key:"deleteIdForKey",value:function(e,t){null!=e&&this.getIdsFor(e).delete(t)}},{key:"getNumberOfIdsForKey",value:function(e){return null==e?0:this.getIdsFor(e).size}},{key:"updateKeyMappingFor",value:function(e){var t=e.id(),n=this.keyForId.get(t),r=this.getKey(e);this.deleteIdForKey(n,t),this.addIdForKey(r,t),this.keyForId.set(t,r)}},{key:"deleteKeyMappingFor",value:function(e){var t=e.id(),n=this.keyForId.get(t);this.deleteIdForKey(n,t),this.keyForId.delete(t)}},{key:"keyHasChangedFor",value:function(e){var t=e.id();return this.keyForId.get(t)!==this.getKey(e)}},{key:"isInvalid",value:function(e){return this.keyHasChangedFor(e)||this.doesEleInvalidateKey(e)}},{key:"getCachesAt",value:function(e){var t=this.cachesByLvl,n=this.lvls,r=t.get(e);return r||(r=new eZ,t.set(e,r),n.push(e)),r}},{key:"getCache",value:function(e,t){return this.getCachesAt(t).get(e)}},{key:"get",value:function(e,t){var n=this.getKey(e),r=this.getCache(n,t);return null!=r&&this.updateKeyMappingFor(e),r}},{key:"getForCachedKey",value:function(e,t){var n=this.keyForId.get(e.id());return this.getCache(n,t)}},{key:"hasCache",value:function(e,t){return this.getCachesAt(t).has(e)}},{key:"has",value:function(e,t){var n=this.getKey(e);return this.hasCache(n,t)}},{key:"setCache",value:function(e,t,n){n.key=e,this.getCachesAt(t).set(e,n)}},{key:"set",value:function(e,t,n){var r=this.getKey(e);this.setCache(r,t,n),this.updateKeyMappingFor(e)}},{key:"deleteCache",value:function(e,t){this.getCachesAt(t).delete(e)}},{key:"delete",value:function(e,t){var n=this.getKey(e);this.deleteCache(n,t)}},{key:"invalidateKey",value:function(e){var t=this;this.lvls.forEach(function(n){return t.deleteCache(e,n)})}},{key:"invalidate",value:function(e){var t=e.id(),n=this.keyForId.get(t);this.deleteKeyMappingFor(e);var r=this.doesEleInvalidateKey(e);return r&&this.invalidateKey(n),r||0===this.getNumberOfIdsForKey(n)}}]),l2={dequeue:"dequeue",downscale:"downscale",highQuality:"highQuality"},l5=eY({getKey:null,doesEleInvalidateKey:eR,drawElement:null,getBoundingBox:null,getRotationPoint:null,getRotationOffset:null,isVisible:eM,allowEdgeTxrCaching:!0,allowParentTxrCaching:!0}),l3=function(e,t){this.renderer=e,this.onDequeues=[];var n=l5(t);J(this,n),this.lookup=new l1(n.getKey,n.doesEleInvalidateKey),this.setupDequeueing()},l4=l3.prototype;l4.reasons=l2,l4.getTextureQueue=function(e){return this.eleImgCaches=this.eleImgCaches||{},this.eleImgCaches[e]=this.eleImgCaches[e]||[]},l4.getRetiredTextureQueue=function(e){var t=this.eleImgCaches.retired=this.eleImgCaches.retired||{};return t[e]=t[e]||[]},l4.getElementQueue=function(){return this.eleCacheQueue=this.eleCacheQueue||new e3(function(e,t){return t.reqs-e.reqs})},l4.getElementKeyToQueue=function(){return this.eleKeyToCacheQueue=this.eleKeyToCacheQueue||{}},l4.getElement=function(e,t,n,r,i){var a,o,s,l=this,u=this.renderer,c=u.cy.zoom(),d=this.lookup;if(!t||0===t.w||0===t.h||isNaN(t.w)||isNaN(t.h)||!e.visible()||e.removed()||!l.allowEdgeTxrCaching&&e.isEdge()||!l.allowParentTxrCaching&&e.isParent())return null;if(null==r&&(r=Math.ceil(tc(c*n))),r<-4)r=-4;else if(c>=7.99||r>3)return null;var h=Math.pow(2,r),f=t.h*h,p=t.w*h,g=u.eleTextBiggerThanMin(e,h);if(!this.isVisible(e,g))return null;var v=d.get(e,r);if(v&&v.invalidated&&(v.invalidated=!1,v.texture.invalidatedWidth-=v.width),v)return v;if(a=f<=25?25:f<=50?50:50*Math.ceil(f/50),f>1024||p>1024)return null;var y=l.getTextureQueue(a),b=y[y.length-2],x=function(){return l.recycleTexture(a,p)||l.addTexture(a,p)};b||(b=y[y.length-1]),b||(b=x()),b.width-b.usedWidth<p&&(b=x());for(var w=function(e){return e&&e.scaledLabelShown===g},E=i&&i===l2.dequeue,T=i&&i===l2.highQuality,C=i&&i===l2.downscale,k=r+1;k<=3;k++){var P=d.get(e,k);if(P){o=P;break}}var S=o&&o.level===r+1?o:null,B=function(){b.context.drawImage(S.texture.canvas,S.x,0,S.width,S.height,b.usedWidth,0,p,f)};if(b.context.setTransform(1,0,0,1,0,0),b.context.clearRect(b.usedWidth,0,p,a),w(S))B();else if(w(o))if(!T)return l.queueElement(e,o.level-1),o;else{for(var D=o.level;D>r;D--)S=l.getElement(e,t,n,D,l2.downscale);B()}else{if(!E&&!T&&!C)for(var _=r-1;_>=-4;_--){var A=d.get(e,_);if(A){s=A;break}}if(w(s))return l.queueElement(e,r),s;b.context.translate(b.usedWidth,0),b.context.scale(h,h),this.drawElement(b.context,e,t,g,!1),b.context.scale(1/h,1/h),b.context.translate(-b.usedWidth,0)}return v={x:b.usedWidth,texture:b,level:r,scale:h,width:p,height:f,scaledLabelShown:g},b.usedWidth+=Math.ceil(p+8),b.eleCaches.push(v),d.set(e,r,v),l.checkTextureFullness(b),v},l4.invalidateElements=function(e){for(var t=0;t<e.length;t++)this.invalidateElement(e[t])},l4.invalidateElement=function(e){var t=this.lookup,n=[];if(t.isInvalid(e)){for(var r=-4;r<=3;r++){var i=t.getForCachedKey(e,r);i&&n.push(i)}if(t.invalidate(e))for(var a=0;a<n.length;a++){var o=n[a],s=o.texture;s.invalidatedWidth+=o.width,o.invalidated=!0,this.checkTextureUtility(s)}this.removeFromQueue(e)}},l4.checkTextureUtility=function(e){e.invalidatedWidth>=.2*e.width&&this.retireTexture(e)},l4.checkTextureFullness=function(e){var t=this.getTextureQueue(e.height);e.usedWidth/e.width>.8&&e.fullnessChecks>=10?eq(t,e):e.fullnessChecks++},l4.retireTexture=function(e){var t=e.height,n=this.getTextureQueue(t),r=this.lookup;eq(n,e),e.retired=!0;for(var i=e.eleCaches,a=0;a<i.length;a++){var o=i[a];r.deleteCache(o.key,o.level)}eW(i),this.getRetiredTextureQueue(t).push(e)},l4.addTexture=function(e,t){var n=this.getTextureQueue(e),r={};return n.push(r),r.eleCaches=[],r.height=e,r.width=Math.max(1024,t),r.usedWidth=0,r.invalidatedWidth=0,r.fullnessChecks=0,r.canvas=this.renderer.makeOffscreenCanvas(r.width,r.height),r.context=r.canvas.getContext("2d"),r},l4.recycleTexture=function(e,t){for(var n=this.getTextureQueue(e),r=this.getRetiredTextureQueue(e),i=0;i<r.length;i++){var a=r[i];if(a.width>=t)return a.retired=!1,a.usedWidth=0,a.invalidatedWidth=0,a.fullnessChecks=0,eW(a.eleCaches),a.context.setTransform(1,0,0,1,0,0),a.context.clearRect(0,0,a.width,a.height),eq(r,a),n.push(a),a}},l4.queueElement=function(e,t){var n=this.getElementQueue(),r=this.getElementKeyToQueue(),i=this.getKey(e),a=r[i];if(a)a.level=Math.max(a.level,t),a.eles.merge(e),a.reqs++,n.updateItem(a);else{var o={eles:e.spawn().merge(e),level:t,reqs:1,key:i};n.push(o),r[i]=o}},l4.dequeue=function(e){for(var t=this.getElementQueue(),n=this.getElementKeyToQueue(),r=[],i=this.lookup,a=0;a<1;a++)if(t.size()>0){var o=t.pop(),s=o.key,l=o.eles[0],u=i.hasCache(l,o.level);if(n[s]=null,u)continue;r.push(o);var c=this.getBoundingBox(l);this.getElement(l,c,e,o.level,l2.dequeue)}else break;return r},l4.removeFromQueue=function(e){var t=this.getElementQueue(),n=this.getElementKeyToQueue(),r=this.getKey(e),i=n[r];null!=i&&(1===i.eles.length?(i.reqs=eA,t.updateItem(i),t.pop(),n[r]=null):i.eles.unmerge(e))},l4.onDequeue=function(e){this.onDequeues.push(e)},l4.offDequeue=function(e){eq(this.onDequeues,e)},l4.setupDequeueing=l0.setupDequeueing({deqRedrawThreshold:100,deqCost:.15,deqAvgCost:.1,deqNoDrawCost:.9,deqFastCost:.9,deq:function(e,t,n){return e.dequeue(t,n)},onDeqd:function(e,t){for(var n=0;n<e.onDequeues.length;n++)(0,e.onDequeues[n])(t)},shouldRedraw:function(e,t,n,r){for(var i=0;i<t.length;i++)for(var a=t[i].eles,o=0;o<a.length;o++)if(tP(a[o].boundingBox(),r))return!0;return!1},priority:function(e){return e.renderer.beforeRenderPriorities.eleTxrDeq}});var l9=function(e){var t=this,n=t.renderer=e,r=n.cy;t.layersByLevel={},t.firstGet=!0,t.lastInvalidationTime=ev()-500,t.skipping=!1,t.eleTxrDeqs=r.collection(),t.scheduleElementRefinement=ep(function(){t.refineElementTextures(t.eleTxrDeqs),t.eleTxrDeqs.unmerge(t.eleTxrDeqs)},50),n.beforeRender(function(e,n){n-t.lastInvalidationTime<=250?t.skipping=!0:t.skipping=!1},n.beforeRenderPriorities.lyrTxrSkip),t.layersQueue=new e3(function(e,t){return t.reqs-e.reqs}),t.setupDequeueing()},l6=l9.prototype,l8=0;l6.makeLayer=function(e,t){var n=Math.pow(2,t),r=Math.ceil(e.w*n),i=Math.ceil(e.h*n),a=this.renderer.makeOffscreenCanvas(r,i),o={id:l8=++l8%0x1fffffffffffff,bb:e,level:t,width:r,height:i,canvas:a,context:a.getContext("2d"),eles:[],elesQueue:[],reqs:0},s=o.context,l=-o.bb.x1,u=-o.bb.y1;return s.scale(n,n),s.translate(l,u),o},l6.getLayers=function(e,t,n){var r,i,a=this,o=a.renderer.cy.zoom(),s=a.firstGet;if(a.firstGet=!1,null==n){if((n=Math.ceil(tc(o*t)))<-4)n=-4;else if(o>=3.99||n>2)return null}a.validateLayersElesOrdering(n,e);var l=a.layersByLevel,u=Math.pow(2,n),c=l[n]=l[n]||[];if(a.levelIsComplete(n,e))return c;var d=function(t){if(a.validateLayersElesOrdering(t,e),a.levelIsComplete(t,e))return i=l[t],!0},h=function(e){if(!i)for(var t=n+e;-4<=t&&t<=2&&!d(t);t+=e);};h(1),h(-1);for(var f=c.length-1;f>=0;f--){var p=c[f];p.invalid&&eq(c,p)}var g=function(){if(!r){r=tb();for(var t=0;t<e.length;t++)tw(r,e[t].boundingBox())}return r};if(a.skipping&&!s)return null;for(var v=null,y=e.length/1,b=!s,x=0;x<e.length;x++){var w=e[x],E=w._private.rscratch,T=E.imgLayerCaches=E.imgLayerCaches||{},C=T[n];if(C){v=C;continue}if((!v||v.eles.length>=y||!tD(v.bb,w.boundingBox()))&&!(v=function(e){var t=(e=e||{}).after;g();var i=Math.ceil(r.w*u),o=Math.ceil(r.h*u);if(i>32767||o>32767||i*o>16e6)return null;var s=a.makeLayer(r,n);if(null!=t){var l=c.indexOf(t)+1;c.splice(l,0,s)}else(void 0===e.insert||e.insert)&&c.unshift(s);return s}({insert:!0,after:v})))return null;i||b?a.queueLayer(v,w):a.drawEleInLayer(v,w,n,t),v.eles.push(w),T[n]=v}return i||(b?null:c)},l6.getEleLevelForLayerLevel=function(e,t){return e},l6.drawEleInLayer=function(e,t,n,r){var i=this.renderer,a=e.context,o=t.boundingBox();0!==o.w&&0!==o.h&&t.visible()&&(n=this.getEleLevelForLayerLevel(n,r),i.setImgSmoothing(a,!1),i.drawCachedElement(a,t,null,null,n,!0),i.setImgSmoothing(a,!0))},l6.levelIsComplete=function(e,t){var n=this.layersByLevel[e];if(!n||0===n.length)return!1;for(var r=0,i=0;i<n.length;i++){var a=n[i];if(a.reqs>0||a.invalid)return!1;r+=a.eles.length}return r===t.length},l6.validateLayersElesOrdering=function(e,t){var n=this.layersByLevel[e];if(n)for(var r=0;r<n.length;r++){for(var i=n[r],a=-1,o=0;o<t.length;o++)if(i.eles[0]===t[o]){a=o;break}if(a<0){this.invalidateLayer(i);continue}for(var s=a,o=0;o<i.eles.length;o++)if(i.eles[o]!==t[s+o]){this.invalidateLayer(i);break}}},l6.updateElementsInLayers=function(e,t){for(var n=N(e[0]),r=0;r<e.length;r++)for(var i=n?null:e[r],a=n?e[r]:e[r].ele,o=a._private.rscratch,s=o.imgLayerCaches=o.imgLayerCaches||{},l=-4;l<=2;l++){var u=s[l];u&&(i&&this.getEleLevelForLayerLevel(u.level)!==i.level||t(u,a,i))}},l6.haveLayers=function(){for(var e=!1,t=-4;t<=2;t++){var n=this.layersByLevel[t];if(n&&n.length>0){e=!0;break}}return e},l6.invalidateElements=function(e){var t=this;0!==e.length&&(t.lastInvalidationTime=ev(),0!==e.length&&t.haveLayers()&&t.updateElementsInLayers(e,function(e,n,r){t.invalidateLayer(e)}))},l6.invalidateLayer=function(e){if(this.lastInvalidationTime=ev(),!e.invalid){var t=e.level,n=e.eles;eq(this.layersByLevel[t],e),e.elesQueue=[],e.invalid=!0,e.replacement&&(e.replacement.invalid=!0);for(var r=0;r<n.length;r++){var i=n[r]._private.rscratch.imgLayerCaches;i&&(i[t]=null)}}},l6.refineElementTextures=function(e){var t=this;t.updateElementsInLayers(e,function(e,n,r){var i=e.replacement;if(i||((i=e.replacement=t.makeLayer(e.bb,e.level)).replaces=e,i.eles=e.eles),!i.reqs)for(var a=0;a<i.eles.length;a++)t.queueLayer(i,i.eles[a])})},l6.enqueueElementRefinement=function(e){this.eleTxrDeqs.merge(e),this.scheduleElementRefinement()},l6.queueLayer=function(e,t){var n=this.layersQueue,r=e.elesQueue,i=r.hasId=r.hasId||{};if(!e.replacement){if(t){if(i[t.id()])return;r.push(t),i[t.id()]=!0}e.reqs?(e.reqs++,n.updateItem(e)):(e.reqs=1,n.push(e))}},l6.dequeue=function(e){for(var t=this.layersQueue,n=[],r=0;r<1&&0!==t.size();){var i=t.peek();if(i.replacement||i.replaces&&i!==i.replaces.replacement||i.invalid){t.pop();continue}var a=i.elesQueue.shift();a&&(this.drawEleInLayer(i,a,i.level,e),r++),0===n.length&&n.push(!0),0===i.elesQueue.length&&(t.pop(),i.reqs=0,i.replaces&&this.applyLayerReplacement(i),this.requestRedraw())}return n},l6.applyLayerReplacement=function(e){var t=this.layersByLevel[e.level],n=e.replaces,r=t.indexOf(n);if(!(r<0)&&!n.invalid){t[r]=e;for(var i=0;i<e.eles.length;i++){var a=e.eles[i]._private,o=a.imgLayerCaches=a.imgLayerCaches||{};o&&(o[e.level]=e)}this.requestRedraw()}},l6.requestRedraw=ep(function(){var e=this.renderer;e.redrawHint("eles",!0),e.redrawHint("drag",!0),e.redraw()},100),l6.setupDequeueing=l0.setupDequeueing({deqRedrawThreshold:50,deqCost:.15,deqAvgCost:.1,deqNoDrawCost:.9,deqFastCost:.9,deq:function(e,t){return e.dequeue(t)},onDeqd:eN,shouldRedraw:eM,priority:function(e){return e.renderer.beforeRenderPriorities.lyrTxrDeq}});var l7={};function ue(e,t){for(var n=0;n<t.length;n++){var r=t[n];e.lineTo(r.x,r.y)}}function ut(e,t,n){for(var r,i=0;i<t.length;i++){var a=t[i];0===i&&(r=a),e.lineTo(a.x,a.y)}e.quadraticCurveTo(n.x,n.y,r.x,r.y)}function un(e,t,n){e.beginPath&&e.beginPath();for(var r=0;r<t.length;r++){var i=t[r];e.lineTo(i.x,i.y)}var a=n[0];e.moveTo(a.x,a.y);for(var r=1;r<n.length;r++){var i=n[r];e.lineTo(i.x,i.y)}e.closePath&&e.closePath()}function ur(e,t,n,r,i){e.beginPath&&e.beginPath(),e.arc(n,r,i,0,2*Math.PI,!1);var a=t[0];e.moveTo(a.x,a.y);for(var o=0;o<t.length;o++){var s=t[o];e.lineTo(s.x,s.y)}e.closePath&&e.closePath()}function ui(e,t,n,r){e.arc(t,n,r,0,2*Math.PI,!1)}l7.arrowShapeImpl=function(e){return(b||(b={polygon:ue,"triangle-backcurve":ut,"triangle-tee":un,"circle-triangle":ur,"triangle-cross":un,circle:ui}))[e]};var ua={};ua.drawElement=function(e,t,n,r,i,a){t.isNode()?this.drawNode(e,t,n,r,i,a):this.drawEdge(e,t,n,r,i,a)},ua.drawElementOverlay=function(e,t){t.isNode()?this.drawNodeOverlay(e,t):this.drawEdgeOverlay(e,t)},ua.drawElementUnderlay=function(e,t){t.isNode()?this.drawNodeUnderlay(e,t):this.drawEdgeUnderlay(e,t)},ua.drawCachedElementPortion=function(e,t,n,r,i,a,o,s){var l=n.getBoundingBox(t);if(0!==l.w&&0!==l.h){var u=n.getElement(t,l,r,i,a);if(null!=u){var c,d,h,f,p,g,v=s(this,t);if(0===v)return;var y=o(this,t),b=l.x1,x=l.y1,w=l.w,E=l.h;if(0!==y){var T=n.getRotationPoint(t);h=T.x,f=T.y,e.translate(h,f),e.rotate(y),(p=this.getImgSmoothing(e))||this.setImgSmoothing(e,!0);var C=n.getRotationOffset(t);c=C.x,d=C.y}else c=b,d=x;1!==v&&(g=e.globalAlpha,e.globalAlpha=g*v),e.drawImage(u.texture.canvas,u.x,0,u.width,u.height,c,d,w,E),1!==v&&(e.globalAlpha=g),0!==y&&(e.rotate(-y),e.translate(-h,-f),p||this.setImgSmoothing(e,!1))}else n.drawElement(e,t)}};var uo=function(){return 0},us=function(e,t){return e.getTextAngle(t,null)},ul=function(e,t){return e.getTextAngle(t,"source")},uu=function(e,t){return e.getTextAngle(t,"target")},uc=function(e,t){return t.effectiveOpacity()},ud=function(e,t){return t.pstyle("text-opacity").pfValue*t.effectiveOpacity()};ua.drawCachedElement=function(e,t,n,r,i,a){var o=this.data,s=o.eleTxrCache,l=o.lblTxrCache,u=o.slbTxrCache,c=o.tlbTxrCache,d=t.boundingBox(),h=!0===a?s.reasons.highQuality:null;if(0!==d.w&&0!==d.h&&t.visible()&&(!r||tP(d,r))){var f=t.isEdge(),p=t.element()._private.rscratch.badLine;this.drawElementUnderlay(e,t),this.drawCachedElementPortion(e,t,s,n,i,h,uo,uc),f&&p||this.drawCachedElementPortion(e,t,l,n,i,h,us,ud),f&&!p&&(this.drawCachedElementPortion(e,t,u,n,i,h,ul,ud),this.drawCachedElementPortion(e,t,c,n,i,h,uu,ud)),this.drawElementOverlay(e,t)}},ua.drawElements=function(e,t){for(var n=0;n<t.length;n++){var r=t[n];this.drawElement(e,r)}},ua.drawCachedElements=function(e,t,n,r){for(var i=0;i<t.length;i++){var a=t[i];this.drawCachedElement(e,a,n,r)}},ua.drawCachedNodes=function(e,t,n,r){for(var i=0;i<t.length;i++){var a=t[i];a.isNode()&&this.drawCachedElement(e,a,n,r)}},ua.drawLayeredElements=function(e,t,n,r){var i=this.data.lyrTxrCache.getLayers(t,n);if(i)for(var a=0;a<i.length;a++){var o=i[a],s=o.bb;0!==s.w&&0!==s.h&&e.drawImage(o.canvas,s.x1,s.y1,s.w,s.h)}else this.drawCachedElements(e,t,n,r)};var uh={};uh.drawEdge=function(e,t,n){var r,i=!(arguments.length>3)||void 0===arguments[3]||arguments[3],a=!(arguments.length>4)||void 0===arguments[4]||arguments[4],o=!(arguments.length>5)||void 0===arguments[5]||arguments[5],s=this,l=t._private.rscratch;if((!o||t.visible())&&!(l.badLine||null==l.allpts||isNaN(l.allpts[0]))){n&&(r=n,e.translate(-r.x1,-r.y1));var u=o?t.pstyle("opacity").value:1,c=o?t.pstyle("line-opacity").value:1,d=t.pstyle("curve-style").value,h=t.pstyle("line-style").value,f=t.pstyle("width").pfValue,p=t.pstyle("line-cap").value,g=t.pstyle("line-outline-width").value,v=t.pstyle("line-outline-color").value,y=u*c,b=u*c,x=function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:y;"straight-triangle"===d?(s.eleStrokeStyle(e,t,n),s.drawEdgeTrianglePath(t,e,l.allpts)):(e.lineWidth=f,e.lineCap=p,s.eleStrokeStyle(e,t,n),s.drawEdgePath(t,e,l.allpts,h),e.lineCap="butt")},w=function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:b;s.drawArrowheads(e,t,n)};if(e.lineJoin="round","yes"===t.pstyle("ghost").value){var E=t.pstyle("ghost-offset-x").pfValue,T=t.pstyle("ghost-offset-y").pfValue,C=y*t.pstyle("ghost-opacity").value;e.translate(E,T),x(C),w(C),e.translate(-E,-T)}else!function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:y;if(e.lineWidth=f+g,e.lineCap=p,g>0)s.colorStrokeStyle(e,v[0],v[1],v[2],n);else{e.lineCap="butt";return}"straight-triangle"===d?s.drawEdgeTrianglePath(t,e,l.allpts):(s.drawEdgePath(t,e,l.allpts,h),e.lineCap="butt")}();a&&s.drawEdgeUnderlay(e,t),x(),w(),a&&s.drawEdgeOverlay(e,t),s.drawElementText(e,t,null,i),n&&e.translate(r.x1,r.y1)}};var uf=function(e){if(!["overlay","underlay"].includes(e))throw Error("Invalid state");return function(t,n){if(n.visible()){var r=n.pstyle("".concat(e,"-opacity")).value;if(0!==r){var i=this.usePaths(),a=n._private.rscratch,o=n.pstyle("".concat(e,"-padding")).pfValue,s=n.pstyle("".concat(e,"-color")).value;t.lineWidth=2*o,"self"!==a.edgeType||i?t.lineCap="round":t.lineCap="butt",this.colorStrokeStyle(t,s[0],s[1],s[2],r),this.drawEdgePath(n,t,a.allpts,"solid")}}}};uh.drawEdgeOverlay=uf("overlay"),uh.drawEdgeUnderlay=uf("underlay"),uh.drawEdgePath=function(e,t,n,r){var i=e._private.rscratch,a=t,s=!1,l=this.usePaths(),u=e.pstyle("line-dash-pattern").pfValue,c=e.pstyle("line-dash-offset").pfValue;if(l){var d=n.join("$");i.pathCacheKey&&i.pathCacheKey===d?(p=t=i.pathCache,s=!0):(p=t=new Path2D,i.pathCacheKey=d,i.pathCache=p)}if(a.setLineDash)switch(r){case"dotted":a.setLineDash([1,1]);break;case"dashed":a.setLineDash(u),a.lineDashOffset=c;break;case"solid":a.setLineDash([])}if(!s&&!i.badLine)switch(t.beginPath&&t.beginPath(),t.moveTo(n[0],n[1]),i.edgeType){case"bezier":case"self":case"compound":case"multibezier":for(var h=2;h+3<n.length;h+=4)t.quadraticCurveTo(n[h],n[h+1],n[h+2],n[h+3]);break;case"straight":case"haystack":for(var f=2;f+1<n.length;f+=2)t.lineTo(n[f],n[f+1]);break;case"segments":if(i.isRound){var p,g,v=o(i.roundCorners);try{for(v.s();!(g=v.n()).done;){var y=g.value;l_(t,y)}}catch(e){v.e(e)}finally{v.f()}t.lineTo(n[n.length-2],n[n.length-1])}else for(var b=2;b+1<n.length;b+=2)t.lineTo(n[b],n[b+1])}t=a,l?t.stroke(p):t.stroke(),t.setLineDash&&t.setLineDash([])},uh.drawEdgeTrianglePath=function(e,t,n){t.fillStyle=t.strokeStyle;for(var r=e.pstyle("width").pfValue,i=0;i+1<n.length;i+=2){var a=[n[i+2]-n[i],n[i+3]-n[i+1]],o=Math.sqrt(a[0]*a[0]+a[1]*a[1]),s=[a[1]/o,-a[0]/o],l=[s[0]*r/2,s[1]*r/2];t.beginPath(),t.moveTo(n[i]-l[0],n[i+1]-l[1]),t.lineTo(n[i]+l[0],n[i+1]+l[1]),t.lineTo(n[i+2],n[i+3]),t.closePath(),t.fill()}},uh.drawArrowheads=function(e,t,n){var r=t._private.rscratch,i="haystack"===r.edgeType;i||this.drawArrowhead(e,t,"source",r.arrowStartX,r.arrowStartY,r.srcArrowAngle,n),this.drawArrowhead(e,t,"mid-target",r.midX,r.midY,r.midtgtArrowAngle,n),this.drawArrowhead(e,t,"mid-source",r.midX,r.midY,r.midsrcArrowAngle,n),i||this.drawArrowhead(e,t,"target",r.arrowEndX,r.arrowEndY,r.tgtArrowAngle,n)},uh.drawArrowhead=function(e,t,n,r,i,a,o){if(!(isNaN(r)||null==r||isNaN(i)||null==i||isNaN(a))&&null!=a){var s=t.pstyle(n+"-arrow-shape").value;if("none"!==s){var l="hollow"===t.pstyle(n+"-arrow-fill").value?"both":"filled",u=t.pstyle(n+"-arrow-fill").value,c=t.pstyle("width").pfValue,d=t.pstyle(n+"-arrow-width"),h="match-line"===d.value?c:d.pfValue;"%"===d.units&&(h*=c);var f=t.pstyle("opacity").value;void 0===o&&(o=f);var p=e.globalCompositeOperation;(1!==o||"hollow"===u)&&(e.globalCompositeOperation="destination-out",this.colorFillStyle(e,255,255,255,1),this.colorStrokeStyle(e,255,255,255,1),this.drawArrowShape(t,e,l,c,s,h,r,i,a),e.globalCompositeOperation=p);var g=t.pstyle(n+"-arrow-color").value;this.colorFillStyle(e,g[0],g[1],g[2],o),this.colorStrokeStyle(e,g[0],g[1],g[2],o),this.drawArrowShape(t,e,u,c,s,h,r,i,a)}}},uh.drawArrowShape=function(e,t,n,r,i,a,o,s,l){var u,c=this.usePaths()&&"triangle-cross"!==i,d=!1,h=t,f=e.pstyle("arrow-scale").value,p=this.getArrowWidth(r,f),g=this.arrowShapes[i];if(c){var v=this.arrowPathCache=this.arrowPathCache||[],y=ek(i),b=v[y];null!=b?(u=t=b,d=!0):(u=t=new Path2D,v[y]=u)}!d&&(t.beginPath&&t.beginPath(),c?g.draw(t,1,0,{x:0,y:0},1):g.draw(t,p,l,{x:o,y:s},r),t.closePath&&t.closePath()),t=h,c&&(t.translate(o,s),t.rotate(l),t.scale(p,p)),("filled"===n||"both"===n)&&(c?t.fill(u):t.fill()),("hollow"===n||"both"===n)&&(t.lineWidth=a/(c?p:1),t.lineJoin="miter",c?t.stroke(u):t.stroke()),c&&(t.scale(1/p,1/p),t.rotate(-l),t.translate(-o,-s))};var up={};up.safeDrawImage=function(e,t,n,r,i,a,o,s,l,u){if(!(i<=0)&&!(a<=0)&&!(l<=0)&&!(u<=0))try{e.drawImage(t,n,r,i,a,o,s,l,u)}catch(e){ez(e)}},up.drawInscribedImage=function(e,t,n,r,i){var a=n.position(),o=a.x,s=a.y,l=n.cy().style(),u=l.getIndexedStyle.bind(l),c=u(n,"background-fit","value",r),d=u(n,"background-repeat","value",r),h=n.width(),f=n.height(),p=2*n.padding(),g=h+("inner"===u(n,"background-width-relative-to","value",r)?0:p),v=f+("inner"===u(n,"background-height-relative-to","value",r)?0:p),y=n._private.rscratch,b="node"===u(n,"background-clip","value",r),x=u(n,"background-image-opacity","value",r)*i,w=u(n,"background-image-smoothing","value",r),E=n.pstyle("corner-radius").value;"auto"!==E&&(E=n.pstyle("corner-radius").pfValue);var T=t.width||t.cachedW,C=t.height||t.cachedH;(null==T||null==C)&&(document.body.appendChild(t),T=t.cachedW=t.width||t.offsetWidth,C=t.cachedH=t.height||t.offsetHeight,document.body.removeChild(t));var k=T,P=C;if("auto"!==u(n,"background-width","value",r)&&(k="%"===u(n,"background-width","units",r)?u(n,"background-width","pfValue",r)*g:u(n,"background-width","pfValue",r)),"auto"!==u(n,"background-height","value",r)&&(P="%"===u(n,"background-height","units",r)?u(n,"background-height","pfValue",r)*v:u(n,"background-height","pfValue",r)),0!==k&&0!==P){if("contain"===c){var S=Math.min(g/k,v/P);k*=S,P*=S}else if("cover"===c){var S=Math.max(g/k,v/P);k*=S,P*=S}var B=o-g/2,D=u(n,"background-position-x","units",r),_=u(n,"background-position-x","pfValue",r);"%"===D?B+=(g-k)*_:B+=_;var A=u(n,"background-offset-x","units",r),M=u(n,"background-offset-x","pfValue",r);"%"===A?B+=(g-k)*M:B+=M;var R=s-v/2,I=u(n,"background-position-y","units",r),N=u(n,"background-position-y","pfValue",r);"%"===I?R+=(v-P)*N:R+=N;var L=u(n,"background-offset-y","units",r),O=u(n,"background-offset-y","pfValue",r);"%"===L?R+=(v-P)*O:R+=O,y.pathCache&&(B-=o,R-=s,o=0,s=0);var z=e.globalAlpha;e.globalAlpha=x;var V=this.getImgSmoothing(e),F=!1;if("no"===w&&V?(this.setImgSmoothing(e,!1),F=!0):"yes"!==w||V||(this.setImgSmoothing(e,!0),F=!0),"no-repeat"===d)b&&(e.save(),y.pathCache?e.clip(y.pathCache):(this.nodeShapes[this.getNodeShape(n)].draw(e,o,s,g,v,E,y),e.clip())),this.safeDrawImage(e,t,0,0,T,C,B,R,k,P),b&&e.restore();else{var X=e.createPattern(t,d);e.fillStyle=X,this.nodeShapes[this.getNodeShape(n)].draw(e,o,s,g,v,E,y),e.translate(B,R),e.fill(),e.translate(-B,-R)}e.globalAlpha=z,F&&this.setImgSmoothing(e,V)}};var ug={};function uv(e,t,n,r,i){var a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:5,o=Math.min(a,r/2,i/2);e.beginPath(),e.moveTo(t+o,n),e.lineTo(t+r-o,n),e.quadraticCurveTo(t+r,n,t+r,n+o),e.lineTo(t+r,n+i-o),e.quadraticCurveTo(t+r,n+i,t+r-o,n+i),e.lineTo(t+o,n+i),e.quadraticCurveTo(t,n+i,t,n+i-o),e.lineTo(t,n+o),e.quadraticCurveTo(t,n,t+o,n),e.closePath()}ug.eleTextBiggerThanMin=function(e,t){return t||(t=Math.pow(2,Math.ceil(tc(e.cy().zoom()*this.getPixelRatio())))),!(e.pstyle("font-size").pfValue*t<e.pstyle("min-zoomed-font-size").pfValue)},ug.drawElementText=function(e,t,n,r,i){var a,o=!(arguments.length>5)||void 0===arguments[5]||arguments[5];if(null==r){if(o&&!this.eleTextBiggerThanMin(t))return}else if(!1===r)return;if(t.isNode()){var s=t.pstyle("label");if(!s||!s.value)return;e.textAlign=this.getLabelJustification(t),e.textBaseline="bottom"}else{var l=t.element()._private.rscratch.badLine,u=t.pstyle("label"),c=t.pstyle("source-label"),d=t.pstyle("target-label");if(l||(!u||!u.value)&&(!c||!c.value)&&(!d||!d.value))return;e.textAlign="center",e.textBaseline="bottom"}var h=!n;n&&(a=n,e.translate(-a.x1,-a.y1)),null==i?(this.drawText(e,t,null,h,o),t.isEdge()&&(this.drawText(e,t,"source",h,o),this.drawText(e,t,"target",h,o))):this.drawText(e,t,i,h,o),n&&e.translate(a.x1,a.y1)},ug.getFontCache=function(e){var t;this.fontCaches=this.fontCaches||[];for(var n=0;n<this.fontCaches.length;n++)if((t=this.fontCaches[n]).context===e)return t;return t={context:e},this.fontCaches.push(t),t},ug.setupTextStyle=function(e,t){var n=!(arguments.length>2)||void 0===arguments[2]||arguments[2],r=t.pstyle("font-style").strValue,i=t.pstyle("font-size").pfValue+"px",a=t.pstyle("font-family").strValue,o=t.pstyle("font-weight").strValue,s=n?t.effectiveOpacity()*t.pstyle("text-opacity").value:1,l=t.pstyle("text-outline-opacity").value*s,u=t.pstyle("color").value,c=t.pstyle("text-outline-color").value;e.font=r+" "+o+" "+i+" "+a,e.lineJoin="round",this.colorFillStyle(e,u[0],u[1],u[2],s),this.colorStrokeStyle(e,c[0],c[1],c[2],l)},ug.getTextAngle=function(e,t){var n,r=e._private.rscratch,i=t?t+"-":"",a=e.pstyle(i+"text-rotation");if("autorotate"===a.strValue){var o=eG(r,"labelAngle",t);n=e.isEdge()?o:0}else n="none"===a.strValue?0:a.pfValue;return n},ug.drawText=function(e,t,n){var r=!(arguments.length>3)||void 0===arguments[3]||arguments[3],i=!(arguments.length>4)||void 0===arguments[4]||arguments[4],a=t._private.rscratch,o=i?t.effectiveOpacity():1;if(!i||0!==o&&0!==t.pstyle("text-opacity").value){"main"===n&&(n=null);var s=eG(a,"labelX",n),l=eG(a,"labelY",n),u=this.getLabelText(t,n);if(null!=u&&""!==u&&!isNaN(s)&&!isNaN(l)){this.setupTextStyle(e,t,i);var c,d,h,f=n?n+"-":"",p=eG(a,"labelWidth",n),g=eG(a,"labelHeight",n),v=t.pstyle(f+"text-margin-x").pfValue,y=t.pstyle(f+"text-margin-y").pfValue,b=t.isEdge(),x=t.pstyle("text-halign").value,w=t.pstyle("text-valign").value;switch(b&&(x="center",w="center"),s+=v,l+=y,0!==(h=r?this.getTextAngle(t,n):0)&&(c=s,d=l,e.translate(c,d),e.rotate(h),s=0,l=0),w){case"top":break;case"center":l+=g/2;break;case"bottom":l+=g}var E=t.pstyle("text-background-opacity").value,T=t.pstyle("text-border-opacity").value,C=t.pstyle("text-border-width").pfValue,k=t.pstyle("text-background-padding").pfValue,P=t.pstyle("text-background-shape").strValue,S="round-rectangle"===P||"roundrectangle"===P;if(E>0||C>0&&T>0){var B,D,_=e.fillStyle,A=e.strokeStyle,M=e.lineWidth,R=t.pstyle("text-background-color").value,I=t.pstyle("text-border-color").value,N=t.pstyle("text-border-style").value,L=E>0,O=C>0&&T>0,z=s-k;switch(x){case"left":z-=p;break;case"center":z-=p/2}var V=l-g-k,F=p+2*k,X=g+2*k;if(L&&(e.fillStyle="rgba(".concat(R[0],",").concat(R[1],",").concat(R[2],",").concat(E*o,")")),O&&(e.strokeStyle="rgba(".concat(I[0],",").concat(I[1],",").concat(I[2],",").concat(T*o,")"),e.lineWidth=C,e.setLineDash))switch(N){case"dotted":e.setLineDash([1,1]);break;case"dashed":e.setLineDash([4,2]);break;case"double":e.lineWidth=C/4,e.setLineDash([]);break;default:e.setLineDash([])}if(S?(e.beginPath(),uv(e,z,V,F,X,2)):"circle"===P?(e.beginPath(),B=z,D=Math.min(F,X),e.beginPath(),e.arc(B+F/2,V+X/2,D/2,0,2*Math.PI),e.closePath()):(e.beginPath(),e.rect(z,V,F,X)),L&&e.fill(),O&&e.stroke(),O&&"double"===N){var j=C/2;e.beginPath(),S?uv(e,z+j,V+j,F-2*j,X-2*j,2):e.rect(z+j,V+j,F-2*j,X-2*j),e.stroke()}e.fillStyle=_,e.strokeStyle=A,e.lineWidth=M,e.setLineDash&&e.setLineDash([])}var Y=2*t.pstyle("text-outline-width").pfValue;if(Y>0&&(e.lineWidth=Y),"wrap"===t.pstyle("text-wrap").value){var q=eG(a,"labelWrapCachedLines",n),W=eG(a,"labelLineHeight",n),U=p/2,G=this.getLabelJustification(t);switch("auto"===G||("left"===x?"left"===G?s+=-p:"center"===G&&(s+=-U):"center"===x?"left"===G?s+=-U:"right"===G&&(s+=U):"right"===x&&("center"===G?s+=U:"right"===G&&(s+=p))),w){case"top":case"center":case"bottom":l-=(q.length-1)*W}for(var H=0;H<q.length;H++)Y>0&&e.strokeText(q[H],s,l),e.fillText(q[H],s,l),l+=W}else Y>0&&e.strokeText(u,s,l),e.fillText(u,s,l);0!==h&&(e.rotate(-h),e.translate(-c,-d))}}};var uy={};uy.drawNode=function(e,t,n){var r,i,a,o,s=!(arguments.length>3)||void 0===arguments[3]||arguments[3],l=!(arguments.length>4)||void 0===arguments[4]||arguments[4],u=!(arguments.length>5)||void 0===arguments[5]||arguments[5],c=this,d=t._private,h=d.rscratch,f=t.position();if(M(f.x)&&M(f.y)&&(!u||t.visible())){var p=u?t.effectiveOpacity():1,g=c.usePaths(),v=!1,y=t.padding();r=t.width()+2*y,i=t.height()+2*y,n&&(o=n,e.translate(-o.x1,-o.y1));for(var b=t.pstyle("background-image").value,x=Array(b.length),w=Array(b.length),E=0,T=0;T<b.length;T++){var C=b[T];if(x[T]=null!=C&&"none"!==C){var k=t.cy().style().getIndexedStyle(t,"background-image-crossorigin","value",T);E++,w[T]=c.getCachedImage(C,k,function(){d.backgroundTimestamp=Date.now(),t.emitAndNotify("background")})}}var P=t.pstyle("background-blacken").value,S=t.pstyle("border-width").pfValue,B=t.pstyle("background-opacity").value*p,D=t.pstyle("border-color").value,_=t.pstyle("border-style").value,A=t.pstyle("border-join").value,R=t.pstyle("border-cap").value,I=t.pstyle("border-position").value,N=t.pstyle("border-dash-pattern").pfValue,L=t.pstyle("border-dash-offset").pfValue,O=t.pstyle("border-opacity").value*p,z=t.pstyle("outline-width").pfValue,V=t.pstyle("outline-color").value,F=t.pstyle("outline-style").value,X=t.pstyle("outline-opacity").value*p,j=t.pstyle("outline-offset").value,Y=t.pstyle("corner-radius").value;"auto"!==Y&&(Y=t.pstyle("corner-radius").pfValue);var q=function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:B;c.eleFillStyle(e,t,n)},W=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:O;c.colorStrokeStyle(e,D[0],D[1],D[2],t)},U=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:X;c.colorStrokeStyle(e,V[0],V[1],V[2],t)},G=function(e,t,n,r){var i,a=c.nodePathCache=c.nodePathCache||[],o=eP("polygon"===n?n+","+r.join(","):n,""+t,""+e,""+Y),s=a[o],l=!1;return null!=s?(l=!0,h.pathCache=i=s):(i=new Path2D,a[o]=h.pathCache=i),{path:i,cacheHit:l}},H=t.pstyle("shape").strValue,K=t.pstyle("shape-polygon-points").pfValue;if(g){e.translate(f.x,f.y);var Z=G(r,i,H,K);a=Z.path,v=Z.cacheHit}var $=function(){if(!v){var n=f;g&&(n={x:0,y:0}),c.nodeShapes[c.getNodeShape(t)].draw(a||e,n.x,n.y,r,i,Y,h)}g?e.fill(a):e.fill()},Q=function(){for(var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:p,r=!(arguments.length>1)||void 0===arguments[1]||arguments[1],i=d.backgrounding,a=0,o=0;o<w.length;o++){var s=t.cy().style().getIndexedStyle(t,"background-image-containment","value",o);if(r&&"over"===s||!r&&"inside"===s){a++;continue}x[o]&&w[o].complete&&!w[o].error&&(a++,c.drawInscribedImage(e,w[o],t,o,n))}d.backgrounding=a!==E,i!==d.backgrounding&&t.updateStyle(!1)},J=function(){var n=arguments.length>0&&void 0!==arguments[0]&&arguments[0],a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:p;c.hasPie(t)&&(c.drawPie(e,t,a),n&&!g&&c.nodeShapes[c.getNodeShape(t)].draw(e,f.x,f.y,r,i,Y,h))},ee=function(){var n=arguments.length>0&&void 0!==arguments[0]&&arguments[0],a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:p;c.hasStripe(t)&&(e.save(),g?e.clip(h.pathCache):(c.nodeShapes[c.getNodeShape(t)].draw(e,f.x,f.y,r,i,Y,h),e.clip()),c.drawStripe(e,t,a),e.restore(),n&&!g&&c.nodeShapes[c.getNodeShape(t)].draw(e,f.x,f.y,r,i,Y,h))},et=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:p,n=P>0?0:255;0!==P&&(c.colorFillStyle(e,n,n,n,(P>0?P:-P)*t),g?e.fill(a):e.fill())},en=function(){if(S>0){if(e.lineWidth=S,e.lineCap=R,e.lineJoin=A,e.setLineDash)switch(_){case"dotted":e.setLineDash([1,1]);break;case"dashed":e.setLineDash(N),e.lineDashOffset=L;break;case"solid":case"double":e.setLineDash([])}if("center"!==I){if(e.save(),e.lineWidth*=2,"inside"===I)g?e.clip(a):e.clip();else{var t=new Path2D;t.rect(-r/2-S,-i/2-S,r+2*S,i+2*S),t.addPath(a),e.clip(t,"evenodd")}g?e.stroke(a):e.stroke(),e.restore()}else g?e.stroke(a):e.stroke();if("double"===_){e.lineWidth=S/3;var n=e.globalCompositeOperation;e.globalCompositeOperation="destination-out",g?e.stroke(a):e.stroke(),e.globalCompositeOperation=n}e.setLineDash&&e.setLineDash([])}},er=function(){if(z>0){if(e.lineWidth=z,e.lineCap="butt",e.setLineDash)switch(F){case"dotted":e.setLineDash([1,1]);break;case"dashed":e.setLineDash([4,2]);break;case"solid":case"double":e.setLineDash([])}var n=f;g&&(n={x:0,y:0});var a=c.getNodeShape(t),o=S;"inside"===I&&(o=0),"outside"===I&&(o*=2);var s=(r+o+(z+j))/r,l=(i+o+(z+j))/i,u=r*s,d=i*l,h=c.nodeShapes[a].points;if(g&&(k=G(u,d,a,h).path),"ellipse"===a)c.drawEllipsePath(k||e,n.x,n.y,u,d);else if(["round-diamond","round-heptagon","round-hexagon","round-octagon","round-pentagon","round-polygon","round-triangle","round-tag"].includes(a)){var p=0,v=0,y=0;"round-diamond"===a?p=(o+j+z)*1.4:"round-heptagon"===a?(p=(o+j+z)*1.075,y=-(o/2+j+z)/35):"round-hexagon"===a?p=(o+j+z)*1.12:"round-pentagon"===a?(p=(o+j+z)*1.13,y=-(o/2+j+z)/15):"round-tag"===a?(p=(o+j+z)*1.12,v=(o/2+z+j)*.07):"round-triangle"===a&&(p=Math.PI/2*(o+j+z),y=-(o+j/2+z)/Math.PI),0!==p&&(s=(r+p)/r,u=r*s,["round-hexagon","round-tag"].includes(a)||(l=(i+p)/i,d=i*l)),Y="auto"===Y?t2(u,d):Y;for(var b=u/2,x=d/2,w=Y+(o+z+j)/2,E=Array(h.length/2),T=Array(h.length/2),C=0;C<h.length/2;C++)E[C]={x:n.x+v+b*h[2*C],y:n.y+y+x*h[2*C+1]};var k,P,B,D,_,A=E.length;for(P=0,B=E[A-1];P<A;P++)D=E[P%A],_=E[(P+1)%A],T[P]=lA(B,D,_,w),B=D,D=_;c.drawRoundPolygonPath(k||e,n.x+v,n.y+y,r*s,i*l,h,T)}else["roundrectangle","round-rectangle"].includes(a)?(Y="auto"===Y?t1(u,d):Y,c.drawRoundRectanglePath(k||e,n.x,n.y,u,d,Y+(o+z+j)/2)):["cutrectangle","cut-rectangle"].includes(a)?(Y="auto"===Y?t5():Y,c.drawCutRectanglePath(k||e,n.x,n.y,u,d,null,Y+(o+z+j)/4)):["bottomroundrectangle","bottom-round-rectangle"].includes(a)?(Y="auto"===Y?t1(u,d):Y,c.drawBottomRoundRectanglePath(k||e,n.x,n.y,u,d,Y+(o+z+j)/2)):"barrel"===a?c.drawBarrelPath(k||e,n.x,n.y,u,d):(h=a.startsWith("polygon")||["rhomboid","right-rhomboid","round-tag","tag","vee"].includes(a)?tX(tj(h,(o+z+j)/r)):tX(tj(h,-((o+z+j)/r))),c.drawPolygonPath(k||e,n.x,n.y,r,i,h));if(g?e.stroke(k):e.stroke(),"double"===F){e.lineWidth=o/3;var M=e.globalCompositeOperation;e.globalCompositeOperation="destination-out",g?e.stroke(k):e.stroke(),e.globalCompositeOperation=M}e.setLineDash&&e.setLineDash([])}};if("yes"===t.pstyle("ghost").value){var ei=t.pstyle("ghost-offset-x").pfValue,ea=t.pstyle("ghost-offset-y").pfValue,eo=t.pstyle("ghost-opacity").value,es=eo*p;e.translate(ei,ea),U(),er(),q(eo*B),$(),Q(es,!0),W(eo*O),en(),J(0!==P||0!==S),ee(0!==P||0!==S),Q(es,!1),et(es),e.translate(-ei,-ea)}g&&e.translate(-f.x,-f.y),l&&c.drawNodeUnderlay(e,t,f,r,i),g&&e.translate(f.x,f.y),U(),er(),q(),$(),Q(p,!0),W(),en(),J(0!==P||0!==S),ee(0!==P||0!==S),Q(p,!1),et(),g&&e.translate(-f.x,-f.y),c.drawElementText(e,t,null,s),l&&c.drawNodeOverlay(e,t,f,r,i),n&&e.translate(o.x1,o.y1)}};var um=function(e){if(!["overlay","underlay"].includes(e))throw Error("Invalid state");return function(t,n,r,i,a){if(n.visible()){var o=n.pstyle("".concat(e,"-padding")).pfValue,s=n.pstyle("".concat(e,"-opacity")).value,l=n.pstyle("".concat(e,"-color")).value,u=n.pstyle("".concat(e,"-shape")).value,c=n.pstyle("".concat(e,"-corner-radius")).value;if(s>0){if(r=r||n.position(),null==i||null==a){var d=n.padding();i=n.width()+2*d,a=n.height()+2*d}this.colorFillStyle(t,l[0],l[1],l[2],s),this.nodeShapes[u].draw(t,r.x,r.y,i+2*o,a+2*o,c),t.fill()}}}};uy.drawNodeOverlay=um("overlay"),uy.drawNodeUnderlay=um("underlay"),uy.hasPie=function(e){return(e=e[0])._private.hasPie},uy.hasStripe=function(e){return(e=e[0])._private.hasStripe},uy.drawPie=function(e,t,n,r){t=t[0],r=r||t.position();var i,a=t.cy().style(),o=t.pstyle("pie-size"),s=t.pstyle("pie-hole"),l=t.pstyle("pie-start-angle").pfValue,u=r.x,c=r.y,d=Math.min(t.width(),t.height())/2,h=0;if(this.usePaths()&&(u=0,c=0),"%"===o.units?d*=o.pfValue:void 0!==o.pfValue&&(d=o.pfValue/2),"%"===s.units?i=d*s.pfValue:void 0!==s.pfValue&&(i=s.pfValue/2),!(i>=d))for(var f=1;f<=a.pieBackgroundN;f++){var p=t.pstyle("pie-"+f+"-background-size").value,g=t.pstyle("pie-"+f+"-background-color").value,v=t.pstyle("pie-"+f+"-background-opacity").value*n,y=p/100;y+h>1&&(y=1-h);var b=1.5*Math.PI+2*Math.PI*h,x=2*Math.PI*y,w=(b+=l)+x;0===p||h>=1||h+y>1||(0===i?(e.beginPath(),e.moveTo(u,c),e.arc(u,c,d,b,w)):(e.beginPath(),e.arc(u,c,d,b,w),e.arc(u,c,i,w,b,!0)),e.closePath(),this.colorFillStyle(e,g[0],g[1],g[2],v),e.fill(),h+=y)}},uy.drawStripe=function(e,t,n,r){t=t[0],r=r||t.position();var i=t.cy().style(),a=r.x,o=r.y,s=t.width(),l=t.height(),u=0,c=this.usePaths();e.save();var d=t.pstyle("stripe-direction").value,h=t.pstyle("stripe-size");switch(d){case"vertical":break;case"righward":e.rotate(-Math.PI/2)}var f=s,p=l;"%"===h.units?(f*=h.pfValue,p*=h.pfValue):void 0!==h.pfValue&&(f=h.pfValue,p=h.pfValue),c&&(a=0,o=0),o-=f/2,a-=p/2;for(var g=1;g<=i.stripeBackgroundN;g++){var v=t.pstyle("stripe-"+g+"-background-size").value,y=t.pstyle("stripe-"+g+"-background-color").value,b=t.pstyle("stripe-"+g+"-background-opacity").value*n,x=v/100;x+u>1&&(x=1-u),0===v||u>=1||u+x>1||(e.beginPath(),e.rect(a,o+p*u,f,p*x),e.closePath(),this.colorFillStyle(e,y[0],y[1],y[2],b),e.fill(),u+=x)}e.restore()};var ub={};function ux(e,t,n){var r=e.createShader(t);if(e.shaderSource(r,n),e.compileShader(r),!e.getShaderParameter(r,e.COMPILE_STATUS))throw Error(e.getShaderInfoLog(r));return r}function uw(e,t,n){void 0===n&&(n=t);var r=e.makeOffscreenCanvas(t,n),i=r.context=r.getContext("2d");return r.clear=function(){return i.clearRect(0,0,r.width,r.height)},r.clear(),r}function uE(e){var t=e.pixelRatio,n=e.cy.zoom(),r=e.cy.pan();return{zoom:n*t,pan:{x:r.x*t,y:r.y*t}}}function uT(e){return"solid"===e.pstyle("background-fill").value&&"none"===e.pstyle("background-image").strValue&&(0===e.pstyle("border-width").value||0===e.pstyle("border-opacity").value||"solid"===e.pstyle("border-style").value)}function uC(e,t,n){var r=e[0]/255,i=e[1]/255,a=e[2]/255,o=n||[,,,,];return o[0]=r*t,o[1]=i*t,o[2]=a*t,o[3]=t,o}function uk(e,t){var n=t||[,,,,];return n[0]=((0|e)&255)/255,n[1]=(e>>8&255)/255,n[2]=(e>>16&255)/255,n[3]=(e>>24&255)/255,n}function uP(e,t){switch(t){case"float":return[1,e.FLOAT,4];case"vec2":return[2,e.FLOAT,4];case"vec3":return[3,e.FLOAT,4];case"vec4":return[4,e.FLOAT,4];case"int":return[1,e.INT,4];case"ivec2":return[2,e.INT,4]}}function uS(e,t,n){switch(t){case e.FLOAT:return new Float32Array(n);case e.INT:return new Int32Array(n)}}function uB(e,t,n,r){var i=l(uP(e,n),3),a=i[0],o=i[1],s=i[2],u=uS(e,o,t*a),c=a*s,d=e.createBuffer();e.bindBuffer(e.ARRAY_BUFFER,d),e.bufferData(e.ARRAY_BUFFER,t*c,e.DYNAMIC_DRAW),e.enableVertexAttribArray(r),o===e.FLOAT?e.vertexAttribPointer(r,a,o,!1,c,0):o===e.INT&&e.vertexAttribIPointer(r,a,o,c,0),e.vertexAttribDivisor(r,1),e.bindBuffer(e.ARRAY_BUFFER,null);for(var h=Array(t),f=0;f<t;f++)h[f]=function(e,t,n,r,i,a){switch(t){case e.FLOAT:return new Float32Array(n.buffer,a*r,i);case e.INT:return new Int32Array(n.buffer,a*r,i)}}(e,o,u,c,a,f);return d.dataArray=u,d.stride=c,d.size=a,d.getView=function(e){return h[e]},d.setPoint=function(e,t,n){var r=h[e];r[0]=t,r[1]=n},d.bufferSubData=function(t){e.bindBuffer(e.ARRAY_BUFFER,d),t?e.bufferSubData(e.ARRAY_BUFFER,0,u,0,t*a):e.bufferSubData(e.ARRAY_BUFFER,0,u)},d}ub.getPixelRatio=function(){var e=this.data.contexts[0];if(null!=this.forcedPixelRatio)return this.forcedPixelRatio;var t=this.cy.window(),n=e.backingStorePixelRatio||e.webkitBackingStorePixelRatio||e.mozBackingStorePixelRatio||e.msBackingStorePixelRatio||e.oBackingStorePixelRatio||e.backingStorePixelRatio||1;return(t.devicePixelRatio||1)/n},ub.paintCache=function(e){for(var t,n=this.paintCaches=this.paintCaches||[],r=!0,i=0;i<n.length;i++)if((t=n[i]).context===e){r=!1;break}return r&&(t={context:e},n.push(t)),t},ub.createGradientStyleFor=function(e,t,n,r,i){var a,o=this.usePaths(),s=n.pstyle(t+"-gradient-stop-colors").value,l=n.pstyle(t+"-gradient-stop-positions").pfValue;if("radial-gradient"===r)if(n.isEdge()){var u=n.sourceEndpoint(),c=n.targetEndpoint(),d=n.midpoint(),h=th(u,d),f=th(c,d);a=e.createRadialGradient(d.x,d.y,0,d.x,d.y,Math.max(h,f))}else{var p=o?{x:0,y:0}:n.position(),g=n.paddedWidth(),v=n.paddedHeight();a=e.createRadialGradient(p.x,p.y,0,p.x,p.y,Math.max(g,v))}else if(n.isEdge()){var y=n.sourceEndpoint(),b=n.targetEndpoint();a=e.createLinearGradient(y.x,y.y,b.x,b.y)}else{var x=o?{x:0,y:0}:n.position(),w=n.paddedWidth(),E=n.paddedHeight(),T=w/2,C=E/2;switch(n.pstyle("background-gradient-direction").value){case"to-bottom":a=e.createLinearGradient(x.x,x.y-C,x.x,x.y+C);break;case"to-top":a=e.createLinearGradient(x.x,x.y+C,x.x,x.y-C);break;case"to-left":a=e.createLinearGradient(x.x+T,x.y,x.x-T,x.y);break;case"to-right":a=e.createLinearGradient(x.x-T,x.y,x.x+T,x.y);break;case"to-bottom-right":case"to-right-bottom":a=e.createLinearGradient(x.x-T,x.y-C,x.x+T,x.y+C);break;case"to-top-right":case"to-right-top":a=e.createLinearGradient(x.x-T,x.y+C,x.x+T,x.y-C);break;case"to-bottom-left":case"to-left-bottom":a=e.createLinearGradient(x.x+T,x.y-C,x.x-T,x.y+C);break;case"to-top-left":case"to-left-top":a=e.createLinearGradient(x.x+T,x.y+C,x.x-T,x.y-C)}}if(!a)return null;for(var k=l.length===s.length,P=s.length,S=0;S<P;S++)a.addColorStop(k?l[S]:S/(P-1),"rgba("+s[S][0]+","+s[S][1]+","+s[S][2]+","+i+")");return a},ub.gradientFillStyle=function(e,t,n,r){var i=this.createGradientStyleFor(e,"background",t,n,r);if(!i)return null;e.fillStyle=i},ub.colorFillStyle=function(e,t,n,r,i){e.fillStyle="rgba("+t+","+n+","+r+","+i+")"},ub.eleFillStyle=function(e,t,n){var r=t.pstyle("background-fill").value;if("linear-gradient"===r||"radial-gradient"===r)this.gradientFillStyle(e,t,r,n);else{var i=t.pstyle("background-color").value;this.colorFillStyle(e,i[0],i[1],i[2],n)}},ub.gradientStrokeStyle=function(e,t,n,r){var i=this.createGradientStyleFor(e,"line",t,n,r);if(!i)return null;e.strokeStyle=i},ub.colorStrokeStyle=function(e,t,n,r,i){e.strokeStyle="rgba("+t+","+n+","+r+","+i+")"},ub.eleStrokeStyle=function(e,t,n){var r=t.pstyle("line-fill").value;if("linear-gradient"===r||"radial-gradient"===r)this.gradientStrokeStyle(e,t,r,n);else{var i=t.pstyle("line-color").value;this.colorStrokeStyle(e,i[0],i[1],i[2],n)}},ub.matchCanvasSize=function(e){var t,n=this.data,r=this.findContainerClientCoords(),i=r[2],a=r[3],o=this.getPixelRatio(),s=this.motionBlurPxRatio;(e===this.data.bufferCanvases[this.MOTIONBLUR_BUFFER_NODE]||e===this.data.bufferCanvases[this.MOTIONBLUR_BUFFER_DRAG])&&(o=s);var l=i*o,u=a*o;if(l!==this.canvasWidth||u!==this.canvasHeight){this.fontCaches=null;var c=n.canvasContainer;c.style.width=i+"px",c.style.height=a+"px";for(var d=0;d<this.CANVAS_LAYERS;d++)(t=n.canvases[d]).width=l,t.height=u,t.style.width=i+"px",t.style.height=a+"px";for(var d=0;d<this.BUFFER_COUNT;d++)(t=n.bufferCanvases[d]).width=l,t.height=u,t.style.width=i+"px",t.style.height=a+"px";this.textureMult=1,o<=1&&(t=n.bufferCanvases[this.TEXTURE_BUFFER],this.textureMult=2,t.width=l*this.textureMult,t.height=u*this.textureMult),this.canvasWidth=l,this.canvasHeight=u,this.pixelRatio=o}},ub.renderTo=function(e,t,n,r){this.render({forcedContext:e,forcedZoom:t,forcedPan:n,drawAllLayers:!0,forcedPxRatio:r})},ub.clearCanvas=function(){var e=this,t=e.data;function n(t){t.clearRect(0,0,e.canvasWidth,e.canvasHeight)}n(t.contexts[e.NODE]),n(t.contexts[e.DRAG])},ub.render=function(e){var t=this;e=e||ej();var n=t.cy,r=e.forcedContext,i=e.drawAllLayers,a=e.drawOnlyNodeLayer,o=e.forcedZoom,s=e.forcedPan,l=void 0===e.forcedPxRatio?this.getPixelRatio():e.forcedPxRatio,u=t.data,c=u.canvasNeedsRedraw,d=t.textureOnViewport&&!r&&(t.pinching||t.hoverData.dragging||t.swipePanning||t.data.wheelZooming),h=void 0!==e.motionBlur?e.motionBlur:t.motionBlur,f=t.motionBlurPxRatio,p=n.hasCompoundNodes(),g=t.hoverData.draggingEles,v=!!t.hoverData.selecting||!!t.touchData.selecting,y=h=h&&!r&&t.motionBlurEnabled&&!v;r||(t.prevPxRatio!==l&&(t.invalidateContainerClientCoordsCache(),t.matchCanvasSize(t.container),t.redrawHint("eles",!0),t.redrawHint("drag",!0)),t.prevPxRatio=l),!r&&t.motionBlurTimeout&&clearTimeout(t.motionBlurTimeout),h&&(null==t.mbFrames&&(t.mbFrames=0),t.mbFrames++,t.mbFrames<3&&(y=!1),t.mbFrames>t.minMbLowQualFrames&&(t.motionBlurPxRatio=t.mbPxRBlurry)),t.clearingMotionBlur&&(t.motionBlurPxRatio=1),t.textureDrawLastFrame&&!d&&(c[t.NODE]=!0,c[t.SELECT_BOX]=!0);var b=n.style(),x=n.zoom(),w=void 0!==o?o:x,E=n.pan(),T={x:E.x,y:E.y},C={zoom:x,pan:{x:E.x,y:E.y}},k=t.prevViewport;void 0===k||C.zoom!==k.zoom||C.pan.x!==k.pan.x||C.pan.y!==k.pan.y||g&&!p||(t.motionBlurPxRatio=1),s&&(T=s),w*=l,T.x*=l,T.y*=l;var P=t.getCachedZSortedEles();function S(e,n,r,i,a){var o=e.globalCompositeOperation;e.globalCompositeOperation="destination-out",t.colorFillStyle(e,255,255,255,t.motionBlurTransparency),e.fillRect(n,r,i,a),e.globalCompositeOperation=o}function B(e,n){var a,l,c,d;t.clearingMotionBlur||e!==u.bufferContexts[t.MOTIONBLUR_BUFFER_NODE]&&e!==u.bufferContexts[t.MOTIONBLUR_BUFFER_DRAG]?(a=T,l=w,c=t.canvasWidth,d=t.canvasHeight):(a={x:E.x*f,y:E.y*f},l=x*f,c=t.canvasWidth*f,d=t.canvasHeight*f),e.setTransform(1,0,0,1,0,0),"motionBlur"===n?S(e,0,0,c,d):!r&&(void 0===n||n)&&e.clearRect(0,0,c,d),i||(e.translate(a.x,a.y),e.scale(l,l)),s&&e.translate(s.x,s.y),o&&e.scale(o,o)}if(d||(t.textureDrawLastFrame=!1),d){if(t.textureDrawLastFrame=!0,!t.textureCache){t.textureCache={},t.textureCache.bb=n.mutableElements().boundingBox(),t.textureCache.texture=t.data.bufferCanvases[t.TEXTURE_BUFFER];var D=t.data.bufferContexts[t.TEXTURE_BUFFER];D.setTransform(1,0,0,1,0,0),D.clearRect(0,0,t.canvasWidth*t.textureMult,t.canvasHeight*t.textureMult),t.render({forcedContext:D,drawOnlyNodeLayer:!0,forcedPxRatio:l*t.textureMult});var C=t.textureCache.viewport={zoom:n.zoom(),pan:n.pan(),width:t.canvasWidth,height:t.canvasHeight};C.mpan={x:(0-C.pan.x)/C.zoom,y:(0-C.pan.y)/C.zoom}}c[t.DRAG]=!1,c[t.NODE]=!1;var _=u.contexts[t.NODE],A=t.textureCache.texture,C=t.textureCache.viewport;_.setTransform(1,0,0,1,0,0),h?S(_,0,0,C.width,C.height):_.clearRect(0,0,C.width,C.height);var M=b.core("outside-texture-bg-color").value,R=b.core("outside-texture-bg-opacity").value;t.colorFillStyle(_,M[0],M[1],M[2],R),_.fillRect(0,0,C.width,C.height);var x=n.zoom();B(_,!1),_.clearRect(C.mpan.x,C.mpan.y,C.width/C.zoom/l,C.height/C.zoom/l),_.drawImage(A,C.mpan.x,C.mpan.y,C.width/C.zoom/l,C.height/C.zoom/l)}else t.textureOnViewport&&!r&&(t.textureCache=null);var I=n.extent(),N=t.pinching||t.hoverData.dragging||t.swipePanning||t.data.wheelZooming||t.hoverData.draggingEles||t.cy.animated(),L=t.hideEdgesOnViewport&&N,O=[];if(O[t.NODE]=!c[t.NODE]&&h&&!t.clearedForMotionBlur[t.NODE]||t.clearingMotionBlur,O[t.NODE]&&(t.clearedForMotionBlur[t.NODE]=!0),O[t.DRAG]=!c[t.DRAG]&&h&&!t.clearedForMotionBlur[t.DRAG]||t.clearingMotionBlur,O[t.DRAG]&&(t.clearedForMotionBlur[t.DRAG]=!0),c[t.NODE]||i||a||O[t.NODE]){var z=h&&!O[t.NODE]&&1!==f,_=r||(z?t.data.bufferContexts[t.MOTIONBLUR_BUFFER_NODE]:u.contexts[t.NODE]);B(_,h&&!z?"motionBlur":void 0),L?t.drawCachedNodes(_,P.nondrag,l,I):t.drawLayeredElements(_,P.nondrag,l,I),t.debug&&t.drawDebugPoints(_,P.nondrag),i||h||(c[t.NODE]=!1)}if(!a&&(c[t.DRAG]||i||O[t.DRAG])){var z=h&&!O[t.DRAG]&&1!==f,_=r||(z?t.data.bufferContexts[t.MOTIONBLUR_BUFFER_DRAG]:u.contexts[t.DRAG]);B(_,h&&!z?"motionBlur":void 0),L?t.drawCachedNodes(_,P.drag,l,I):t.drawCachedElements(_,P.drag,l,I),t.debug&&t.drawDebugPoints(_,P.drag),i||h||(c[t.DRAG]=!1)}if(this.drawSelectionRectangle(e,B),h&&1!==f){var V=u.contexts[t.NODE],F=t.data.bufferCanvases[t.MOTIONBLUR_BUFFER_NODE],X=u.contexts[t.DRAG],j=t.data.bufferCanvases[t.MOTIONBLUR_BUFFER_DRAG],Y=function(e,n,r){e.setTransform(1,0,0,1,0,0),r||!y?e.clearRect(0,0,t.canvasWidth,t.canvasHeight):S(e,0,0,t.canvasWidth,t.canvasHeight),e.drawImage(n,0,0,t.canvasWidth*f,t.canvasHeight*f,0,0,t.canvasWidth,t.canvasHeight)};(c[t.NODE]||O[t.NODE])&&(Y(V,F,O[t.NODE]),c[t.NODE]=!1),(c[t.DRAG]||O[t.DRAG])&&(Y(X,j,O[t.DRAG]),c[t.DRAG]=!1)}t.prevViewport=C,t.clearingMotionBlur&&(t.clearingMotionBlur=!1,t.motionBlurCleared=!0,t.motionBlur=!0),h&&(t.motionBlurTimeout=setTimeout(function(){t.motionBlurTimeout=null,t.clearedForMotionBlur[t.NODE]=!1,t.clearedForMotionBlur[t.DRAG]=!1,t.motionBlur=!1,t.clearingMotionBlur=!d,t.mbFrames=0,c[t.NODE]=!0,c[t.DRAG]=!0,t.redraw()},100)),r||n.emit("render")},ub.drawSelectionRectangle=function(e,t){var n=this.cy,r=this.data,i=n.style(),a=e.drawOnlyNodeLayer,o=e.drawAllLayers,s=r.canvasNeedsRedraw,l=e.forcedContext;if(this.showFps||!a&&s[this.SELECT_BOX]&&!o){var u=l||r.contexts[this.SELECT_BOX];if(t(u),1==this.selection[4]&&(this.hoverData.selecting||this.touchData.selecting)){var c=this.cy.zoom(),d=i.core("selection-box-border-width").value/c;u.lineWidth=d,u.fillStyle="rgba("+i.core("selection-box-color").value[0]+","+i.core("selection-box-color").value[1]+","+i.core("selection-box-color").value[2]+","+i.core("selection-box-opacity").value+")",u.fillRect(this.selection[0],this.selection[1],this.selection[2]-this.selection[0],this.selection[3]-this.selection[1]),d>0&&(u.strokeStyle="rgba("+i.core("selection-box-border-color").value[0]+","+i.core("selection-box-border-color").value[1]+","+i.core("selection-box-border-color").value[2]+","+i.core("selection-box-opacity").value+")",u.strokeRect(this.selection[0],this.selection[1],this.selection[2]-this.selection[0],this.selection[3]-this.selection[1]))}if(r.bgActivePosistion&&!this.hoverData.selecting){var c=this.cy.zoom(),h=r.bgActivePosistion;u.fillStyle="rgba("+i.core("active-bg-color").value[0]+","+i.core("active-bg-color").value[1]+","+i.core("active-bg-color").value[2]+","+i.core("active-bg-opacity").value+")",u.beginPath(),u.arc(h.x,h.y,i.core("active-bg-size").pfValue/c,0,2*Math.PI),u.fill()}var f=this.lastRedrawTime;if(this.showFps&&f){var p=Math.round(1e3/(f=Math.round(f))),g="1 frame = "+f+" ms = "+p+" fps";u.setTransform(1,0,0,1,0,0),u.fillStyle="rgba(255, 0, 0, 0.75)",u.strokeStyle="rgba(255, 0, 0, 0.75)",u.font="30px Arial",x||(x=u.measureText(g).actualBoundingBoxAscent),u.fillText(g,0,x),u.strokeRect(0,x+10,250,20),u.fillRect(0,x+10,250*Math.min(p/60,1),20)}o||(s[this.SELECT_BOX]=!1)}};var uD="undefined"!=typeof Float32Array?Float32Array:Array;function u_(){var e=new uD(9);return uD!=Float32Array&&(e[1]=0,e[2]=0,e[3]=0,e[5]=0,e[6]=0,e[7]=0),e[0]=1,e[4]=1,e[8]=1,e}function uA(e){return e[0]=1,e[1]=0,e[2]=0,e[3]=0,e[4]=1,e[5]=0,e[6]=0,e[7]=0,e[8]=1,e}function uM(e,t,n){var r=t[0],i=t[1],a=t[2],o=t[3],s=t[4],l=t[5],u=t[6],c=t[7],d=t[8],h=n[0],f=n[1];return e[0]=r,e[1]=i,e[2]=a,e[3]=o,e[4]=s,e[5]=l,e[6]=h*r+f*o+u,e[7]=h*i+f*s+c,e[8]=h*a+f*l+d,e}function uR(e,t,n){var r=t[0],i=t[1],a=t[2],o=t[3],s=t[4],l=t[5],u=t[6],c=t[7],d=t[8],h=Math.sin(n),f=Math.cos(n);return e[0]=f*r+h*o,e[1]=f*i+h*s,e[2]=f*a+h*l,e[3]=f*o-h*r,e[4]=f*s-h*i,e[5]=f*l-h*a,e[6]=u,e[7]=c,e[8]=d,e}function uI(e,t,n){var r=n[0],i=n[1];return e[0]=r*t[0],e[1]=r*t[1],e[2]=r*t[2],e[3]=i*t[3],e[4]=i*t[4],e[5]=i*t[5],e[6]=t[6],e[7]=t[7],e[8]=t[8],e}Math.hypot||(Math.hypot=function(){for(var e=0,t=arguments.length;t--;)e+=arguments[t]*arguments[t];return Math.sqrt(e)});var uN=a(function e(t,n,r,a){i(this,e),this.debugID=Math.floor(1e4*Math.random()),this.r=t,this.texSize=n,this.texRows=r,this.texHeight=Math.floor(n/r),this.enableWrapping=!0,this.locked=!1,this.texture=null,this.needsBuffer=!0,this.freePointer={x:0,row:0},this.keyToLocation=new Map,this.canvas=a(t,n,n),this.scratch=a(t,n,this.texHeight,"scratch")},[{key:"lock",value:function(){this.locked=!0}},{key:"getKeys",value:function(){return new Set(this.keyToLocation.keys())}},{key:"getScale",value:function(e){var t=e.w,n=e.h,r=this.texHeight,i=this.texSize,a=r/n,o=t*a,s=n*a;return o>i&&(a=i/t,o=t*a,s=n*a),{scale:a,texW:o,texH:s}}},{key:"draw",value:function(e,t,n){var r,i,a,o,s,l,u,c=this;if(this.locked)throw Error("can't draw, atlas is locked");var d=this.texSize,h=this.texRows,f=this.texHeight,p=this.getScale(t),g=p.scale,v=p.texW,y=p.texH,b=function(e,r){if(n&&r){var i=r.context,a=e.x,o=f*e.row;i.save(),i.translate(a,o),i.scale(g,g),n(i,t),i.restore()}},x=[null,null],w=function(){b(c.freePointer,c.canvas),x[0]={x:c.freePointer.x,y:c.freePointer.row*f,w:v,h:y},x[1]={x:c.freePointer.x+v,y:c.freePointer.row*f,w:0,h:y},c.freePointer.x+=v,c.freePointer.x==d&&(c.freePointer.x=0,c.freePointer.row++)},E=function(){c.freePointer.x=0,c.freePointer.row++};if(this.freePointer.x+v<=d)w();else{if(this.freePointer.row>=h-1)return!1;this.freePointer.x===d?(E(),w()):this.enableWrapping?(r=c.scratch,i=c.canvas,r.clear(),b({x:0,row:0},r),o=v-(a=d-c.freePointer.x),s=c.freePointer.x,l=c.freePointer.row*f,i.context.drawImage(r,0,0,a,f,s,l,a,f),x[0]={x:s,y:l,w:a,h:y},u=(c.freePointer.row+1)*f,i&&i.context.drawImage(r,a,0,o,f,0,u,o,f),x[1]={x:0,y:u,w:o,h:y},c.freePointer.x=o,c.freePointer.row++):(E(),w())}return this.keyToLocation.set(e,x),this.needsBuffer=!0,x}},{key:"getOffsets",value:function(e){return this.keyToLocation.get(e)}},{key:"isEmpty",value:function(){return 0===this.freePointer.x&&0===this.freePointer.row}},{key:"canFit",value:function(e){if(this.locked)return!1;var t=this.texSize,n=this.texRows,r=this.getScale(e).texW;return!(this.freePointer.x+r>t)||this.freePointer.row<n-1}},{key:"bufferIfNeeded",value:function(e){var t;this.texture||(this.texture=(this.debugID,(t=e.createTexture()).buffer=function(n){e.bindTexture(e.TEXTURE_2D,t),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_S,e.CLAMP_TO_EDGE),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_T,e.CLAMP_TO_EDGE),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MAG_FILTER,e.LINEAR),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MIN_FILTER,e.LINEAR_MIPMAP_NEAREST),e.pixelStorei(e.UNPACK_PREMULTIPLY_ALPHA_WEBGL,!0),e.texImage2D(e.TEXTURE_2D,0,e.RGBA,e.RGBA,e.UNSIGNED_BYTE,n),e.generateMipmap(e.TEXTURE_2D),e.bindTexture(e.TEXTURE_2D,null)},t.deleteTexture=function(){e.deleteTexture(t)},t)),this.needsBuffer&&(this.texture.buffer(this.canvas),this.needsBuffer=!1,this.locked&&(this.canvas=null,this.scratch=null))}},{key:"dispose",value:function(){this.texture&&(this.texture.deleteTexture(),this.texture=null),this.canvas=null,this.scratch=null,this.locked=!0}}]),uL=a(function e(t,n,r,a){i(this,e),this.r=t,this.texSize=n,this.texRows=r,this.createTextureCanvas=a,this.atlases=[],this.styleKeyToAtlas=new Map,this.markedKeys=new Set},[{key:"getKeys",value:function(){return new Set(this.styleKeyToAtlas.keys())}},{key:"_createAtlas",value:function(){return new uN(this.r,this.texSize,this.texRows,this.createTextureCanvas)}},{key:"_getScratchCanvas",value:function(){if(!this.scratch){var e=this.r,t=this.texSize,n=this.texRows,r=this.createTextureCanvas,i=Math.floor(t/n);this.scratch=r(e,t,i,"scratch")}return this.scratch}},{key:"draw",value:function(e,t,n){var r=this.styleKeyToAtlas.get(e);return r||((r=this.atlases[this.atlases.length-1])&&r.canFit(t)||(r&&r.lock(),r=this._createAtlas(),this.atlases.push(r)),r.draw(e,t,n),this.styleKeyToAtlas.set(e,r)),r}},{key:"getAtlas",value:function(e){return this.styleKeyToAtlas.get(e)}},{key:"hasAtlas",value:function(e){return this.styleKeyToAtlas.has(e)}},{key:"markKeyForGC",value:function(e){this.markedKeys.add(e)}},{key:"gc",value:function(){var e=this,t=this.markedKeys;if(0===t.size)return void console.log("nothing to garbage collect");var n,r=[],i=new Map,a=null,s=o(this.atlases);try{for(s.s();!(n=s.n()).done;)if(function(){var s=n.value,c=s.getKeys(),d=function(e,t){return e.intersection?e.intersection(t):new Set(u(e).filter(function(e){return t.has(e)}))}(t,c);if(0===d.size)return r.push(s),c.forEach(function(e){return i.set(e,s)}),1;a||(a=e._createAtlas(),r.push(a));var h,f=o(c);try{for(f.s();!(h=f.n()).done;){var p=h.value;if(!d.has(p)){var g=s.getOffsets(p),v=l(g,2),y=v[0],b=v[1];a.canFit({w:y.w+b.w,h:y.h})||(a.lock(),a=e._createAtlas(),r.push(a)),s.canvas&&(e._copyTextureToNewAtlas(p,s,a),i.set(p,a))}}}catch(e){f.e(e)}finally{f.f()}s.dispose()}())continue}catch(e){s.e(e)}finally{s.f()}this.atlases=r,this.styleKeyToAtlas=i,this.markedKeys=new Set}},{key:"_copyTextureToNewAtlas",value:function(e,t,n){var r=l(t.getOffsets(e),2),i=r[0],a=r[1];if(0===a.w)n.draw(e,i,function(e){e.drawImage(t.canvas,i.x,i.y,i.w,i.h,0,0,i.w,i.h)});else{var o=this._getScratchCanvas();o.clear(),o.context.drawImage(t.canvas,i.x,i.y,i.w,i.h,0,0,i.w,i.h),o.context.drawImage(t.canvas,a.x,a.y,a.w,a.h,i.w,0,a.w,a.h);var s=i.w+a.w,u=i.h;n.draw(e,{w:s,h:u},function(e){e.drawImage(o,0,0,s,u,0,0,s,u)})}}},{key:"getCounts",value:function(){return{keyCount:this.styleKeyToAtlas.size,atlasCount:new Set(this.styleKeyToAtlas.values()).size}}}]),uO=a(function e(t,n){i(this,e),this.r=t,this.globalOptions=n,this.atlasSize=n.webglTexSize,this.maxAtlasesPerBatch=n.webglTexPerBatch,this.renderTypes=new Map,this.collections=new Map,this.typeAndIdToKey=new Map},[{key:"getAtlasSize",value:function(){return this.atlasSize}},{key:"addAtlasCollection",value:function(e,t){var n=this.globalOptions,r=n.webglTexSize,i=n.createTextureCanvas,a=t.texRows,o=this._cacheScratchCanvas(i),s=new uL(this.r,r,a,o);this.collections.set(e,s)}},{key:"addRenderType",value:function(e,t){var n=t.collection;if(!this.collections.has(n))throw Error("invalid atlas collection name '".concat(n,"'"));var r=J({type:e,atlasCollection:this.collections.get(n)},t);this.renderTypes.set(e,r)}},{key:"getRenderTypeOpts",value:function(e){return this.renderTypes.get(e)}},{key:"getAtlasCollection",value:function(e){return this.collections.get(e)}},{key:"_cacheScratchCanvas",value:function(e){var t=-1,n=-1,r=null;return function(i,a,o,s){return s?(r&&a==t&&o==n||(t=a,n=o,r=e(i,a,o)),r):e(i,a,o)}}},{key:"_key",value:function(e,t){return"".concat(e,"-").concat(t)}},{key:"invalidate",value:function(e){var t,n=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=r.forceRedraw,a=void 0!==i&&i,s=r.filterEle,l=void 0===s?function(){return!0}:s,u=r.filterType,c=void 0===u?function(){return!0}:u,d=!1,h=!1,f=o(e);try{for(f.s();!(t=f.n()).done;){var p=t.value;if(l(p)){var g,v=o(this.renderTypes.values());try{for(v.s();!(g=v.n()).done;)!function(){var e=g.value,t=e.type;if(c(t)){var r=n.collections.get(e.collection),i=e.getKey(p),o=Array.isArray(i)?i:[i];if(a)o.forEach(function(e){return r.markKeyForGC(e)}),h=!0;else{var s=e.getID?e.getID(p):p.id(),l=n._key(t,s),u=n.typeAndIdToKey.get(l);void 0!==u&&!function(e,t){if(e.length!==t.length)return!1;for(var n=0;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}(o,u)&&(d=!0,n.typeAndIdToKey.delete(l),u.forEach(function(e){return r.markKeyForGC(e)}))}}}()}catch(e){v.e(e)}finally{v.f()}}}}catch(e){f.e(e)}finally{f.f()}return h&&(this.gc(),d=!1),d}},{key:"gc",value:function(){var e,t=o(this.collections.values());try{for(t.s();!(e=t.n()).done;)e.value.gc()}catch(e){t.e(e)}finally{t.f()}}},{key:"getOrCreateAtlas",value:function(e,t,n,r){var i=this.renderTypes.get(t),a=this.collections.get(i.collection),o=!1,s=a.draw(r,n,function(t){i.drawClipped?(t.save(),t.beginPath(),t.rect(0,0,n.w,n.h),t.clip(),i.drawElement(t,e,n,!0,!0),t.restore()):i.drawElement(t,e,n,!0,!0),o=!0});if(o){var l=i.getID?i.getID(e):e.id(),u=this._key(t,l);this.typeAndIdToKey.has(u)?this.typeAndIdToKey.get(u).push(r):this.typeAndIdToKey.set(u,[r])}return s}},{key:"getAtlasInfo",value:function(e,t){var n=this,r=this.renderTypes.get(t),i=r.getKey(e);return(Array.isArray(i)?i:[i]).map(function(i){var a=r.getBoundingBox(e,i),o=n.getOrCreateAtlas(e,t,a,i),s=l(o.getOffsets(i),2),u=s[0];return{atlas:o,tex:u,tex1:u,tex2:s[1],bb:a}})}},{key:"getDebugInfo",value:function(){var e,t=[],n=o(this.collections);try{for(n.s();!(e=n.n()).done;){var r=l(e.value,2),i=r[0],a=r[1].getCounts(),s=a.keyCount,u=a.atlasCount;t.push({type:i,keyCount:s,atlasCount:u})}}catch(e){n.e(e)}finally{n.f()}return t}}]),uz=a(function e(t){i(this,e),this.globalOptions=t,this.atlasSize=t.webglTexSize,this.maxAtlasesPerBatch=t.webglTexPerBatch,this.batchAtlases=[]},[{key:"getMaxAtlasesPerBatch",value:function(){return this.maxAtlasesPerBatch}},{key:"getAtlasSize",value:function(){return this.atlasSize}},{key:"getIndexArray",value:function(){return Array.from({length:this.maxAtlasesPerBatch},function(e,t){return t})}},{key:"startBatch",value:function(){this.batchAtlases=[]}},{key:"getAtlasCount",value:function(){return this.batchAtlases.length}},{key:"getAtlases",value:function(){return this.batchAtlases}},{key:"canAddToCurrentBatch",value:function(e){return this.batchAtlases.length!==this.maxAtlasesPerBatch||this.batchAtlases.includes(e)}},{key:"getAtlasIndexForBatch",value:function(e){var t=this.batchAtlases.indexOf(e);if(t<0){if(this.batchAtlases.length===this.maxAtlasesPerBatch)throw Error("cannot add more atlases to batch");this.batchAtlases.push(e),t=this.batchAtlases.length-1}return t}}]),uV={SCREEN:{name:"screen",screen:!0},PICKING:{name:"picking",picking:!0}},uF={IGNORE:1,USE_BB:2},uX=a(function e(t,n,r){i(this,e),this.r=t,this.gl=n,this.maxInstances=r.webglBatchSize,this.atlasSize=r.webglTexSize,this.bgColor=r.bgColor,this.debug=r.webglDebug,this.batchDebugInfo=[],r.enableWrapping=!0,r.createTextureCanvas=uw,this.atlasManager=new uO(t,r),this.batchManager=new uz(r),this.simpleShapeOptions=new Map,this.program=this._createShaderProgram(uV.SCREEN),this.pickingProgram=this._createShaderProgram(uV.PICKING),this.vao=this._createVAO()},[{key:"addAtlasCollection",value:function(e,t){this.atlasManager.addAtlasCollection(e,t)}},{key:"addTextureAtlasRenderType",value:function(e,t){this.atlasManager.addRenderType(e,t)}},{key:"addSimpleShapeRenderType",value:function(e,t){this.simpleShapeOptions.set(e,t)}},{key:"invalidate",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.type,r=this.atlasManager;return n?r.invalidate(e,{filterType:function(e){return e===n},forceRedraw:!0}):r.invalidate(e)}},{key:"gc",value:function(){this.atlasManager.gc()}},{key:"_createShaderProgram",value:function(e){var t=this.gl,n="#version 300 es\n      precision highp float;\n\n      uniform mat3 uPanZoomMatrix;\n      uniform int  uAtlasSize;\n      \n      // instanced\n      in vec2 aPosition; // a vertex from the unit square\n      \n      in mat3 aTransform; // used to transform verticies, eg into a bounding box\n      in int aVertType; // the type of thing we are rendering\n\n      // the z-index that is output when using picking mode\n      in vec4 aIndex;\n      \n      // For textures\n      in int aAtlasId; // which shader unit/atlas to use\n      in vec4 aTex; // x/y/w/h of texture in atlas\n\n      // for edges\n      in vec4 aPointAPointB;\n      in vec4 aPointCPointD;\n      in vec2 aLineWidth; // also used for node border width\n\n      // simple shapes\n      in vec4 aCornerRadius; // for round-rectangle [top-right, bottom-right, top-left, bottom-left]\n      in vec4 aColor; // also used for edges\n      in vec4 aBorderColor; // aLineWidth is used for border width\n\n      // output values passed to the fragment shader\n      out vec2 vTexCoord;\n      out vec4 vColor;\n      out vec2 vPosition;\n      // flat values are not interpolated\n      flat out int vAtlasId; \n      flat out int vVertType;\n      flat out vec2 vTopRight;\n      flat out vec2 vBotLeft;\n      flat out vec4 vCornerRadius;\n      flat out vec4 vBorderColor;\n      flat out vec2 vBorderWidth;\n      flat out vec4 vIndex;\n      \n      void main(void) {\n        int vid = gl_VertexID;\n        vec2 position = aPosition; // TODO make this a vec3, simplifies some code below\n\n        if(aVertType == ".concat(0,") {\n          float texX = aTex.x; // texture coordinates\n          float texY = aTex.y;\n          float texW = aTex.z;\n          float texH = aTex.w;\n\n          if(vid == 1 || vid == 2 || vid == 4) {\n            texX += texW;\n          }\n          if(vid == 2 || vid == 4 || vid == 5) {\n            texY += texH;\n          }\n\n          float d = float(uAtlasSize);\n          vTexCoord = vec2(texX / d, texY / d); // tex coords must be between 0 and 1\n\n          gl_Position = vec4(uPanZoomMatrix * aTransform * vec3(position, 1.0), 1.0);\n        }\n        else if(aVertType == ").concat(4," || aVertType == ").concat(7," \n             || aVertType == ").concat(5," || aVertType == ").concat(6,") { // simple shapes\n\n          // the bounding box is needed by the fragment shader\n          vBotLeft  = (aTransform * vec3(0, 0, 1)).xy; // flat\n          vTopRight = (aTransform * vec3(1, 1, 1)).xy; // flat\n          vPosition = (aTransform * vec3(position, 1)).xy; // will be interpolated\n\n          // calculations are done in the fragment shader, just pass these along\n          vColor = aColor;\n          vCornerRadius = aCornerRadius;\n          vBorderColor = aBorderColor;\n          vBorderWidth = aLineWidth;\n\n          gl_Position = vec4(uPanZoomMatrix * aTransform * vec3(position, 1.0), 1.0);\n        }\n        else if(aVertType == ").concat(1,") {\n          vec2 source = aPointAPointB.xy;\n          vec2 target = aPointAPointB.zw;\n\n          // adjust the geometry so that the line is centered on the edge\n          position.y = position.y - 0.5;\n\n          // stretch the unit square into a long skinny rectangle\n          vec2 xBasis = target - source;\n          vec2 yBasis = normalize(vec2(-xBasis.y, xBasis.x));\n          vec2 point = source + xBasis * position.x + yBasis * aLineWidth[0] * position.y;\n\n          gl_Position = vec4(uPanZoomMatrix * vec3(point, 1.0), 1.0);\n          vColor = aColor;\n        } \n        else if(aVertType == ").concat(2,") {\n          vec2 pointA = aPointAPointB.xy;\n          vec2 pointB = aPointAPointB.zw;\n          vec2 pointC = aPointCPointD.xy;\n          vec2 pointD = aPointCPointD.zw;\n\n          // adjust the geometry so that the line is centered on the edge\n          position.y = position.y - 0.5;\n\n          vec2 p0, p1, p2, pos;\n          if(position.x == 0.0) { // The left side of the unit square\n            p0 = pointA;\n            p1 = pointB;\n            p2 = pointC;\n            pos = position;\n          } else { // The right side of the unit square, use same approach but flip the geometry upside down\n            p0 = pointD;\n            p1 = pointC;\n            p2 = pointB;\n            pos = vec2(0.0, -position.y);\n          }\n\n          vec2 p01 = p1 - p0;\n          vec2 p12 = p2 - p1;\n          vec2 p21 = p1 - p2;\n\n          // Find the normal vector.\n          vec2 tangent = normalize(normalize(p12) + normalize(p01));\n          vec2 normal = vec2(-tangent.y, tangent.x);\n\n          // Find the vector perpendicular to p0 -> p1.\n          vec2 p01Norm = normalize(vec2(-p01.y, p01.x));\n\n          // Determine the bend direction.\n          float sigma = sign(dot(p01 + p21, normal));\n          float width = aLineWidth[0];\n\n          if(sign(pos.y) == -sigma) {\n            // This is an intersecting vertex. Adjust the position so that there's no overlap.\n            vec2 point = 0.5 * width * normal * -sigma / dot(normal, p01Norm);\n            gl_Position = vec4(uPanZoomMatrix * vec3(p1 + point, 1.0), 1.0);\n          } else {\n            // This is a non-intersecting vertex. Treat it like a mitre join.\n            vec2 point = 0.5 * width * normal * sigma * dot(normal, p01Norm);\n            gl_Position = vec4(uPanZoomMatrix * vec3(p1 + point, 1.0), 1.0);\n          }\n\n          vColor = aColor;\n        } \n        else if(aVertType == ").concat(3," && vid < 3) {\n          // massage the first triangle into an edge arrow\n          if(vid == 0)\n            position = vec2(-0.15, -0.3);\n          if(vid == 1)\n            position = vec2(  0.0,  0.0);\n          if(vid == 2)\n            position = vec2( 0.15, -0.3);\n\n          gl_Position = vec4(uPanZoomMatrix * aTransform * vec3(position, 1.0), 1.0);\n          vColor = aColor;\n        }\n        else {\n          gl_Position = vec4(2.0, 0.0, 0.0, 1.0); // discard vertex by putting it outside webgl clip space\n        }\n\n        vAtlasId = aAtlasId;\n        vVertType = aVertType;\n        vIndex = aIndex;\n      }\n    "),r=this.batchManager.getIndexArray(),i=function(e,t,n){var r=ux(e,e.VERTEX_SHADER,t),i=ux(e,e.FRAGMENT_SHADER,n),a=e.createProgram();if(e.attachShader(a,r),e.attachShader(a,i),e.linkProgram(a),!e.getProgramParameter(a,e.LINK_STATUS))throw Error("Could not initialize shaders");return a}(t,n,"#version 300 es\n      precision highp float;\n\n      // declare texture unit for each texture atlas in the batch\n      ".concat(r.map(function(e){return"uniform sampler2D uTexture".concat(e,";")}).join("\n	"),"\n\n      uniform vec4 uBGColor;\n      uniform float uZoom;\n\n      in vec2 vTexCoord;\n      in vec4 vColor;\n      in vec2 vPosition; // model coordinates\n\n      flat in int vAtlasId;\n      flat in vec4 vIndex;\n      flat in int vVertType;\n      flat in vec2 vTopRight;\n      flat in vec2 vBotLeft;\n      flat in vec4 vCornerRadius;\n      flat in vec4 vBorderColor;\n      flat in vec2 vBorderWidth;\n\n      out vec4 outColor;\n\n      ").concat("\n  float circleSD(vec2 p, float r) {\n    return distance(vec2(0), p) - r; // signed distance\n  }\n","\n      ").concat("\n  float rectangleSD(vec2 p, vec2 b) {\n    vec2 d = abs(p)-b;\n    return distance(vec2(0),max(d,0.0)) + min(max(d.x,d.y),0.0);\n  }\n","\n      ").concat("\n  float roundRectangleSD(vec2 p, vec2 b, vec4 cr) {\n    cr.xy = (p.x > 0.0) ? cr.xy : cr.zw;\n    cr.x  = (p.y > 0.0) ? cr.x  : cr.y;\n    vec2 q = abs(p) - b + cr.x;\n    return min(max(q.x, q.y), 0.0) + distance(vec2(0), max(q, 0.0)) - cr.x;\n  }\n","\n      ").concat("\n  float ellipseSD(vec2 p, vec2 ab) {\n    p = abs( p ); // symmetry\n\n    // find root with Newton solver\n    vec2 q = ab*(p-ab);\n    float w = (q.x<q.y)? 1.570796327 : 0.0;\n    for( int i=0; i<5; i++ ) {\n      vec2 cs = vec2(cos(w),sin(w));\n      vec2 u = ab*vec2( cs.x,cs.y);\n      vec2 v = ab*vec2(-cs.y,cs.x);\n      w = w + dot(p-u,v)/(dot(p-u,u)+dot(v,v));\n    }\n    \n    // compute final point and distance\n    float d = length(p-ab*vec2(cos(w),sin(w)));\n    \n    // return signed distance\n    return (dot(p/ab,p/ab)>1.0) ? d : -d;\n  }\n","\n\n      vec4 blend(vec4 top, vec4 bot) { // blend colors with premultiplied alpha\n        return vec4( \n          top.rgb + (bot.rgb * (1.0 - top.a)),\n          top.a   + (bot.a   * (1.0 - top.a)) \n        );\n      }\n\n      vec4 distInterp(vec4 cA, vec4 cB, float d) { // interpolate color using Signed Distance\n        // scale to the zoom level so that borders don't look blurry when zoomed in\n        // note 1.5 is an aribitrary value chosen because it looks good\n        return mix(cA, cB, 1.0 - smoothstep(0.0, 1.5 / uZoom, abs(d))); \n      }\n\n      void main(void) {\n        if(vVertType == ").concat(0,") {\n          // look up the texel from the texture unit\n          ").concat(r.map(function(e){return"if(vAtlasId == ".concat(e,") outColor = texture(uTexture").concat(e,", vTexCoord);")}).join("\n	else "),"\n        } \n        else if(vVertType == ").concat(3,") {\n          // mimics how canvas renderer uses context.globalCompositeOperation = 'destination-out';\n          outColor = blend(vColor, uBGColor);\n          outColor.a = 1.0; // make opaque, masks out line under arrow\n        }\n        else if(vVertType == ").concat(4," && vBorderWidth == vec2(0.0)) { // simple rectangle with no border\n          outColor = vColor; // unit square is already transformed to the rectangle, nothing else needs to be done\n        }\n        else if(vVertType == ").concat(4," || vVertType == ").concat(7," \n          || vVertType == ").concat(5," || vVertType == ").concat(6,") { // use SDF\n\n          float outerBorder = vBorderWidth[0];\n          float innerBorder = vBorderWidth[1];\n          float borderPadding = outerBorder * 2.0;\n          float w = vTopRight.x - vBotLeft.x - borderPadding;\n          float h = vTopRight.y - vBotLeft.y - borderPadding;\n          vec2 b = vec2(w/2.0, h/2.0); // half width, half height\n          vec2 p = vPosition - vec2(vTopRight.x - b[0] - outerBorder, vTopRight.y - b[1] - outerBorder); // translate to center\n\n          float d; // signed distance\n          if(vVertType == ").concat(4,") {\n            d = rectangleSD(p, b);\n          } else if(vVertType == ").concat(7," && w == h) {\n            d = circleSD(p, b.x); // faster than ellipse\n          } else if(vVertType == ").concat(7,") {\n            d = ellipseSD(p, b);\n          } else {\n            d = roundRectangleSD(p, b, vCornerRadius.wzyx);\n          }\n\n          // use the distance to interpolate a color to smooth the edges of the shape, doesn't need multisampling\n          // we must smooth colors inwards, because we can't change pixels outside the shape's bounding box\n          if(d > 0.0) {\n            if(d > outerBorder) {\n              discard;\n            } else {\n              outColor = distInterp(vBorderColor, vec4(0), d - outerBorder);\n            }\n          } else {\n            if(d > innerBorder) {\n              vec4 outerColor = outerBorder == 0.0 ? vec4(0) : vBorderColor;\n              vec4 innerBorderColor = blend(vBorderColor, vColor);\n              outColor = distInterp(innerBorderColor, outerColor, d);\n            } \n            else {\n              vec4 outerColor;\n              if(innerBorder == 0.0 && outerBorder == 0.0) {\n                outerColor = vec4(0);\n              } else if(innerBorder == 0.0) {\n                outerColor = vBorderColor;\n              } else {\n                outerColor = blend(vBorderColor, vColor);\n              }\n              outColor = distInterp(vColor, outerColor, d - innerBorder);\n            }\n          }\n        }\n        else {\n          outColor = vColor;\n        }\n\n        ").concat(e.picking?"if(outColor.a == 0.0) discard;\n             else outColor = vIndex;":"","\n      }\n    "));i.aPosition=t.getAttribLocation(i,"aPosition"),i.aIndex=t.getAttribLocation(i,"aIndex"),i.aVertType=t.getAttribLocation(i,"aVertType"),i.aTransform=t.getAttribLocation(i,"aTransform"),i.aAtlasId=t.getAttribLocation(i,"aAtlasId"),i.aTex=t.getAttribLocation(i,"aTex"),i.aPointAPointB=t.getAttribLocation(i,"aPointAPointB"),i.aPointCPointD=t.getAttribLocation(i,"aPointCPointD"),i.aLineWidth=t.getAttribLocation(i,"aLineWidth"),i.aColor=t.getAttribLocation(i,"aColor"),i.aCornerRadius=t.getAttribLocation(i,"aCornerRadius"),i.aBorderColor=t.getAttribLocation(i,"aBorderColor"),i.uPanZoomMatrix=t.getUniformLocation(i,"uPanZoomMatrix"),i.uAtlasSize=t.getUniformLocation(i,"uAtlasSize"),i.uBGColor=t.getUniformLocation(i,"uBGColor"),i.uZoom=t.getUniformLocation(i,"uZoom"),i.uTextures=[];for(var a=0;a<this.batchManager.getMaxAtlasesPerBatch();a++)i.uTextures.push(t.getUniformLocation(i,"uTexture".concat(a)));return i}},{key:"_createVAO",value:function(){var e,t,n,r,i,a,o=[0,0,1,0,1,1,0,0,1,1,0,1];this.vertexCount=o.length/2;var s=this.maxInstances,u=this.gl,c=this.program,d=u.createVertexArray();return u.bindVertexArray(d),e=c.aPosition,n=(t=l(uP(u,"vec2"),2))[0],i=uS(u,r=t[1],o),a=u.createBuffer(),u.bindBuffer(u.ARRAY_BUFFER,a),u.bufferData(u.ARRAY_BUFFER,i,u.STATIC_DRAW),r===u.FLOAT?u.vertexAttribPointer(e,n,r,!1,0,0):r===u.INT&&u.vertexAttribIPointer(e,n,r,0,0),u.enableVertexAttribArray(e),u.bindBuffer(u.ARRAY_BUFFER,null),this.transformBuffer=function(e,t,n){for(var r=new Float32Array(9*t),i=Array(t),a=0;a<t;a++){var o=9*a*4;i[a]=new Float32Array(r.buffer,o,9)}var s=e.createBuffer();e.bindBuffer(e.ARRAY_BUFFER,s),e.bufferData(e.ARRAY_BUFFER,r.byteLength,e.DYNAMIC_DRAW);for(var l=0;l<3;l++){var u=n+l;e.enableVertexAttribArray(u),e.vertexAttribPointer(u,3,e.FLOAT,!1,36,12*l),e.vertexAttribDivisor(u,1)}return e.bindBuffer(e.ARRAY_BUFFER,null),s.getMatrixView=function(e){return i[e]},s.setData=function(e,t){i[t].set(e,0)},s.bufferSubData=function(){e.bindBuffer(e.ARRAY_BUFFER,s),e.bufferSubData(e.ARRAY_BUFFER,0,r)},s}(u,s,c.aTransform),this.indexBuffer=uB(u,s,"vec4",c.aIndex),this.vertTypeBuffer=uB(u,s,"int",c.aVertType),this.atlasIdBuffer=uB(u,s,"int",c.aAtlasId),this.texBuffer=uB(u,s,"vec4",c.aTex),this.pointAPointBBuffer=uB(u,s,"vec4",c.aPointAPointB),this.pointCPointDBuffer=uB(u,s,"vec4",c.aPointCPointD),this.lineWidthBuffer=uB(u,s,"vec2",c.aLineWidth),this.colorBuffer=uB(u,s,"vec4",c.aColor),this.cornerRadiusBuffer=uB(u,s,"vec4",c.aCornerRadius),this.borderColorBuffer=uB(u,s,"vec4",c.aBorderColor),u.bindVertexArray(null),d}},{key:"buffers",get:function(){var e=this;return this._buffers||(this._buffers=Object.keys(this).filter(function(e){return U(e,"Buffer")}).map(function(t){return e[t]})),this._buffers}},{key:"startFrame",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:uV.SCREEN;this.panZoomMatrix=e,this.renderTarget=t,this.batchDebugInfo=[],this.wrappedCount=0,this.simpleCount=0,this.startBatch()}},{key:"startBatch",value:function(){this.instanceCount=0,this.batchManager.startBatch()}},{key:"endFrame",value:function(){this.endBatch()}},{key:"_isVisible",value:function(e,t){return!!e.visible()&&(!t||!t.isVisible||t.isVisible(e))}},{key:"drawTexture",value:function(e,t,n){var r=this.atlasManager,i=this.batchManager,a=r.getRenderTypeOpts(n);if(this._isVisible(e,a)&&(!e.isEdge()||this._isValidEdge(e))){if(this.renderTarget.picking&&a.getTexPickingMode){var s=a.getTexPickingMode(e);if(s===uF.IGNORE)return;if(s==uF.USE_BB)return void this.drawPickingRectangle(e,t,n)}var u,c=o(r.getAtlasInfo(e,n));try{for(c.s();!(u=c.n()).done;){var d=u.value,h=d.atlas,f=d.tex1,p=d.tex2;i.canAddToCurrentBatch(h)||this.endBatch();for(var g=i.getAtlasIndexForBatch(h),v=0,y=[[f,!0],[p,!1]];v<y.length;v++){var b=l(y[v],2),x=b[0],w=b[1];if(0!=x.w){var E=this.instanceCount;this.vertTypeBuffer.getView(E)[0]=0;var T=this.indexBuffer.getView(E);uk(t,T),this.atlasIdBuffer.getView(E)[0]=g;var C=this.texBuffer.getView(E);C[0]=x.x,C[1]=x.y,C[2]=x.w,C[3]=x.h;var k=this.transformBuffer.getMatrixView(E);this.setTransformMatrix(e,k,a,d,w),this.instanceCount++,!w&&this.wrappedCount++,this.instanceCount>=this.maxInstances&&this.endBatch()}}}}catch(e){c.e(e)}finally{c.f()}}}},{key:"setTransformMatrix",value:function(e,t,n,r){var i=!(arguments.length>4)||void 0===arguments[4]||arguments[4],a=0;if(n.shapeProps&&n.shapeProps.padding&&(a=e.pstyle(n.shapeProps.padding).pfValue),r){var o=r.bb,s=r.tex1,l=r.tex2,u=s.w/(s.w+l.w);i||(u=1-u);var c=this._getAdjustedBB(o,a,i,u);this._applyTransformMatrix(t,c,n,e)}else{var d=n.getBoundingBox(e),h=this._getAdjustedBB(d,a,!0,1);this._applyTransformMatrix(t,h,n,e)}}},{key:"_applyTransformMatrix",value:function(e,t,n,r){uA(e);var i,a,o=n.getRotation?n.getRotation(r):0;if(0!==o){var s=n.getRotationPoint(r);uM(e,e,[s.x,s.y]),uR(e,e,o);var l=n.getRotationOffset(r);i=l.x+(t.xOffset||0),a=l.y+(t.yOffset||0)}else i=t.x1,a=t.y1;uM(e,e,[i,a]),uI(e,e,[t.w,t.h])}},{key:"_getAdjustedBB",value:function(e,t,n,r){var i=e.x1,a=e.y1,o=e.w,s=e.h,l=e.yOffset;t&&(i-=t,a-=t,o+=2*t,s+=2*t);var u=0,c=o*r;return n&&r<1?o=c:!n&&r<1&&(i+=u=o-c,o=c),{x1:i,y1:a,w:o,h:s,xOffset:u,yOffset:l}}},{key:"drawPickingRectangle",value:function(e,t,n){var r=this.atlasManager.getRenderTypeOpts(n),i=this.instanceCount;this.vertTypeBuffer.getView(i)[0]=4,uk(t,this.indexBuffer.getView(i)),uC([0,0,0],1,this.colorBuffer.getView(i));var a=this.transformBuffer.getMatrixView(i);this.setTransformMatrix(e,a,r),this.simpleCount++,this.instanceCount++,this.instanceCount>=this.maxInstances&&this.endBatch()}},{key:"drawNode",value:function(e,t,n){var r=this.simpleShapeOptions.get(n);if(this._isVisible(e,r)){var i=r.shapeProps,a=this._getVertTypeForShape(e,i.shape);if(void 0===a||r.isSimple&&!r.isSimple(e))return void this.drawTexture(e,t,n);var o=this.instanceCount;if(this.vertTypeBuffer.getView(o)[0]=a,5===a||6===a){var s=r.getBoundingBox(e),l=this._getCornerRadius(e,i.radius,s),u=this.cornerRadiusBuffer.getView(o);u[0]=l,u[1]=l,u[2]=l,u[3]=l,6===a&&(u[0]=0,u[2]=0)}uk(t,this.indexBuffer.getView(o)),uC(e.pstyle(i.color).value,e.pstyle(i.opacity).value,this.colorBuffer.getView(o));var c=this.lineWidthBuffer.getView(o);if(c[0]=0,c[1]=0,i.border){var d=e.pstyle("border-width").value;if(d>0){uC(e.pstyle("border-color").value,e.pstyle("border-opacity").value,this.borderColorBuffer.getView(o));var h=e.pstyle("border-position").value;if("inside"===h)c[0]=0,c[1]=-d;else if("outside"===h)c[0]=d,c[1]=0;else{var f=d/2;c[0]=f,c[1]=-f}}}var p=this.transformBuffer.getMatrixView(o);this.setTransformMatrix(e,p,r),this.simpleCount++,this.instanceCount++,this.instanceCount>=this.maxInstances&&this.endBatch()}}},{key:"_getVertTypeForShape",value:function(e,t){switch(e.pstyle(t).value){case"rectangle":return 4;case"ellipse":return 7;case"roundrectangle":case"round-rectangle":return 5;case"bottom-round-rectangle":return 6;default:return}}},{key:"_getCornerRadius",value:function(e,t,n){var r=n.w,i=n.h;return"auto"===e.pstyle(t).value?t1(r,i):Math.min(e.pstyle(t).pfValue,i/2,r/2)}},{key:"drawEdgeArrow",value:function(e,t,n){if(e.visible()){var r,i,a,o=e._private.rscratch;if(("source"===n?(r=o.arrowStartX,i=o.arrowStartY,a=o.srcArrowAngle):(r=o.arrowEndX,i=o.arrowEndY,a=o.tgtArrowAngle),!(isNaN(r)||null==r||isNaN(i)||null==i||isNaN(a))&&null!=a)&&"none"!==e.pstyle(n+"-arrow-shape").value){var s=e.pstyle(n+"-arrow-color").value,l=e.pstyle("opacity").value,u=e.pstyle("line-opacity").value,c=e.pstyle("width").pfValue,d=e.pstyle("arrow-scale").value,h=this.r.getArrowWidth(c,d),f=this.instanceCount,p=this.transformBuffer.getMatrixView(f);uA(p),uM(p,p,[r,i]),uI(p,p,[h,h]),uR(p,p,a),this.vertTypeBuffer.getView(f)[0]=3,uk(t,this.indexBuffer.getView(f)),uC(s,l*u,this.colorBuffer.getView(f)),this.instanceCount++,this.instanceCount>=this.maxInstances&&this.endBatch()}}}},{key:"drawEdgeLine",value:function(e,t){if(e.visible()){var n=this._getEdgePoints(e);if(n){var r=e.pstyle("opacity").value,i=e.pstyle("line-opacity").value,a=e.pstyle("width").pfValue,o=e.pstyle("line-color").value,s=r*i;if(n.length/2+this.instanceCount>this.maxInstances&&this.endBatch(),4==n.length){var l=this.instanceCount;this.vertTypeBuffer.getView(l)[0]=1,uk(t,this.indexBuffer.getView(l)),uC(o,s,this.colorBuffer.getView(l)),this.lineWidthBuffer.getView(l)[0]=a;var u=this.pointAPointBBuffer.getView(l);u[0]=n[0],u[1]=n[1],u[2]=n[2],u[3]=n[3],this.instanceCount++,this.instanceCount>=this.maxInstances&&this.endBatch()}else for(var c=0;c<n.length-2;c+=2){var d=this.instanceCount;this.vertTypeBuffer.getView(d)[0]=2,uk(t,this.indexBuffer.getView(d)),uC(o,s,this.colorBuffer.getView(d)),this.lineWidthBuffer.getView(d)[0]=a;var h=n[c-2],f=n[c-1],p=n[c],g=n[c+1],v=n[c+2],y=n[c+3],b=n[c+4],x=n[c+5];0==c&&(h=2*p-v+.001,f=2*g-y+.001),c==n.length-4&&(b=2*v-p+.001,x=2*y-g+.001);var w=this.pointAPointBBuffer.getView(d);w[0]=h,w[1]=f,w[2]=p,w[3]=g;var E=this.pointCPointDBuffer.getView(d);E[0]=v,E[1]=y,E[2]=b,E[3]=x,this.instanceCount++,this.instanceCount>=this.maxInstances&&this.endBatch()}}}}},{key:"_isValidEdge",value:function(e){var t=e._private.rscratch;return!(t.badLine||null==t.allpts||isNaN(t.allpts[0]))}},{key:"_getEdgePoints",value:function(e){var t=e._private.rscratch;if(this._isValidEdge(e)){var n=t.allpts;if(4==n.length)return n;var r=this._getNumSegments(e);return this._getCurveSegmentPoints(n,r)}}},{key:"_getNumSegments",value:function(e){return Math.min(15,this.maxInstances)}},{key:"_getCurveSegmentPoints",value:function(e,t){if(4==e.length)return e;for(var n=Array((t+1)*2),r=0;r<=t;r++)if(0==r)n[0]=e[0],n[1]=e[1];else if(r==t)n[2*r]=e[e.length-2],n[2*r+1]=e[e.length-1];else{var i=r/t;this._setCurvePoint(e,i,n,2*r)}return n}},{key:"_setCurvePoint",value:function(e,t,n,r){if(e.length<=2)n[r]=e[0],n[r+1]=e[1];else{for(var i=Array(e.length-2),a=0;a<i.length;a+=2){var o=(1-t)*e[a]+t*e[a+2],s=(1-t)*e[a+1]+t*e[a+3];i[a]=o,i[a+1]=s}return this._setCurvePoint(i,t,n,r)}}},{key:"endBatch",value:function(){var e=this.gl,t=this.vao,n=this.vertexCount,r=this.instanceCount;if(0!==r){var i,a,s=this.renderTarget.picking?this.pickingProgram:this.program;e.useProgram(s),e.bindVertexArray(t);var l,u=o(this.buffers);try{for(u.s();!(l=u.n()).done;)l.value.bufferSubData(r)}catch(e){u.e(e)}finally{u.f()}for(var c=this.batchManager.getAtlases(),d=0;d<c.length;d++)c[d].bufferIfNeeded(e);for(var h=0;h<c.length;h++)e.activeTexture(e.TEXTURE0+h),e.bindTexture(e.TEXTURE_2D,c[h].texture),e.uniform1i(s.uTextures[h],h);e.uniform1f(s.uZoom,(a=(i=this.r).pixelRatio,i.cy.zoom()*a)),e.uniformMatrix3fv(s.uPanZoomMatrix,!1,this.panZoomMatrix),e.uniform1i(s.uAtlasSize,this.batchManager.getAtlasSize());var f=uC(this.bgColor,1);e.uniform4fv(s.uBGColor,f),e.drawArraysInstanced(e.TRIANGLES,0,n,r),e.bindVertexArray(null),e.bindTexture(e.TEXTURE_2D,null),this.debug&&this.batchDebugInfo.push({count:r,atlasCount:c.length}),this.startBatch()}}},{key:"getDebugInfo",value:function(){var e=this.atlasManager.getDebugInfo(),t=e.reduce(function(e,t){return e+t.atlasCount},0),n=this.batchDebugInfo,r=n.reduce(function(e,t){return e+t.count},0);return{atlasInfo:e,totalAtlases:t,wrappedCount:this.wrappedCount,simpleCount:this.simpleCount,batchCount:n.length,batchInfo:n,totalInstances:r}}}]),uj={};function uY(e,t){return eG(e._private.rscratch,"labelWrapCachedLines",t)||[]}uj.initWebgl=function(e,t){var n,r,i,a,s,u,c,d,h=this,f=h.data.contexts[h.WEBGL];e.bgColor=er((n=h.cy.container())&&n.style&&n.style.backgroundColor||"white"),e.webglTexSize=Math.min(e.webglTexSize,f.getParameter(f.MAX_TEXTURE_SIZE)),e.webglTexRows=Math.min(e.webglTexRows,54),e.webglTexRowsNodes=Math.min(e.webglTexRowsNodes,54),e.webglBatchSize=Math.min(e.webglBatchSize,16384),e.webglTexPerBatch=Math.min(e.webglTexPerBatch,f.getParameter(f.MAX_TEXTURE_IMAGE_UNITS)),h.webglDebug=e.webglDebug,h.webglDebugShowAtlases=e.webglDebugShowAtlases,c=f.createFramebuffer(),f.bindFramebuffer(f.FRAMEBUFFER,c),d=f.createTexture(),f.bindTexture(f.TEXTURE_2D,d),f.texParameteri(f.TEXTURE_2D,f.TEXTURE_MIN_FILTER,f.LINEAR),f.texParameteri(f.TEXTURE_2D,f.TEXTURE_WRAP_S,f.CLAMP_TO_EDGE),f.texParameteri(f.TEXTURE_2D,f.TEXTURE_WRAP_T,f.CLAMP_TO_EDGE),f.framebufferTexture2D(f.FRAMEBUFFER,f.COLOR_ATTACHMENT0,f.TEXTURE_2D,d,0),f.bindFramebuffer(f.FRAMEBUFFER,null),c.setFramebufferAttachmentSizes=function(e,t){f.bindTexture(f.TEXTURE_2D,d),f.texImage2D(f.TEXTURE_2D,0,f.RGBA,e,t,0,f.RGBA,f.UNSIGNED_BYTE,null)},h.pickingFrameBuffer=c,h.pickingFrameBuffer.needsDraw=!0,h.drawing=new uX(h,f,e);var p=function(e){return function(t){return h.getTextAngle(t,e)}},g=function(e){return function(t){var n=t.pstyle(e);return n&&n.value}},v=function(e){return function(t){return t.pstyle("".concat(e,"-opacity")).value>0}},y=function(e){return"yes"===e.pstyle("text-events").strValue?uF.USE_BB:uF.IGNORE},b=function(e){var t=e.position(),n=t.x,r=t.y,i=e.outerWidth(),a=e.outerHeight();return{w:i,h:a,x1:n-i/2,y1:r-a/2}};h.drawing.addAtlasCollection("node",{texRows:e.webglTexRowsNodes}),h.drawing.addAtlasCollection("label",{texRows:e.webglTexRows}),h.drawing.addTextureAtlasRenderType("node-body",{collection:"node",getKey:t.getStyleKey,getBoundingBox:t.getElementBox,drawElement:t.drawElement}),h.drawing.addSimpleShapeRenderType("node-body",{getBoundingBox:b,isSimple:uT,shapeProps:{shape:"shape",color:"background-color",opacity:"background-opacity",radius:"corner-radius",border:!0}}),h.drawing.addSimpleShapeRenderType("node-overlay",{getBoundingBox:b,isVisible:v("overlay"),shapeProps:{shape:"overlay-shape",color:"overlay-color",opacity:"overlay-opacity",padding:"overlay-padding",radius:"overlay-corner-radius"}}),h.drawing.addSimpleShapeRenderType("node-underlay",{getBoundingBox:b,isVisible:v("underlay"),shapeProps:{shape:"underlay-shape",color:"underlay-color",opacity:"underlay-opacity",padding:"underlay-padding",radius:"underlay-corner-radius"}}),h.drawing.addTextureAtlasRenderType("label",{collection:"label",getTexPickingMode:y,getKey:uq(t.getLabelKey,null),getBoundingBox:uW(t.getLabelBox,null),drawClipped:!0,drawElement:t.drawLabel,getRotation:p(null),getRotationPoint:t.getLabelRotationPoint,getRotationOffset:t.getLabelRotationOffset,isVisible:g("label")}),h.drawing.addTextureAtlasRenderType("edge-source-label",{collection:"label",getTexPickingMode:y,getKey:uq(t.getSourceLabelKey,"source"),getBoundingBox:uW(t.getSourceLabelBox,"source"),drawClipped:!0,drawElement:t.drawSourceLabel,getRotation:p("source"),getRotationPoint:t.getSourceLabelRotationPoint,getRotationOffset:t.getSourceLabelRotationOffset,isVisible:g("source-label")}),h.drawing.addTextureAtlasRenderType("edge-target-label",{collection:"label",getTexPickingMode:y,getKey:uq(t.getTargetLabelKey,"target"),getBoundingBox:uW(t.getTargetLabelBox,"target"),drawClipped:!0,drawElement:t.drawTargetLabel,getRotation:p("target"),getRotationPoint:t.getTargetLabelRotationPoint,getRotationOffset:t.getTargetLabelRotationOffset,isVisible:g("target-label")});var x=ep(function(){console.log("garbage collect flag set"),h.data.gc=!0},1e4);h.onUpdateEleCalcs(function(e,t){var n=!1;t&&t.length>0&&(n|=h.drawing.invalidate(t)),n&&x()}),i=(r=h).render,r.render=function(e){e=e||{};var t,n,a,o,s=r.cy;r.webgl&&(s.zoom()>7.99?((n=(t=r).data.contexts[t.WEBGL]).clear(n.COLOR_BUFFER_BIT|n.DEPTH_BUFFER_BIT),i.call(r,e)):(a=r,(o=function(e){e.save(),e.setTransform(1,0,0,1,0,0),e.clearRect(0,0,a.canvasWidth,a.canvasHeight),e.restore()})(a.data.contexts[a.NODE]),o(a.data.contexts[a.DRAG]),uH(r,e,uV.SCREEN)))},a=r.matchCanvasSize,r.matchCanvasSize=function(e){a.call(r,e),r.pickingFrameBuffer.setFramebufferAttachmentSizes(r.canvasWidth,r.canvasHeight),r.pickingFrameBuffer.needsDraw=!0},r.findNearestElements=function(e,t,n,i){return function(e,t,n){var r,i,a,s=function(e,t,n,r,i){var a=uE(e),o=a.pan,s=a.zoom,u=l((v=t*s+o.x,y=n*s+o.y,[v,y=Math.round(e.canvasHeight-y)]),2),c=u[0],d=u[1],h=e.data.contexts[e.WEBGL];h.bindFramebuffer(h.FRAMEBUFFER,e.pickingFrameBuffer),e.pickingFrameBuffer.needsDraw&&(h.viewport(0,0,h.canvas.width,h.canvas.height),uH(e,null,uV.PICKING),e.pickingFrameBuffer.needsDraw=!1);var f=new Uint8Array(144);h.readPixels(c-3,d-3,6,6,h.RGBA,h.UNSIGNED_BYTE,f),h.bindFramebuffer(h.FRAMEBUFFER,null);for(var p=new Set,g=0;g<36;g++){var v,y,b,x=(b=f.slice(4*g,4*g+4))[0]+(b[1]<<8)+(b[2]<<16)+(b[3]<<24)-1;x>=0&&p.add(x)}return p}(e,t,n),u=e.getCachedZSortedEles(),c=o(s);try{for(c.s();!(a=c.n()).done;){var d=u[a.value];if(!r&&d.isNode()&&(r=d),!i&&d.isEdge()&&(i=d),r&&i)break}}catch(e){c.e(e)}finally{c.f()}return[r,i].filter(Boolean)}(r,e,t)},s=r.invalidateCachedZSortedEles,r.invalidateCachedZSortedEles=function(){s.call(r),r.pickingFrameBuffer.needsDraw=!0},u=r.notify,r.notify=function(e,t){u.call(r,e,t),"viewport"===e||"bounds"===e?r.pickingFrameBuffer.needsDraw=!0:"background"===e&&r.drawing.invalidate(t,{type:"node-body"})}};var uq=function(e,t){return function(n){var r=e(n),i=uY(n,t);return i.length>1?i.map(function(e,t){return"".concat(r,"_").concat(t)}):r}},uW=function(e,t){return function(n,r){var i=e(n);if("string"==typeof r){var a=r.indexOf("_");if(a>0){var o=Number(r.substring(a+1)),s=uY(n,t),l=i.h/s.length,u=l*o,c=i.y1+u;return{x1:i.x1,w:i.w,y1:c,h:l,yOffset:u}}}return i}};function uU(e,t){var n=e.canvasWidth,r=e.canvasHeight,i=uE(e),a=i.pan,o=i.zoom;t.setTransform(1,0,0,1,0,0),t.clearRect(0,0,n,r),t.translate(a.x,a.y),t.scale(o,o)}function uG(e,t,n){var r=e.drawing;t+=1,n.isNode()?(r.drawNode(n,t,"node-underlay"),r.drawNode(n,t,"node-body"),r.drawTexture(n,t,"label"),r.drawNode(n,t,"node-overlay")):(r.drawEdgeLine(n,t),r.drawEdgeArrow(n,t,"source"),r.drawEdgeArrow(n,t,"target"),r.drawTexture(n,t,"label"),r.drawTexture(n,t,"edge-source-label"),r.drawTexture(n,t,"edge-target-label"))}function uH(e,t,n){e.webglDebug&&(G=performance.now());var r=e.drawing,i=0;if(n.screen&&e.data.canvasNeedsRedraw[e.SELECT_BOX]&&e.drawSelectionRectangle(t,function(t){return uU(e,t)}),e.data.canvasNeedsRedraw[e.NODE]||n.picking){var a,s,l,u,c,d,h,f,p,g,v,y,b,x,w,E,T,C,k,P,S,B,D,_,A,M,R,I,N,L,O,z=e.data.contexts[e.WEBGL];n.screen?(z.clearColor(0,0,0,0),z.enable(z.BLEND),z.blendFunc(z.ONE,z.ONE_MINUS_SRC_ALPHA)):z.disable(z.BLEND),z.clear(z.COLOR_BUFFER_BIT|z.DEPTH_BUFFER_BIT),z.viewport(0,0,z.canvas.width,z.canvas.height);var V=(a=e.canvasWidth,s=e.canvasHeight,u=(l=uE(e)).pan,c=l.zoom,uM(d=u_(),d,[u.x,u.y]),uI(d,d,[c,c]),f=h=u_(),f[0]=2/a,f[1]=0,f[2]=0,f[3]=0,f[4]=-2/s,f[5]=0,f[6]=-1,f[7]=1,f[8]=1,g=p=u_(),v=h[0],y=h[1],b=h[2],x=h[3],w=h[4],E=h[5],T=h[6],C=h[7],k=h[8],P=d[0],S=d[1],B=d[2],D=d[3],_=d[4],A=d[5],M=d[6],R=d[7],I=d[8],g[0]=P*v+S*x+B*T,g[1]=P*y+S*w+B*C,g[2]=P*b+S*E+B*k,g[3]=D*v+_*x+A*T,g[4]=D*y+_*w+A*C,g[5]=D*b+_*E+A*k,g[6]=M*v+R*x+I*T,g[7]=M*y+R*w+I*C,g[8]=M*b+R*E+I*k,p),F=e.getCachedZSortedEles();if(i=F.length,r.startFrame(V,n),n.screen){for(var X=0;X<F.nondrag.length;X++)uG(e,X,F.nondrag[X]);for(var j=0;j<F.drag.length;j++)uG(e,j,F.drag[j])}else if(n.picking)for(var Y=0;Y<F.length;Y++)uG(e,Y,F[Y]);r.endFrame(),n.screen&&e.webglDebugShowAtlases&&((N=e.data.contexts[e.NODE]).save(),uU(e,N),N.strokeStyle="rgba(0, 0, 0, 0.3)",N.beginPath(),N.moveTo(-1e3,0),N.lineTo(1e3,0),N.stroke(),N.beginPath(),N.moveTo(0,-1e3),N.lineTo(0,1e3),N.stroke(),N.restore(),O=0,(L=function(t,n,r){for(var i=t.atlasManager.getAtlasCollection(n),a=e.data.contexts[e.NODE],o=i.atlases,s=0;s<o.length;s++){var l=o[s].canvas;if(l){var u=l.width,c=l.height,d=u*s,h=l.height*r;a.save(),a.scale(.4,.4),a.drawImage(l,d,h),a.strokeStyle="black",a.rect(d,h,u,c),a.stroke(),a.restore()}}})(e.drawing,"node",O++),L(e.drawing,"label",O++)),e.data.canvasNeedsRedraw[e.NODE]=!1,e.data.canvasNeedsRedraw[e.DRAG]=!1}if(e.webglDebug){var q=Math.ceil(performance.now()-G),W=r.getDebugInfo(),U=["".concat(i," elements"),"".concat(W.totalInstances," instances"),"".concat(W.batchCount," batches"),"".concat(W.totalAtlases," atlases"),"".concat(W.wrappedCount," wrapped textures"),"".concat(W.simpleCount," simple shapes")].join(", ");console.log("WebGL (".concat(n.name,") - frame time ").concat(q,"ms")),console.log("Totals:"),console.log("  ".concat(U)),console.log("Texture Atlases Used:");var G,H,K=o(W.atlasInfo);try{for(K.s();!(H=K.n()).done;){var Z=H.value;console.log("  ".concat(Z.type,": ").concat(Z.keyCount," keys, ").concat(Z.atlasCount," atlases"))}}catch(e){K.e(e)}finally{K.f()}console.log("")}e.data.gc&&(console.log("Garbage Collect!"),e.data.gc=!1,r.gc())}var uK={};uK.drawPolygonPath=function(e,t,n,r,i,a){var o=r/2,s=i/2;e.beginPath&&e.beginPath(),e.moveTo(t+o*a[0],n+s*a[1]);for(var l=1;l<a.length/2;l++)e.lineTo(t+o*a[2*l],n+s*a[2*l+1]);e.closePath()},uK.drawRoundPolygonPath=function(e,t,n,r,i,a,o){o.forEach(function(t){return l_(e,t)}),e.closePath()},uK.drawRoundRectanglePath=function(e,t,n,r,i,a){var o=r/2,s=i/2,l="auto"===a?t1(r,i):Math.min(a,s,o);e.beginPath&&e.beginPath(),e.moveTo(t,n-s),e.arcTo(t+o,n-s,t+o,n,l),e.arcTo(t+o,n+s,t,n+s,l),e.arcTo(t-o,n+s,t-o,n,l),e.arcTo(t-o,n-s,t,n-s,l),e.lineTo(t,n-s),e.closePath()},uK.drawBottomRoundRectanglePath=function(e,t,n,r,i,a){var o=r/2,s=i/2,l="auto"===a?t1(r,i):a;e.beginPath&&e.beginPath(),e.moveTo(t,n-s),e.lineTo(t+o,n-s),e.lineTo(t+o,n),e.arcTo(t+o,n+s,t,n+s,l),e.arcTo(t-o,n+s,t-o,n,l),e.lineTo(t-o,n-s),e.lineTo(t,n-s),e.closePath()},uK.drawCutRectanglePath=function(e,t,n,r,i,a,o){var s=r/2,l=i/2,u="auto"===o?t5():o;e.beginPath&&e.beginPath(),e.moveTo(t-s+u,n-l),e.lineTo(t+s-u,n-l),e.lineTo(t+s,n-l+u),e.lineTo(t+s,n+l-u),e.lineTo(t+s-u,n+l),e.lineTo(t-s+u,n+l),e.lineTo(t-s,n+l-u),e.lineTo(t-s,n-l+u),e.closePath()},uK.drawBarrelPath=function(e,t,n,r,i){var a=r/2,o=i/2,s=t-a,l=t+a,u=n-o,c=n+o,d=t3(r,i),h=d.widthOffset,f=d.heightOffset,p=d.ctrlPtOffsetPct*h;e.beginPath&&e.beginPath(),e.moveTo(s,u+f),e.lineTo(s,c-f),e.quadraticCurveTo(s+p,c,s+h,c),e.lineTo(l-h,c),e.quadraticCurveTo(l-p,c,l,c-f),e.lineTo(l,u+f),e.quadraticCurveTo(l-p,u,l-h,u),e.lineTo(s+h,u),e.quadraticCurveTo(s+p,u,s,u+f),e.closePath()};for(var uZ={},u$={},uQ=Math.PI/40,uJ=0*Math.PI;uJ<2*Math.PI;uJ+=uQ)uZ[uJ]=Math.sin(uJ),u$[uJ]=Math.cos(uJ);uK.drawEllipsePath=function(e,t,n,r,i){if(e.beginPath&&e.beginPath(),e.ellipse)e.ellipse(t,n,r/2,i/2,0,0,2*Math.PI);else for(var a,o,s=r/2,l=i/2,u=0*Math.PI;u<2*Math.PI;u+=uQ)a=t-s*uZ[u]*0+s*u$[u]*1,o=n+l*u$[u]*0+l*uZ[u]*1,0===u?e.moveTo(a,o):e.lineTo(a,o);e.closePath()};var u0={};function u1(e){var t=e.indexOf(",");return e.substr(t+1)}function u2(e,t,n){var r=function(){return t.toDataURL(n,e.quality)};switch(e.output){case"blob-promise":return new re(function(r,i){try{t.toBlob(function(e){null!=e?r(e):i(Error("`canvas.toBlob()` sent a null value in its callback"))},n,e.quality)}catch(e){i(e)}});case"blob":return function(e,t){for(var n=atob(e),r=new ArrayBuffer(n.length),i=new Uint8Array(r),a=0;a<n.length;a++)i[a]=n.charCodeAt(a);return new Blob([r],{type:t})}(u1(r()),n);case"base64":return u1(r());default:return r()}}u0.createBuffer=function(e,t){var n=document.createElement("canvas");return n.width=e,n.height=t,[n,n.getContext("2d")]},u0.bufferCanvasImage=function(e){var t=this.cy,n=t.mutableElements().boundingBox(),r=this.findContainerClientCoords(),i=e.full?Math.ceil(n.w):r[2],a=e.full?Math.ceil(n.h):r[3],o=M(e.maxWidth)||M(e.maxHeight),s=this.getPixelRatio(),l=1;if(void 0!==e.scale)i*=e.scale,a*=e.scale,l=e.scale;else if(o){var u=1/0,c=1/0;M(e.maxWidth)&&(u=l*e.maxWidth/i),M(e.maxHeight)&&(c=l*e.maxHeight/a),i*=l=Math.min(u,c),a*=l}o||(i*=s,a*=s,l*=s);var d=document.createElement("canvas");d.width=i,d.height=a,d.style.width=i+"px",d.style.height=a+"px";var h=d.getContext("2d");if(i>0&&a>0){h.clearRect(0,0,i,a),h.globalCompositeOperation="source-over";var f=this.getCachedZSortedEles();if(e.full)h.translate(-n.x1*l,-n.y1*l),h.scale(l,l),this.drawElements(h,f),h.scale(1/l,1/l),h.translate(n.x1*l,n.y1*l);else{var p=t.pan(),g={x:p.x*l,y:p.y*l};l*=t.zoom(),h.translate(g.x,g.y),h.scale(l,l),this.drawElements(h,f),h.scale(1/l,1/l),h.translate(-g.x,-g.y)}e.bg&&(h.globalCompositeOperation="destination-over",h.fillStyle=e.bg,h.rect(0,0,i,a),h.fill())}return d},u0.png=function(e){return u2(e,this.bufferCanvasImage(e),"image/png")},u0.jpg=function(e){return u2(e,this.bufferCanvasImage(e),"image/jpeg")};var u5={};u5.nodeShapeImpl=function(e,t,n,r,i,a,o,s){switch(e){case"ellipse":return this.drawEllipsePath(t,n,r,i,a);case"polygon":return this.drawPolygonPath(t,n,r,i,a,o);case"round-polygon":return this.drawRoundPolygonPath(t,n,r,i,a,o,s);case"roundrectangle":case"round-rectangle":return this.drawRoundRectanglePath(t,n,r,i,a,s);case"cutrectangle":case"cut-rectangle":return this.drawCutRectanglePath(t,n,r,i,a,o,s);case"bottomroundrectangle":case"bottom-round-rectangle":return this.drawBottomRoundRectanglePath(t,n,r,i,a,s);case"barrel":return this.drawBarrelPath(t,n,r,i,a)}};var u3=u4.prototype;function u4(e){var t=this,n=t.cy.window().document;e.webgl&&(u3.CANVAS_LAYERS=t.CANVAS_LAYERS=4,console.log("webgl rendering enabled")),t.data={canvases:Array(u3.CANVAS_LAYERS),contexts:Array(u3.CANVAS_LAYERS),canvasNeedsRedraw:Array(u3.CANVAS_LAYERS),bufferCanvases:Array(u3.BUFFER_COUNT),bufferContexts:Array(u3.CANVAS_LAYERS)};var r="-webkit-tap-highlight-color",i="rgba(0,0,0,0)";t.data.canvasContainer=n.createElement("div");var a=t.data.canvasContainer.style;t.data.canvasContainer.style[r]=i,a.position="relative",a.zIndex="0",a.overflow="hidden";var o=e.cy.container();o.appendChild(t.data.canvasContainer),o.style[r]=i;var s={"-webkit-user-select":"none","-moz-user-select":"-moz-none","user-select":"none","-webkit-tap-highlight-color":"rgba(0,0,0,0)","outline-style":"none"};E&&E.userAgent.match(/msie|trident|edge/i)&&(s["-ms-touch-action"]="none",s["touch-action"]="none");for(var l=0;l<u3.CANVAS_LAYERS;l++){var u=t.data.canvases[l]=n.createElement("canvas"),c=u3.CANVAS_TYPES[l];t.data.contexts[l]=u.getContext(c),t.data.contexts[l]||eL("Could not create canvas of type "+c),Object.keys(s).forEach(function(e){u.style[e]=s[e]}),u.style.position="absolute",u.setAttribute("data-id","layer"+l),u.style.zIndex=String(u3.CANVAS_LAYERS-l),t.data.canvasContainer.appendChild(u),t.data.canvasNeedsRedraw[l]=!1}t.data.topCanvas=t.data.canvases[0],t.data.canvases[u3.NODE].setAttribute("data-id","layer"+u3.NODE+"-node"),t.data.canvases[u3.SELECT_BOX].setAttribute("data-id","layer"+u3.SELECT_BOX+"-selectbox"),t.data.canvases[u3.DRAG].setAttribute("data-id","layer"+u3.DRAG+"-drag"),t.data.canvases[u3.WEBGL]&&t.data.canvases[u3.WEBGL].setAttribute("data-id","layer"+u3.WEBGL+"-webgl");for(var l=0;l<u3.BUFFER_COUNT;l++)t.data.bufferCanvases[l]=n.createElement("canvas"),t.data.bufferContexts[l]=t.data.bufferCanvases[l].getContext("2d"),t.data.bufferCanvases[l].style.position="absolute",t.data.bufferCanvases[l].setAttribute("data-id","buffer"+l),t.data.bufferCanvases[l].style.zIndex=String(-l-1),t.data.bufferCanvases[l].style.visibility="hidden";t.pathsEnabled=!0;var d=tb(),h=function(e){return{x:-e.w/2,y:-e.h/2}},f=function(e){return e[0]._private.nodeKey},p=function(e){return e[0]._private.labelStyleKey},g=function(e){return e[0]._private.sourceLabelStyleKey},v=function(e){return e[0]._private.targetLabelStyleKey},y=function(e,n,r,i,a){return t.drawElement(e,n,r,!1,!1,a)},b=function(e,n,r,i,a){return t.drawElementText(e,n,r,i,"main",a)},x=function(e,n,r,i,a){return t.drawElementText(e,n,r,i,"source",a)},w=function(e,n,r,i,a){return t.drawElementText(e,n,r,i,"target",a)},T=function(e){return e.boundingBox(),e[0]._private.bodyBounds},C=function(e){return e.boundingBox(),e[0]._private.labelBounds.main||d},k=function(e){return e.boundingBox(),e[0]._private.labelBounds.source||d},P=function(e){return e.boundingBox(),e[0]._private.labelBounds.target||d},S=function(e,t){return t},B=function(e){var t;return{x:((t=T(e)).x1+t.x2)/2,y:(t.y1+t.y2)/2}},D=function(e,t,n){var r=e?e+"-":"";return{x:t.x+n.pstyle(r+"text-margin-x").pfValue,y:t.y+n.pstyle(r+"text-margin-y").pfValue}},_=function(e,t,n){var r=e[0]._private.rscratch;return{x:r[t],y:r[n]}},A=function(e){return D("",_(e,"labelX","labelY"),e)},M=function(e){return D("source",_(e,"sourceLabelX","sourceLabelY"),e)},R=function(e){return D("target",_(e,"targetLabelX","targetLabelY"),e)},I=function(e){return h(T(e))},N=function(e){return h(k(e))},L=function(e){return h(P(e))},O=function(e){var t=C(e),n=h(C(e));if(e.isNode()){switch(e.pstyle("text-halign").value){case"left":n.x=-t.w-(t.leftPad||0);break;case"right":n.x=-(t.rightPad||0)}switch(e.pstyle("text-valign").value){case"top":n.y=-t.h-(t.topPad||0);break;case"bottom":n.y=-(t.botPad||0)}}return n},z=t.data.eleTxrCache=new l3(t,{getKey:f,doesEleInvalidateKey:function(e){var t=e[0]._private;return t.oldBackgroundTimestamp!==t.backgroundTimestamp},drawElement:y,getBoundingBox:T,getRotationPoint:B,getRotationOffset:I,allowEdgeTxrCaching:!1,allowParentTxrCaching:!1}),V=t.data.lblTxrCache=new l3(t,{getKey:p,drawElement:b,getBoundingBox:C,getRotationPoint:A,getRotationOffset:O,isVisible:S}),F=t.data.slbTxrCache=new l3(t,{getKey:g,drawElement:x,getBoundingBox:k,getRotationPoint:M,getRotationOffset:N,isVisible:S}),X=t.data.tlbTxrCache=new l3(t,{getKey:v,drawElement:w,getBoundingBox:P,getRotationPoint:R,getRotationOffset:L,isVisible:S}),j=t.data.lyrTxrCache=new l9(t);t.onUpdateEleCalcs(function(e,t){z.invalidateElements(t),V.invalidateElements(t),F.invalidateElements(t),X.invalidateElements(t),j.invalidateElements(t);for(var n=0;n<t.length;n++){var r=t[n]._private;r.oldBackgroundTimestamp=r.backgroundTimestamp}});var Y=function(e){for(var t=0;t<e.length;t++)j.enqueueElementRefinement(e[t].ele)};z.onDequeue(Y),V.onDequeue(Y),F.onDequeue(Y),X.onDequeue(Y),e.webgl&&t.initWebgl(e,{getStyleKey:f,getLabelKey:p,getSourceLabelKey:g,getTargetLabelKey:v,drawElement:y,drawLabel:b,drawSourceLabel:x,drawTargetLabel:w,getElementBox:T,getLabelBox:C,getSourceLabelBox:k,getTargetLabelBox:P,getElementRotationPoint:B,getElementRotationOffset:I,getLabelRotationPoint:A,getSourceLabelRotationPoint:M,getTargetLabelRotationPoint:R,getLabelRotationOffset:O,getSourceLabelRotationOffset:N,getTargetLabelRotationOffset:L})}u3.CANVAS_LAYERS=3,u3.SELECT_BOX=0,u3.DRAG=1,u3.NODE=2,u3.WEBGL=3,u3.CANVAS_TYPES=["2d","2d","2d","webgl2"],u3.BUFFER_COUNT=3,u3.TEXTURE_BUFFER=0,u3.MOTIONBLUR_BUFFER_NODE=1,u3.MOTIONBLUR_BUFFER_DRAG=2,u3.redrawHint=function(e,t){switch(e){case"eles":this.data.canvasNeedsRedraw[u3.NODE]=t;break;case"drag":this.data.canvasNeedsRedraw[u3.DRAG]=t;break;case"select":this.data.canvasNeedsRedraw[u3.SELECT_BOX]=t;break;case"gc":this.data.gc=!0}};var u9="undefined"!=typeof Path2D;u3.path2dEnabled=function(e){if(void 0===e)return this.pathsEnabled;this.pathsEnabled=!!e},u3.usePaths=function(){return u9&&this.pathsEnabled},u3.setImgSmoothing=function(e,t){null!=e.imageSmoothingEnabled?e.imageSmoothingEnabled=t:(e.webkitImageSmoothingEnabled=t,e.mozImageSmoothingEnabled=t,e.msImageSmoothingEnabled=t)},u3.getImgSmoothing=function(e){return null!=e.imageSmoothingEnabled?e.imageSmoothingEnabled:e.webkitImageSmoothingEnabled||e.mozImageSmoothingEnabled||e.msImageSmoothingEnabled},u3.makeOffscreenCanvas=function(e,t){var n;return("undefined"==typeof OffscreenCanvas?"undefined":d(OffscreenCanvas))!=="undefined"?n=new OffscreenCanvas(e,t):((n=this.cy.window().document.createElement("canvas")).width=e,n.height=t),n},[l7,ua,uh,up,ug,uy,ub,uj,uK,u0,u5].forEach(function(e){J(u3,e)});var u6=[{name:"null",impl:a1},{name:"base",impl:l$},{name:"canvas",impl:u4}],u8={},u7={};function ce(e,t,n){var r=n,i=function(n){ez("Can not register `"+t+"` for `"+e+"` since `"+n+"` already exists in the prototype and can not be overridden")};if("core"===e)if(ap.prototype[t])return i(t);else ap.prototype[t]=n;else if("collection"===e)if(iG.prototype[t])return i(t);else iG.prototype[t]=n;else if("layout"===e){for(var a=function(e){this.options=e,n.call(this,e),A(this._private)||(this._private={}),this._private.cy=e.cy,this._private.listeners=[],this.createEmitter()},o=a.prototype=Object.create(n.prototype),s=[],l=0;l<s.length;l++){var u=s[l];o[u]=o[u]||function(){return this}}o.start&&!o.run?o.run=function(){return this.start(),this}:!o.start&&o.run&&(o.start=function(){return this.run(),this});var c=n.prototype.stop;o.stop=function(){var e=this.options;if(e&&e.animate){var t=this.animations;if(t)for(var n=0;n<t.length;n++)t[n].stop()}return c?c.call(this):this.emit("layoutstop"),this},o.destroy||(o.destroy=function(){return this}),o.cy=function(){return this._private.cy};var d=function(e){return e._private.cy},h={addEventFields:function(e,t){t.layout=e,t.cy=d(e),t.target=e},bubble:function(){return!0},parent:function(e){return d(e)}};J(o,{createEmitter:function(){return this._private.emitter=new iy(h,this),this},emitter:function(){return this._private.emitter},on:function(e,t){return this.emitter().on(e,t),this},one:function(e,t){return this.emitter().one(e,t),this},once:function(e,t){return this.emitter().one(e,t),this},removeListener:function(e,t){return this.emitter().removeListener(e,t),this},removeAllListeners:function(){return this.emitter().removeAllListeners(),this},emit:function(e,t){return this.emitter().emit(e,t),this}}),ry.eventAliasesOn(o),r=a}else if("renderer"===e&&"null"!==t&&"base"!==t){var f=ct("renderer","base"),p=f.prototype,g=n.prototype,v=function(){f.apply(this,arguments),n.apply(this,arguments)},y=v.prototype;for(var b in p){var x=p[b];if(null!=g[b])return i(b);y[b]=x}for(var w in g)y[w]=g[w];p.clientFunctions.forEach(function(e){y[e]=y[e]||function(){eL("Renderer does not implement `renderer."+e+"()` on its prototype")}}),r=v}else if("__proto__"===e||"constructor"===e||"prototype"===e)return eL(e+" is an illegal type to be registered, possibly lead to prototype pollutions");return ea({map:u8,keys:[e,t],value:r})}function ct(e,t){return eo({map:u8,keys:[e,t]})}function cn(e,t,n,r,i){return ea({map:u7,keys:[e,t,n,r],value:i})}function cr(e,t,n,r){return eo({map:u7,keys:[e,t,n,r]})}var ci=function(){return 2==arguments.length?ct.apply(null,arguments):3==arguments.length?ce.apply(null,arguments):4==arguments.length?cr.apply(null,arguments):5==arguments.length?cn.apply(null,arguments):void eL("Invalid extension access syntax")};ap.prototype.extension=ci,[{type:"layout",extensions:a0},{type:"renderer",extensions:u6}].forEach(function(e){e.extensions.forEach(function(t){ce(e.type,t.name,t.impl)})});var ca=function(){if(!(this instanceof ca))return new ca;this.length=0},co=ca.prototype;co.instanceString=function(){return"stylesheet"},co.selector=function(e){return this[this.length++]={selector:e,properties:[]},this},co.css=function(e,t){var n=this.length-1;if(B(e))this[n].properties.push({name:e,value:t});else if(A(e))for(var r=Object.keys(e),i=0;i<r.length;i++){var a=r[i],o=e[a];if(null!=o){var s=ac.properties[a]||ac.properties[Y(a)];if(null!=s){var l=s.name;this[n].properties.push({name:l,value:o})}}}return this},co.style=co.css,co.generateStyle=function(e){var t=new ac(e);return this.appendToStyle(t)},co.appendToStyle=function(e){for(var t=0;t<this.length;t++){var n=this[t],r=n.selector,i=n.properties;e.selector(r);for(var a=0;a<i.length;a++){var o=i[a];e.css(o.name,o.value)}}return e};var cs=function(e){return(void 0===e&&(e={}),A(e))?new ap(e):B(e)?ci.apply(ci,arguments):void 0};cs.use=function(e){var t=Array.prototype.slice.call(arguments,1);return t.unshift(cs),e.apply(null,t),this},cs.warnings=function(e){return eO(e)},cs.version="3.33.1",cs.stylesheet=cs.Stylesheet=ca}}]);