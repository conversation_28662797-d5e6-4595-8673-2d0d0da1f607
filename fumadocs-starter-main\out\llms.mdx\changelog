# Changelog: Product Updates
URL: /docs/changelog
Source: https://raw.githubusercontent.com/techwithanirudh/fumadocs-starter/refs/heads/main/content/docs/changelog/index.mdx

New updates and improvements
        
<Updates>
  <Update label="May 2025">
    ### General Improvements

    * Redesigned notification settings for better control over email and in-app alerts.
    * Fixed issue where some users were not receiving 2FA codes via SMS.
    * Activity log now includes more granular details for API usage.
    * Added keyboard shortcuts to the dashboard for quicker navigation.
  </Update>

  <Update label="April 2025">
    ### Mobile Enhancements

    * Major performance optimizations for older Android devices.
    * Added gesture support for calendar rescheduling.
    * Resolved an issue causing data sync delays when switching between networks.
    * Push notification reliability improved for background updates.
  </Update>

  <Update label="March 2025">
    ### Integration Upgrades

    * Microsoft Teams integration now supports deep-linking to projects.
    * Added webhook event for task reassignment.
    * Jira integration now includes sprint sync and story point mapping.
    * New Zapier triggers for completed tasks and file uploads.
  </Update>

  <Update label="February 2025">
    ### Analytics & Reporting

    * Custom reports can now be scheduled and emailed automatically.
    * Dashboard now supports drill-down charts for task status and team performance.
    * Added time tracking summary by user and project.
    * Export options now include PDF format with improved styling.
  </Update>

  <Update label="January 2025">
    ### UX & Accessibility

    * Redesigned sidebar with collapsible sections for cleaner navigation.
    * Improved screen reader support for form fields and buttons.
    * Fixed inconsistent behavior of dropdowns in Safari.
    * New color contrast settings for better visibility in dark mode.
  </Update>

  <Update label="December 2024">
    ### Year-End Stability Release

    * Fixed edge-case bugs reported during November release.
    * Reduced server response latency during peak hours by 15%.
    * Backend job processing now has retry logic with alerting on failure.
    * Cleaned up deprecated API endpoints — refer to migration guide if needed.
  </Update>

  <Update label="November 2024">
    ### Feature Updates

    * New “Smart Labels” automatically categorize tasks based on content.
    * Added batch actions for checklists and subtasks.
    * Admins can now view login history per user.
    * Kanban board now remembers last viewed filters and column order.
  </Update>

  <Update label="October 2024">
    ### User Management

    * Invite flow redesigned to allow bulk user imports from CSV.
    * Added user deactivation audit events.
    * Users can now request access to private projects (pending approval).
    * SSO login time reduced by \~40% with token reuse.
  </Update>

  <Update label="September 2024">
    ### Productivity Tools

    * Introduced “Focus Mode” – hides sidebar and notifications for distraction-free work.
    * Improved calendar drag-and-drop accuracy for overlapping events.
    * Sticky notes now support markdown formatting.
    * Weekly summary emails now include completed tasks and pending reviews.
  </Update>

  <Update label="August 2024">
    ### Search & Navigation

    * Global search now supports filters for date ranges, users, and tags.
    * Improved relevance ranking for file/document results.
    * Quick navigation panel added (press `/` to activate).
    * Recently viewed items show up in search suggestions.
  </Update>

  <Update label="July 2024">
    ### Security & Compliance

    * GDPR compliance updates: added data export + delete requests UI.
    * New admin-level controls for session expiration and login limits.
    * Enforced minimum password strength requirements across all users.
    * Bug fix: File links no longer accessible after permission removal.
  </Update>

  <Update label="June 2024">
    ### Performance & Quality Updates

    * Improved load times on dashboard by 25% through API response caching.
    * Fixed a bug where recurring events were duplicated when edited in bulk.
    * Added support for exporting user activity logs as CSV.
    * Updated UI components to match new branding guidelines.
    * Reduced idle memory usage in background sync tasks.
  </Update>

  <Update label="May 2024">
    ### New Integrations

    * Slack integration now supports direct replies to alerts.
    * Added native support for Notion links in task descriptions.
    * Webhooks now include retry logic with exponential backoff.
    * OAuth flow improved for Google and Microsoft accounts.
  </Update>
</Updates>
