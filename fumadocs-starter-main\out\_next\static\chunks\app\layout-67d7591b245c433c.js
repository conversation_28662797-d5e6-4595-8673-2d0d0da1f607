(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7177],{449:(e,t,r)=>{"use strict";r.d(t,{ThemeToggle:()=>f});var l=r(22342),s=r(83101),n=r(59427),i=r(37494),a=r(16399),o=r(70273),c=r(5379),d=r(33670),u=r(76445);let h=[{key:"light",icon:n.A,label:"Light theme"},{key:"dark",icon:i.A,label:"Dark theme"},{key:"system",icon:a.A,label:"System theme"}],m=(0,s.F)("relative size-6.5 rounded-full p-1.5 text-fd-muted-foreground",{variants:{active:{true:"bg-fd-accent text-fd-accent-foreground",false:"text-fd-muted-foreground"}}});function f(e){let{className:t,mode:r="light-dark",...s}=e,{setTheme:n,theme:i,resolvedTheme:a}=(0,c.D)(),[f,x]=(0,d.useState)(!1);(0,d.useLayoutEffect)(()=>{x(!0)},[]);let g=(0,u.cn)("relative inline-flex items-center rounded-full p-1 ring-1 ring-border",t);if("light-dark"===r){let e=f?a:null;return(0,l.jsx)("button",{className:g,"aria-label":"Toggle Theme",onClick:()=>n("light"===e?"dark":"light"),"data-theme-toggle":"",...s,children:h.map(t=>{let{key:r,icon:s}=t,n=e===r;if("system"!==r)return(0,l.jsxs)("div",{className:(0,u.cn)(m({active:n})),children:[n&&(0,l.jsx)(o.P.div,{layoutId:"activeTheme",className:"absolute inset-0 rounded-full bg-accent",transition:{type:"spring",duration:.4}}),(0,l.jsx)(s,{className:"size-full",fill:"currentColor"})]},r)})})}let v=f?i:null;return(0,l.jsx)("div",{className:g,"data-theme-toggle":"",...s,children:h.map(e=>{let{key:t,icon:r}=e;return(0,l.jsxs)("button",{type:"button","aria-label":t,className:(0,u.cn)(m({active:v===t})),onClick:()=>n(t),children:[v===t&&(0,l.jsx)(o.P.div,{layoutId:"activeTheme",className:"absolute inset-0 rounded-full bg-accent",transition:{type:"spring",duration:.4}}),(0,l.jsx)(r,{className:"size-full",fill:"currentColor"})]},t)})})}},11976:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,81356,23)),Promise.resolve().then(r.t.bind(r,34097,23)),Promise.resolve().then(r.t.bind(r,19511,23)),Promise.resolve().then(r.bind(r,21919)),Promise.resolve().then(r.bind(r,62163)),Promise.resolve().then(r.bind(r,449)),Promise.resolve().then(r.t.bind(r,93836,23))},21919:(e,t,r)=>{"use strict";r.d(t,{Body:()=>i});var l=r(22342),s=r(20063),n=r(76445);function i(e){let{children:t}=e,r=function(){let{slug:e}=(0,s.useParams)();return Array.isArray(e)&&e.length>0?e[0]:void 0}();return(0,l.jsx)("body",{className:(0,n.cn)(r,"relative flex min-h-screen flex-col"),children:t})}},62163:(e,t,r)=>{"use strict";r.d(t,{Providers:()=>m});var l=r(22342),s=r(49534),n=r(60729),i=r(47342),a=r(371),o=r(53878);let c=()=>(0,l.jsx)("div",{className:"flex flex-col items-center justify-between gap-2 px-2 py-12 text-center text-sm",children:(0,l.jsx)(o.I18nLabel,{label:"searchNoResult"})});function d(e){let{locale:t}=(0,o.s9)(),{search:r,setSearch:s,query:n}=(0,i.J)({type:"static",from:"/search.json",locale:t});return(0,l.jsxs)(a.Rc,{search:r,onSearchChange:s,isLoading:n.isLoading,...e,children:[(0,l.jsx)(a.Xq,{}),(0,l.jsxs)(a.Ct,{children:[(0,l.jsxs)(a.i,{children:[(0,l.jsx)(a.y,{}),(0,l.jsx)(a.vo,{}),(0,l.jsx)(a.hx,{})]}),(0,l.jsx)(a.FI,{items:"empty"!==n.data?n.data:null,Empty:c})]})]})}var u=r(47520);function h(e){let{delayDuration:t=0,...r}=e;return(0,l.jsx)(u.Kq,{"data-slot":"tooltip-provider",delayDuration:t,...r})}function m(e){let{children:t}=e;return(0,l.jsx)(n.u4,{search:{SearchDialog:d},children:(0,l.jsx)(s.V,{height:"2px",color:"var(--color-primary)",options:{showSpinner:!1},stopDelay:1e3,delay:1e3,startOnLoad:!0,shallowRouting:!0,children:(0,l.jsx)(h,{children:t})})})}},76445:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var l=r(2821),s=r(75889);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,l.$)(t))}},93836:()=>{}},e=>{e.O(0,[2339,713,1407,6787,2619,4494,6708,4478,7358],()=>e(e.s=11976)),_N_E=e.O()}]);