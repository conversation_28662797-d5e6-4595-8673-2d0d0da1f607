<!DOCTYPE html><!--qiBTFv8yDH6CETnfs0eR8--><html lang="en" class="__className_e8ce0c dark"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/2a77d629393b8303.css" data-precedence="next"/><link rel="stylesheet" href="/_next/static/css/a3ab7692850dd48a.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-3cf906aee7635ecc.js"/><script src="/_next/static/chunks/f5e865f6-f7ce3acdc8f53367.js" async=""></script><script src="/_next/static/chunks/4478-3b40d1cd2fe693af.js" async=""></script><script src="/_next/static/chunks/main-app-6c91cab41ed72c60.js" async=""></script><script src="/_next/static/chunks/1407-f13a996aa5f2cddb.js" async=""></script><script src="/_next/static/chunks/6787-9719cbea8ecc0c67.js" async=""></script><script src="/_next/static/chunks/2619-1994d4ab6e34980c.js" async=""></script><script src="/_next/static/chunks/4494-b74022c3780bb5bc.js" async=""></script><script src="/_next/static/chunks/app/layout-67d7591b245c433c.js" async=""></script><script src="/_next/static/chunks/app/not-found-d9d426fe0712fb5f.js" async=""></script><link rel="expect" href="#_R_" blocking="render"/><meta name="robots" content="noindex"/><meta name="theme-color" media="(prefers-color-scheme: dark)" content="#0A0A0A"/><meta name="theme-color" media="(prefers-color-scheme: light)" content="#fff"/><title>Starter Kit</title><meta name="description" content="The Next.js framework for building documentation sites"/><meta property="og:title" content="Starter Kit"/><meta property="og:description" content="The Next.js framework for building documentation sites"/><meta property="og:url" content="https://fumadocs-starter.vercel.app"/><meta property="og:site_name" content="Starter Kit"/><meta property="og:image" content="https://http/localhost:3000/banner.png"/><meta name="twitter:card" content="summary_large_image"/><meta name="twitter:creator" content="@AnirudhWith"/><meta name="twitter:title" content="Starter Kit"/><meta name="twitter:description" content="The Next.js framework for building documentation sites"/><meta name="twitter:image" content="https://http/localhost:3000/banner.png"/><link rel="icon" href="/icon.png?91227d45bd5eaf78" type="image/png" sizes="512x512"/><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="relative flex min-h-screen flex-col"><div hidden=""><!--$--><!--/$--></div><script>((a,b,c,d,e,f,g,h)=>{let i=document.documentElement,j=["light","dark"];function k(b){var c;(Array.isArray(a)?a:[a]).forEach(a=>{let c="class"===a,d=c&&f?e.map(a=>f[a]||a):e;c?(i.classList.remove(...d),i.classList.add(f&&f[b]?f[b]:b)):i.setAttribute(a,b)}),c=b,h&&j.includes(c)&&(i.style.colorScheme=c)}if(d)k(d);else try{let a=localStorage.getItem(b)||c,d=g&&"system"===a?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":a;k(d)}catch(a){}})("class","theme","system",null,["light","dark"],null,true,true)</script><style>
:root {
  --bprogress-color: var(--color-primary);
  --bprogress-height: 2px;
  --bprogress-spinner-size: 18px;
  --bprogress-spinner-animation-duration: 400ms;
  --bprogress-spinner-border-size: 2px;
  --bprogress-box-shadow: 0 0 10px var(--color-primary), 0 0 5px var(--color-primary);
  --bprogress-z-index: 99999;
  --bprogress-spinner-top: 15px;
  --bprogress-spinner-bottom: auto;
  --bprogress-spinner-right: 15px;
  --bprogress-spinner-left: auto;
}

.bprogress {
  width: 0;
  height: 0;
  pointer-events: none;
  z-index: var(--bprogress-z-index);
}

.bprogress .bar {
  background: var(--bprogress-color);
  position: fixed;
  z-index: var(--bprogress-z-index);
  top: 0;
  left: 0;
  width: 100%;
  height: var(--bprogress-height);
}

/* Fancy blur effect */
.bprogress .peg {
  display: block;
  position: absolute;
  right: 0;
  width: 100px;
  height: 100%;
  box-shadow: var(--bprogress-box-shadow);
  opacity: 1.0;
  transform: rotate(3deg) translate(0px, -4px);
}

/* Remove these to get rid of the spinner */
.bprogress .spinner {
  display: block;
  position: fixed;
  z-index: var(--bprogress-z-index);
  top: var(--bprogress-spinner-top);
  bottom: var(--bprogress-spinner-bottom);
  right: var(--bprogress-spinner-right);
  left: var(--bprogress-spinner-left);
}

.bprogress .spinner-icon {
  width: var(--bprogress-spinner-size);
  height: var(--bprogress-spinner-size);
  box-sizing: border-box;
  border: solid var(--bprogress-spinner-border-size) transparent;
  border-top-color: var(--bprogress-color);
  border-left-color: var(--bprogress-color);
  border-radius: 50%;
  -webkit-animation: bprogress-spinner var(--bprogress-spinner-animation-duration) linear infinite;
  animation: bprogress-spinner var(--bprogress-spinner-animation-duration) linear infinite;
}

.bprogress-custom-parent {
  overflow: hidden;
  position: relative;
}

.bprogress-custom-parent .bprogress .spinner,
.bprogress-custom-parent .bprogress .bar {
  position: absolute;
}

.bprogress .indeterminate {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: var(--bprogress-height);
  overflow: hidden;
}

.bprogress .indeterminate .inc,
.bprogress .indeterminate .dec {
  position: absolute;
  top: 0;
  height: 100%;
  background-color: var(--bprogress-color);
}

.bprogress .indeterminate .inc {
  animation: bprogress-indeterminate-increase 2s infinite;
}

.bprogress .indeterminate .dec {
  animation: bprogress-indeterminate-decrease 2s 0.5s infinite;
}

@-webkit-keyframes bprogress-spinner {
  0%   { -webkit-transform: rotate(0deg); transform: rotate(0deg); }
  100% { -webkit-transform: rotate(360deg); transform: rotate(360deg); }
}

@keyframes bprogress-spinner {
  0%   { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes bprogress-indeterminate-increase {
  from { left: -5%; width: 5%; }
  to { left: 130%; width: 100%; }
}

@keyframes bprogress-indeterminate-decrease {
  from { left: -80%; width: 80%; }
  to { left: 110%; width: 10%; }
}
</style><!--$!--><template data-dgst="BAILOUT_TO_CLIENT_SIDE_RENDERING"></template><!--/$--><main class="flex flex-1"><div class="flex flex-auto flex-col items-center justify-center px-4 text-center sm:flex-row"><h1 class="border-border font-extrabold text-2xl text-foreground tracking-tight sm:mr-6 sm:border-r sm:pr-6 sm:text-3xl">404</h1><h2 class="mt-2 text-muted-foreground sm:mt-0">This page could not be found.<!-- --> <a class="text-primary underline underline-offset-4 hover:text-primary/80" href="/">Go back home</a></h2></div></main><!--$--><!--/$--><script src="/_next/static/chunks/webpack-3cf906aee7635ecc.js" id="_R_" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[21919,[\"1407\",\"static/chunks/1407-f13a996aa5f2cddb.js\",\"6787\",\"static/chunks/6787-9719cbea8ecc0c67.js\",\"2619\",\"static/chunks/2619-1994d4ab6e34980c.js\",\"4494\",\"static/chunks/4494-b74022c3780bb5bc.js\",\"7177\",\"static/chunks/app/layout-67d7591b245c433c.js\"],\"Body\"]\n3:I[62163,[\"1407\",\"static/chunks/1407-f13a996aa5f2cddb.js\",\"6787\",\"static/chunks/6787-9719cbea8ecc0c67.js\",\"2619\",\"static/chunks/2619-1994d4ab6e34980c.js\",\"4494\",\"static/chunks/4494-b74022c3780bb5bc.js\",\"7177\",\"static/chunks/app/layout-67d7591b245c433c.js\"],\"Providers\"]\n4:I[9766,[],\"\"]\n5:I[98924,[],\"\"]\n6:I[52619,[\"2619\",\"static/chunks/2619-1994d4ab6e34980c.js\",\"4345\",\"static/chunks/app/not-found-d9d426fe0712fb5f.js\"],\"\"]\n7:I[24431,[],\"OutletBoundary\"]\n9:I[15278,[],\"AsyncMetadataOutlet\"]\nb:I[24431,[],\"ViewportBoundary\"]\nd:I[24431,[],\"MetadataBoundary\"]\ne:\"$Sreact.suspense\"\n10:I[57150,[],\"\"]\n:HL[\"/_next/static/css/2a77d629393b8303.css\",\"style\"]\n:HL[\"/_next/static/css/a3ab7692850dd48a.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"qiBTFv8yDH6CETnfs0eR8\",\"p\":\"\",\"c\":[\"\",\"_not-found\",\"\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"/_not-found\",{\"children\":[\"__PAGE__\",{}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/2a77d629393b8303.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"1\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/a3ab7692850dd48a.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"className\":\"__className_e8ce0c dark\",\"suppressHydrationWarning\":true,\"children\":[\"$\",\"$L2\",null,{\"children\":[\"$\",\"$L3\",null,{\"children\":[\"$\",\"$L4\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L5\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"main\",null,{\"className\":\"flex flex-1\",\"children\":[\"$\",\"div\",null,{\"className\":\"flex flex-auto flex-col items-center justify-center px-4 text-center sm:flex-row\",\"children\":[[\"$\",\"h1\",null,{\"className\":\"border-border font-extrabold text-2xl text-foreground tracking-tight sm:mr-6 sm:border-r sm:pr-6 sm:text-3xl\",\"children\":\"404\"}],[\"$\",\"h2\",null,{\"className\":\"mt-2 text-muted-foreground sm:mt-0\",\"children\":[\"This page could not be found.\",\" \",[\"$\",\"$L6\",null,{\"href\":\"/\",\"className\":\"text-primary underline underline-offset-4 hover:text-primary/80\",\"children\":\"Go back home\"}]]}]]}]}],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]}]}]]}],{\"children\":[\"/_not-found\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L4\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L5\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"main\",null,{\"className\":\"flex flex-1\",\"children\":[\"$\",\"div\",null,{\"className\":\"flex flex-auto flex-col items-center justify-center px-4 text-center sm:flex-row\",\"children\":[[\"$\",\"h1\",null,{\"className\":\"border-border font-extrabold text-2xl text-foreground tracking-tight sm:mr-6 sm:border-r sm:pr-6 sm:text-3xl\",\"children\":\"404\"}],[\"$\",\"h2\",null,{\"className\":\"mt-2 text-muted-foreground sm:mt-0\",\"children\":[\"This page could not be found.\",\" \",[\"$\",\"$L6\",null,{\"href\":\"/\",\"className\":\"text-primary underline underline-offset-4 hover:text-primary/80\",\"children\":\"Go back home\"}]]}]]}]}],null,[\"$\",\"$L7\",null,{\"children\":[\"$L8\",[\"$\",\"$L9\",null,{\"promise\":\"$@a\"}]]}]]}],{},null,false]},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[[\"$\",\"meta\",null,{\"name\":\"robots\",\"content\":\"noindex\"}],[[\"$\",\"$Lb\",null,{\"children\":\"$Lc\"}],null],[\"$\",\"$Ld\",null,{\"children\":[\"$\",\"div\",null,{\"hidden\":true,\"children\":[\"$\",\"$e\",null,{\"fallback\":null,\"children\":\"$Lf\"}]}]}]]}],false]],\"m\":\"$undefined\",\"G\":[\"$10\",[]],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"c:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"2\",{\"name\":\"theme-color\",\"media\":\"(prefers-color-scheme: dark)\",\"content\":\"#0A0A0A\"}],[\"$\",\"meta\",\"3\",{\"name\":\"theme-color\",\"media\":\"(prefers-color-scheme: light)\",\"content\":\"#fff\"}]]\n8:null\n"])</script><script>self.__next_f.push([1,"11:I[80622,[],\"IconMark\"]\n"])</script><script>self.__next_f.push([1,"a:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"Starter Kit\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"The Next.js framework for building documentation sites\"}],[\"$\",\"meta\",\"2\",{\"property\":\"og:title\",\"content\":\"Starter Kit\"}],[\"$\",\"meta\",\"3\",{\"property\":\"og:description\",\"content\":\"The Next.js framework for building documentation sites\"}],[\"$\",\"meta\",\"4\",{\"property\":\"og:url\",\"content\":\"https://fumadocs-starter.vercel.app\"}],[\"$\",\"meta\",\"5\",{\"property\":\"og:site_name\",\"content\":\"Starter Kit\"}],[\"$\",\"meta\",\"6\",{\"property\":\"og:image\",\"content\":\"https://http/localhost:3000/banner.png\"}],[\"$\",\"meta\",\"7\",{\"name\":\"twitter:card\",\"content\":\"summary_large_image\"}],[\"$\",\"meta\",\"8\",{\"name\":\"twitter:creator\",\"content\":\"@AnirudhWith\"}],[\"$\",\"meta\",\"9\",{\"name\":\"twitter:title\",\"content\":\"Starter Kit\"}],[\"$\",\"meta\",\"10\",{\"name\":\"twitter:description\",\"content\":\"The Next.js framework for building documentation sites\"}],[\"$\",\"meta\",\"11\",{\"name\":\"twitter:image\",\"content\":\"https://http/localhost:3000/banner.png\"}],[\"$\",\"link\",\"12\",{\"rel\":\"icon\",\"href\":\"/icon.png?91227d45bd5eaf78\",\"type\":\"image/png\",\"sizes\":\"512x512\"}],[\"$\",\"$L11\",\"13\",{}]],\"error\":null,\"digest\":\"$undefined\"}\n"])</script><script>self.__next_f.push([1,"f:\"$a:metadata\"\n"])</script></body></html>