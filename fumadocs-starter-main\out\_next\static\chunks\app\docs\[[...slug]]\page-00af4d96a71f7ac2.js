(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7870],{449:(e,t,r)=>{"use strict";r.d(t,{ThemeToggle:()=>f});var n=r(22342),o=r(83101),i=r(59427),s=r(37494),a=r(16399),l=r(70273),d=r(5379),c=r(33670),u=r(76445);let h=[{key:"light",icon:i.A,label:"Light theme"},{key:"dark",icon:s.A,label:"Dark theme"},{key:"system",icon:a.A,label:"System theme"}],m=(0,o.F)("relative size-6.5 rounded-full p-1.5 text-fd-muted-foreground",{variants:{active:{true:"bg-fd-accent text-fd-accent-foreground",false:"text-fd-muted-foreground"}}});function f(e){let{className:t,mode:r="light-dark",...o}=e,{setTheme:i,theme:s,resolvedTheme:a}=(0,d.D)(),[f,v]=(0,c.useState)(!1);(0,c.useLayoutEffect)(()=>{v(!0)},[]);let g=(0,u.cn)("relative inline-flex items-center rounded-full p-1 ring-1 ring-border",t);if("light-dark"===r){let e=f?a:null;return(0,n.jsx)("button",{className:g,"aria-label":"Toggle Theme",onClick:()=>i("light"===e?"dark":"light"),"data-theme-toggle":"",...o,children:h.map(t=>{let{key:r,icon:o}=t,i=e===r;if("system"!==r)return(0,n.jsxs)("div",{className:(0,u.cn)(m({active:i})),children:[i&&(0,n.jsx)(l.P.div,{layoutId:"activeTheme",className:"absolute inset-0 rounded-full bg-accent",transition:{type:"spring",duration:.4}}),(0,n.jsx)(o,{className:"size-full",fill:"currentColor"})]},r)})})}let p=f?s:null;return(0,n.jsx)("div",{className:g,"data-theme-toggle":"",...o,children:h.map(e=>{let{key:t,icon:r}=e;return(0,n.jsxs)("button",{type:"button","aria-label":t,className:(0,u.cn)(m({active:p===t})),onClick:()=>i(t),children:[p===t&&(0,n.jsx)(l.P.div,{layoutId:"activeTheme",className:"absolute inset-0 rounded-full bg-accent",transition:{type:"spring",duration:.4}}),(0,n.jsx)(r,{className:"size-full",fill:"currentColor"})]},t)})})}},6510:(e,t,r)=>{"use strict";r.d(t,{LLMCopyButton:()=>g,ViewOptions:()=>x});var n=r(22342),o=r(83101),i=r(81501),s=r(25502),a=r(58807),l=r(5917),d=r(75426),c=r(5937),u=r(24033),h=r(93499),m=r(33670),f=r(76445);let v=new Map;function g(e){let{markdownUrl:t}=e,[r,o]=(0,m.useState)(!1),[s,c]=(0,a.x)(async()=>{let e=v.get(t);if(e)return navigator.clipboard.writeText(e);o(!0);try{await navigator.clipboard.write([new ClipboardItem({"text/plain":fetch(t).then(async e=>{let r=await e.text();return v.set(t,r),r})})])}finally{o(!1)}});return(0,n.jsxs)("button",{type:"button",disabled:r,className:(0,f.cn)((0,i.r)({color:"secondary",size:"sm",className:"gap-2 [&_svg]:size-3.5 [&_svg]:text-fd-muted-foreground"})),onClick:c,children:[s?(0,n.jsx)(l.A,{}):(0,n.jsx)(d.A,{}),"Copy Markdown"]})}let p=(0,o.F)("text-sm p-2 rounded-lg inline-flex items-center gap-2 hover:text-fd-accent-foreground hover:bg-fd-accent [&_svg]:size-4");function x(e){let{markdownUrl:t,githubUrl:r}=e,o=(0,m.useMemo)(()=>{let e=new URL(t,window.location.origin),o="Read ".concat(e,", I want to ask questions about it.");return[{title:"Open in GitHub",href:r,icon:(0,n.jsxs)("svg",{fill:"currentColor",role:"img",viewBox:"0 0 24 24",children:[(0,n.jsx)("title",{children:"GitHub"}),(0,n.jsx)("path",{d:"M12 .297c-6.63 0-12 5.373-12 12 0 5.303 3.438 9.8 8.205 11.385.6.113.82-.258.82-.577 0-.285-.01-1.04-.015-2.04-3.338.724-4.042-1.61-4.042-1.61C4.422 18.07 3.633 17.7 3.633 17.7c-1.087-.744.084-.729.084-.729 1.205.084 1.838 1.236 1.838 1.236 1.07 1.835 2.809 1.305 3.495.998.108-.776.417-1.305.76-1.605-2.665-.3-5.466-1.332-5.466-5.93 0-1.31.465-2.38 1.235-3.22-.135-.303-.54-1.523.105-3.176 0 0 1.005-.322 3.3 1.23.96-.267 1.98-.399 3-.405 1.02.006 2.04.138 3 .405 2.28-1.552 3.285-1.23 3.285-1.23.645 1.653.24 2.873.12 3.176.765.84 1.23 1.91 1.23 3.22 0 4.61-2.805 5.625-5.475 5.92.42.36.81 1.096.81 2.22 0 1.606-.015 2.896-.015 3.286 0 .315.21.69.825.57C20.565 22.092 24 17.592 24 12.297c0-6.627-5.373-12-12-12"})]})},{title:"Open in Scira AI",href:"https://scira.ai/?".concat(new URLSearchParams({q:o})),icon:(0,n.jsxs)("svg",{width:"910",height:"934",viewBox:"0 0 910 934",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,n.jsx)("path",{d:"M647.664 197.775C569.13 189.049 525.5 145.419 516.774 66.8849C508.048 145.419 464.418 189.049 385.884 197.775C464.418 206.501 508.048 250.131 516.774 328.665C525.5 250.131 569.13 206.501 647.664 197.775Z",fill:"currentColor",stroke:"currentColor",strokeWidth:"8",strokeLinejoin:"round"}),(0,n.jsx)("path",{d:"M516.774 304.217C510.299 275.491 498.208 252.087 480.335 234.214C462.462 216.341 439.058 204.251 410.333 197.775C439.059 191.3 462.462 179.209 480.335 161.336C498.208 143.463 510.299 120.06 516.774 91.334C523.25 120.059 535.34 143.463 553.213 161.336C571.086 179.209 594.49 191.3 623.216 197.775C594.49 204.251 571.086 216.341 553.213 234.214C535.34 252.087 523.25 275.491 516.774 304.217Z",fill:"currentColor",stroke:"currentColor",strokeWidth:"8",strokeLinejoin:"round"}),(0,n.jsx)("path",{d:"M857.5 508.116C763.259 497.644 710.903 445.288 700.432 351.047C689.961 445.288 637.605 497.644 543.364 508.116C637.605 518.587 689.961 570.943 700.432 665.184C710.903 570.943 763.259 518.587 857.5 508.116Z",stroke:"currentColor",strokeWidth:"20",strokeLinejoin:"round"}),(0,n.jsx)("path",{d:"M700.432 615.957C691.848 589.05 678.575 566.357 660.383 548.165C642.191 529.973 619.499 516.7 592.593 508.116C619.499 499.533 642.191 486.258 660.383 468.066C678.575 449.874 691.848 427.181 700.432 400.274C709.015 427.181 722.289 449.874 740.481 468.066C758.673 486.258 781.365 499.533 808.271 508.116C781.365 516.7 758.673 529.973 740.481 548.165C722.289 566.357 709.015 589.05 700.432 615.957Z",stroke:"currentColor",strokeWidth:"20",strokeLinejoin:"round"}),(0,n.jsx)("path",{d:"M889.949 121.237C831.049 114.692 798.326 81.9698 791.782 23.0692C785.237 81.9698 752.515 114.692 693.614 121.237C752.515 127.781 785.237 160.504 791.782 219.404C798.326 160.504 831.049 127.781 889.949 121.237Z",fill:"currentColor",stroke:"currentColor",strokeWidth:"8",strokeLinejoin:"round"}),(0,n.jsx)("path",{d:"M791.782 196.795C786.697 176.937 777.869 160.567 765.16 147.858C752.452 135.15 736.082 126.322 716.226 121.237C736.082 116.152 752.452 107.324 765.16 94.6152C777.869 81.9065 786.697 65.5368 791.782 45.6797C796.867 65.5367 805.695 81.9066 818.403 94.6152C831.112 107.324 847.481 116.152 867.338 121.237C847.481 126.322 831.112 135.15 818.403 147.858C805.694 160.567 796.867 176.937 791.782 196.795Z",fill:"currentColor",stroke:"currentColor",strokeWidth:"8",strokeLinejoin:"round"}),(0,n.jsx)("path",{d:"M760.632 764.337C720.719 814.616 669.835 855.1 611.872 882.692C553.91 910.285 490.404 924.255 426.213 923.533C362.022 922.812 298.846 907.419 241.518 878.531C184.19 849.643 134.228 808.026 95.4548 756.863C56.6815 705.7 30.1238 646.346 17.8129 583.343C5.50207 520.339 7.76433 455.354 24.4266 393.359C41.089 331.364 71.7099 274.001 113.947 225.658C156.184 177.315 208.919 139.273 268.117 114.442",stroke:"currentColor",strokeWidth:"30",strokeLinecap:"round",strokeLinejoin:"round"})]})},{title:"Open in ChatGPT",href:"https://chatgpt.com/?".concat(new URLSearchParams({hints:"search",q:o})),icon:(0,n.jsxs)("svg",{role:"img",viewBox:"0 0 24 24",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg",children:[(0,n.jsx)("title",{children:"OpenAI"}),(0,n.jsx)("path",{d:"M22.2819 9.8211a5.9847 5.9847 0 0 0-.5157-4.9108 6.0462 6.0462 0 0 0-6.5098-2.9A6.0651 6.0651 0 0 0 4.9807 4.1818a5.9847 5.9847 0 0 0-3.9977 2.9 6.0462 6.0462 0 0 0 .7427 7.0966 5.98 5.98 0 0 0 .511 4.9107 6.051 6.051 0 0 0 6.5146 2.9001A5.9847 5.9847 0 0 0 13.2599 24a6.0557 6.0557 0 0 0 5.7718-4.2058 5.9894 5.9894 0 0 0 3.9977-2.9001 6.0557 6.0557 0 0 0-.7475-7.0729zm-9.022 12.6081a4.4755 4.4755 0 0 1-2.8764-1.0408l.1419-.0804 4.7783-2.7582a.7948.7948 0 0 0 .3927-.6813v-6.7369l2.02 1.1686a.071.071 0 0 1 .038.052v5.5826a4.504 4.504 0 0 1-4.4945 4.4944zm-9.6607-4.1254a4.4708 4.4708 0 0 1-.5346-3.0137l.142.0852 4.783 2.7582a.7712.7712 0 0 0 .7806 0l5.8428-3.3685v2.3324a.0804.0804 0 0 1-.0332.0615L9.74 19.9502a4.4992 4.4992 0 0 1-6.1408-1.6464zM2.3408 7.8956a4.485 4.485 0 0 1 2.3655-1.9728V11.6a.7664.7664 0 0 0 .3879.6765l5.8144 3.3543-2.0201 1.1685a.0757.0757 0 0 1-.071 0l-4.8303-2.7865A4.504 4.504 0 0 1 2.3408 7.872zm16.5963 3.8558L13.1038 8.364 15.1192 7.2a.0757.0757 0 0 1 .071 0l4.8303 2.7913a4.4944 4.4944 0 0 1-.6765 8.1042v-5.6772a.79.79 0 0 0-.407-.667zm2.0107-3.0231l-.142-.0852-4.7735-2.7818a.7759.7759 0 0 0-.7854 0L9.409 9.2297V6.8974a.0662.0662 0 0 1 .0284-.0615l4.8303-2.7866a4.4992 4.4992 0 0 1 6.6802 4.66zM8.3065 12.863l-2.02-1.1638a.0804.0804 0 0 1-.038-.0567V6.0742a4.4992 4.4992 0 0 1 7.3757-3.4537l-.142.0805L8.704 5.459a.7948.7948 0 0 0-.3927.6813zm1.0976-2.3654l2.602-1.4998 2.6069 1.4998v2.9994l-2.5974 1.4997-2.6067-1.4997Z"})]})},{title:"Open in Claude",href:"https://claude.ai/new?".concat(new URLSearchParams({q:o})),icon:(0,n.jsxs)("svg",{fill:"currentColor",role:"img",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:[(0,n.jsx)("title",{children:"Anthropic"}),(0,n.jsx)("path",{d:"M17.3041 3.541h-3.6718l6.696 16.918H24Zm-10.6082 0L0 20.459h3.7442l1.3693-3.5527h7.0052l1.3693 3.5528h3.7442L10.5363 3.5409Zm-.3712 10.2232 2.2914-5.9456 2.2914 5.9456Z"})]})},{title:"Open in T3 Chat",href:"https://t3.chat/new?".concat(new URLSearchParams({q:o})),icon:(0,n.jsx)(c.A,{})}]},[r,t]);return(0,n.jsxs)(s.Popover,{children:[(0,n.jsxs)(s.PopoverTrigger,{className:(0,f.cn)((0,i.r)({color:"secondary",size:"sm",className:"gap-2"})),children:["Open",(0,n.jsx)(u.A,{className:"size-3.5 text-fd-muted-foreground"})]}),(0,n.jsx)(s.PopoverContent,{className:"flex flex-col overflow-auto",children:o.map(e=>(0,n.jsxs)("a",{href:e.href,rel:"noreferrer noopener",target:"_blank",className:(0,f.cn)(p()),children:[e.icon,e.title,(0,n.jsx)(h.A,{className:"ms-auto size-3.5 text-fd-muted-foreground"})]},e.href))})]})}},12561:(e,t,r)=>{"use strict";r.d(t,{Update:()=>c,Updates:()=>d});var n=r(22342),o=r(32467),i=r(83101),s=r(76445);let a=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:t,variant:r,asChild:i=!1,...l}=e,d=i?o.DX:"span";return(0,n.jsx)(d,{"data-slot":"badge",className:(0,s.cn)(a({variant:r}),t),...l})}function d(e){let{children:t,className:r}=e;return(0,n.jsx)("div",{className:(0,s.cn)("fd-updates flex flex-col",r),children:t})}function c(e){let{children:t,label:r,id:o,className:i}=e,a=o||r.toLowerCase().replace(/\s+/g,"-");return(0,n.jsxs)("div",{id:a,className:(0,s.cn)("fd-update relative flex w-full flex-col items-start gap-2 py-8 lg:flex-row lg:gap-6",i),children:[(0,n.jsx)("div",{className:"group top-[112px] flex w-full flex-shrink-0 flex-col items-start justify-start lg:sticky lg:w-[160px]",children:(0,n.jsx)(l,{variant:"secondary",className:"h-fit flex-grow-0 rounded-lg px-2 py-1 text-sm",children:r})}),(0,n.jsx)("div",{className:"prose prose-gray dark:prose-invert max-w-full flex-1 overflow-hidden px-0.5",children:t})]})}},71202:(e,t,r)=>{"use strict";r.r(t),r.d(t,{Mermaid:()=>s});var n=r(22342),o=r(5379),i=r(33670);function s(e){let{chart:t}=e,s=(0,i.useId)(),[a,l]=(0,i.useState)(""),d=(0,i.useRef)(null),c=(0,i.useRef)(null),{resolvedTheme:u}=(0,o.D)();return(0,i.useEffect)(()=>{if(c.current===t||!d.current)return;let e=d.current;c.current=t,async function(){let{default:n}=await Promise.all([r.e(8446),r.e(7485)]).then(r.bind(r,37485));try{n.initialize({startOnLoad:!1,securityLevel:"loose",fontFamily:"inherit",themeCSS:"margin: 1.5rem auto 0;",theme:"dark"===u?"dark":"default"});let{svg:r,bindFunctions:o}=await n.render(s,t.replaceAll("\\n","\n"));null==o||o(e),l(r)}catch(e){console.error("Error while rendering mermaid",e)}}()},[t,s,u]),(0,n.jsx)("div",{ref:d,dangerouslySetInnerHTML:{__html:a}})}},76445:(e,t,r)=>{"use strict";r.d(t,{cn:()=>i});var n=r(2821),o=r(75889);function i(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,o.QP)((0,n.$)(t))}},82069:(e,t,r)=>{Promise.resolve().then(r.bind(r,99987)),Promise.resolve().then(r.bind(r,1481)),Promise.resolve().then(r.bind(r,18387)),Promise.resolve().then(r.bind(r,23346)),Promise.resolve().then(r.bind(r,60566)),Promise.resolve().then(r.bind(r,8892)),Promise.resolve().then(r.bind(r,26379)),Promise.resolve().then(r.bind(r,47074)),Promise.resolve().then(r.bind(r,81351)),Promise.resolve().then(r.bind(r,69213)),Promise.resolve().then(r.bind(r,53450)),Promise.resolve().then(r.bind(r,64675)),Promise.resolve().then(r.bind(r,42738)),Promise.resolve().then(r.bind(r,15396)),Promise.resolve().then(r.bind(r,8e3)),Promise.resolve().then(r.bind(r,75580)),Promise.resolve().then(r.bind(r,17925)),Promise.resolve().then(r.bind(r,90441)),Promise.resolve().then(r.bind(r,85765)),Promise.resolve().then(r.bind(r,3538)),Promise.resolve().then(r.bind(r,33619)),Promise.resolve().then(r.bind(r,29947)),Promise.resolve().then(r.bind(r,53878)),Promise.resolve().then(r.bind(r,65064)),Promise.resolve().then(r.t.bind(r,81356,23)),Promise.resolve().then(r.bind(r,6510)),Promise.resolve().then(r.bind(r,12561)),Promise.resolve().then(r.bind(r,71202)),Promise.resolve().then(r.bind(r,449)),Promise.resolve().then(r.bind(r,90987))},90987:(e,t,r)=>{"use strict";r.d(t,{HoverCard:()=>s,HoverCardContent:()=>l,HoverCardTrigger:()=>a});var n=r(22342),o=r(74604),i=r(76445);function s(e){let{...t}=e;return(0,n.jsx)(o.bL,{"data-slot":"hover-card",...t})}function a(e){let{...t}=e;return(0,n.jsx)(o.l9,{"data-slot":"hover-card-trigger",...t})}function l(e){let{className:t,align:r="center",sideOffset:s=4,...a}=e;return(0,n.jsx)(o.ZL,{"data-slot":"hover-card-portal",children:(0,n.jsx)(o.UC,{"data-slot":"hover-card-content",align:r,sideOffset:s,className:(0,i.cn)("data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-64 origin-(--radix-hover-card-content-transform-origin) rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-hidden data-[state=closed]:animate-out data-[state=open]:animate-in",t),...a})})}}},e=>{e.O(0,[1407,6787,9972,5580,9696,9983,6708,4478,7358],()=>e(e.s=82069)),_N_E=e.O()}]);