(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9895],{3407:(e,t,n)=>{"use strict";n.d(t,{BM:()=>s,CW:()=>r,Ee:()=>f,HP:()=>c,JQ:()=>a,Ny:()=>h,On:()=>d,cx:()=>o,es:()=>p,lV:()=>i,ok:()=>l,ol:()=>u});let r=m(/[A-Za-z]/),i=m(/[\dA-Za-z]/),o=m(/[#-'*+\--9=?A-Z^-~]/);function a(e){return null!==e&&(e<32||127===e)}let s=m(/\d/),l=m(/[\dA-Fa-f]/),u=m(/[!-/:-@[-`{-~]/);function c(e){return null!==e&&e<-2}function f(e){return null!==e&&(e<0||32===e)}function d(e){return -2===e||-1===e||32===e}let p=m(/\p{P}|\p{S}/u),h=m(/\s/);function m(e){return function(t){return null!==t&&t>-1&&e.test(String.fromCharCode(t))}}},5009:(e,t,n)=>{"use strict";function r(e,t){let n=String(e);if("string"!=typeof t)throw TypeError("Expected character");let r=0,i=n.indexOf(t);for(;-1!==i;)r++,i=n.indexOf(t,i+t.length);return r}n.d(t,{D:()=>r})},5426:(e,t,n)=>{"use strict";function r(e,t,n){function r(n,r){var i;for(let o in Object.defineProperty(n,"_zod",{value:n._zod??{},enumerable:!1}),(i=n._zod).traits??(i.traits=new Set),n._zod.traits.add(e),t(n,r),a.prototype)o in n||Object.defineProperty(n,o,{value:a.prototype[o].bind(n)});n._zod.constr=a,n._zod.def=r}let i=n?.Parent??Object;class o extends i{}function a(e){var t;let i=n?.Parent?new o:this;for(let n of(r(i,e),(t=i._zod).deferred??(t.deferred=[]),i._zod.deferred))n();return i}return Object.defineProperty(o,"name",{value:e}),Object.defineProperty(a,"init",{value:r}),Object.defineProperty(a,Symbol.hasInstance,{value:t=>!!n?.Parent&&t instanceof n.Parent||t?._zod?.traits?.has(e)}),Object.defineProperty(a,"name",{value:e}),a}n.d(t,{$W:()=>a,GT:()=>i,cr:()=>o,xI:()=>r}),Object.freeze({status:"aborted"}),Symbol("zod_brand");class i extends Error{constructor(){super("Encountered Promise during synchronous parse. Use .parseAsync() instead.")}}let o={};function a(e){return e&&Object.assign(o,e),o}},9038:(e,t,n)=>{"use strict";function r(e){return"&#x"+e.toString(16).toUpperCase()+";"}n.d(t,{T:()=>r})},14487:(e,t,n)=>{"use strict";n.d(t,{A:()=>p});var r=n(22342),i=n(1481),o=n(99987),a=n(75889),s=n(13481),l=n(33670);let u="size-5 -me-0.5 fill-(--callout-color) text-fd-card",c=(0,l.forwardRef)(({className:e,children:t,title:n,type:i="info",icon:o,...l},c)=>("warn"===i&&(i="warning"),"tip"===i&&(i="info"),(0,r.jsxs)("div",{ref:c,className:(0,a.QP)("flex gap-2 my-4 rounded-xl border bg-fd-card p-3 ps-1 text-sm text-fd-card-foreground shadow-md",e),...l,style:{"--callout-color":`var(--color-fd-${i}, var(--color-fd-muted))`,...l.style},children:[(0,r.jsx)("div",{role:"none",className:"w-0.5 bg-(--callout-color)/50 rounded-sm"}),o??({info:(0,r.jsx)(s.R2,{className:u}),warning:(0,r.jsx)(s.lW,{className:u}),error:(0,r.jsx)(s.RT,{className:u}),success:(0,r.jsx)(s.rW,{className:u})})[i],(0,r.jsxs)("div",{className:"flex flex-col gap-2 min-w-0 flex-1",children:[n&&(0,r.jsx)("p",{className:"font-medium !my-0",children:n}),(0,r.jsx)("div",{className:"text-fd-muted-foreground prose-no-margin empty:hidden",children:t})]})]})));function f({as:e,className:t,...n}){let i=e??"h1";return n.id?(0,r.jsxs)(i,{className:(0,a.QP)("flex scroll-m-28 flex-row items-center gap-2",t),...n,children:[(0,r.jsx)("a",{"data-card":"",href:`#${n.id}`,className:"peer",children:n.children}),(0,r.jsx)(s.N_,{"aria-label":"Link to section",className:"size-3.5 shrink-0 text-fd-muted-foreground opacity-0 transition-opacity peer-hover:opacity-100"})]}):(0,r.jsx)(i,{className:t,...n})}c.displayName="Callout";var d=n(75580);let p={CodeBlockTab:d.CodeBlockTab,CodeBlockTabs:d.CodeBlockTabs,CodeBlockTabsList:d.CodeBlockTabsList,CodeBlockTabsTrigger:d.CodeBlockTabsTrigger,pre:e=>(0,r.jsx)(d.CodeBlock,{...e,children:(0,r.jsx)(d.Pre,{children:e.children})}),Card:function({icon:e,title:t,description:n,...o}){let s=o.href?i.default:"div";return(0,r.jsxs)(s,{...o,"data-card":!0,className:(0,a.QP)("block rounded-xl border bg-fd-card p-4 text-fd-card-foreground transition-colors @max-lg:col-span-full",o.href&&"hover:bg-fd-accent/80",o.className),children:[e?(0,r.jsx)("div",{className:"not-prose mb-2 w-fit shadow-md rounded-lg border bg-fd-muted p-1.5 text-fd-muted-foreground [&_svg]:size-4",children:e}):null,(0,r.jsx)("h3",{className:"not-prose mb-1 text-sm font-medium",children:t}),n?(0,r.jsx)("p",{className:"!my-0 text-sm text-fd-muted-foreground",children:n}):null,(0,r.jsx)("div",{className:"text-sm text-fd-muted-foreground prose-no-margin empty:hidden",children:o.children})]})},Cards:function(e){return(0,r.jsx)("div",{...e,className:(0,a.QP)("grid grid-cols-2 gap-3 @container",e.className),children:e.children})},a:i.default,img:function(e){return(0,r.jsx)(o.Image,{sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 70vw, 900px",...e,src:e.src,className:(0,a.QP)("rounded-lg",e.className)})},h1:e=>(0,r.jsx)(f,{as:"h1",...e}),h2:e=>(0,r.jsx)(f,{as:"h2",...e}),h3:e=>(0,r.jsx)(f,{as:"h3",...e}),h4:e=>(0,r.jsx)(f,{as:"h4",...e}),h5:e=>(0,r.jsx)(f,{as:"h5",...e}),h6:e=>(0,r.jsx)(f,{as:"h6",...e}),table:function(e){return(0,r.jsx)("div",{className:"relative overflow-auto prose-no-margin my-6",children:(0,r.jsx)("table",{...e})})},Callout:c}},17032:(e,t,n)=>{"use strict";function r(e,t,n,r){let i,o=e.length,a=0;if(t=t<0?-t>o?0:o+t:t>o?o:t,n=n>0?n:0,r.length<1e4)(i=Array.from(r)).unshift(t,n),e.splice(...i);else for(n&&e.splice(t,n);a<r.length;)(i=r.slice(a,a+1e4)).unshift(t,0),e.splice(...i),a+=1e4,t+=1e4}function i(e,t){return e.length>0?(r(e,e.length,0,t),e):t}n.d(t,{V:()=>i,m:()=>r})},18085:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(71847).A)("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]])},23362:(e,t,n)=>{"use strict";n.d(t,{S:()=>i});var r=n(3407);function i(e){return null===e||(0,r.Ee)(e)||(0,r.Ny)(e)?1:(0,r.es)(e)?2:void 0}},23978:(e,t,n)=>{"use strict";n.d(t,{fd:()=>i,rs:()=>r}),Symbol("ZodOutput"),Symbol("ZodInput");class r{constructor(){this._map=new Map,this._idmap=new Map}add(e,...t){let n=t[0];if(this._map.set(e,n),n&&"object"==typeof n&&"id"in n){if(this._idmap.has(n.id))throw Error(`ID ${n.id} already exists in the registry`);this._idmap.set(n.id,e)}return this}clear(){return this._map=new Map,this._idmap=new Map,this}remove(e){let t=this._map.get(e);return t&&"object"==typeof t&&"id"in t&&this._idmap.delete(t.id),this._map.delete(e),this}get(e){let t=e._zod.parent;if(t){let n={...this.get(t)??{}};delete n.id;let r={...n,...this._map.get(e)};return Object.keys(r).length?r:void 0}return this._map.get(e)}has(e){return this._map.has(e)}}let i=new r},25070:e=>{e.exports=function(e,t){let n;if("function"!=typeof e)throw TypeError(`Expected the first argument to be a \`function\`, got \`${typeof e}\`.`);let r=0;return function(...i){clearTimeout(n);let o=Date.now(),a=t-(o-r);a<=0?(r=o,e.apply(this,i)):n=setTimeout(()=>{r=Date.now(),e.apply(this,i)},a)}}},26713:(e,t,n)=>{"use strict";function r(e){let t=Object.values(e).filter(e=>"number"==typeof e);return Object.entries(e).filter(([e,n])=>-1===t.indexOf(+e)).map(([e,t])=>t)}function i(e,t){return"bigint"==typeof t?t.toString():t}function o(e){return{get value(){{let t=e();return Object.defineProperty(this,"value",{value:t}),t}}}}function a(e){return null==e}function s(e){let t=+!!e.startsWith("^"),n=e.endsWith("$")?e.length-1:e.length;return e.slice(t,n)}function l(e,t){let n=(e.toString().split(".")[1]||"").length,r=t.toString(),i=(r.split(".")[1]||"").length;if(0===i&&/\d?e-\d?/.test(r)){let e=r.match(/\d?e-(\d?)/);e?.[1]&&(i=Number.parseInt(e[1]))}let o=n>i?n:i;return Number.parseInt(e.toFixed(o).replace(".",""))%Number.parseInt(t.toFixed(o).replace(".",""))/10**o}n.d(t,{$f:()=>x,A2:()=>w,Gv:()=>g,LG:()=>l,NM:()=>z,OH:()=>C,PO:()=>o,QH:()=>O,Qd:()=>v,Rc:()=>M,UQ:()=>h,Up:()=>A,Vy:()=>d,X$:()=>P,ZV:()=>f,cJ:()=>I,cl:()=>a,gJ:()=>c,gx:()=>m,h1:()=>E,hI:()=>y,iR:()=>N,k8:()=>i,lQ:()=>j,mw:()=>T,o8:()=>_,p6:()=>s,qQ:()=>b,sn:()=>F,w5:()=>r,yG:()=>k,zH:()=>S});let u=Symbol("evaluating");function c(e,t,n){let r;Object.defineProperty(e,t,{get(){if(r!==u)return void 0===r&&(r=u,r=n()),r},set(n){Object.defineProperty(e,t,{value:n})},configurable:!0})}function f(e){return Object.create(Object.getPrototypeOf(e),Object.getOwnPropertyDescriptors(e))}function d(e,t,n){Object.defineProperty(e,t,{value:n,writable:!0,enumerable:!0,configurable:!0})}function p(...e){let t={};for(let n of e)Object.assign(t,Object.getOwnPropertyDescriptors(n));return Object.defineProperties({},t)}function h(e){return JSON.stringify(e)}let m="captureStackTrace"in Error?Error.captureStackTrace:(...e)=>{};function g(e){return"object"==typeof e&&null!==e&&!Array.isArray(e)}let y=o(()=>{if("undefined"!=typeof navigator&&navigator?.userAgent?.includes("Cloudflare"))return!1;try{return Function(""),!0}catch(e){return!1}});function v(e){if(!1===g(e))return!1;let t=e.constructor;if(void 0===t)return!0;let n=t.prototype;return!1!==g(n)&&!1!==Object.prototype.hasOwnProperty.call(n,"isPrototypeOf")}function k(e){return v(e)?{...e}:e}let b=new Set(["string","number","symbol"]);function x(e){return e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function _(e,t,n){let r=new e._zod.constr(t??e._zod.def);return(!t||n?.parent)&&(r._zod.parent=e),r}function w(e){if(!e)return{};if("string"==typeof e)return{error:()=>e};if(e?.message!==void 0){if(e?.error!==void 0)throw Error("Cannot specify both `message` and `error` params");e.error=e.message}return(delete e.message,"string"==typeof e.error)?{...e,error:()=>e.error}:e}function z(e){return Object.keys(e).filter(t=>"optional"===e[t]._zod.optin&&"optional"===e[t]._zod.optout)}let S={safeint:[Number.MIN_SAFE_INTEGER,Number.MAX_SAFE_INTEGER],int32:[-0x80000000,0x7fffffff],uint32:[0,0xffffffff],float32:[-34028234663852886e22,34028234663852886e22],float64:[-Number.MAX_VALUE,Number.MAX_VALUE]};function A(e,t){let n=e._zod.def,r=p(e._zod.def,{get shape(){let e={};for(let r in t){if(!(r in n.shape))throw Error(`Unrecognized key: "${r}"`);t[r]&&(e[r]=n.shape[r])}return d(this,"shape",e),e},checks:[]});return _(e,r)}function I(e,t){let n=e._zod.def,r=p(e._zod.def,{get shape(){let r={...e._zod.def.shape};for(let e in t){if(!(e in n.shape))throw Error(`Unrecognized key: "${e}"`);t[e]&&delete r[e]}return d(this,"shape",r),r},checks:[]});return _(e,r)}function P(e,t){if(!v(t))throw Error("Invalid input to extend: expected a plain object");let n=p(e._zod.def,{get shape(){let n={...e._zod.def.shape,...t};return d(this,"shape",n),n},checks:[]});return _(e,n)}function E(e,t){let n=p(e._zod.def,{get shape(){let n={...e._zod.def.shape,...t._zod.def.shape};return d(this,"shape",n),n},get catchall(){return t._zod.def.catchall},checks:[]});return _(e,n)}function C(e,t,n){let r=p(t._zod.def,{get shape(){let r=t._zod.def.shape,i={...r};if(n)for(let t in n){if(!(t in r))throw Error(`Unrecognized key: "${t}"`);n[t]&&(i[t]=e?new e({type:"optional",innerType:r[t]}):r[t])}else for(let t in r)i[t]=e?new e({type:"optional",innerType:r[t]}):r[t];return d(this,"shape",i),i},checks:[]});return _(t,r)}function T(e,t,n){let r=p(t._zod.def,{get shape(){let r=t._zod.def.shape,i={...r};if(n)for(let t in n){if(!(t in i))throw Error(`Unrecognized key: "${t}"`);n[t]&&(i[t]=new e({type:"nonoptional",innerType:r[t]}))}else for(let t in r)i[t]=new e({type:"nonoptional",innerType:r[t]});return d(this,"shape",i),i},checks:[]});return _(t,r)}function O(e,t=0){for(let n=t;n<e.issues.length;n++)if(e.issues[n]?.continue!==!0)return!0;return!1}function j(e,t){return t.map(t=>(t.path??(t.path=[]),t.path.unshift(e),t))}function $(e){return"string"==typeof e?e:e?.message}function N(e,t,n){let r={...e,path:e.path??[]};return e.message||(r.message=$(e.inst?._zod.def?.error?.(e))??$(t?.error?.(e))??$(n.customError?.(e))??$(n.localeError?.(e))??"Invalid input"),delete r.inst,delete r.continue,t?.reportInput||delete r.input,r}function M(e){return Array.isArray(e)?"array":"string"==typeof e?"string":"unknown"}function F(...e){let[t,n,r]=e;return"string"==typeof t?{message:t,code:"custom",input:n,inst:r}:{...t}}},31219:(e,t,n)=>{"use strict";n.d(t,{EB:()=>ty,YO:()=>tK,K3:()=>t$,zM:()=>tV,Ie:()=>nv,gM:()=>t3,k5:()=>ne,Nl:()=>nk,RZ:()=>ng,eu:()=>nn,_H:()=>t1,Zm:()=>tQ,ch:()=>tU,ai:()=>tD,Ik:()=>tX,lq:()=>no,g1:()=>t7,re:()=>t0,Yj:()=>tg,KC:()=>t4,L5:()=>tW});var r=n(5426);let i=/^[cC][^\s-]{8,}$/,o=/^[0-9a-z]+$/,a=/^[0-9A-HJKMNP-TV-Za-hjkmnp-tv-z]{26}$/,s=/^[0-9a-vA-V]{20}$/,l=/^[A-Za-z0-9]{27}$/,u=/^[a-zA-Z0-9_-]{21}$/,c=/^P(?:(\d+W)|(?!.*W)(?=\d|T\d)(\d+Y)?(\d+M)?(\d+D)?(T(?=\d)(\d+H)?(\d+M)?(\d+([.,]\d+)?S)?)?)$/,f=/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12})$/,d=e=>e?RegExp(`^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-${e}[0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12})$`):/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-8][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}|00000000-0000-0000-0000-000000000000)$/,p=/^(?!\.)(?!.*\.\.)([A-Za-z0-9_'+\-\.]*)[A-Za-z0-9_+-]@([A-Za-z0-9][A-Za-z0-9\-]*\.)+[A-Za-z]{2,}$/,h=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,m=/^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::|([0-9a-fA-F]{1,4})?::([0-9a-fA-F]{1,4}:?){0,6})$/,g=/^((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/([0-9]|[1-2][0-9]|3[0-2])$/,y=/^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::|([0-9a-fA-F]{1,4})?::([0-9a-fA-F]{1,4}:?){0,6})\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,v=/^$|^(?:[0-9a-zA-Z+/]{4})*(?:(?:[0-9a-zA-Z+/]{2}==)|(?:[0-9a-zA-Z+/]{3}=))?$/,k=/^[A-Za-z0-9_-]*$/,b=/^(?=.{1,253}\.?$)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[-0-9a-zA-Z]{0,61}[0-9a-zA-Z])?)*\.?$/,x=/^\+(?:[0-9]){6,14}[0-9]$/,_="(?:(?:\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-(?:(?:0[13578]|1[02])-(?:0[1-9]|[12]\\d|3[01])|(?:0[469]|11)-(?:0[1-9]|[12]\\d|30)|(?:02)-(?:0[1-9]|1\\d|2[0-8])))",w=RegExp(`^${_}$`);function z(e){let t="(?:[01]\\d|2[0-3]):[0-5]\\d";return"number"==typeof e.precision?-1===e.precision?`${t}`:0===e.precision?`${t}:[0-5]\\d`:`${t}:[0-5]\\d\\.\\d{${e.precision}}`:`${t}(?::[0-5]\\d(?:\\.\\d+)?)?`}let S=/^\d+$/,A=/^-?\d+(?:\.\d+)?/i,I=/true|false/i,P=/null/i,E=/^[^A-Z]*$/,C=/^[^a-z]*$/;var T=n(26713);let O=r.xI("$ZodCheck",(e,t)=>{var n;e._zod??(e._zod={}),e._zod.def=t,(n=e._zod).onattach??(n.onattach=[])}),j={number:"number",bigint:"bigint",object:"date"},$=r.xI("$ZodCheckLessThan",(e,t)=>{O.init(e,t);let n=j[typeof t.value];e._zod.onattach.push(e=>{let n=e._zod.bag,r=(t.inclusive?n.maximum:n.exclusiveMaximum)??1/0;t.value<r&&(t.inclusive?n.maximum=t.value:n.exclusiveMaximum=t.value)}),e._zod.check=r=>{(t.inclusive?r.value<=t.value:r.value<t.value)||r.issues.push({origin:n,code:"too_big",maximum:t.value,input:r.value,inclusive:t.inclusive,inst:e,continue:!t.abort})}}),N=r.xI("$ZodCheckGreaterThan",(e,t)=>{O.init(e,t);let n=j[typeof t.value];e._zod.onattach.push(e=>{let n=e._zod.bag,r=(t.inclusive?n.minimum:n.exclusiveMinimum)??-1/0;t.value>r&&(t.inclusive?n.minimum=t.value:n.exclusiveMinimum=t.value)}),e._zod.check=r=>{(t.inclusive?r.value>=t.value:r.value>t.value)||r.issues.push({origin:n,code:"too_small",minimum:t.value,input:r.value,inclusive:t.inclusive,inst:e,continue:!t.abort})}}),M=r.xI("$ZodCheckMultipleOf",(e,t)=>{O.init(e,t),e._zod.onattach.push(e=>{var n;(n=e._zod.bag).multipleOf??(n.multipleOf=t.value)}),e._zod.check=n=>{if(typeof n.value!=typeof t.value)throw Error("Cannot mix number and bigint in multiple_of check.");("bigint"==typeof n.value?n.value%t.value===BigInt(0):0===T.LG(n.value,t.value))||n.issues.push({origin:typeof n.value,code:"not_multiple_of",divisor:t.value,input:n.value,inst:e,continue:!t.abort})}}),F=r.xI("$ZodCheckNumberFormat",(e,t)=>{O.init(e,t),t.format=t.format||"float64";let n=t.format?.includes("int"),r=n?"int":"number",[i,o]=T.zH[t.format];e._zod.onattach.push(e=>{let r=e._zod.bag;r.format=t.format,r.minimum=i,r.maximum=o,n&&(r.pattern=S)}),e._zod.check=a=>{let s=a.value;if(n){if(!Number.isInteger(s))return void a.issues.push({expected:r,format:t.format,code:"invalid_type",continue:!1,input:s,inst:e});if(!Number.isSafeInteger(s))return void(s>0?a.issues.push({input:s,code:"too_big",maximum:Number.MAX_SAFE_INTEGER,note:"Integers must be within the safe integer range.",inst:e,origin:r,continue:!t.abort}):a.issues.push({input:s,code:"too_small",minimum:Number.MIN_SAFE_INTEGER,note:"Integers must be within the safe integer range.",inst:e,origin:r,continue:!t.abort}))}s<i&&a.issues.push({origin:"number",input:s,code:"too_small",minimum:i,inclusive:!0,inst:e,continue:!t.abort}),s>o&&a.issues.push({origin:"number",input:s,code:"too_big",maximum:o,inst:e})}}),Z=r.xI("$ZodCheckMaxLength",(e,t)=>{var n;O.init(e,t),(n=e._zod.def).when??(n.when=e=>{let t=e.value;return!T.cl(t)&&void 0!==t.length}),e._zod.onattach.push(e=>{let n=e._zod.bag.maximum??1/0;t.maximum<n&&(e._zod.bag.maximum=t.maximum)}),e._zod.check=n=>{let r=n.value;if(r.length<=t.maximum)return;let i=T.Rc(r);n.issues.push({origin:i,code:"too_big",maximum:t.maximum,inclusive:!0,input:r,inst:e,continue:!t.abort})}}),D=r.xI("$ZodCheckMinLength",(e,t)=>{var n;O.init(e,t),(n=e._zod.def).when??(n.when=e=>{let t=e.value;return!T.cl(t)&&void 0!==t.length}),e._zod.onattach.push(e=>{let n=e._zod.bag.minimum??-1/0;t.minimum>n&&(e._zod.bag.minimum=t.minimum)}),e._zod.check=n=>{let r=n.value;if(r.length>=t.minimum)return;let i=T.Rc(r);n.issues.push({origin:i,code:"too_small",minimum:t.minimum,inclusive:!0,input:r,inst:e,continue:!t.abort})}}),L=r.xI("$ZodCheckLengthEquals",(e,t)=>{var n;O.init(e,t),(n=e._zod.def).when??(n.when=e=>{let t=e.value;return!T.cl(t)&&void 0!==t.length}),e._zod.onattach.push(e=>{let n=e._zod.bag;n.minimum=t.length,n.maximum=t.length,n.length=t.length}),e._zod.check=n=>{let r=n.value,i=r.length;if(i===t.length)return;let o=T.Rc(r),a=i>t.length;n.issues.push({origin:o,...a?{code:"too_big",maximum:t.length}:{code:"too_small",minimum:t.length},inclusive:!0,exact:!0,input:n.value,inst:e,continue:!t.abort})}}),R=r.xI("$ZodCheckStringFormat",(e,t)=>{var n,r;O.init(e,t),e._zod.onattach.push(e=>{let n=e._zod.bag;n.format=t.format,t.pattern&&(n.patterns??(n.patterns=new Set),n.patterns.add(t.pattern))}),t.pattern?(n=e._zod).check??(n.check=n=>{t.pattern.lastIndex=0,t.pattern.test(n.value)||n.issues.push({origin:"string",code:"invalid_format",format:t.format,input:n.value,...t.pattern?{pattern:t.pattern.toString()}:{},inst:e,continue:!t.abort})}):(r=e._zod).check??(r.check=()=>{})}),B=r.xI("$ZodCheckRegex",(e,t)=>{R.init(e,t),e._zod.check=n=>{t.pattern.lastIndex=0,t.pattern.test(n.value)||n.issues.push({origin:"string",code:"invalid_format",format:"regex",input:n.value,pattern:t.pattern.toString(),inst:e,continue:!t.abort})}}),V=r.xI("$ZodCheckLowerCase",(e,t)=>{t.pattern??(t.pattern=E),R.init(e,t)}),H=r.xI("$ZodCheckUpperCase",(e,t)=>{t.pattern??(t.pattern=C),R.init(e,t)}),U=r.xI("$ZodCheckIncludes",(e,t)=>{O.init(e,t);let n=T.$f(t.includes),r=new RegExp("number"==typeof t.position?`^.{${t.position}}${n}`:n);t.pattern=r,e._zod.onattach.push(e=>{let t=e._zod.bag;t.patterns??(t.patterns=new Set),t.patterns.add(r)}),e._zod.check=n=>{n.value.includes(t.includes,t.position)||n.issues.push({origin:"string",code:"invalid_format",format:"includes",includes:t.includes,input:n.value,inst:e,continue:!t.abort})}}),J=r.xI("$ZodCheckStartsWith",(e,t)=>{O.init(e,t);let n=RegExp(`^${T.$f(t.prefix)}.*`);t.pattern??(t.pattern=n),e._zod.onattach.push(e=>{let t=e._zod.bag;t.patterns??(t.patterns=new Set),t.patterns.add(n)}),e._zod.check=n=>{n.value.startsWith(t.prefix)||n.issues.push({origin:"string",code:"invalid_format",format:"starts_with",prefix:t.prefix,input:n.value,inst:e,continue:!t.abort})}}),W=r.xI("$ZodCheckEndsWith",(e,t)=>{O.init(e,t);let n=RegExp(`.*${T.$f(t.suffix)}$`);t.pattern??(t.pattern=n),e._zod.onattach.push(e=>{let t=e._zod.bag;t.patterns??(t.patterns=new Set),t.patterns.add(n)}),e._zod.check=n=>{n.value.endsWith(t.suffix)||n.issues.push({origin:"string",code:"invalid_format",format:"ends_with",suffix:t.suffix,input:n.value,inst:e,continue:!t.abort})}}),q=r.xI("$ZodCheckOverwrite",(e,t)=>{O.init(e,t),e._zod.check=e=>{e.value=t.tx(e.value)}});class Q{constructor(e=[]){this.content=[],this.indent=0,this&&(this.args=e)}indented(e){this.indent+=1,e(this),this.indent-=1}write(e){if("function"==typeof e){e(this,{execution:"sync"}),e(this,{execution:"async"});return}let t=e.split("\n").filter(e=>e),n=Math.min(...t.map(e=>e.length-e.trimStart().length));for(let e of t.map(e=>e.slice(n)).map(e=>" ".repeat(2*this.indent)+e))this.content.push(e)}compile(){return Function(...this?.args,[...(this?.content??[""]).map(e=>`  ${e}`)].join("\n"))}}var G=n(48956);let K={major:4,minor:0,patch:17},Y=r.xI("$ZodType",(e,t)=>{var n;e??(e={}),e._zod.def=t,e._zod.bag=e._zod.bag||{},e._zod.version=K;let i=[...e._zod.def.checks??[]];for(let t of(e._zod.traits.has("$ZodCheck")&&i.unshift(e),i))for(let n of t._zod.onattach)n(e);if(0===i.length)(n=e._zod).deferred??(n.deferred=[]),e._zod.deferred?.push(()=>{e._zod.run=e._zod.parse});else{let t=(e,t,n)=>{let i,o=T.QH(e);for(let a of t){if(a._zod.def.when){if(!a._zod.def.when(e))continue}else if(o)continue;let t=e.issues.length,s=a._zod.check(e);if(s instanceof Promise&&n?.async===!1)throw new r.GT;if(i||s instanceof Promise)i=(i??Promise.resolve()).then(async()=>{await s,e.issues.length!==t&&(o||(o=T.QH(e,t)))});else{if(e.issues.length===t)continue;o||(o=T.QH(e,t))}}return i?i.then(()=>e):e};e._zod.run=(n,o)=>{let a=e._zod.parse(n,o);if(a instanceof Promise){if(!1===o.async)throw new r.GT;return a.then(e=>t(e,i,o))}return t(a,i,o)}}e["~standard"]={validate:t=>{try{let n=(0,G.xL)(e,t);return n.success?{value:n.data}:{issues:n.error?.issues}}catch(n){return(0,G.bp)(e,t).then(e=>e.success?{value:e.data}:{issues:e.error?.issues})}},vendor:"zod",version:1}}),X=r.xI("$ZodString",(e,t)=>{Y.init(e,t),e._zod.pattern=[...e?._zod.bag?.patterns??[]].pop()??(e=>{let t=e?`[\\s\\S]{${e?.minimum??0},${e?.maximum??""}}`:"[\\s\\S]*";return RegExp(`^${t}$`)})(e._zod.bag),e._zod.parse=(n,r)=>{if(t.coerce)try{n.value=String(n.value)}catch(e){}return"string"==typeof n.value||n.issues.push({expected:"string",code:"invalid_type",input:n.value,inst:e}),n}}),ee=r.xI("$ZodStringFormat",(e,t)=>{R.init(e,t),X.init(e,t)}),et=r.xI("$ZodGUID",(e,t)=>{t.pattern??(t.pattern=f),ee.init(e,t)}),en=r.xI("$ZodUUID",(e,t)=>{if(t.version){let e={v1:1,v2:2,v3:3,v4:4,v5:5,v6:6,v7:7,v8:8}[t.version];if(void 0===e)throw Error(`Invalid UUID version: "${t.version}"`);t.pattern??(t.pattern=d(e))}else t.pattern??(t.pattern=d());ee.init(e,t)}),er=r.xI("$ZodEmail",(e,t)=>{t.pattern??(t.pattern=p),ee.init(e,t)}),ei=r.xI("$ZodURL",(e,t)=>{ee.init(e,t),e._zod.check=n=>{try{let r=n.value.trim(),i=new URL(r);t.hostname&&(t.hostname.lastIndex=0,t.hostname.test(i.hostname)||n.issues.push({code:"invalid_format",format:"url",note:"Invalid hostname",pattern:b.source,input:n.value,inst:e,continue:!t.abort})),t.protocol&&(t.protocol.lastIndex=0,t.protocol.test(i.protocol.endsWith(":")?i.protocol.slice(0,-1):i.protocol)||n.issues.push({code:"invalid_format",format:"url",note:"Invalid protocol",pattern:t.protocol.source,input:n.value,inst:e,continue:!t.abort})),t.normalize?n.value=i.href:n.value=r;return}catch(r){n.issues.push({code:"invalid_format",format:"url",input:n.value,inst:e,continue:!t.abort})}}}),eo=r.xI("$ZodEmoji",(e,t)=>{t.pattern??(t.pattern=RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),ee.init(e,t)}),ea=r.xI("$ZodNanoID",(e,t)=>{t.pattern??(t.pattern=u),ee.init(e,t)}),es=r.xI("$ZodCUID",(e,t)=>{t.pattern??(t.pattern=i),ee.init(e,t)}),el=r.xI("$ZodCUID2",(e,t)=>{t.pattern??(t.pattern=o),ee.init(e,t)}),eu=r.xI("$ZodULID",(e,t)=>{t.pattern??(t.pattern=a),ee.init(e,t)}),ec=r.xI("$ZodXID",(e,t)=>{t.pattern??(t.pattern=s),ee.init(e,t)}),ef=r.xI("$ZodKSUID",(e,t)=>{t.pattern??(t.pattern=l),ee.init(e,t)}),ed=r.xI("$ZodISODateTime",(e,t)=>{t.pattern??(t.pattern=function(e){let t=z({precision:e.precision}),n=["Z"];e.local&&n.push(""),e.offset&&n.push("([+-](?:[01]\\d|2[0-3]):[0-5]\\d)");let r=`${t}(?:${n.join("|")})`;return RegExp(`^${_}T(?:${r})$`)}(t)),ee.init(e,t)}),ep=r.xI("$ZodISODate",(e,t)=>{t.pattern??(t.pattern=w),ee.init(e,t)}),eh=r.xI("$ZodISOTime",(e,t)=>{t.pattern??(t.pattern=RegExp(`^${z(t)}$`)),ee.init(e,t)}),em=r.xI("$ZodISODuration",(e,t)=>{t.pattern??(t.pattern=c),ee.init(e,t)}),eg=r.xI("$ZodIPv4",(e,t)=>{t.pattern??(t.pattern=h),ee.init(e,t),e._zod.onattach.push(e=>{e._zod.bag.format="ipv4"})}),ey=r.xI("$ZodIPv6",(e,t)=>{t.pattern??(t.pattern=m),ee.init(e,t),e._zod.onattach.push(e=>{e._zod.bag.format="ipv6"}),e._zod.check=n=>{try{new URL(`http://[${n.value}]`)}catch{n.issues.push({code:"invalid_format",format:"ipv6",input:n.value,inst:e,continue:!t.abort})}}}),ev=r.xI("$ZodCIDRv4",(e,t)=>{t.pattern??(t.pattern=g),ee.init(e,t)}),ek=r.xI("$ZodCIDRv6",(e,t)=>{t.pattern??(t.pattern=y),ee.init(e,t),e._zod.check=n=>{let[r,i]=n.value.split("/");try{if(!i)throw Error();let e=Number(i);if(`${e}`!==i||e<0||e>128)throw Error();new URL(`http://[${r}]`)}catch{n.issues.push({code:"invalid_format",format:"cidrv6",input:n.value,inst:e,continue:!t.abort})}}});function eb(e){if(""===e)return!0;if(e.length%4!=0)return!1;try{return atob(e),!0}catch{return!1}}let ex=r.xI("$ZodBase64",(e,t)=>{t.pattern??(t.pattern=v),ee.init(e,t),e._zod.onattach.push(e=>{e._zod.bag.contentEncoding="base64"}),e._zod.check=n=>{eb(n.value)||n.issues.push({code:"invalid_format",format:"base64",input:n.value,inst:e,continue:!t.abort})}}),e_=r.xI("$ZodBase64URL",(e,t)=>{t.pattern??(t.pattern=k),ee.init(e,t),e._zod.onattach.push(e=>{e._zod.bag.contentEncoding="base64url"}),e._zod.check=n=>{!function(e){if(!k.test(e))return!1;let t=e.replace(/[-_]/g,e=>"-"===e?"+":"/");return eb(t.padEnd(4*Math.ceil(t.length/4),"="))}(n.value)&&n.issues.push({code:"invalid_format",format:"base64url",input:n.value,inst:e,continue:!t.abort})}}),ew=r.xI("$ZodE164",(e,t)=>{t.pattern??(t.pattern=x),ee.init(e,t)}),ez=r.xI("$ZodJWT",(e,t)=>{ee.init(e,t),e._zod.check=n=>{!function(e,t=null){try{let n=e.split(".");if(3!==n.length)return!1;let[r]=n;if(!r)return!1;let i=JSON.parse(atob(r));if("typ"in i&&i?.typ!=="JWT"||!i.alg||t&&(!("alg"in i)||i.alg!==t))return!1;return!0}catch{return!1}}(n.value,t.alg)&&n.issues.push({code:"invalid_format",format:"jwt",input:n.value,inst:e,continue:!t.abort})}}),eS=r.xI("$ZodNumber",(e,t)=>{Y.init(e,t),e._zod.pattern=e._zod.bag.pattern??A,e._zod.parse=(n,r)=>{if(t.coerce)try{n.value=Number(n.value)}catch(e){}let i=n.value;if("number"==typeof i&&!Number.isNaN(i)&&Number.isFinite(i))return n;let o="number"==typeof i?Number.isNaN(i)?"NaN":Number.isFinite(i)?void 0:"Infinity":void 0;return n.issues.push({expected:"number",code:"invalid_type",input:i,inst:e,...o?{received:o}:{}}),n}}),eA=r.xI("$ZodNumber",(e,t)=>{F.init(e,t),eS.init(e,t)}),eI=r.xI("$ZodBoolean",(e,t)=>{Y.init(e,t),e._zod.pattern=I,e._zod.parse=(n,r)=>{if(t.coerce)try{n.value=!!n.value}catch(e){}let i=n.value;return"boolean"==typeof i||n.issues.push({expected:"boolean",code:"invalid_type",input:i,inst:e}),n}}),eP=r.xI("$ZodNull",(e,t)=>{Y.init(e,t),e._zod.pattern=P,e._zod.values=new Set([null]),e._zod.parse=(t,n)=>{let r=t.value;return null===r||t.issues.push({expected:"null",code:"invalid_type",input:r,inst:e}),t}}),eE=r.xI("$ZodUnknown",(e,t)=>{Y.init(e,t),e._zod.parse=e=>e}),eC=r.xI("$ZodNever",(e,t)=>{Y.init(e,t),e._zod.parse=(t,n)=>(t.issues.push({expected:"never",code:"invalid_type",input:t.value,inst:e}),t)});function eT(e,t,n){e.issues.length&&t.issues.push(...T.lQ(n,e.issues)),t.value[n]=e.value}let eO=r.xI("$ZodArray",(e,t)=>{Y.init(e,t),e._zod.parse=(n,r)=>{let i=n.value;if(!Array.isArray(i))return n.issues.push({expected:"array",code:"invalid_type",input:i,inst:e}),n;n.value=Array(i.length);let o=[];for(let e=0;e<i.length;e++){let a=i[e],s=t.element._zod.run({value:a,issues:[]},r);s instanceof Promise?o.push(s.then(t=>eT(t,n,e))):eT(s,n,e)}return o.length?Promise.all(o).then(()=>n):n}});function ej(e,t,n,r){e.issues.length&&t.issues.push(...T.lQ(n,e.issues)),void 0===e.value?n in r&&(t.value[n]=void 0):t.value[n]=e.value}let e$=r.xI("$ZodObject",(e,t)=>{let n,i;Y.init(e,t);let o=T.PO(()=>{let e=Object.keys(t.shape);for(let n of e)if(!t.shape[n]._zod.traits.has("$ZodType"))throw Error(`Invalid element at key "${n}": expected a Zod schema`);let n=T.NM(t.shape);return{shape:t.shape,keys:e,keySet:new Set(e),numKeys:e.length,optionalKeys:new Set(n)}});T.gJ(e._zod,"propValues",()=>{let e=t.shape,n={};for(let t in e){let r=e[t]._zod;if(r.values)for(let e of(n[t]??(n[t]=new Set),r.values))n[t].add(e)}return n});let a=T.Gv,s=!r.cr.jitless,l=T.hI,u=s&&l.value,c=t.catchall;e._zod.parse=(r,l)=>{i??(i=o.value);let f=r.value;if(!a(f))return r.issues.push({expected:"object",code:"invalid_type",input:f,inst:e}),r;let d=[];if(s&&u&&l?.async===!1&&!0!==l.jitless)n||(n=(e=>{let t=new Q(["shape","payload","ctx"]),n=o.value,r=e=>{let t=T.UQ(e);return`shape[${t}]._zod.run({ value: input[${t}], issues: [] }, ctx)`};t.write("const input = payload.value;");let i=Object.create(null),a=0;for(let e of n.keys)i[e]=`key_${a++}`;for(let e of(t.write("const newResult = {}"),n.keys)){let n=i[e],o=T.UQ(e);t.write(`const ${n} = ${r(e)};`),t.write(`
        if (${n}.issues.length) {
          payload.issues = payload.issues.concat(${n}.issues.map(iss => ({
            ...iss,
            path: iss.path ? [${o}, ...iss.path] : [${o}]
          })));
        }
        
        if (${n}.value === undefined) {
          if (${o} in input) {
            newResult[${o}] = undefined;
          }
        } else {
          newResult[${o}] = ${n}.value;
        }
      `)}t.write("payload.value = newResult;"),t.write("return payload;");let s=t.compile();return(t,n)=>s(e,t,n)})(t.shape)),r=n(r,l);else{r.value={};let e=i.shape;for(let t of i.keys){let n=e[t]._zod.run({value:f[t],issues:[]},l);n instanceof Promise?d.push(n.then(e=>ej(e,r,t,f))):ej(n,r,t,f)}}if(!c)return d.length?Promise.all(d).then(()=>r):r;let p=[],h=i.keySet,m=c._zod,g=m.def.type;for(let e of Object.keys(f)){if(h.has(e))continue;if("never"===g){p.push(e);continue}let t=m.run({value:f[e],issues:[]},l);t instanceof Promise?d.push(t.then(t=>ej(t,r,e,f))):ej(t,r,e,f)}return(p.length&&r.issues.push({code:"unrecognized_keys",keys:p,input:f,inst:e}),d.length)?Promise.all(d).then(()=>r):r}});function eN(e,t,n,i){for(let n of e)if(0===n.issues.length)return t.value=n.value,t;let o=e.filter(e=>!T.QH(e));return 1===o.length?(t.value=o[0].value,o[0]):(t.issues.push({code:"invalid_union",input:t.value,inst:n,errors:e.map(e=>e.issues.map(e=>T.iR(e,i,r.$W())))}),t)}let eM=r.xI("$ZodUnion",(e,t)=>{Y.init(e,t),T.gJ(e._zod,"optin",()=>t.options.some(e=>"optional"===e._zod.optin)?"optional":void 0),T.gJ(e._zod,"optout",()=>t.options.some(e=>"optional"===e._zod.optout)?"optional":void 0),T.gJ(e._zod,"values",()=>{if(t.options.every(e=>e._zod.values))return new Set(t.options.flatMap(e=>Array.from(e._zod.values)))}),T.gJ(e._zod,"pattern",()=>{if(t.options.every(e=>e._zod.pattern)){let e=t.options.map(e=>e._zod.pattern);return RegExp(`^(${e.map(e=>T.p6(e.source)).join("|")})$`)}});let n=1===t.options.length,r=t.options[0]._zod.run;e._zod.parse=(i,o)=>{if(n)return r(i,o);let a=!1,s=[];for(let e of t.options){let t=e._zod.run({value:i.value,issues:[]},o);if(t instanceof Promise)s.push(t),a=!0;else{if(0===t.issues.length)return t;s.push(t)}}return a?Promise.all(s).then(t=>eN(t,i,e,o)):eN(s,i,e,o)}}),eF=r.xI("$ZodDiscriminatedUnion",(e,t)=>{eM.init(e,t);let n=e._zod.parse;T.gJ(e._zod,"propValues",()=>{let e={};for(let n of t.options){let r=n._zod.propValues;if(!r||0===Object.keys(r).length)throw Error(`Invalid discriminated union option at index "${t.options.indexOf(n)}"`);for(let[t,n]of Object.entries(r))for(let r of(e[t]||(e[t]=new Set),n))e[t].add(r)}return e});let r=T.PO(()=>{let e=t.options,n=new Map;for(let r of e){let e=r._zod.propValues?.[t.discriminator];if(!e||0===e.size)throw Error(`Invalid discriminated union option at index "${t.options.indexOf(r)}"`);for(let t of e){if(n.has(t))throw Error(`Duplicate discriminator value "${String(t)}"`);n.set(t,r)}}return n});e._zod.parse=(i,o)=>{let a=i.value;if(!T.Gv(a))return i.issues.push({code:"invalid_type",expected:"object",input:a,inst:e}),i;let s=r.value.get(a?.[t.discriminator]);return s?s._zod.run(i,o):t.unionFallback?n(i,o):(i.issues.push({code:"invalid_union",errors:[],note:"No matching discriminator",discriminator:t.discriminator,input:a,path:[t.discriminator],inst:e}),i)}}),eZ=r.xI("$ZodIntersection",(e,t)=>{Y.init(e,t),e._zod.parse=(e,n)=>{let r=e.value,i=t.left._zod.run({value:r,issues:[]},n),o=t.right._zod.run({value:r,issues:[]},n);return i instanceof Promise||o instanceof Promise?Promise.all([i,o]).then(([t,n])=>eD(e,t,n)):eD(e,i,o)}});function eD(e,t,n){if(t.issues.length&&e.issues.push(...t.issues),n.issues.length&&e.issues.push(...n.issues),T.QH(e))return e;let r=function e(t,n){if(t===n||t instanceof Date&&n instanceof Date&&+t==+n)return{valid:!0,data:t};if(T.Qd(t)&&T.Qd(n)){let r=Object.keys(n),i=Object.keys(t).filter(e=>-1!==r.indexOf(e)),o={...t,...n};for(let r of i){let i=e(t[r],n[r]);if(!i.valid)return{valid:!1,mergeErrorPath:[r,...i.mergeErrorPath]};o[r]=i.data}return{valid:!0,data:o}}if(Array.isArray(t)&&Array.isArray(n)){if(t.length!==n.length)return{valid:!1,mergeErrorPath:[]};let r=[];for(let i=0;i<t.length;i++){let o=e(t[i],n[i]);if(!o.valid)return{valid:!1,mergeErrorPath:[i,...o.mergeErrorPath]};r.push(o.data)}return{valid:!0,data:r}}return{valid:!1,mergeErrorPath:[]}}(t.value,n.value);if(!r.valid)throw Error(`Unmergable intersection. Error path: ${JSON.stringify(r.mergeErrorPath)}`);return e.value=r.data,e}let eL=r.xI("$ZodRecord",(e,t)=>{Y.init(e,t),e._zod.parse=(n,i)=>{let o=n.value;if(!T.Qd(o))return n.issues.push({expected:"record",code:"invalid_type",input:o,inst:e}),n;let a=[];if(t.keyType._zod.values){let r,s=t.keyType._zod.values;for(let e of(n.value={},s))if("string"==typeof e||"number"==typeof e||"symbol"==typeof e){let r=t.valueType._zod.run({value:o[e],issues:[]},i);r instanceof Promise?a.push(r.then(t=>{t.issues.length&&n.issues.push(...T.lQ(e,t.issues)),n.value[e]=t.value})):(r.issues.length&&n.issues.push(...T.lQ(e,r.issues)),n.value[e]=r.value)}for(let e in o)s.has(e)||(r=r??[]).push(e);r&&r.length>0&&n.issues.push({code:"unrecognized_keys",input:o,inst:e,keys:r})}else for(let s of(n.value={},Reflect.ownKeys(o))){if("__proto__"===s)continue;let l=t.keyType._zod.run({value:s,issues:[]},i);if(l instanceof Promise)throw Error("Async schemas not supported in object keys currently");if(l.issues.length){n.issues.push({code:"invalid_key",origin:"record",issues:l.issues.map(e=>T.iR(e,i,r.$W())),input:s,path:[s],inst:e}),n.value[l.value]=l.value;continue}let u=t.valueType._zod.run({value:o[s],issues:[]},i);u instanceof Promise?a.push(u.then(e=>{e.issues.length&&n.issues.push(...T.lQ(s,e.issues)),n.value[l.value]=e.value})):(u.issues.length&&n.issues.push(...T.lQ(s,u.issues)),n.value[l.value]=u.value)}return a.length?Promise.all(a).then(()=>n):n}}),eR=r.xI("$ZodEnum",(e,t)=>{Y.init(e,t);let n=T.w5(t.entries),r=new Set(n);e._zod.values=r,e._zod.pattern=RegExp(`^(${n.filter(e=>T.qQ.has(typeof e)).map(e=>"string"==typeof e?T.$f(e):e.toString()).join("|")})$`),e._zod.parse=(t,i)=>{let o=t.value;return r.has(o)||t.issues.push({code:"invalid_value",values:n,input:o,inst:e}),t}}),eB=r.xI("$ZodLiteral",(e,t)=>{if(Y.init(e,t),0===t.values.length)throw Error("Cannot create literal schema with no valid values");e._zod.values=new Set(t.values),e._zod.pattern=RegExp(`^(${t.values.map(e=>"string"==typeof e?T.$f(e):e?T.$f(e.toString()):String(e)).join("|")})$`),e._zod.parse=(n,r)=>{let i=n.value;return e._zod.values.has(i)||n.issues.push({code:"invalid_value",values:t.values,input:i,inst:e}),n}}),eV=r.xI("$ZodTransform",(e,t)=>{Y.init(e,t),e._zod.parse=(e,n)=>{let i=t.transform(e.value,e);if(n.async)return(i instanceof Promise?i:Promise.resolve(i)).then(t=>(e.value=t,e));if(i instanceof Promise)throw new r.GT;return e.value=i,e}});function eH(e,t){return e.issues.length&&void 0===t?{issues:[],value:void 0}:e}let eU=r.xI("$ZodOptional",(e,t)=>{Y.init(e,t),e._zod.optin="optional",e._zod.optout="optional",T.gJ(e._zod,"values",()=>t.innerType._zod.values?new Set([...t.innerType._zod.values,void 0]):void 0),T.gJ(e._zod,"pattern",()=>{let e=t.innerType._zod.pattern;return e?RegExp(`^(${T.p6(e.source)})?$`):void 0}),e._zod.parse=(e,n)=>{if("optional"===t.innerType._zod.optin){let r=t.innerType._zod.run(e,n);return r instanceof Promise?r.then(t=>eH(t,e.value)):eH(r,e.value)}return void 0===e.value?e:t.innerType._zod.run(e,n)}}),eJ=r.xI("$ZodNullable",(e,t)=>{Y.init(e,t),T.gJ(e._zod,"optin",()=>t.innerType._zod.optin),T.gJ(e._zod,"optout",()=>t.innerType._zod.optout),T.gJ(e._zod,"pattern",()=>{let e=t.innerType._zod.pattern;return e?RegExp(`^(${T.p6(e.source)}|null)$`):void 0}),T.gJ(e._zod,"values",()=>t.innerType._zod.values?new Set([...t.innerType._zod.values,null]):void 0),e._zod.parse=(e,n)=>null===e.value?e:t.innerType._zod.run(e,n)}),eW=r.xI("$ZodDefault",(e,t)=>{Y.init(e,t),e._zod.optin="optional",T.gJ(e._zod,"values",()=>t.innerType._zod.values),e._zod.parse=(e,n)=>{if(void 0===e.value)return e.value=t.defaultValue,e;let r=t.innerType._zod.run(e,n);return r instanceof Promise?r.then(e=>eq(e,t)):eq(r,t)}});function eq(e,t){return void 0===e.value&&(e.value=t.defaultValue),e}let eQ=r.xI("$ZodPrefault",(e,t)=>{Y.init(e,t),e._zod.optin="optional",T.gJ(e._zod,"values",()=>t.innerType._zod.values),e._zod.parse=(e,n)=>(void 0===e.value&&(e.value=t.defaultValue),t.innerType._zod.run(e,n))}),eG=r.xI("$ZodNonOptional",(e,t)=>{Y.init(e,t),T.gJ(e._zod,"values",()=>{let e=t.innerType._zod.values;return e?new Set([...e].filter(e=>void 0!==e)):void 0}),e._zod.parse=(n,r)=>{let i=t.innerType._zod.run(n,r);return i instanceof Promise?i.then(t=>eK(t,e)):eK(i,e)}});function eK(e,t){return e.issues.length||void 0!==e.value||e.issues.push({code:"invalid_type",expected:"nonoptional",input:e.value,inst:t}),e}let eY=r.xI("$ZodCatch",(e,t)=>{Y.init(e,t),T.gJ(e._zod,"optin",()=>t.innerType._zod.optin),T.gJ(e._zod,"optout",()=>t.innerType._zod.optout),T.gJ(e._zod,"values",()=>t.innerType._zod.values),e._zod.parse=(e,n)=>{let i=t.innerType._zod.run(e,n);return i instanceof Promise?i.then(i=>(e.value=i.value,i.issues.length&&(e.value=t.catchValue({...e,error:{issues:i.issues.map(e=>T.iR(e,n,r.$W()))},input:e.value}),e.issues=[]),e)):(e.value=i.value,i.issues.length&&(e.value=t.catchValue({...e,error:{issues:i.issues.map(e=>T.iR(e,n,r.$W()))},input:e.value}),e.issues=[]),e)}}),eX=r.xI("$ZodPipe",(e,t)=>{Y.init(e,t),T.gJ(e._zod,"values",()=>t.in._zod.values),T.gJ(e._zod,"optin",()=>t.in._zod.optin),T.gJ(e._zod,"optout",()=>t.out._zod.optout),T.gJ(e._zod,"propValues",()=>t.in._zod.propValues),e._zod.parse=(e,n)=>{let r=t.in._zod.run(e,n);return r instanceof Promise?r.then(e=>e0(e,t,n)):e0(r,t,n)}});function e0(e,t,n){return e.issues.length?e:t.out._zod.run({value:e.value,issues:e.issues},n)}let e1=r.xI("$ZodReadonly",(e,t)=>{Y.init(e,t),T.gJ(e._zod,"propValues",()=>t.innerType._zod.propValues),T.gJ(e._zod,"values",()=>t.innerType._zod.values),T.gJ(e._zod,"optin",()=>t.innerType._zod.optin),T.gJ(e._zod,"optout",()=>t.innerType._zod.optout),e._zod.parse=(e,n)=>{let r=t.innerType._zod.run(e,n);return r instanceof Promise?r.then(e2):e2(r)}});function e2(e){return e.value=Object.freeze(e.value),e}let e4=r.xI("$ZodLazy",(e,t)=>{Y.init(e,t),T.gJ(e._zod,"innerType",()=>t.getter()),T.gJ(e._zod,"pattern",()=>e._zod.innerType._zod.pattern),T.gJ(e._zod,"propValues",()=>e._zod.innerType._zod.propValues),T.gJ(e._zod,"optin",()=>e._zod.innerType._zod.optin??void 0),T.gJ(e._zod,"optout",()=>e._zod.innerType._zod.optout??void 0),e._zod.parse=(t,n)=>e._zod.innerType._zod.run(t,n)}),e9=r.xI("$ZodCustom",(e,t)=>{O.init(e,t),Y.init(e,t),e._zod.parse=(e,t)=>e,e._zod.check=n=>{let r=n.value,i=t.fn(r);if(i instanceof Promise)return i.then(t=>e3(t,n,r,e));e3(i,n,r,e)}});function e3(e,t,n,r){if(!e){let e={code:"custom",input:n,inst:r,path:[...r._zod.def.path??[]],continue:!r._zod.def.abort};r._zod.def.params&&(e.params=r._zod.def.params),t.issues.push(T.sn(e))}}var e6=n(23978);function e5(e,t){return new e({type:"string",format:"guid",check:"string_format",abort:!1,...T.A2(t)})}function e7(e,t){return new e({type:"string",format:"base64",check:"string_format",abort:!1,...T.A2(t)})}function e8(e,t){return new $({check:"less_than",...T.A2(t),value:e,inclusive:!1})}function te(e,t){return new $({check:"less_than",...T.A2(t),value:e,inclusive:!0})}function tt(e,t){return new N({check:"greater_than",...T.A2(t),value:e,inclusive:!1})}function tn(e,t){return new N({check:"greater_than",...T.A2(t),value:e,inclusive:!0})}function tr(e,t){return new M({check:"multiple_of",...T.A2(t),value:e})}function ti(e,t){return new Z({check:"max_length",...T.A2(t),maximum:e})}function to(e,t){return new D({check:"min_length",...T.A2(t),minimum:e})}function ta(e,t){return new L({check:"length_equals",...T.A2(t),length:e})}function ts(e){return new q({check:"overwrite",tx:e})}let tl=r.xI("ZodISODateTime",(e,t)=>{ed.init(e,t),ty.init(e,t)}),tu=r.xI("ZodISODate",(e,t)=>{ep.init(e,t),ty.init(e,t)}),tc=r.xI("ZodISOTime",(e,t)=>{eh.init(e,t),ty.init(e,t)}),tf=r.xI("ZodISODuration",(e,t)=>{em.init(e,t),ty.init(e,t)});var td=n(53024);let tp=r.xI("ZodType",(e,t)=>(Y.init(e,t),e.def=t,Object.defineProperty(e,"_def",{value:t}),e.check=(...n)=>e.clone({...t,checks:[...t.checks??[],...n.map(e=>"function"==typeof e?{_zod:{check:e,def:{check:"custom"},onattach:[]}}:e)]}),e.clone=(t,n)=>T.o8(e,t,n),e.brand=()=>e,e.register=(t,n)=>(t.add(e,n),e),e.parse=(t,n)=>td.qg(e,t,n,{callee:e.parse}),e.safeParse=(t,n)=>td.xL(e,t,n),e.parseAsync=async(t,n)=>td.EJ(e,t,n,{callee:e.parseAsync}),e.safeParseAsync=async(t,n)=>td.bp(e,t,n),e.spa=e.safeParseAsync,e.refine=(t,n)=>e.check(function(e,t={}){return new ny({type:"custom",check:"custom",fn:e,...T.A2(t)})}(t,n)),e.superRefine=t=>e.check(function(e){let t=function(e,t){let n=new O({check:"custom",...T.A2(void 0)});return n._zod.check=e,n}(n=>(n.addIssue=e=>{"string"==typeof e?n.issues.push(T.sn(e,n.value,t._zod.def)):(e.fatal&&(e.continue=!1),e.code??(e.code="custom"),e.input??(e.input=n.value),e.inst??(e.inst=t),e.continue??(e.continue=!t._zod.def.abort),n.issues.push(T.sn(e)))},e(n.value,n)));return t}(t)),e.overwrite=t=>e.check(ts(t)),e.optional=()=>no(e),e.nullable=()=>ns(e),e.nullish=()=>no(ns(e)),e.nonoptional=t=>{var n,r;return n=e,r=t,new nc({type:"nonoptional",innerType:n,...T.A2(r)})},e.array=()=>tK(e),e.or=t=>t4([e,t]),e.and=t=>new t6({type:"intersection",left:e,right:t}),e.transform=t=>np(e,new nr({type:"transform",transform:t})),e.default=t=>(function(e,t){return new nl({type:"default",innerType:e,get defaultValue(){return"function"==typeof t?t():T.yG(t)}})})(e,t),e.prefault=t=>(function(e,t){return new nu({type:"prefault",innerType:e,get defaultValue(){return"function"==typeof t?t():T.yG(t)}})})(e,t),e.catch=t=>(function(e,t){return new nf({type:"catch",innerType:e,catchValue:"function"==typeof t?t:()=>t})})(e,t),e.pipe=t=>np(e,t),e.readonly=()=>new nh({type:"readonly",innerType:e}),e.describe=t=>{let n=e.clone();return e6.fd.add(n,{description:t}),n},Object.defineProperty(e,"description",{get:()=>e6.fd.get(e)?.description,configurable:!0}),e.meta=(...t)=>{if(0===t.length)return e6.fd.get(e);let n=e.clone();return e6.fd.add(n,t[0]),n},e.isOptional=()=>e.safeParse(void 0).success,e.isNullable=()=>e.safeParse(null).success,e)),th=r.xI("_ZodString",(e,t)=>{X.init(e,t),tp.init(e,t);let n=e._zod.bag;e.format=n.format??null,e.minLength=n.minimum??null,e.maxLength=n.maximum??null,e.regex=(...t)=>e.check(function(e,t){return new B({check:"string_format",format:"regex",...T.A2(t),pattern:e})}(...t)),e.includes=(...t)=>e.check(function(e,t){return new U({check:"string_format",format:"includes",...T.A2(t),includes:e})}(...t)),e.startsWith=(...t)=>e.check(function(e,t){return new J({check:"string_format",format:"starts_with",...T.A2(t),prefix:e})}(...t)),e.endsWith=(...t)=>e.check(function(e,t){return new W({check:"string_format",format:"ends_with",...T.A2(t),suffix:e})}(...t)),e.min=(...t)=>e.check(to(...t)),e.max=(...t)=>e.check(ti(...t)),e.length=(...t)=>e.check(ta(...t)),e.nonempty=(...t)=>e.check(to(1,...t)),e.lowercase=t=>e.check(new V({check:"string_format",format:"lowercase",...T.A2(t)})),e.uppercase=t=>e.check(new H({check:"string_format",format:"uppercase",...T.A2(t)})),e.trim=()=>e.check(ts(e=>e.trim())),e.normalize=(...t)=>e.check(function(e){return ts(t=>t.normalize(e))}(...t)),e.toLowerCase=()=>e.check(ts(e=>e.toLowerCase())),e.toUpperCase=()=>e.check(ts(e=>e.toUpperCase()))}),tm=r.xI("ZodString",(e,t)=>{X.init(e,t),th.init(e,t),e.email=t=>e.check(new tv({type:"string",format:"email",check:"string_format",abort:!1,...T.A2(t)})),e.url=t=>e.check(new tx({type:"string",format:"url",check:"string_format",abort:!1,...T.A2(t)})),e.jwt=t=>e.check(new tF({type:"string",format:"jwt",check:"string_format",abort:!1,...T.A2(t)})),e.emoji=t=>e.check(new t_({type:"string",format:"emoji",check:"string_format",abort:!1,...T.A2(t)})),e.guid=t=>e.check(e5(tk,t)),e.uuid=t=>e.check(new tb({type:"string",format:"uuid",check:"string_format",abort:!1,...T.A2(t)})),e.uuidv4=t=>e.check(new tb({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v4",...T.A2(t)})),e.uuidv6=t=>e.check(new tb({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v6",...T.A2(t)})),e.uuidv7=t=>e.check(new tb({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v7",...T.A2(t)})),e.nanoid=t=>e.check(new tw({type:"string",format:"nanoid",check:"string_format",abort:!1,...T.A2(t)})),e.guid=t=>e.check(e5(tk,t)),e.cuid=t=>e.check(new tz({type:"string",format:"cuid",check:"string_format",abort:!1,...T.A2(t)})),e.cuid2=t=>e.check(new tS({type:"string",format:"cuid2",check:"string_format",abort:!1,...T.A2(t)})),e.ulid=t=>e.check(new tA({type:"string",format:"ulid",check:"string_format",abort:!1,...T.A2(t)})),e.base64=t=>e.check(e7(tj,t)),e.base64url=t=>e.check(new tN({type:"string",format:"base64url",check:"string_format",abort:!1,...T.A2(t)})),e.xid=t=>e.check(new tI({type:"string",format:"xid",check:"string_format",abort:!1,...T.A2(t)})),e.ksuid=t=>e.check(new tP({type:"string",format:"ksuid",check:"string_format",abort:!1,...T.A2(t)})),e.ipv4=t=>e.check(new tE({type:"string",format:"ipv4",check:"string_format",abort:!1,...T.A2(t)})),e.ipv6=t=>e.check(new tC({type:"string",format:"ipv6",check:"string_format",abort:!1,...T.A2(t)})),e.cidrv4=t=>e.check(new tT({type:"string",format:"cidrv4",check:"string_format",abort:!1,...T.A2(t)})),e.cidrv6=t=>e.check(new tO({type:"string",format:"cidrv6",check:"string_format",abort:!1,...T.A2(t)})),e.e164=t=>e.check(new tM({type:"string",format:"e164",check:"string_format",abort:!1,...T.A2(t)})),e.datetime=t=>e.check(function(e){return new tl({type:"string",format:"datetime",check:"string_format",offset:!1,local:!1,precision:null,...T.A2(e)})}(t)),e.date=t=>e.check(function(e){return new tu({type:"string",format:"date",check:"string_format",...T.A2(e)})}(t)),e.time=t=>e.check(function(e){return new tc({type:"string",format:"time",check:"string_format",precision:null,...T.A2(e)})}(t)),e.duration=t=>e.check(function(e){return new tf({type:"string",format:"duration",check:"string_format",...T.A2(e)})}(t))});function tg(e){return new tm({type:"string",...T.A2(e)})}let ty=r.xI("ZodStringFormat",(e,t)=>{ee.init(e,t),th.init(e,t)}),tv=r.xI("ZodEmail",(e,t)=>{er.init(e,t),ty.init(e,t)}),tk=r.xI("ZodGUID",(e,t)=>{et.init(e,t),ty.init(e,t)}),tb=r.xI("ZodUUID",(e,t)=>{en.init(e,t),ty.init(e,t)}),tx=r.xI("ZodURL",(e,t)=>{ei.init(e,t),ty.init(e,t)}),t_=r.xI("ZodEmoji",(e,t)=>{eo.init(e,t),ty.init(e,t)}),tw=r.xI("ZodNanoID",(e,t)=>{ea.init(e,t),ty.init(e,t)}),tz=r.xI("ZodCUID",(e,t)=>{es.init(e,t),ty.init(e,t)}),tS=r.xI("ZodCUID2",(e,t)=>{el.init(e,t),ty.init(e,t)}),tA=r.xI("ZodULID",(e,t)=>{eu.init(e,t),ty.init(e,t)}),tI=r.xI("ZodXID",(e,t)=>{ec.init(e,t),ty.init(e,t)}),tP=r.xI("ZodKSUID",(e,t)=>{ef.init(e,t),ty.init(e,t)}),tE=r.xI("ZodIPv4",(e,t)=>{eg.init(e,t),ty.init(e,t)}),tC=r.xI("ZodIPv6",(e,t)=>{ey.init(e,t),ty.init(e,t)}),tT=r.xI("ZodCIDRv4",(e,t)=>{ev.init(e,t),ty.init(e,t)}),tO=r.xI("ZodCIDRv6",(e,t)=>{ek.init(e,t),ty.init(e,t)}),tj=r.xI("ZodBase64",(e,t)=>{ex.init(e,t),ty.init(e,t)});function t$(e){return e7(tj,e)}let tN=r.xI("ZodBase64URL",(e,t)=>{e_.init(e,t),ty.init(e,t)}),tM=r.xI("ZodE164",(e,t)=>{ew.init(e,t),ty.init(e,t)}),tF=r.xI("ZodJWT",(e,t)=>{ez.init(e,t),ty.init(e,t)}),tZ=r.xI("ZodNumber",(e,t)=>{eS.init(e,t),tp.init(e,t),e.gt=(t,n)=>e.check(tt(t,n)),e.gte=(t,n)=>e.check(tn(t,n)),e.min=(t,n)=>e.check(tn(t,n)),e.lt=(t,n)=>e.check(e8(t,n)),e.lte=(t,n)=>e.check(te(t,n)),e.max=(t,n)=>e.check(te(t,n)),e.int=t=>e.check(tR(t)),e.safe=t=>e.check(tR(t)),e.positive=t=>e.check(tt(0,t)),e.nonnegative=t=>e.check(tn(0,t)),e.negative=t=>e.check(e8(0,t)),e.nonpositive=t=>e.check(te(0,t)),e.multipleOf=(t,n)=>e.check(tr(t,n)),e.step=(t,n)=>e.check(tr(t,n)),e.finite=()=>e;let n=e._zod.bag;e.minValue=Math.max(n.minimum??-1/0,n.exclusiveMinimum??-1/0)??null,e.maxValue=Math.min(n.maximum??1/0,n.exclusiveMaximum??1/0)??null,e.isInt=(n.format??"").includes("int")||Number.isSafeInteger(n.multipleOf??.5),e.isFinite=!0,e.format=n.format??null});function tD(e){return new tZ({type:"number",checks:[],...T.A2(e)})}let tL=r.xI("ZodNumberFormat",(e,t)=>{eA.init(e,t),tZ.init(e,t)});function tR(e){return new tL({type:"number",check:"number_format",abort:!1,format:"safeint",...T.A2(e)})}let tB=r.xI("ZodBoolean",(e,t)=>{eI.init(e,t),tp.init(e,t)});function tV(e){return new tB({type:"boolean",...T.A2(e)})}let tH=r.xI("ZodNull",(e,t)=>{eP.init(e,t),tp.init(e,t)});function tU(e){return new tH({type:"null",...T.A2(e)})}let tJ=r.xI("ZodUnknown",(e,t)=>{eE.init(e,t),tp.init(e,t)});function tW(){return new tJ({type:"unknown"})}let tq=r.xI("ZodNever",(e,t)=>{eC.init(e,t),tp.init(e,t)});function tQ(e){return new tq({type:"never",...T.A2(e)})}let tG=r.xI("ZodArray",(e,t)=>{eO.init(e,t),tp.init(e,t),e.element=t.element,e.min=(t,n)=>e.check(to(t,n)),e.nonempty=t=>e.check(to(1,t)),e.max=(t,n)=>e.check(ti(t,n)),e.length=(t,n)=>e.check(ta(t,n)),e.unwrap=()=>e.element});function tK(e,t){return new tG({type:"array",element:e,...T.A2(t)})}let tY=r.xI("ZodObject",(e,t)=>{e$.init(e,t),tp.init(e,t),T.gJ(e,"shape",()=>t.shape),e.keyof=()=>ne(Object.keys(e._zod.def.shape)),e.catchall=t=>e.clone({...e._zod.def,catchall:t}),e.passthrough=()=>e.clone({...e._zod.def,catchall:tW()}),e.loose=()=>e.clone({...e._zod.def,catchall:tW()}),e.strict=()=>e.clone({...e._zod.def,catchall:tQ()}),e.strip=()=>e.clone({...e._zod.def,catchall:void 0}),e.extend=t=>T.X$(e,t),e.merge=t=>T.h1(e,t),e.pick=t=>T.Up(e,t),e.omit=t=>T.cJ(e,t),e.partial=(...t)=>T.OH(ni,e,t[0]),e.required=(...t)=>T.mw(nc,e,t[0])});function tX(e,t){return new tY({type:"object",get shape(){return T.Vy(this,"shape",e?T.ZV(e):{}),this.shape},...T.A2(t)})}function t0(e,t){return new tY({type:"object",get shape(){return T.Vy(this,"shape",T.ZV(e)),this.shape},catchall:tQ(),...T.A2(t)})}function t1(e,t){return new tY({type:"object",get shape(){return T.Vy(this,"shape",T.ZV(e)),this.shape},catchall:tW(),...T.A2(t)})}let t2=r.xI("ZodUnion",(e,t)=>{eM.init(e,t),tp.init(e,t),e.options=t.options});function t4(e,t){return new t2({type:"union",options:e,...T.A2(t)})}let t9=r.xI("ZodDiscriminatedUnion",(e,t)=>{t2.init(e,t),eF.init(e,t)});function t3(e,t,n){return new t9({type:"union",options:t,discriminator:e,...T.A2(n)})}let t6=r.xI("ZodIntersection",(e,t)=>{eZ.init(e,t),tp.init(e,t)}),t5=r.xI("ZodRecord",(e,t)=>{eL.init(e,t),tp.init(e,t),e.keyType=t.keyType,e.valueType=t.valueType});function t7(e,t,n){return new t5({type:"record",keyType:e,valueType:t,...T.A2(n)})}let t8=r.xI("ZodEnum",(e,t)=>{eR.init(e,t),tp.init(e,t),e.enum=t.entries,e.options=Object.values(t.entries);let n=new Set(Object.keys(t.entries));e.extract=(e,r)=>{let i={};for(let r of e)if(n.has(r))i[r]=t.entries[r];else throw Error(`Key ${r} not found in enum`);return new t8({...t,checks:[],...T.A2(r),entries:i})},e.exclude=(e,r)=>{let i={...t.entries};for(let t of e)if(n.has(t))delete i[t];else throw Error(`Key ${t} not found in enum`);return new t8({...t,checks:[],...T.A2(r),entries:i})}});function ne(e,t){return new t8({type:"enum",entries:Array.isArray(e)?Object.fromEntries(e.map(e=>[e,e])):e,...T.A2(t)})}let nt=r.xI("ZodLiteral",(e,t)=>{eB.init(e,t),tp.init(e,t),e.values=new Set(t.values),Object.defineProperty(e,"value",{get(){if(t.values.length>1)throw Error("This schema contains multiple valid literal values. Use `.values` instead.");return t.values[0]}})});function nn(e,t){return new nt({type:"literal",values:Array.isArray(e)?e:[e],...T.A2(t)})}let nr=r.xI("ZodTransform",(e,t)=>{eV.init(e,t),tp.init(e,t),e._zod.parse=(n,r)=>{n.addIssue=r=>{"string"==typeof r?n.issues.push(T.sn(r,n.value,t)):(r.fatal&&(r.continue=!1),r.code??(r.code="custom"),r.input??(r.input=n.value),r.inst??(r.inst=e),n.issues.push(T.sn(r)))};let i=t.transform(n.value,n);return i instanceof Promise?i.then(e=>(n.value=e,n)):(n.value=i,n)}}),ni=r.xI("ZodOptional",(e,t)=>{eU.init(e,t),tp.init(e,t),e.unwrap=()=>e._zod.def.innerType});function no(e){return new ni({type:"optional",innerType:e})}let na=r.xI("ZodNullable",(e,t)=>{eJ.init(e,t),tp.init(e,t),e.unwrap=()=>e._zod.def.innerType});function ns(e){return new na({type:"nullable",innerType:e})}let nl=r.xI("ZodDefault",(e,t)=>{eW.init(e,t),tp.init(e,t),e.unwrap=()=>e._zod.def.innerType,e.removeDefault=e.unwrap}),nu=r.xI("ZodPrefault",(e,t)=>{eQ.init(e,t),tp.init(e,t),e.unwrap=()=>e._zod.def.innerType}),nc=r.xI("ZodNonOptional",(e,t)=>{eG.init(e,t),tp.init(e,t),e.unwrap=()=>e._zod.def.innerType}),nf=r.xI("ZodCatch",(e,t)=>{eY.init(e,t),tp.init(e,t),e.unwrap=()=>e._zod.def.innerType,e.removeCatch=e.unwrap}),nd=r.xI("ZodPipe",(e,t)=>{eX.init(e,t),tp.init(e,t),e.in=t.in,e.out=t.out});function np(e,t){return new nd({type:"pipe",in:e,out:t})}let nh=r.xI("ZodReadonly",(e,t)=>{e1.init(e,t),tp.init(e,t),e.unwrap=()=>e._zod.def.innerType}),nm=r.xI("ZodLazy",(e,t)=>{e4.init(e,t),tp.init(e,t),e.unwrap=()=>e._zod.def.getter()});function ng(e){return new nm({type:"lazy",getter:e})}let ny=r.xI("ZodCustom",(e,t)=>{e9.init(e,t),tp.init(e,t)});function nv(e,t){let n=T.A2(t);return n.abort??(n.abort=!0),new ny({type:"custom",check:"custom",fn:e??(()=>!0),...n})}function nk(e,t={error:`Input not instance of ${e.name}`}){let n=new ny({type:"custom",check:"custom",fn:t=>t instanceof e,abort:!0,...T.A2(t)});return n._zod.bag.Class=e,n}},33634:(e,t,n)=>{"use strict";n.d(t,{B:()=>o});var r=n(79168),i=n(3407);let o={partial:!0,tokenize:function(e,t,n){return function(t){return(0,i.On)(t)?(0,r.N)(e,o,"linePrefix")(t):o(t)};function o(e){return null===e||(0,i.HP)(e)?t(e):n(e)}}}},35299:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(71847).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},35983:(e,t,n)=>{"use strict";function r(e){return e.replace(/[\t\n\r ]+/g," ").replace(/^ | $/g,"").toLowerCase().toUpperCase()}n.d(t,{B:()=>r})},38220:(e,t,n)=>{"use strict";n.d(t,{y:()=>o});var r=n(17032);let i={}.hasOwnProperty;function o(e){let t={},n=-1;for(;++n<e.length;)!function(e,t){let n;for(n in t){let o,a=(i.call(e,n)?e[n]:void 0)||(e[n]={}),s=t[n];if(s)for(o in s){i.call(a,o)||(a[o]=[]);let e=s[o];!function(e,t){let n=-1,i=[];for(;++n<t.length;)("after"===t[n].add?e:i).push(t[n]);(0,r.m)(e,0,0,i)}(a[o],Array.isArray(e)?e:e?[e]:[])}}}(t,e[n]);return t}},40674:(e,t,n)=>{"use strict";n.d(t,{Y_:()=>_});var r,i,o,a,s,l,u,c,f,d,p=n(33670),h=n(12507),m=n(25070),g=(e,t,n)=>{if(!t.has(e))throw TypeError("Cannot "+n)},y=(e,t,n)=>(g(e,t,"read from private field"),n?n.call(e):t.get(e)),v=(e,t,n)=>{if(t.has(e))throw TypeError("Cannot add the same private member more than once");t instanceof WeakSet?t.add(e):t.set(e,n)},k=(e,t,n,r)=>(g(e,t,"write to private field"),r?r.call(e,n):t.set(e,n),n),b=class{constructor(e=[]){v(this,r,void 0),v(this,i,"ready"),v(this,o,void 0),v(this,a,new Set),v(this,s,new Set),v(this,l,new Set),this.pushMessage=e=>{k(this,r,y(this,r).concat(e)),y(this,u).call(this)},this.popMessage=()=>{k(this,r,y(this,r).slice(0,-1)),y(this,u).call(this)},this.replaceMessage=(e,t)=>{k(this,r,[...y(this,r).slice(0,e),this.snapshot(t),...y(this,r).slice(e+1)]),y(this,u).call(this)},this.snapshot=e=>structuredClone(e),this["~registerMessagesCallback"]=(e,t)=>{let n=t?function(e,t){return null!=t?m(e,t):e}(e,t):e;return y(this,a).add(n),()=>{y(this,a).delete(n)}},this["~registerStatusCallback"]=e=>(y(this,s).add(e),()=>{y(this,s).delete(e)}),this["~registerErrorCallback"]=e=>(y(this,l).add(e),()=>{y(this,l).delete(e)}),v(this,u,()=>{y(this,a).forEach(e=>e())}),v(this,c,()=>{y(this,s).forEach(e=>e())}),v(this,f,()=>{y(this,l).forEach(e=>e())}),k(this,r,e)}get status(){return y(this,i)}set status(e){k(this,i,e),y(this,c).call(this)}get error(){return y(this,o)}set error(e){k(this,o,e),y(this,f).call(this)}get messages(){return y(this,r)}set messages(e){k(this,r,[...e]),y(this,u).call(this)}};r=new WeakMap,i=new WeakMap,o=new WeakMap,a=new WeakMap,s=new WeakMap,l=new WeakMap,u=new WeakMap,c=new WeakMap,f=new WeakMap;var x=class extends h.vl{constructor({messages:e,...t}){let n=new b(e);super({...t,state:n}),v(this,d,void 0),this["~registerMessagesCallback"]=(e,t)=>y(this,d)["~registerMessagesCallback"](e,t),this["~registerStatusCallback"]=e=>y(this,d)["~registerStatusCallback"](e),this["~registerErrorCallback"]=e=>y(this,d)["~registerErrorCallback"](e),k(this,d,n)}};function _({experimental_throttle:e,resume:t=!1,...n}={}){let r=(0,p.useRef)("chat"in n?n.chat:new x(n));("chat"in n&&n.chat!==r.current||"id"in n&&r.current.id!==n.id)&&(r.current="chat"in n?n.chat:new x(n));let i="id"in n?n.id:null,o=(0,p.useCallback)(t=>r.current["~registerMessagesCallback"](t,e),[e,i]),a=(0,p.useSyncExternalStore)(o,()=>r.current.messages,()=>r.current.messages),s=(0,p.useSyncExternalStore)(r.current["~registerStatusCallback"],()=>r.current.status,()=>r.current.status),l=(0,p.useSyncExternalStore)(r.current["~registerErrorCallback"],()=>r.current.error,()=>r.current.error),u=(0,p.useCallback)(e=>{"function"==typeof e&&(e=e(r.current.messages)),r.current.messages=e},[r]);return(0,p.useEffect)(()=>{t&&r.current.resumeStream()},[t,r]),{id:r.current.id,messages:a,setMessages:u,sendMessage:r.current.sendMessage,regenerate:r.current.regenerate,clearError:r.current.clearError,stop:r.current.stop,error:l,resumeStream:r.current.resumeStream,status:s,addToolResult:r.current.addToolResult}}d=new WeakMap},41006:(e,t,n)=>{"use strict";function r(e,t,n){let r=[],i=-1;for(;++i<e.length;){let o=e[i].resolveAll;o&&!r.includes(o)&&(t=o(t,n),r.push(o))}return t}n.d(t,{W:()=>r})},41954:(e,t,n)=>{"use strict";n.d(t,{JM:()=>l,Kd:()=>s,Wk:()=>u,a$:()=>a});var r=n(5426),i=n(26713);let o=(e,t)=>{e.name="$ZodError",Object.defineProperty(e,"_zod",{value:e._zod,enumerable:!1}),Object.defineProperty(e,"issues",{value:t,enumerable:!1}),e.message=JSON.stringify(t,i.k8,2),Object.defineProperty(e,"toString",{value:()=>e.message,enumerable:!1})},a=(0,r.xI)("$ZodError",o),s=(0,r.xI)("$ZodError",o,{Parent:Error});function l(e,t=e=>e.message){let n={},r=[];for(let i of e.issues)i.path.length>0?(n[i.path[0]]=n[i.path[0]]||[],n[i.path[0]].push(t(i))):r.push(t(i));return{formErrors:r,fieldErrors:n}}function u(e,t){let n=t||function(e){return e.message},r={_errors:[]},i=e=>{for(let t of e.issues)if("invalid_union"===t.code&&t.errors.length)t.errors.map(e=>i({issues:e}));else if("invalid_key"===t.code)i({issues:t.issues});else if("invalid_element"===t.code)i({issues:t.issues});else if(0===t.path.length)r._errors.push(n(t));else{let e=r,i=0;for(;i<t.path.length;){let r=t.path[i];i===t.path.length-1?(e[r]=e[r]||{_errors:[]},e[r]._errors.push(n(t))):e[r]=e[r]||{_errors:[]},e=e[r],i++}}};return i(e),r}},48411:(e,t,n)=>{"use strict";n.d(t,{f:()=>a});var r=n(95139),i=n(56522),o=n(84151);function a(e,t){let n=!1;return(0,r.YR)(e,function(e){if("value"in e&&/\r?\n|\r/.test(e.value)||"break"===e.type)return n=!0,i.dc}),!!((!e.depth||e.depth<3)&&(0,o.d)(e)&&(t.options.setext||n))}},48956:(e,t,n)=>{"use strict";n.d(t,{Od:()=>l,Rb:()=>s,Tj:()=>a,bp:()=>f,wG:()=>c,xL:()=>u});var r=n(5426),i=n(41954),o=n(26713);let a=e=>(t,n,i,a)=>{let s=i?Object.assign(i,{async:!1}):{async:!1},l=t._zod.run({value:n,issues:[]},s);if(l instanceof Promise)throw new r.GT;if(l.issues.length){let t=new(a?.Err??e)(l.issues.map(e=>o.iR(e,s,r.$W())));throw o.gx(t,a?.callee),t}return l.value};i.Kd;let s=e=>async(t,n,i,a)=>{let s=i?Object.assign(i,{async:!0}):{async:!0},l=t._zod.run({value:n,issues:[]},s);if(l instanceof Promise&&(l=await l),l.issues.length){let t=new(a?.Err??e)(l.issues.map(e=>o.iR(e,s,r.$W())));throw o.gx(t,a?.callee),t}return l.value};i.Kd;let l=e=>(t,n,a)=>{let s=a?{...a,async:!1}:{async:!1},l=t._zod.run({value:n,issues:[]},s);if(l instanceof Promise)throw new r.GT;return l.issues.length?{success:!1,error:new(e??i.a$)(l.issues.map(e=>o.iR(e,s,r.$W())))}:{success:!0,data:l.value}},u=l(i.Kd),c=e=>async(t,n,i)=>{let a=i?Object.assign(i,{async:!0}):{async:!0},s=t._zod.run({value:n,issues:[]},a);return s instanceof Promise&&(s=await s),s.issues.length?{success:!1,error:new e(s.issues.map(e=>o.iR(e,a,r.$W())))}:{success:!0,data:s.value}},f=c(i.Kd)},53024:(e,t,n)=>{"use strict";n.d(t,{qg:()=>u,EJ:()=>c,xL:()=>f,bp:()=>d});var r=n(48956),i=n(41954),o=n(5426),a=n(26713);let s=(e,t)=>{i.a$.init(e,t),e.name="ZodError",Object.defineProperties(e,{format:{value:t=>i.Wk(e,t)},flatten:{value:t=>i.JM(e,t)},addIssue:{value:t=>{e.issues.push(t),e.message=JSON.stringify(e.issues,a.k8,2)}},addIssues:{value:t=>{e.issues.push(...t),e.message=JSON.stringify(e.issues,a.k8,2)}},isEmpty:{get:()=>0===e.issues.length}})};o.xI("ZodError",s);let l=o.xI("ZodError",s,{Parent:Error}),u=r.Tj(l),c=r.Rb(l),f=r.Od(l),d=r.wG(l)},56522:(e,t,n)=>{"use strict";n.d(t,{dc:()=>o,VG:()=>a});var r=n(95502);let i=[],o=!1;function a(e,t,n,a){let s;"function"==typeof t&&"function"!=typeof n?(a=n,n=t):s=t;let l=(0,r.C)(s),u=a?-1:1;(function e(r,s,c){let f=r&&"object"==typeof r?r:{};if("string"==typeof f.type){let e="string"==typeof f.tagName?f.tagName:"string"==typeof f.name?f.name:void 0;Object.defineProperty(d,"name",{value:"node ("+r.type+(e?"<"+e+">":"")+")"})}return d;function d(){var f;let d,p,h,m=i;if((!t||l(r,s,c[c.length-1]||void 0))&&(m=Array.isArray(f=n(r,c))?f:"number"==typeof f?[!0,f]:null==f?i:[f])[0]===o)return m;if("children"in r&&r.children&&r.children&&"skip"!==m[0])for(p=(a?r.children.length:-1)+u,h=c.concat(r);p>-1&&p<r.children.length;){if((d=e(r.children[p],p,h)())[0]===o)return d;p="number"==typeof d[1]?d[1]:p+u}return m}})(e,void 0,[])()}},62737:(e,t,n)=>{"use strict";function r(e,t,n){return">"+(n?"":" ")+e}n.d(t,{p:()=>A});var i=n(91305);function o(e,t,n,r){let o=-1;for(;++o<n.unsafe.length;)if("\n"===n.unsafe[o].character&&(0,i.q)(n.stack,n.unsafe[o]))return/[ \t]/.test(r.before)?"":" ";return"\\\n"}var a=n(69512);function s(e,t,n){return(n?"":"    ")+e}function l(e){let t=e.options.quote||'"';if('"'!==t&&"'"!==t)throw Error("Cannot serialize title with `"+t+"` for `options.quote`, expected `\"`, or `'`");return t}var u=n(9038),c=n(23362);function f(e,t,n){let r=(0,c.S)(e),i=(0,c.S)(t);return void 0===r?void 0===i?"_"===n?{inside:!0,outside:!0}:{inside:!1,outside:!1}:1===i?{inside:!0,outside:!0}:{inside:!1,outside:!0}:1===r?void 0===i?{inside:!1,outside:!1}:1===i?{inside:!0,outside:!0}:{inside:!1,outside:!1}:void 0===i?{inside:!1,outside:!1}:1===i?{inside:!0,outside:!1}:{inside:!1,outside:!1}}function d(e,t,n,r){let i=function(e){let t=e.options.emphasis||"*";if("*"!==t&&"_"!==t)throw Error("Cannot serialize emphasis with `"+t+"` for `options.emphasis`, expected `*`, or `_`");return t}(n),o=n.enter("emphasis"),a=n.createTracker(r),s=a.move(i),l=a.move(n.containerPhrasing(e,{after:i,before:s,...a.current()})),c=l.charCodeAt(0),d=f(r.before.charCodeAt(r.before.length-1),c,i);d.inside&&(l=(0,u.T)(c)+l.slice(1));let p=l.charCodeAt(l.length-1),h=f(r.after.charCodeAt(0),p,i);h.inside&&(l=l.slice(0,-1)+(0,u.T)(p));let m=a.move(i);return o(),n.attentionEncodeSurroundingInfo={after:h.outside,before:d.outside},s+l+m}d.peek=function(e,t,n){return n.options.emphasis||"*"};var p=n(48411);function h(e){return e.value||""}function m(e,t,n,r){let i=l(n),o='"'===i?"Quote":"Apostrophe",a=n.enter("image"),s=n.enter("label"),u=n.createTracker(r),c=u.move("![");return c+=u.move(n.safe(e.alt,{before:c,after:"]",...u.current()})),c+=u.move("]("),s(),!e.url&&e.title||/[\0- \u007F]/.test(e.url)?(s=n.enter("destinationLiteral"),c+=u.move("<"),c+=u.move(n.safe(e.url,{before:c,after:">",...u.current()})),c+=u.move(">")):(s=n.enter("destinationRaw"),c+=u.move(n.safe(e.url,{before:c,after:e.title?" ":")",...u.current()}))),s(),e.title&&(s=n.enter(`title${o}`),c+=u.move(" "+i),c+=u.move(n.safe(e.title,{before:c,after:i,...u.current()})),c+=u.move(i),s()),c+=u.move(")"),a(),c}function g(e,t,n,r){let i=e.referenceType,o=n.enter("imageReference"),a=n.enter("label"),s=n.createTracker(r),l=s.move("!["),u=n.safe(e.alt,{before:l,after:"]",...s.current()});l+=s.move(u+"]["),a();let c=n.stack;n.stack=[],a=n.enter("reference");let f=n.safe(n.associationId(e),{before:l,after:"]",...s.current()});return a(),n.stack=c,o(),"full"!==i&&u&&u===f?"shortcut"===i?l=l.slice(0,-1):l+=s.move("]"):l+=s.move(f+"]"),l}function y(e,t,n){let r=e.value||"",i="`",o=-1;for(;RegExp("(^|[^`])"+i+"([^`]|$)").test(r);)i+="`";for(/[^ \r\n]/.test(r)&&(/^[ \r\n]/.test(r)&&/[ \r\n]$/.test(r)||/^`|`$/.test(r))&&(r=" "+r+" ");++o<n.unsafe.length;){let e,t=n.unsafe[o],i=n.compilePattern(t);if(t.atBreak)for(;e=i.exec(r);){let t=e.index;10===r.charCodeAt(t)&&13===r.charCodeAt(t-1)&&t--,r=r.slice(0,t)+" "+r.slice(e.index+1)}}return i+r+i}h.peek=function(){return"<"},m.peek=function(){return"!"},g.peek=function(){return"!"},y.peek=function(){return"`"};var v=n(84151);function k(e,t){let n=(0,v.d)(e);return!!(!t.options.resourceLink&&e.url&&!e.title&&e.children&&1===e.children.length&&"text"===e.children[0].type&&(n===e.url||"mailto:"+n===e.url)&&/^[a-z][a-z+.-]+:/i.test(e.url)&&!/[\0- <>\u007F]/.test(e.url))}function b(e,t,n,r){let i,o,a=l(n),s='"'===a?"Quote":"Apostrophe",u=n.createTracker(r);if(k(e,n)){let t=n.stack;n.stack=[],i=n.enter("autolink");let r=u.move("<");return r+=u.move(n.containerPhrasing(e,{before:r,after:">",...u.current()})),r+=u.move(">"),i(),n.stack=t,r}i=n.enter("link"),o=n.enter("label");let c=u.move("[");return c+=u.move(n.containerPhrasing(e,{before:c,after:"](",...u.current()})),c+=u.move("]("),o(),!e.url&&e.title||/[\0- \u007F]/.test(e.url)?(o=n.enter("destinationLiteral"),c+=u.move("<"),c+=u.move(n.safe(e.url,{before:c,after:">",...u.current()})),c+=u.move(">")):(o=n.enter("destinationRaw"),c+=u.move(n.safe(e.url,{before:c,after:e.title?" ":")",...u.current()}))),o(),e.title&&(o=n.enter(`title${s}`),c+=u.move(" "+a),c+=u.move(n.safe(e.title,{before:c,after:a,...u.current()})),c+=u.move(a),o()),c+=u.move(")"),i(),c}function x(e,t,n,r){let i=e.referenceType,o=n.enter("linkReference"),a=n.enter("label"),s=n.createTracker(r),l=s.move("["),u=n.containerPhrasing(e,{before:l,after:"]",...s.current()});l+=s.move(u+"]["),a();let c=n.stack;n.stack=[],a=n.enter("reference");let f=n.safe(n.associationId(e),{before:l,after:"]",...s.current()});return a(),n.stack=c,o(),"full"!==i&&u&&u===f?"shortcut"===i?l=l.slice(0,-1):l+=s.move("]"):l+=s.move(f+"]"),l}function _(e){let t=e.options.bullet||"*";if("*"!==t&&"+"!==t&&"-"!==t)throw Error("Cannot serialize items with `"+t+"` for `options.bullet`, expected `*`, `+`, or `-`");return t}function w(e){let t=e.options.rule||"*";if("*"!==t&&"-"!==t&&"_"!==t)throw Error("Cannot serialize rules with `"+t+"` for `options.rule`, expected `*`, `-`, or `_`");return t}b.peek=function(e,t,n){return k(e,n)?"<":"["},x.peek=function(){return"["};let z=(0,n(95502).C)(["break","delete","emphasis","footnote","footnoteReference","image","imageReference","inlineCode","inlineMath","link","linkReference","mdxJsxTextElement","mdxTextExpression","strong","text","textDirective"]);function S(e,t,n,r){let i=function(e){let t=e.options.strong||"*";if("*"!==t&&"_"!==t)throw Error("Cannot serialize strong with `"+t+"` for `options.strong`, expected `*`, or `_`");return t}(n),o=n.enter("strong"),a=n.createTracker(r),s=a.move(i+i),l=a.move(n.containerPhrasing(e,{after:i,before:s,...a.current()})),c=l.charCodeAt(0),d=f(r.before.charCodeAt(r.before.length-1),c,i);d.inside&&(l=(0,u.T)(c)+l.slice(1));let p=l.charCodeAt(l.length-1),h=f(r.after.charCodeAt(0),p,i);h.inside&&(l=l.slice(0,-1)+(0,u.T)(p));let m=a.move(i+i);return o(),n.attentionEncodeSurroundingInfo={after:h.outside,before:d.outside},s+l+m}S.peek=function(e,t,n){return n.options.strong||"*"};let A={blockquote:function(e,t,n,i){let o=n.enter("blockquote"),a=n.createTracker(i);a.move("> "),a.shift(2);let s=n.indentLines(n.containerFlow(e,a.current()),r);return o(),s},break:o,code:function(e,t,n,r){let i=function(e){let t=e.options.fence||"`";if("`"!==t&&"~"!==t)throw Error("Cannot serialize code with `"+t+"` for `options.fence`, expected `` ` `` or `~`");return t}(n),o=e.value||"",l="`"===i?"GraveAccent":"Tilde";if((0,a.m)(e,n)){let e=n.enter("codeIndented"),t=n.indentLines(o,s);return e(),t}let u=n.createTracker(r),c=i.repeat(Math.max(function(e,t){let n=String(e),r=n.indexOf(t),i=r,o=0,a=0;if("string"!=typeof t)throw TypeError("Expected substring");for(;-1!==r;)r===i?++o>a&&(a=o):o=1,i=r+t.length,r=n.indexOf(t,i);return a}(o,i)+1,3)),f=n.enter("codeFenced"),d=u.move(c);if(e.lang){let t=n.enter(`codeFencedLang${l}`);d+=u.move(n.safe(e.lang,{before:d,after:" ",encode:["`"],...u.current()})),t()}if(e.lang&&e.meta){let t=n.enter(`codeFencedMeta${l}`);d+=u.move(" "),d+=u.move(n.safe(e.meta,{before:d,after:"\n",encode:["`"],...u.current()})),t()}return d+=u.move("\n"),o&&(d+=u.move(o+"\n")),d+=u.move(c),f(),d},definition:function(e,t,n,r){let i=l(n),o='"'===i?"Quote":"Apostrophe",a=n.enter("definition"),s=n.enter("label"),u=n.createTracker(r),c=u.move("[");return c+=u.move(n.safe(n.associationId(e),{before:c,after:"]",...u.current()})),c+=u.move("]: "),s(),!e.url||/[\0- \u007F]/.test(e.url)?(s=n.enter("destinationLiteral"),c+=u.move("<"),c+=u.move(n.safe(e.url,{before:c,after:">",...u.current()})),c+=u.move(">")):(s=n.enter("destinationRaw"),c+=u.move(n.safe(e.url,{before:c,after:e.title?" ":"\n",...u.current()}))),s(),e.title&&(s=n.enter(`title${o}`),c+=u.move(" "+i),c+=u.move(n.safe(e.title,{before:c,after:i,...u.current()})),c+=u.move(i),s()),a(),c},emphasis:d,hardBreak:o,heading:function(e,t,n,r){let i=Math.max(Math.min(6,e.depth||1),1),o=n.createTracker(r);if((0,p.f)(e,n)){let t=n.enter("headingSetext"),r=n.enter("phrasing"),a=n.containerPhrasing(e,{...o.current(),before:"\n",after:"\n"});return r(),t(),a+"\n"+(1===i?"=":"-").repeat(a.length-(Math.max(a.lastIndexOf("\r"),a.lastIndexOf("\n"))+1))}let a="#".repeat(i),s=n.enter("headingAtx"),l=n.enter("phrasing");o.move(a+" ");let c=n.containerPhrasing(e,{before:"# ",after:"\n",...o.current()});return/^[\t ]/.test(c)&&(c=(0,u.T)(c.charCodeAt(0))+c.slice(1)),c=c?a+" "+c:a,n.options.closeAtx&&(c+=" "+a),l(),s(),c},html:h,image:m,imageReference:g,inlineCode:y,link:b,linkReference:x,list:function(e,t,n,r){let i=n.enter("list"),o=n.bulletCurrent,a=e.ordered?function(e){let t=e.options.bulletOrdered||".";if("."!==t&&")"!==t)throw Error("Cannot serialize items with `"+t+"` for `options.bulletOrdered`, expected `.` or `)`");return t}(n):_(n),s=e.ordered?"."===a?")":".":function(e){let t=_(e),n=e.options.bulletOther;if(!n)return"*"===t?"-":"*";if("*"!==n&&"+"!==n&&"-"!==n)throw Error("Cannot serialize items with `"+n+"` for `options.bulletOther`, expected `*`, `+`, or `-`");if(n===t)throw Error("Expected `bullet` (`"+t+"`) and `bulletOther` (`"+n+"`) to be different");return n}(n),l=!!t&&!!n.bulletLastUsed&&a===n.bulletLastUsed;if(!e.ordered){let t=e.children?e.children[0]:void 0;if("*"!==a&&"-"!==a||!t||t.children&&t.children[0]||"list"!==n.stack[n.stack.length-1]||"listItem"!==n.stack[n.stack.length-2]||"list"!==n.stack[n.stack.length-3]||"listItem"!==n.stack[n.stack.length-4]||0!==n.indexStack[n.indexStack.length-1]||0!==n.indexStack[n.indexStack.length-2]||0!==n.indexStack[n.indexStack.length-3]||(l=!0),w(n)===a&&t){let t=-1;for(;++t<e.children.length;){let n=e.children[t];if(n&&"listItem"===n.type&&n.children&&n.children[0]&&"thematicBreak"===n.children[0].type){l=!0;break}}}}l&&(a=s),n.bulletCurrent=a;let u=n.containerFlow(e,r);return n.bulletLastUsed=a,n.bulletCurrent=o,i(),u},listItem:function(e,t,n,r){let i=function(e){let t=e.options.listItemIndent||"one";if("tab"!==t&&"one"!==t&&"mixed"!==t)throw Error("Cannot serialize items with `"+t+"` for `options.listItemIndent`, expected `tab`, `one`, or `mixed`");return t}(n),o=n.bulletCurrent||_(n);t&&"list"===t.type&&t.ordered&&(o=("number"==typeof t.start&&t.start>-1?t.start:1)+(!1===n.options.incrementListMarker?0:t.children.indexOf(e))+o);let a=o.length+1;("tab"===i||"mixed"===i&&(t&&"list"===t.type&&t.spread||e.spread))&&(a=4*Math.ceil(a/4));let s=n.createTracker(r);s.move(o+" ".repeat(a-o.length)),s.shift(a);let l=n.enter("listItem"),u=n.indentLines(n.containerFlow(e,s.current()),function(e,t,n){return t?(n?"":" ".repeat(a))+e:(n?o:o+" ".repeat(a-o.length))+e});return l(),u},paragraph:function(e,t,n,r){let i=n.enter("paragraph"),o=n.enter("phrasing"),a=n.containerPhrasing(e,r);return o(),i(),a},root:function(e,t,n,r){return(e.children.some(function(e){return z(e)})?n.containerPhrasing:n.containerFlow).call(n,e,r)},strong:S,text:function(e,t,n,r){return n.safe(e.value,r)},thematicBreak:function(e,t,n){let r=(w(n)+(n.options.ruleSpaces?" ":"")).repeat(function(e){let t=e.options.ruleRepetition||3;if(t<3)throw Error("Cannot serialize rules with repetition `"+t+"` for `options.ruleRepetition`, expected `3` or more");return t}(n));return n.options.ruleSpaces?r.slice(0,-1):r}}},64876:(e,t,n)=>{"use strict";n.d(t,{Di:()=>y,b8:()=>$,bD:()=>d,eM:()=>S,iM:()=>C,u1:()=>p,u6:()=>x});var r,i,o,a,s,l,u="vercel.ai.error",c=Symbol.for(u),f=class e extends Error{constructor({name:e,message:t,cause:n}){super(t),this[r]=!0,this.name=e,this.cause=n}static isInstance(t){return e.hasMarker(t,u)}static hasMarker(e,t){let n=Symbol.for(t);return null!=e&&"object"==typeof e&&n in e&&"boolean"==typeof e[n]&&!0===e[n]}};r=c;var d=f;function p(e){return null==e?"unknown error":"string"==typeof e?e:e instanceof Error?e.message:JSON.stringify(e)}Symbol.for("vercel.ai.error.AI_APICallError"),Symbol.for("vercel.ai.error.AI_EmptyResponseBodyError");var h="AI_InvalidArgumentError",m=`vercel.ai.error.${h}`,g=Symbol.for(m),y=class extends d{constructor({message:e,cause:t,argument:n}){super({name:h,message:e,cause:t}),this[i]=!0,this.argument=n}static isInstance(e){return d.hasMarker(e,m)}};i=g,Symbol.for("vercel.ai.error.AI_InvalidPromptError"),Symbol.for("vercel.ai.error.AI_InvalidResponseDataError");var v="AI_JSONParseError",k=`vercel.ai.error.${v}`,b=Symbol.for(k),x=class extends d{constructor({text:e,cause:t}){super({name:v,message:`JSON parsing failed: Text: ${e}.
Error message: ${p(t)}`,cause:t}),this[o]=!0,this.text=e}static isInstance(e){return d.hasMarker(e,k)}};o=b,Symbol.for("vercel.ai.error.AI_LoadAPIKeyError"),Symbol.for("vercel.ai.error.AI_LoadSettingError"),Symbol.for("vercel.ai.error.AI_NoContentGeneratedError");var _="AI_NoSuchModelError",w=`vercel.ai.error.${_}`,z=Symbol.for(w),S=class extends d{constructor({errorName:e=_,modelId:t,modelType:n,message:r=`No such ${n}: ${t}`}){super({name:e,message:r}),this[a]=!0,this.modelId=t,this.modelType=n}static isInstance(e){return d.hasMarker(e,w)}};a=z,Symbol.for("vercel.ai.error.AI_TooManyEmbeddingValuesForCallError");var A="AI_TypeValidationError",I=`vercel.ai.error.${A}`,P=Symbol.for(I),E=class e extends d{constructor({value:e,cause:t}){super({name:A,message:`Type validation failed: Value: ${JSON.stringify(e)}.
Error message: ${p(t)}`,cause:t}),this[s]=!0,this.value=e}static isInstance(e){return d.hasMarker(e,I)}static wrap({value:t,cause:n}){return e.isInstance(n)&&n.value===t?n:new e({value:t,cause:n})}};s=P;var C=E,T="AI_UnsupportedFunctionalityError",O=`vercel.ai.error.${T}`,j=Symbol.for(O),$=class extends d{constructor({functionality:e,message:t=`'${e}' functionality not supported.`}){super({name:T,message:t}),this[l]=!0,this.functionality=e}static isInstance(e){return d.hasMarker(e,O)}};l=j},65229:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(71847).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},69512:(e,t,n)=>{"use strict";function r(e,t){return!!(!1===t.options.fences&&e.value&&!e.lang&&/[^ \r\n]/.test(e.value)&&!/^[\t ]*(?:[\r\n]|$)|(?:^|[\r\n])[\t ]*$/.test(e.value))}n.d(t,{m:()=>r})},70011:e=>{"use strict";var t=Object.prototype.hasOwnProperty,n=Object.prototype.toString,r=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=function(e){return"function"==typeof Array.isArray?Array.isArray(e):"[object Array]"===n.call(e)},a=function(e){if(!e||"[object Object]"!==n.call(e))return!1;var r,i=t.call(e,"constructor"),o=e.constructor&&e.constructor.prototype&&t.call(e.constructor.prototype,"isPrototypeOf");if(e.constructor&&!i&&!o)return!1;for(r in e);return void 0===r||t.call(e,r)},s=function(e,t){r&&"__proto__"===t.name?r(e,t.name,{enumerable:!0,configurable:!0,value:t.newValue,writable:!0}):e[t.name]=t.newValue},l=function(e,n){if("__proto__"===n){if(!t.call(e,n))return;else if(i)return i(e,n).value}return e[n]};e.exports=function e(){var t,n,r,i,u,c,f=arguments[0],d=1,p=arguments.length,h=!1;for("boolean"==typeof f&&(h=f,f=arguments[1]||{},d=2),(null==f||"object"!=typeof f&&"function"!=typeof f)&&(f={});d<p;++d)if(t=arguments[d],null!=t)for(n in t)r=l(f,n),f!==(i=l(t,n))&&(h&&i&&(a(i)||(u=o(i)))?(u?(u=!1,c=r&&o(r)?r:[]):c=r&&a(r)?r:{},s(f,{name:n,newValue:e(h,c,i)})):void 0!==i&&s(f,{name:n,newValue:i}));return f}},71824:(e,t,n)=>{"use strict";n.d(t,{A:()=>C});var r=n(63758);let i="object"==typeof self?self:globalThis,o=e=>((e,t)=>{let n=(t,n)=>(e.set(n,t),t),r=o=>{if(e.has(o))return e.get(o);let[a,s]=t[o];switch(a){case 0:case -1:return n(s,o);case 1:{let e=n([],o);for(let t of s)e.push(r(t));return e}case 2:{let e=n({},o);for(let[t,n]of s)e[r(t)]=r(n);return e}case 3:return n(new Date(s),o);case 4:{let{source:e,flags:t}=s;return n(new RegExp(e,t),o)}case 5:{let e=n(new Map,o);for(let[t,n]of s)e.set(r(t),r(n));return e}case 6:{let e=n(new Set,o);for(let t of s)e.add(r(t));return e}case 7:{let{name:e,message:t}=s;return n(new i[e](t),o)}case 8:return n(BigInt(s),o);case"BigInt":return n(Object(BigInt(s)),o);case"ArrayBuffer":return n(new Uint8Array(s).buffer,s);case"DataView":{let{buffer:e}=new Uint8Array(s);return n(new DataView(e),s)}}return n(new i[a](s),o)};return r})(new Map,e)(0),{toString:a}={},{keys:s}=Object,l=e=>{let t=typeof e;if("object"!==t||!e)return[0,t];let n=a.call(e).slice(8,-1);switch(n){case"Array":return[1,""];case"Object":return[2,""];case"Date":return[3,""];case"RegExp":return[4,""];case"Map":return[5,""];case"Set":return[6,""];case"DataView":return[1,n]}return n.includes("Array")?[1,n]:n.includes("Error")?[7,n]:[2,n]},u=([e,t])=>0===e&&("function"===t||"symbol"===t),c=(e,{json:t,lossy:n}={})=>{let r=[];return((e,t,n,r)=>{let i=(e,t)=>{let i=r.push(e)-1;return n.set(t,i),i},o=r=>{if(n.has(r))return n.get(r);let[a,c]=l(r);switch(a){case 0:{let t=r;switch(c){case"bigint":a=8,t=r.toString();break;case"function":case"symbol":if(e)throw TypeError("unable to serialize "+c);t=null;break;case"undefined":return i([-1],r)}return i([a,t],r)}case 1:{if(c){let e=r;return"DataView"===c?e=new Uint8Array(r.buffer):"ArrayBuffer"===c&&(e=new Uint8Array(r)),i([c,[...e]],r)}let e=[],t=i([a,e],r);for(let t of r)e.push(o(t));return t}case 2:{if(c)switch(c){case"BigInt":return i([c,r.toString()],r);case"Boolean":case"Number":case"String":return i([c,r.valueOf()],r)}if(t&&"toJSON"in r)return o(r.toJSON());let n=[],f=i([a,n],r);for(let t of s(r))(e||!u(l(r[t])))&&n.push([o(t),o(r[t])]);return f}case 3:return i([a,r.toISOString()],r);case 4:{let{source:e,flags:t}=r;return i([a,{source:e,flags:t}],r)}case 5:{let t=[],n=i([a,t],r);for(let[n,i]of r)(e||!(u(l(n))||u(l(i))))&&t.push([o(n),o(i)]);return n}case 6:{let t=[],n=i([a,t],r);for(let n of r)(e||!u(l(n)))&&t.push(o(n));return n}}let{message:f}=r;return i([a,{name:c,message:f}],r)};return o})(!(t||n),!!t,new Map,r)(e),r},f="function"==typeof structuredClone?(e,t)=>t&&("json"in t||"lossy"in t)?o(c(e,t)):structuredClone(e):(e,t)=>o(c(e,t));var d=n(3407);function p(e){let t=[],n=-1,r=0,i=0;for(;++n<e.length;){let o=e.charCodeAt(n),a="";if(37===o&&(0,d.lV)(e.charCodeAt(n+1))&&(0,d.lV)(e.charCodeAt(n+2)))i=2;else if(o<128)/[!#$&-;=?-Z_a-z~]/.test(String.fromCharCode(o))||(a=String.fromCharCode(o));else if(o>55295&&o<57344){let t=e.charCodeAt(n+1);o<56320&&t>56319&&t<57344?(a=String.fromCharCode(o,t),i=1):a="�"}else a=String.fromCharCode(o);a&&(t.push(e.slice(r,n),encodeURIComponent(a)),r=n+i+1,a=""),i&&(n+=i,i=0)}return t.join("")+e.slice(r)}function h(e,t){let n=[{type:"text",value:"↩"}];return t>1&&n.push({type:"element",tagName:"sup",properties:{},children:[{type:"text",value:String(t)}]}),n}function m(e,t){return"Back to reference "+(e+1)+(t>1?"-"+t:"")}var g=n(95139),y=n(73545);function v(e,t){let n=t.referenceType,r="]";if("collapsed"===n?r+="[]":"full"===n&&(r+="["+(t.label||t.identifier)+"]"),"imageReference"===t.type)return[{type:"text",value:"!["+t.alt+r}];let i=e.all(t),o=i[0];o&&"text"===o.type?o.value="["+o.value:i.unshift({type:"text",value:"["});let a=i[i.length-1];return a&&"text"===a.type?a.value+=r:i.push({type:"text",value:r}),i}function k(e){let t=e.spread;return null==t?e.children.length>1:t}function b(e,t,n){let r=0,i=e.length;if(t){let t=e.codePointAt(r);for(;9===t||32===t;)r++,t=e.codePointAt(r)}if(n){let t=e.codePointAt(i-1);for(;9===t||32===t;)i--,t=e.codePointAt(i-1)}return i>r?e.slice(r,i):""}let x={blockquote:function(e,t){let n={type:"element",tagName:"blockquote",properties:{},children:e.wrap(e.all(t),!0)};return e.patch(t,n),e.applyData(t,n)},break:function(e,t){let n={type:"element",tagName:"br",properties:{},children:[]};return e.patch(t,n),[e.applyData(t,n),{type:"text",value:"\n"}]},code:function(e,t){let n=t.value?t.value+"\n":"",r={};t.lang&&(r.className=["language-"+t.lang]);let i={type:"element",tagName:"code",properties:r,children:[{type:"text",value:n}]};return t.meta&&(i.data={meta:t.meta}),e.patch(t,i),i={type:"element",tagName:"pre",properties:{},children:[i=e.applyData(t,i)]},e.patch(t,i),i},delete:function(e,t){let n={type:"element",tagName:"del",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},emphasis:function(e,t){let n={type:"element",tagName:"em",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},footnoteReference:function(e,t){let n,r="string"==typeof e.options.clobberPrefix?e.options.clobberPrefix:"user-content-",i=String(t.identifier).toUpperCase(),o=p(i.toLowerCase()),a=e.footnoteOrder.indexOf(i),s=e.footnoteCounts.get(i);void 0===s?(s=0,e.footnoteOrder.push(i),n=e.footnoteOrder.length):n=a+1,s+=1,e.footnoteCounts.set(i,s);let l={type:"element",tagName:"a",properties:{href:"#"+r+"fn-"+o,id:r+"fnref-"+o+(s>1?"-"+s:""),dataFootnoteRef:!0,ariaDescribedBy:["footnote-label"]},children:[{type:"text",value:String(n)}]};e.patch(t,l);let u={type:"element",tagName:"sup",properties:{},children:[l]};return e.patch(t,u),e.applyData(t,u)},heading:function(e,t){let n={type:"element",tagName:"h"+t.depth,properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},html:function(e,t){if(e.options.allowDangerousHtml){let n={type:"raw",value:t.value};return e.patch(t,n),e.applyData(t,n)}},imageReference:function(e,t){let n=String(t.identifier).toUpperCase(),r=e.definitionById.get(n);if(!r)return v(e,t);let i={src:p(r.url||""),alt:t.alt};null!==r.title&&void 0!==r.title&&(i.title=r.title);let o={type:"element",tagName:"img",properties:i,children:[]};return e.patch(t,o),e.applyData(t,o)},image:function(e,t){let n={src:p(t.url)};null!==t.alt&&void 0!==t.alt&&(n.alt=t.alt),null!==t.title&&void 0!==t.title&&(n.title=t.title);let r={type:"element",tagName:"img",properties:n,children:[]};return e.patch(t,r),e.applyData(t,r)},inlineCode:function(e,t){let n={type:"text",value:t.value.replace(/\r?\n|\r/g," ")};e.patch(t,n);let r={type:"element",tagName:"code",properties:{},children:[n]};return e.patch(t,r),e.applyData(t,r)},linkReference:function(e,t){let n=String(t.identifier).toUpperCase(),r=e.definitionById.get(n);if(!r)return v(e,t);let i={href:p(r.url||"")};null!==r.title&&void 0!==r.title&&(i.title=r.title);let o={type:"element",tagName:"a",properties:i,children:e.all(t)};return e.patch(t,o),e.applyData(t,o)},link:function(e,t){let n={href:p(t.url)};null!==t.title&&void 0!==t.title&&(n.title=t.title);let r={type:"element",tagName:"a",properties:n,children:e.all(t)};return e.patch(t,r),e.applyData(t,r)},listItem:function(e,t,n){let r=e.all(t),i=n?function(e){let t=!1;if("list"===e.type){t=e.spread||!1;let n=e.children,r=-1;for(;!t&&++r<n.length;)t=k(n[r])}return t}(n):k(t),o={},a=[];if("boolean"==typeof t.checked){let e,n=r[0];n&&"element"===n.type&&"p"===n.tagName?e=n:(e={type:"element",tagName:"p",properties:{},children:[]},r.unshift(e)),e.children.length>0&&e.children.unshift({type:"text",value:" "}),e.children.unshift({type:"element",tagName:"input",properties:{type:"checkbox",checked:t.checked,disabled:!0},children:[]}),o.className=["task-list-item"]}let s=-1;for(;++s<r.length;){let e=r[s];(i||0!==s||"element"!==e.type||"p"!==e.tagName)&&a.push({type:"text",value:"\n"}),"element"!==e.type||"p"!==e.tagName||i?a.push(e):a.push(...e.children)}let l=r[r.length-1];l&&(i||"element"!==l.type||"p"!==l.tagName)&&a.push({type:"text",value:"\n"});let u={type:"element",tagName:"li",properties:o,children:a};return e.patch(t,u),e.applyData(t,u)},list:function(e,t){let n={},r=e.all(t),i=-1;for("number"==typeof t.start&&1!==t.start&&(n.start=t.start);++i<r.length;){let e=r[i];if("element"===e.type&&"li"===e.tagName&&e.properties&&Array.isArray(e.properties.className)&&e.properties.className.includes("task-list-item")){n.className=["contains-task-list"];break}}let o={type:"element",tagName:t.ordered?"ol":"ul",properties:n,children:e.wrap(r,!0)};return e.patch(t,o),e.applyData(t,o)},paragraph:function(e,t){let n={type:"element",tagName:"p",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},root:function(e,t){let n={type:"root",children:e.wrap(e.all(t))};return e.patch(t,n),e.applyData(t,n)},strong:function(e,t){let n={type:"element",tagName:"strong",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},table:function(e,t){let n=e.all(t),r=n.shift(),i=[];if(r){let n={type:"element",tagName:"thead",properties:{},children:e.wrap([r],!0)};e.patch(t.children[0],n),i.push(n)}if(n.length>0){let r={type:"element",tagName:"tbody",properties:{},children:e.wrap(n,!0)},o=(0,y.PW)(t.children[1]),a=(0,y.Y)(t.children[t.children.length-1]);o&&a&&(r.position={start:o,end:a}),i.push(r)}let o={type:"element",tagName:"table",properties:{},children:e.wrap(i,!0)};return e.patch(t,o),e.applyData(t,o)},tableCell:function(e,t){let n={type:"element",tagName:"td",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},tableRow:function(e,t,n){let r=n?n.children:void 0,i=0===(r?r.indexOf(t):1)?"th":"td",o=n&&"table"===n.type?n.align:void 0,a=o?o.length:t.children.length,s=-1,l=[];for(;++s<a;){let n=t.children[s],r={},a=o?o[s]:void 0;a&&(r.align=a);let u={type:"element",tagName:i,properties:r,children:[]};n&&(u.children=e.all(n),e.patch(n,u),u=e.applyData(n,u)),l.push(u)}let u={type:"element",tagName:"tr",properties:{},children:e.wrap(l,!0)};return e.patch(t,u),e.applyData(t,u)},text:function(e,t){let n={type:"text",value:function(e){let t=String(e),n=/\r?\n|\r/g,r=n.exec(t),i=0,o=[];for(;r;)o.push(b(t.slice(i,r.index),i>0,!0),r[0]),i=r.index+r[0].length,r=n.exec(t);return o.push(b(t.slice(i),i>0,!1)),o.join("")}(String(t.value))};return e.patch(t,n),e.applyData(t,n)},thematicBreak:function(e,t){let n={type:"element",tagName:"hr",properties:{},children:[]};return e.patch(t,n),e.applyData(t,n)},toml:_,yaml:_,definition:_,footnoteDefinition:_};function _(){}let w={}.hasOwnProperty,z={};function S(e,t){e.position&&(t.position=(0,y.G1)(e))}function A(e,t){let n=t;if(e&&e.data){let t=e.data.hName,r=e.data.hChildren,i=e.data.hProperties;"string"==typeof t&&("element"===n.type?n.tagName=t:n={type:"element",tagName:t,properties:{},children:"children"in n?n.children:[n]}),"element"===n.type&&i&&Object.assign(n.properties,f(i)),"children"in n&&n.children&&null!=r&&(n.children=r)}return n}function I(e,t){let n=[],r=-1;for(t&&n.push({type:"text",value:"\n"});++r<e.length;)r&&n.push({type:"text",value:"\n"}),n.push(e[r]);return t&&e.length>0&&n.push({type:"text",value:"\n"}),n}function P(e){let t=0,n=e.charCodeAt(t);for(;9===n||32===n;)t++,n=e.charCodeAt(t);return e.slice(t)}function E(e,t){let n=function(e,t){let n=t||z,r=new Map,i=new Map,o={all:function(e){let t=[];if("children"in e){let n=e.children,r=-1;for(;++r<n.length;){let i=o.one(n[r],e);if(i){if(r&&"break"===n[r-1].type&&(Array.isArray(i)||"text"!==i.type||(i.value=P(i.value)),!Array.isArray(i)&&"element"===i.type)){let e=i.children[0];e&&"text"===e.type&&(e.value=P(e.value))}Array.isArray(i)?t.push(...i):t.push(i)}}}return t},applyData:A,definitionById:r,footnoteById:i,footnoteCounts:new Map,footnoteOrder:[],handlers:{...x,...n.handlers},one:function(e,t){let n=e.type,r=o.handlers[n];if(w.call(o.handlers,n)&&r)return r(o,e,t);if(o.options.passThrough&&o.options.passThrough.includes(n)){if("children"in e){let{children:t,...n}=e,r=f(n);return r.children=o.all(e),r}return f(e)}return(o.options.unknownHandler||function(e,t){let n=t.data||{},r="value"in t&&!(w.call(n,"hProperties")||w.call(n,"hChildren"))?{type:"text",value:t.value}:{type:"element",tagName:"div",properties:{},children:e.all(t)};return e.patch(t,r),e.applyData(t,r)})(o,e,t)},options:n,patch:S,wrap:I};return(0,g.YR)(e,function(e){if("definition"===e.type||"footnoteDefinition"===e.type){let t="definition"===e.type?r:i,n=String(e.identifier).toUpperCase();t.has(n)||t.set(n,e)}}),o}(e,t),i=n.one(e,void 0),o=function(e){let t="string"==typeof e.options.clobberPrefix?e.options.clobberPrefix:"user-content-",n=e.options.footnoteBackContent||h,r=e.options.footnoteBackLabel||m,i=e.options.footnoteLabel||"Footnotes",o=e.options.footnoteLabelTagName||"h2",a=e.options.footnoteLabelProperties||{className:["sr-only"]},s=[],l=-1;for(;++l<e.footnoteOrder.length;){let i=e.footnoteById.get(e.footnoteOrder[l]);if(!i)continue;let o=e.all(i),a=String(i.identifier).toUpperCase(),u=p(a.toLowerCase()),c=0,f=[],d=e.footnoteCounts.get(a);for(;void 0!==d&&++c<=d;){f.length>0&&f.push({type:"text",value:" "});let e="string"==typeof n?n:n(l,c);"string"==typeof e&&(e={type:"text",value:e}),f.push({type:"element",tagName:"a",properties:{href:"#"+t+"fnref-"+u+(c>1?"-"+c:""),dataFootnoteBackref:"",ariaLabel:"string"==typeof r?r:r(l,c),className:["data-footnote-backref"]},children:Array.isArray(e)?e:[e]})}let h=o[o.length-1];if(h&&"element"===h.type&&"p"===h.tagName){let e=h.children[h.children.length-1];e&&"text"===e.type?e.value+=" ":h.children.push({type:"text",value:" "}),h.children.push(...f)}else o.push(...f);let m={type:"element",tagName:"li",properties:{id:t+"fn-"+u},children:e.wrap(o,!0)};e.patch(i,m),s.push(m)}if(0!==s.length)return{type:"element",tagName:"section",properties:{dataFootnotes:!0,className:["footnotes"]},children:[{type:"element",tagName:o,properties:{...f(a),id:"footnote-label"},children:[{type:"text",value:i}]},{type:"text",value:"\n"},{type:"element",tagName:"ol",properties:{},children:e.wrap(s,!0)},{type:"text",value:"\n"}]}}(n),a=Array.isArray(i)?{type:"root",children:i}:i||{type:"root",children:[]};return o&&((0,r.ok)("children"in a),a.children.push({type:"text",value:"\n"},o)),a}function C(e,t){return e&&"run"in e?async function(n,r){let i=E(n,{file:r,...t});await e.run(i,r)}:function(n,r){return E(n,{file:r,...e||t})}}},75164:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});let r={}.hasOwnProperty;function i(e,t){let n=t||{};function i(t,...n){let o=i.invalid,a=i.handlers;if(t&&r.call(t,e)){let n=String(t[e]);o=r.call(a,n)?a[n]:i.unknown}if(o)return o.call(this,t,...n)}return i.handlers=n.handlers||{},i.invalid=n.invalid,i.unknown=n.unknown,i}},76524:(e,t,n)=>{"use strict";n.d(t,{A:()=>eO});var r=n(5009),i=n(63758),o=n(3407),a=n(56522),s=n(95502);let l="phrasing",u=["autolink","link","image","label"];function c(e){this.enter({type:"link",title:null,url:"",children:[]},e)}function f(e){this.config.enter.autolinkProtocol.call(this,e)}function d(e){this.config.exit.autolinkProtocol.call(this,e)}function p(e){this.config.exit.data.call(this,e);let t=this.stack[this.stack.length-1];(0,i.ok)("link"===t.type),t.url="http://"+this.sliceSerialize(e)}function h(e){this.config.exit.autolinkEmail.call(this,e)}function m(e){this.exit(e)}function g(e){!function(e,t,n){let r=(0,s.C)((n||{}).ignore||[]),i=function(e){let t=[];if(!Array.isArray(e))throw TypeError("Expected find and replace tuple or list of tuples");let n=!e[0]||Array.isArray(e[0])?e:[e],r=-1;for(;++r<n.length;){var i;let e=n[r];t.push(["string"==typeof(i=e[0])?RegExp(function(e){if("string"!=typeof e)throw TypeError("Expected a string");return e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}(i),"g"):i,function(e){return"function"==typeof e?e:function(){return e}}(e[1])])}return t}(t),o=-1;for(;++o<i.length;)(0,a.VG)(e,"text",l);function l(e,t){let n,a=-1;for(;++a<t.length;){let e=t[a],i=n?n.children:void 0;if(r(e,i?i.indexOf(e):void 0,n))return;n=e}if(n)return function(e,t){let n=t[t.length-1],r=i[o][0],a=i[o][1],s=0,l=n.children.indexOf(e),u=!1,c=[];r.lastIndex=0;let f=r.exec(e.value);for(;f;){let n=f.index,i={index:f.index,input:f.input,stack:[...t,e]},o=a(...f,i);if("string"==typeof o&&(o=o.length>0?{type:"text",value:o}:void 0),!1===o?r.lastIndex=n+1:(s!==n&&c.push({type:"text",value:e.value.slice(s,n)}),Array.isArray(o)?c.push(...o):o&&c.push(o),s=n+f[0].length,u=!0),!r.global)break;f=r.exec(e.value)}return u?(s<e.value.length&&c.push({type:"text",value:e.value.slice(s)}),n.children.splice(l,1,...c)):c=[e],l+c.length}(e,t)}}(e,[[/(https?:\/\/|www(?=\.))([-.\w]+)([^ \t\r\n]*)/gi,y],[/(?<=^|\s|\p{P}|\p{S})([-.\w+]+)@([-\w]+(?:\.[-\w]+)+)/gu,v]],{ignore:["link","linkReference"]})}function y(e,t,n,i,o){let a="";if(!k(o)||(/^w/i.test(t)&&(n=t+n,t="",a="http://"),!function(e){let t=e.split(".");return!(t.length<2||t[t.length-1]&&(/_/.test(t[t.length-1])||!/[a-zA-Z\d]/.test(t[t.length-1]))||t[t.length-2]&&(/_/.test(t[t.length-2])||!/[a-zA-Z\d]/.test(t[t.length-2])))}(n)))return!1;let s=function(e){let t=/[!"&'),.:;<>?\]}]+$/.exec(e);if(!t)return[e,void 0];e=e.slice(0,t.index);let n=t[0],i=n.indexOf(")"),o=(0,r.D)(e,"("),a=(0,r.D)(e,")");for(;-1!==i&&o>a;)e+=n.slice(0,i+1),i=(n=n.slice(i+1)).indexOf(")"),a++;return[e,n]}(n+i);if(!s[0])return!1;let l={type:"link",title:null,url:a+t+s[0],children:[{type:"text",value:t+s[0]}]};return s[1]?[l,{type:"text",value:s[1]}]:l}function v(e,t,n,r){return!(!k(r,!0)||/[-\d_]$/.test(n))&&{type:"link",title:null,url:"mailto:"+t+"@"+n,children:[{type:"text",value:t+"@"+n}]}}function k(e,t){let n=e.input.charCodeAt(e.index-1);return(0===e.index||(0,o.Ny)(n)||(0,o.es)(n))&&(!t||47!==n)}var b=n(35983);function x(){this.buffer()}function _(e){this.enter({type:"footnoteReference",identifier:"",label:""},e)}function w(){this.buffer()}function z(e){this.enter({type:"footnoteDefinition",identifier:"",label:"",children:[]},e)}function S(e){let t=this.resume(),n=this.stack[this.stack.length-1];(0,i.ok)("footnoteReference"===n.type),n.identifier=(0,b.B)(this.sliceSerialize(e)).toLowerCase(),n.label=t}function A(e){this.exit(e)}function I(e){let t=this.resume(),n=this.stack[this.stack.length-1];(0,i.ok)("footnoteDefinition"===n.type),n.identifier=(0,b.B)(this.sliceSerialize(e)).toLowerCase(),n.label=t}function P(e){this.exit(e)}function E(e,t,n,r){let i=n.createTracker(r),o=i.move("[^"),a=n.enter("footnoteReference"),s=n.enter("reference");return o+=i.move(n.safe(n.associationId(e),{after:"]",before:o})),s(),a(),o+=i.move("]")}function C(e,t,n){return 0===t?e:T(e,t,n)}function T(e,t,n){return(n?"":"    ")+e}E.peek=function(){return"["};let O=["autolink","destinationLiteral","destinationRaw","reference","titleQuote","titleApostrophe"];function j(e){this.enter({type:"delete",children:[]},e)}function $(e){this.exit(e)}function N(e,t,n,r){let i=n.createTracker(r),o=n.enter("strikethrough"),a=i.move("~~");return a+=n.containerPhrasing(e,{...i.current(),before:a,after:"~"}),a+=i.move("~~"),o(),a}function M(e){return e.length}function F(e){let t="string"==typeof e?e.codePointAt(0):0;return 67===t||99===t?99:76===t||108===t?108:114*(82===t||114===t)}N.peek=function(){return"~"};var Z=n(62737);function D(e){let t=e._align;(0,i.ok)(t,"expected `_align` on table"),this.enter({type:"table",align:t.map(function(e){return"none"===e?null:e}),children:[]},e),this.data.inTable=!0}function L(e){this.exit(e),this.data.inTable=void 0}function R(e){this.enter({type:"tableRow",children:[]},e)}function B(e){this.exit(e)}function V(e){this.enter({type:"tableCell",children:[]},e)}function H(e){let t=this.resume();this.data.inTable&&(t=t.replace(/\\([\\|])/g,U));let n=this.stack[this.stack.length-1];(0,i.ok)("inlineCode"===n.type),n.value=t,this.exit(e)}function U(e,t){return"|"===t?t:e}function J(e){let t=this.stack[this.stack.length-2];(0,i.ok)("listItem"===t.type),t.checked="taskListCheckValueChecked"===e.type}function W(e){let t=this.stack[this.stack.length-2];if(t&&"listItem"===t.type&&"boolean"==typeof t.checked){let e=this.stack[this.stack.length-1];(0,i.ok)("paragraph"===e.type);let n=e.children[0];if(n&&"text"===n.type){let r,i=t.children,o=-1;for(;++o<i.length;){let e=i[o];if("paragraph"===e.type){r=e;break}}r===e&&(n.value=n.value.slice(1),0===n.value.length?e.children.shift():e.position&&n.position&&"number"==typeof n.position.start.offset&&(n.position.start.column++,n.position.start.offset++,e.position.start=Object.assign({},n.position.start)))}}this.exit(e)}function q(e,t,n,r){let i=e.children[0],o="boolean"==typeof e.checked&&i&&"paragraph"===i.type,a="["+(e.checked?"x":" ")+"] ",s=n.createTracker(r);o&&s.move(a);let l=Z.p.listItem(e,t,n,{...r,...s.current()});return o&&(l=l.replace(/^(?:[*+-]|\d+\.)([\r\n]| {1,3})/,function(e){return e+a})),l}var Q=n(38220);let G={tokenize:function(e,t,n){let r=0;return function t(o){return(87===o||119===o)&&r<3?(r++,e.consume(o),t):46===o&&3===r?(e.consume(o),i):n(o)};function i(e){return null===e?n(e):t(e)}},partial:!0},K={tokenize:function(e,t,n){let r,i,a;return s;function s(t){return 46===t||95===t?e.check(X,u,l)(t):null===t||(0,o.Ee)(t)||(0,o.Ny)(t)||45!==t&&(0,o.es)(t)?u(t):(a=!0,e.consume(t),s)}function l(t){return 95===t?r=!0:(i=r,r=void 0),e.consume(t),s}function u(e){return i||r||!a?n(e):t(e)}},partial:!0},Y={tokenize:function(e,t){let n=0,r=0;return i;function i(s){return 40===s?(n++,e.consume(s),i):41===s&&r<n?a(s):33===s||34===s||38===s||39===s||41===s||42===s||44===s||46===s||58===s||59===s||60===s||63===s||93===s||95===s||126===s?e.check(X,t,a)(s):null===s||(0,o.Ee)(s)||(0,o.Ny)(s)?t(s):(e.consume(s),i)}function a(t){return 41===t&&r++,e.consume(t),i}},partial:!0},X={tokenize:function(e,t,n){return r;function r(s){return 33===s||34===s||39===s||41===s||42===s||44===s||46===s||58===s||59===s||63===s||95===s||126===s?(e.consume(s),r):38===s?(e.consume(s),a):93===s?(e.consume(s),i):60===s||null===s||(0,o.Ee)(s)||(0,o.Ny)(s)?t(s):n(s)}function i(e){return null===e||40===e||91===e||(0,o.Ee)(e)||(0,o.Ny)(e)?t(e):r(e)}function a(t){return(0,o.CW)(t)?function t(i){return 59===i?(e.consume(i),r):(0,o.CW)(i)?(e.consume(i),t):n(i)}(t):n(t)}},partial:!0},ee={tokenize:function(e,t,n){return function(t){return e.consume(t),r};function r(e){return(0,o.lV)(e)?n(e):t(e)}},partial:!0},et={name:"wwwAutolink",tokenize:function(e,t,n){let r=this;return function(t){return 87!==t&&119!==t||!ea.call(r,r.previous)||ec(r.events)?n(t):(e.enter("literalAutolink"),e.enter("literalAutolinkWww"),e.check(G,e.attempt(K,e.attempt(Y,i),n),n)(t))};function i(n){return e.exit("literalAutolinkWww"),e.exit("literalAutolink"),t(n)}},previous:ea},en={name:"protocolAutolink",tokenize:function(e,t,n){let r=this,i="",a=!1;return function(t){return(72===t||104===t)&&es.call(r,r.previous)&&!ec(r.events)?(e.enter("literalAutolink"),e.enter("literalAutolinkHttp"),i+=String.fromCodePoint(t),e.consume(t),s):n(t)};function s(t){if((0,o.CW)(t)&&i.length<5)return i+=String.fromCodePoint(t),e.consume(t),s;if(58===t){let n=i.toLowerCase();if("http"===n||"https"===n)return e.consume(t),l}return n(t)}function l(t){return 47===t?(e.consume(t),a)?u:(a=!0,l):n(t)}function u(t){return null===t||(0,o.JQ)(t)||(0,o.Ee)(t)||(0,o.Ny)(t)||(0,o.es)(t)?n(t):e.attempt(K,e.attempt(Y,c),n)(t)}function c(n){return e.exit("literalAutolinkHttp"),e.exit("literalAutolink"),t(n)}},previous:es},er={name:"emailAutolink",tokenize:function(e,t,n){let r,i,a=this;return function(t){return!eu(t)||!el.call(a,a.previous)||ec(a.events)?n(t):(e.enter("literalAutolink"),e.enter("literalAutolinkEmail"),function t(r){return eu(r)?(e.consume(r),t):64===r?(e.consume(r),s):n(r)}(t))};function s(t){return 46===t?e.check(ee,u,l)(t):45===t||95===t||(0,o.lV)(t)?(i=!0,e.consume(t),s):u(t)}function l(t){return e.consume(t),r=!0,s}function u(s){return i&&r&&(0,o.CW)(a.previous)?(e.exit("literalAutolinkEmail"),e.exit("literalAutolink"),t(s)):n(s)}},previous:el},ei={},eo=48;for(;eo<123;)ei[eo]=er,58==++eo?eo=65:91===eo&&(eo=97);function ea(e){return null===e||40===e||42===e||95===e||91===e||93===e||126===e||(0,o.Ee)(e)}function es(e){return!(0,o.CW)(e)}function el(e){return!(47===e||eu(e))}function eu(e){return 43===e||45===e||46===e||95===e||(0,o.lV)(e)}function ec(e){let t=e.length,n=!1;for(;t--;){let r=e[t][1];if(("labelLink"===r.type||"labelImage"===r.type)&&!r._balanced){n=!0;break}if(r._gfmAutolinkLiteralWalkedInto){n=!1;break}}return e.length>0&&!n&&(e[e.length-1][1]._gfmAutolinkLiteralWalkedInto=!0),n}ei[43]=er,ei[45]=er,ei[46]=er,ei[95]=er,ei[72]=[er,en],ei[104]=[er,en],ei[87]=[er,et],ei[119]=[er,et];var ef=n(33634),ed=n(79168);let ep={tokenize:function(e,t,n){let r=this;return(0,ed.N)(e,function(e){let i=r.events[r.events.length-1];return i&&"gfmFootnoteDefinitionIndent"===i[1].type&&4===i[2].sliceSerialize(i[1],!0).length?t(e):n(e)},"gfmFootnoteDefinitionIndent",5)},partial:!0};function eh(e,t,n){let r,i=this,o=i.events.length,a=i.parser.gfmFootnotes||(i.parser.gfmFootnotes=[]);for(;o--;){let e=i.events[o][1];if("labelImage"===e.type){r=e;break}if("gfmFootnoteCall"===e.type||"labelLink"===e.type||"label"===e.type||"image"===e.type||"link"===e.type)break}return function(o){if(!r||!r._balanced)return n(o);let s=(0,b.B)(i.sliceSerialize({start:r.end,end:i.now()}));return 94===s.codePointAt(0)&&a.includes(s.slice(1))?(e.enter("gfmFootnoteCallLabelMarker"),e.consume(o),e.exit("gfmFootnoteCallLabelMarker"),t(o)):n(o)}}function em(e,t){let n=e.length;for(;n--;)if("labelImage"===e[n][1].type&&"enter"===e[n][0]){e[n][1];break}e[n+1][1].type="data",e[n+3][1].type="gfmFootnoteCallLabelMarker";let r={type:"gfmFootnoteCall",start:Object.assign({},e[n+3][1].start),end:Object.assign({},e[e.length-1][1].end)},i={type:"gfmFootnoteCallMarker",start:Object.assign({},e[n+3][1].end),end:Object.assign({},e[n+3][1].end)};i.end.column++,i.end.offset++,i.end._bufferIndex++;let o={type:"gfmFootnoteCallString",start:Object.assign({},i.end),end:Object.assign({},e[e.length-1][1].start)},a={type:"chunkString",contentType:"string",start:Object.assign({},o.start),end:Object.assign({},o.end)},s=[e[n+1],e[n+2],["enter",r,t],e[n+3],e[n+4],["enter",i,t],["exit",i,t],["enter",o,t],["enter",a,t],["exit",a,t],["exit",o,t],e[e.length-2],e[e.length-1],["exit",r,t]];return e.splice(n,e.length-n+1,...s),e}function eg(e,t,n){let r,i=this,a=i.parser.gfmFootnotes||(i.parser.gfmFootnotes=[]),s=0;return function(t){return e.enter("gfmFootnoteCall"),e.enter("gfmFootnoteCallLabelMarker"),e.consume(t),e.exit("gfmFootnoteCallLabelMarker"),l};function l(t){return 94!==t?n(t):(e.enter("gfmFootnoteCallMarker"),e.consume(t),e.exit("gfmFootnoteCallMarker"),e.enter("gfmFootnoteCallString"),e.enter("chunkString").contentType="string",u)}function u(l){if(s>999||93===l&&!r||null===l||91===l||(0,o.Ee)(l))return n(l);if(93===l){e.exit("chunkString");let r=e.exit("gfmFootnoteCallString");return a.includes((0,b.B)(i.sliceSerialize(r)))?(e.enter("gfmFootnoteCallLabelMarker"),e.consume(l),e.exit("gfmFootnoteCallLabelMarker"),e.exit("gfmFootnoteCall"),t):n(l)}return(0,o.Ee)(l)||(r=!0),s++,e.consume(l),92===l?c:u}function c(t){return 91===t||92===t||93===t?(e.consume(t),s++,u):u(t)}}function ey(e,t,n){let r,i,a=this,s=a.parser.gfmFootnotes||(a.parser.gfmFootnotes=[]),l=0;return function(t){return e.enter("gfmFootnoteDefinition")._container=!0,e.enter("gfmFootnoteDefinitionLabel"),e.enter("gfmFootnoteDefinitionLabelMarker"),e.consume(t),e.exit("gfmFootnoteDefinitionLabelMarker"),u};function u(t){return 94===t?(e.enter("gfmFootnoteDefinitionMarker"),e.consume(t),e.exit("gfmFootnoteDefinitionMarker"),e.enter("gfmFootnoteDefinitionLabelString"),e.enter("chunkString").contentType="string",c):n(t)}function c(t){if(l>999||93===t&&!i||null===t||91===t||(0,o.Ee)(t))return n(t);if(93===t){e.exit("chunkString");let n=e.exit("gfmFootnoteDefinitionLabelString");return r=(0,b.B)(a.sliceSerialize(n)),e.enter("gfmFootnoteDefinitionLabelMarker"),e.consume(t),e.exit("gfmFootnoteDefinitionLabelMarker"),e.exit("gfmFootnoteDefinitionLabel"),d}return(0,o.Ee)(t)||(i=!0),l++,e.consume(t),92===t?f:c}function f(t){return 91===t||92===t||93===t?(e.consume(t),l++,c):c(t)}function d(t){return 58===t?(e.enter("definitionMarker"),e.consume(t),e.exit("definitionMarker"),s.includes(r)||s.push(r),(0,ed.N)(e,p,"gfmFootnoteDefinitionWhitespace")):n(t)}function p(e){return t(e)}}function ev(e,t,n){return e.check(ef.B,t,e.attempt(ep,t,n))}function ek(e){e.exit("gfmFootnoteDefinition")}var eb=n(17032),ex=n(23362),e_=n(41006);class ew{constructor(){this.map=[]}add(e,t,n){!function(e,t,n,r){let i=0;if(0!==n||0!==r.length){for(;i<e.map.length;){if(e.map[i][0]===t){e.map[i][1]+=n,e.map[i][2].push(...r);return}i+=1}e.map.push([t,n,r])}}(this,e,t,n)}consume(e){if(this.map.sort(function(e,t){return e[0]-t[0]}),0===this.map.length)return;let t=this.map.length,n=[];for(;t>0;)t-=1,n.push(e.slice(this.map[t][0]+this.map[t][1]),this.map[t][2]),e.length=this.map[t][0];n.push(e.slice()),e.length=0;let r=n.pop();for(;r;){for(let t of r)e.push(t);r=n.pop()}this.map.length=0}}function ez(e,t,n){let r,i=this,a=0,s=0;return function(e){let t=i.events.length-1;for(;t>-1;){let e=i.events[t][1].type;if("lineEnding"===e||"linePrefix"===e)t--;else break}let r=t>-1?i.events[t][1].type:null,o="tableHead"===r||"tableRow"===r?k:l;return o===k&&i.parser.lazy[i.now().line]?n(e):o(e)};function l(t){var n;return e.enter("tableHead"),e.enter("tableRow"),124===(n=t)||(r=!0,s+=1),u(n)}function u(t){return null===t?n(t):(0,o.HP)(t)?s>1?(s=0,i.interrupt=!0,e.exit("tableRow"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),d):n(t):(0,o.On)(t)?(0,ed.N)(e,u,"whitespace")(t):(s+=1,r&&(r=!1,a+=1),124===t)?(e.enter("tableCellDivider"),e.consume(t),e.exit("tableCellDivider"),r=!0,u):(e.enter("data"),c(t))}function c(t){return null===t||124===t||(0,o.Ee)(t)?(e.exit("data"),u(t)):(e.consume(t),92===t?f:c)}function f(t){return 92===t||124===t?(e.consume(t),c):c(t)}function d(t){return(i.interrupt=!1,i.parser.lazy[i.now().line])?n(t):(e.enter("tableDelimiterRow"),r=!1,(0,o.On)(t))?(0,ed.N)(e,p,"linePrefix",i.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):p(t)}function p(t){return 45===t||58===t?m(t):124===t?(r=!0,e.enter("tableCellDivider"),e.consume(t),e.exit("tableCellDivider"),h):n(t)}function h(t){return(0,o.On)(t)?(0,ed.N)(e,m,"whitespace")(t):m(t)}function m(t){return 58===t?(s+=1,r=!0,e.enter("tableDelimiterMarker"),e.consume(t),e.exit("tableDelimiterMarker"),g):45===t?(s+=1,g(t)):null===t||(0,o.HP)(t)?v(t):n(t)}function g(t){return 45===t?(e.enter("tableDelimiterFiller"),function t(n){return 45===n?(e.consume(n),t):58===n?(r=!0,e.exit("tableDelimiterFiller"),e.enter("tableDelimiterMarker"),e.consume(n),e.exit("tableDelimiterMarker"),y):(e.exit("tableDelimiterFiller"),y(n))}(t)):n(t)}function y(t){return(0,o.On)(t)?(0,ed.N)(e,v,"whitespace")(t):v(t)}function v(i){if(124===i)return p(i);if(null===i||(0,o.HP)(i))return r&&a===s?(e.exit("tableDelimiterRow"),e.exit("tableHead"),t(i)):n(i);return n(i)}function k(t){return e.enter("tableRow"),b(t)}function b(n){return 124===n?(e.enter("tableCellDivider"),e.consume(n),e.exit("tableCellDivider"),b):null===n||(0,o.HP)(n)?(e.exit("tableRow"),t(n)):(0,o.On)(n)?(0,ed.N)(e,b,"whitespace")(n):(e.enter("data"),x(n))}function x(t){return null===t||124===t||(0,o.Ee)(t)?(e.exit("data"),b(t)):(e.consume(t),92===t?_:x)}function _(t){return 92===t||124===t?(e.consume(t),x):x(t)}}function eS(e,t){let n,r,i,o=-1,a=!0,s=0,l=[0,0,0,0],u=[0,0,0,0],c=!1,f=0,d=new ew;for(;++o<e.length;){let p=e[o],h=p[1];"enter"===p[0]?"tableHead"===h.type?(c=!1,0!==f&&(eI(d,t,f,n,r),r=void 0,f=0),n={type:"table",start:Object.assign({},h.start),end:Object.assign({},h.end)},d.add(o,0,[["enter",n,t]])):"tableRow"===h.type||"tableDelimiterRow"===h.type?(a=!0,i=void 0,l=[0,0,0,0],u=[0,o+1,0,0],c&&(c=!1,r={type:"tableBody",start:Object.assign({},h.start),end:Object.assign({},h.end)},d.add(o,0,[["enter",r,t]])),s="tableDelimiterRow"===h.type?2:r?3:1):s&&("data"===h.type||"tableDelimiterMarker"===h.type||"tableDelimiterFiller"===h.type)?(a=!1,0===u[2]&&(0!==l[1]&&(u[0]=u[1],i=eA(d,t,l,s,void 0,i),l=[0,0,0,0]),u[2]=o)):"tableCellDivider"===h.type&&(a?a=!1:(0!==l[1]&&(u[0]=u[1],i=eA(d,t,l,s,void 0,i)),u=[(l=u)[1],o,0,0])):"tableHead"===h.type?(c=!0,f=o):"tableRow"===h.type||"tableDelimiterRow"===h.type?(f=o,0!==l[1]?(u[0]=u[1],i=eA(d,t,l,s,o,i)):0!==u[1]&&(i=eA(d,t,u,s,o,i)),s=0):s&&("data"===h.type||"tableDelimiterMarker"===h.type||"tableDelimiterFiller"===h.type)&&(u[3]=o)}for(0!==f&&eI(d,t,f,n,r),d.consume(t.events),o=-1;++o<t.events.length;){let e=t.events[o];"enter"===e[0]&&"table"===e[1].type&&(e[1]._align=function(e,t){let n=!1,r=[];for(;t<e.length;){let i=e[t];if(n){if("enter"===i[0])"tableContent"===i[1].type&&r.push("tableDelimiterMarker"===e[t+1][1].type?"left":"none");else if("tableContent"===i[1].type){if("tableDelimiterMarker"===e[t-1][1].type){let e=r.length-1;r[e]="left"===r[e]?"center":"right"}}else if("tableDelimiterRow"===i[1].type)break}else"enter"===i[0]&&"tableDelimiterRow"===i[1].type&&(n=!0);t+=1}return r}(t.events,o))}return e}function eA(e,t,n,r,i,o){0!==n[0]&&(o.end=Object.assign({},eP(t.events,n[0])),e.add(n[0],0,[["exit",o,t]]));let a=eP(t.events,n[1]);if(o={type:1===r?"tableHeader":2===r?"tableDelimiter":"tableData",start:Object.assign({},a),end:Object.assign({},a)},e.add(n[1],0,[["enter",o,t]]),0!==n[2]){let i=eP(t.events,n[2]),o=eP(t.events,n[3]),a={type:"tableContent",start:Object.assign({},i),end:Object.assign({},o)};if(e.add(n[2],0,[["enter",a,t]]),2!==r){let r=t.events[n[2]],i=t.events[n[3]];if(r[1].end=Object.assign({},i[1].end),r[1].type="chunkText",r[1].contentType="text",n[3]>n[2]+1){let t=n[2]+1,r=n[3]-n[2]-1;e.add(t,r,[])}}e.add(n[3]+1,0,[["exit",a,t]])}return void 0!==i&&(o.end=Object.assign({},eP(t.events,i)),e.add(i,0,[["exit",o,t]]),o=void 0),o}function eI(e,t,n,r,i){let o=[],a=eP(t.events,n);i&&(i.end=Object.assign({},a),o.push(["exit",i,t])),r.end=Object.assign({},a),o.push(["exit",r,t]),e.add(n+1,0,o)}function eP(e,t){let n=e[t],r="enter"===n[0]?"start":"end";return n[1][r]}let eE={name:"tasklistCheck",tokenize:function(e,t,n){let r=this;return function(t){return null===r.previous&&r._gfmTasklistFirstContentOfListItem?(e.enter("taskListCheck"),e.enter("taskListCheckMarker"),e.consume(t),e.exit("taskListCheckMarker"),i):n(t)};function i(t){return(0,o.Ee)(t)?(e.enter("taskListCheckValueUnchecked"),e.consume(t),e.exit("taskListCheckValueUnchecked"),a):88===t||120===t?(e.enter("taskListCheckValueChecked"),e.consume(t),e.exit("taskListCheckValueChecked"),a):n(t)}function a(t){return 93===t?(e.enter("taskListCheckMarker"),e.consume(t),e.exit("taskListCheckMarker"),e.exit("taskListCheck"),s):n(t)}function s(r){return(0,o.HP)(r)?t(r):(0,o.On)(r)?e.check({tokenize:eC},t,n)(r):n(r)}}};function eC(e,t,n){return(0,ed.N)(e,function(e){return null===e?n(e):t(e)},"whitespace")}let eT={};function eO(e){let t,n=e||eT,r=this.data(),i=r.micromarkExtensions||(r.micromarkExtensions=[]),o=r.fromMarkdownExtensions||(r.fromMarkdownExtensions=[]),a=r.toMarkdownExtensions||(r.toMarkdownExtensions=[]);i.push((0,Q.y)([{text:ei},{document:{91:{name:"gfmFootnoteDefinition",tokenize:ey,continuation:{tokenize:ev},exit:ek}},text:{91:{name:"gfmFootnoteCall",tokenize:eg},93:{name:"gfmPotentialFootnoteCall",add:"after",tokenize:eh,resolveTo:em}}},function(e){let t=(e||{}).singleTilde,n={name:"strikethrough",tokenize:function(e,n,r){let i=this.previous,o=this.events,a=0;return function(s){return 126===i&&"characterEscape"!==o[o.length-1][1].type?r(s):(e.enter("strikethroughSequenceTemporary"),function o(s){let l=(0,ex.S)(i);if(126===s)return a>1?r(s):(e.consume(s),a++,o);if(a<2&&!t)return r(s);let u=e.exit("strikethroughSequenceTemporary"),c=(0,ex.S)(s);return u._open=!c||2===c&&!!l,u._close=!l||2===l&&!!c,n(s)}(s))}},resolveAll:function(e,t){let n=-1;for(;++n<e.length;)if("enter"===e[n][0]&&"strikethroughSequenceTemporary"===e[n][1].type&&e[n][1]._close){let r=n;for(;r--;)if("exit"===e[r][0]&&"strikethroughSequenceTemporary"===e[r][1].type&&e[r][1]._open&&e[n][1].end.offset-e[n][1].start.offset==e[r][1].end.offset-e[r][1].start.offset){e[n][1].type="strikethroughSequence",e[r][1].type="strikethroughSequence";let i={type:"strikethrough",start:Object.assign({},e[r][1].start),end:Object.assign({},e[n][1].end)},o={type:"strikethroughText",start:Object.assign({},e[r][1].end),end:Object.assign({},e[n][1].start)},a=[["enter",i,t],["enter",e[r][1],t],["exit",e[r][1],t],["enter",o,t]],s=t.parser.constructs.insideSpan.null;s&&(0,eb.m)(a,a.length,0,(0,e_.W)(s,e.slice(r+1,n),t)),(0,eb.m)(a,a.length,0,[["exit",o,t],["enter",e[n][1],t],["exit",e[n][1],t],["exit",i,t]]),(0,eb.m)(e,r-1,n-r+3,a),n=r+a.length-2;break}}for(n=-1;++n<e.length;)"strikethroughSequenceTemporary"===e[n][1].type&&(e[n][1].type="data");return e}};return null==t&&(t=!0),{text:{126:n},insideSpan:{null:[n]},attentionMarkers:{null:[126]}}}(n),{flow:{null:{name:"table",tokenize:ez,resolveAll:eS}}},{text:{91:eE}}])),o.push([{transforms:[g],enter:{literalAutolink:c,literalAutolinkEmail:f,literalAutolinkHttp:f,literalAutolinkWww:f},exit:{literalAutolink:m,literalAutolinkEmail:h,literalAutolinkHttp:d,literalAutolinkWww:p}},{enter:{gfmFootnoteCallString:x,gfmFootnoteCall:_,gfmFootnoteDefinitionLabelString:w,gfmFootnoteDefinition:z},exit:{gfmFootnoteCallString:S,gfmFootnoteCall:A,gfmFootnoteDefinitionLabelString:I,gfmFootnoteDefinition:P}},{canContainEols:["delete"],enter:{strikethrough:j},exit:{strikethrough:$}},{enter:{table:D,tableData:V,tableHeader:V,tableRow:R},exit:{codeText:H,table:L,tableData:B,tableHeader:B,tableRow:B}},{exit:{taskListCheckValueChecked:J,taskListCheckValueUnchecked:J,paragraph:W}}]),a.push({extensions:[{unsafe:[{character:"@",before:"[+\\-.\\w]",after:"[\\-.\\w]",inConstruct:l,notInConstruct:u},{character:".",before:"[Ww]",after:"[\\-.\\w]",inConstruct:l,notInConstruct:u},{character:":",before:"[ps]",after:"\\/",inConstruct:l,notInConstruct:u}]},(t=!1,n&&n.firstLineBlank&&(t=!0),{handlers:{footnoteDefinition:function(e,n,r,i){let o=r.createTracker(i),a=o.move("[^"),s=r.enter("footnoteDefinition"),l=r.enter("label");return a+=o.move(r.safe(r.associationId(e),{before:a,after:"]"})),l(),a+=o.move("]:"),e.children&&e.children.length>0&&(o.shift(4),a+=o.move((t?"\n":" ")+r.indentLines(r.containerFlow(e,o.current()),t?T:C))),s(),a},footnoteReference:E},unsafe:[{character:"[",inConstruct:["label","phrasing","reference"]}]}),{unsafe:[{character:"~",inConstruct:"phrasing",notInConstruct:O}],handlers:{delete:N}},function(e){let t=e||{},n=t.tableCellPadding,r=t.tablePipeAlign,i=t.stringLength,o=n?" ":"|";return{unsafe:[{character:"\r",inConstruct:"tableCell"},{character:"\n",inConstruct:"tableCell"},{atBreak:!0,character:"|",after:"[	 :-]"},{character:"|",inConstruct:"tableCell"},{atBreak:!0,character:":",after:"-"},{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{inlineCode:function(e,t,n){let r=Z.p.inlineCode(e,t,n);return n.stack.includes("tableCell")&&(r=r.replace(/\|/g,"\\$&")),r},table:function(e,t,n,r){return s(function(e,t,n){let r=e.children,i=-1,o=[],a=t.enter("table");for(;++i<r.length;)o[i]=l(r[i],t,n);return a(),o}(e,n,r),e.align)},tableCell:a,tableRow:function(e,t,n,r){let i=s([l(e,n,r)]);return i.slice(0,i.indexOf("\n"))}}};function a(e,t,n,r){let i=n.enter("tableCell"),a=n.enter("phrasing"),s=n.containerPhrasing(e,{...r,before:o,after:o});return a(),i(),s}function s(e,t){return function(e,t){let n=t||{},r=(n.align||[]).concat(),i=n.stringLength||M,o=[],a=[],s=[],l=[],u=0,c=-1;for(;++c<e.length;){let t=[],r=[],o=-1;for(e[c].length>u&&(u=e[c].length);++o<e[c].length;){var f;let a=null==(f=e[c][o])?"":String(f);if(!1!==n.alignDelimiters){let e=i(a);r[o]=e,(void 0===l[o]||e>l[o])&&(l[o]=e)}t.push(a)}a[c]=t,s[c]=r}let d=-1;if("object"==typeof r&&"length"in r)for(;++d<u;)o[d]=F(r[d]);else{let e=F(r);for(;++d<u;)o[d]=e}d=-1;let p=[],h=[];for(;++d<u;){let e=o[d],t="",r="";99===e?(t=":",r=":"):108===e?t=":":114===e&&(r=":");let i=!1===n.alignDelimiters?1:Math.max(1,l[d]-t.length-r.length),a=t+"-".repeat(i)+r;!1!==n.alignDelimiters&&((i=t.length+i+r.length)>l[d]&&(l[d]=i),h[d]=i),p[d]=a}a.splice(1,0,p),s.splice(1,0,h),c=-1;let m=[];for(;++c<a.length;){let e=a[c],t=s[c];d=-1;let r=[];for(;++d<u;){let i=e[d]||"",a="",s="";if(!1!==n.alignDelimiters){let e=l[d]-(t[d]||0),n=o[d];114===n?a=" ".repeat(e):99===n?e%2?(a=" ".repeat(e/2+.5),s=" ".repeat(e/2-.5)):s=a=" ".repeat(e/2):s=" ".repeat(e)}!1===n.delimiterStart||d||r.push("|"),!1!==n.padding&&(!1!==n.alignDelimiters||""!==i)&&(!1!==n.delimiterStart||d)&&r.push(" "),!1!==n.alignDelimiters&&r.push(a),r.push(i),!1!==n.alignDelimiters&&r.push(s),!1!==n.padding&&r.push(" "),(!1!==n.delimiterEnd||d!==u-1)&&r.push("|")}m.push(!1===n.delimiterEnd?r.join("").replace(/ +$/,""):r.join(""))}return m.join("\n")}(e,{align:t,alignDelimiters:r,padding:n,stringLength:i})}function l(e,t,n){let r=e.children,i=-1,o=[],s=t.enter("tableRow");for(;++i<r.length;)o[i]=a(r[i],e,t,n);return s(),o}}(n),{unsafe:[{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{listItem:q}}]})}},79168:(e,t,n)=>{"use strict";n.d(t,{N:()=>i});var r=n(3407);function i(e,t,n,i){let o=i?i-1:1/0,a=0;return function(i){return(0,r.On)(i)?(e.enter(n),function i(s){return(0,r.On)(s)&&a++<o?(e.consume(s),i):(e.exit(n),t(s))}(i)):t(i)}}},84151:(e,t,n)=>{"use strict";n.d(t,{d:()=>i});let r={};function i(e,t){let n=t||r;return o(e,"boolean"!=typeof n.includeImageAlt||n.includeImageAlt,"boolean"!=typeof n.includeHtml||n.includeHtml)}function o(e,t,n){var r;if((r=e)&&"object"==typeof r){if("value"in e)return"html"!==e.type||n?e.value:"";if(t&&"alt"in e&&e.alt)return e.alt;if("children"in e)return a(e.children,t,n)}return Array.isArray(e)?a(e,t,n):""}function a(e,t,n){let r=[],i=-1;for(;++i<e.length;)r[i]=o(e[i],t,n);return r.join("")}},85037:(e,t,n)=>{"use strict";n.d(t,{V:()=>ta});var r={};n.r(r),n.d(r,{attentionMarkers:()=>ep,contentInitial:()=>es,disable:()=>eh,document:()=>ea,flow:()=>eu,flowInitial:()=>el,insideSpan:()=>ed,string:()=>ec,text:()=>ef});var i=n(84151),o=n(17032);class a{constructor(e){this.left=e?[...e]:[],this.right=[]}get(e){if(e<0||e>=this.left.length+this.right.length)throw RangeError("Cannot access index `"+e+"` in a splice buffer of size `"+(this.left.length+this.right.length)+"`");return e<this.left.length?this.left[e]:this.right[this.right.length-e+this.left.length-1]}get length(){return this.left.length+this.right.length}shift(){return this.setCursor(0),this.right.pop()}slice(e,t){let n=null==t?1/0:t;return n<this.left.length?this.left.slice(e,n):e>this.left.length?this.right.slice(this.right.length-n+this.left.length,this.right.length-e+this.left.length).reverse():this.left.slice(e).concat(this.right.slice(this.right.length-n+this.left.length).reverse())}splice(e,t,n){this.setCursor(Math.trunc(e));let r=this.right.splice(this.right.length-(t||0),1/0);return n&&s(this.left,n),r.reverse()}pop(){return this.setCursor(1/0),this.left.pop()}push(e){this.setCursor(1/0),this.left.push(e)}pushMany(e){this.setCursor(1/0),s(this.left,e)}unshift(e){this.setCursor(0),this.right.push(e)}unshiftMany(e){this.setCursor(0),s(this.right,e.reverse())}setCursor(e){if(e!==this.left.length&&(!(e>this.left.length)||0!==this.right.length)&&(!(e<0)||0!==this.left.length))if(e<this.left.length){let t=this.left.splice(e,1/0);s(this.right,t.reverse())}else{let t=this.right.splice(this.left.length+this.right.length-e,1/0);s(this.left,t.reverse())}}}function s(e,t){let n=0;if(t.length<1e4)e.push(...t);else for(;n<t.length;)e.push(...t.slice(n,n+1e4)),n+=1e4}function l(e){let t,n,r,i,s,l,u,c={},f=-1,d=new a(e);for(;++f<d.length;){for(;f in c;)f=c[f];if(t=d.get(f),f&&"chunkFlow"===t[1].type&&"listItemPrefix"===d.get(f-1)[1].type&&((r=0)<(l=t[1]._tokenizer.events).length&&"lineEndingBlank"===l[r][1].type&&(r+=2),r<l.length&&"content"===l[r][1].type))for(;++r<l.length&&"content"!==l[r][1].type;)"chunkText"===l[r][1].type&&(l[r][1]._isInFirstContentOfListItem=!0,r++);if("enter"===t[0])t[1].contentType&&(Object.assign(c,function(e,t){let n,r,i=e.get(t)[1],o=e.get(t)[2],a=t-1,s=[],l=i._tokenizer;!l&&(l=o.parser[i.contentType](i.start),i._contentTypeTextTrailing&&(l._contentTypeTextTrailing=!0));let u=l.events,c=[],f={},d=-1,p=i,h=0,m=0,g=[0];for(;p;){for(;e.get(++a)[1]!==p;);s.push(a),!p._tokenizer&&(n=o.sliceStream(p),p.next||n.push(null),r&&l.defineSkip(p.start),p._isInFirstContentOfListItem&&(l._gfmTasklistFirstContentOfListItem=!0),l.write(n),p._isInFirstContentOfListItem&&(l._gfmTasklistFirstContentOfListItem=void 0)),r=p,p=p.next}for(p=i;++d<u.length;)"exit"===u[d][0]&&"enter"===u[d-1][0]&&u[d][1].type===u[d-1][1].type&&u[d][1].start.line!==u[d][1].end.line&&(m=d+1,g.push(m),p._tokenizer=void 0,p.previous=void 0,p=p.next);for(l.events=[],p?(p._tokenizer=void 0,p.previous=void 0):g.pop(),d=g.length;d--;){let t=u.slice(g[d],g[d+1]),n=s.pop();c.push([n,n+t.length-1]),e.splice(n,2,t)}for(c.reverse(),d=-1;++d<c.length;)f[h+c[d][0]]=h+c[d][1],h+=c[d][1]-c[d][0]-1;return f}(d,f)),f=c[f],u=!0);else if(t[1]._container){for(r=f,n=void 0;r--;)if("lineEnding"===(i=d.get(r))[1].type||"lineEndingBlank"===i[1].type)"enter"===i[0]&&(n&&(d.get(n)[1].type="lineEndingBlank"),i[1].type="lineEnding",n=r);else if("linePrefix"===i[1].type||"listItemIndent"===i[1].type);else break;n&&(t[1].end={...d.get(n)[1].start},(s=d.slice(n,f)).unshift(t),d.splice(n,f-n+1,s))}}return(0,o.m)(e,0,1/0,d.slice(0)),!u}var u=n(38220),c=n(79168),f=n(3407);let d={tokenize:function(e){let t,n=e.attempt(this.parser.constructs.contentInitial,function(t){return null===t?void e.consume(t):(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),(0,c.N)(e,n,"linePrefix"))},function(n){return e.enter("paragraph"),function n(r){let i=e.enter("chunkText",{contentType:"text",previous:t});return t&&(t.next=i),t=i,function t(r){if(null===r){e.exit("chunkText"),e.exit("paragraph"),e.consume(r);return}return(0,f.HP)(r)?(e.consume(r),e.exit("chunkText"),n):(e.consume(r),t)}(r)}(n)});return n}},p={tokenize:function(e){let t,n,r,i=this,a=[],s=0;return l;function l(t){if(s<a.length){let n=a[s];return i.containerState=n[1],e.attempt(n[0].continuation,u,c)(t)}return c(t)}function u(e){if(s++,i.containerState._closeFlow){let n;i.containerState._closeFlow=void 0,t&&b();let r=i.events.length,a=r;for(;a--;)if("exit"===i.events[a][0]&&"chunkFlow"===i.events[a][1].type){n=i.events[a][1].end;break}k(s);let l=r;for(;l<i.events.length;)i.events[l][1].end={...n},l++;return(0,o.m)(i.events,a+1,0,i.events.slice(r)),i.events.length=l,c(e)}return l(e)}function c(n){if(s===a.length){if(!t)return m(n);if(t.currentConstruct&&t.currentConstruct.concrete)return y(n);i.interrupt=!!(t.currentConstruct&&!t._gfmTableDynamicInterruptHack)}return i.containerState={},e.check(h,d,p)(n)}function d(e){return t&&b(),k(s),m(e)}function p(e){return i.parser.lazy[i.now().line]=s!==a.length,r=i.now().offset,y(e)}function m(t){return i.containerState={},e.attempt(h,g,y)(t)}function g(e){return s++,a.push([i.currentConstruct,i.containerState]),m(e)}function y(r){if(null===r){t&&b(),k(0),e.consume(r);return}return t=t||i.parser.flow(i.now()),e.enter("chunkFlow",{_tokenizer:t,contentType:"flow",previous:n}),function t(n){if(null===n){v(e.exit("chunkFlow"),!0),k(0),e.consume(n);return}return(0,f.HP)(n)?(e.consume(n),v(e.exit("chunkFlow")),s=0,i.interrupt=void 0,l):(e.consume(n),t)}(r)}function v(e,a){let l=i.sliceStream(e);if(a&&l.push(null),e.previous=n,n&&(n.next=e),n=e,t.defineSkip(e.start),t.write(l),i.parser.lazy[e.start.line]){let e,n,a=t.events.length;for(;a--;)if(t.events[a][1].start.offset<r&&(!t.events[a][1].end||t.events[a][1].end.offset>r))return;let l=i.events.length,u=l;for(;u--;)if("exit"===i.events[u][0]&&"chunkFlow"===i.events[u][1].type){if(e){n=i.events[u][1].end;break}e=!0}for(k(s),a=l;a<i.events.length;)i.events[a][1].end={...n},a++;(0,o.m)(i.events,u+1,0,i.events.slice(l)),i.events.length=a}}function k(t){let n=a.length;for(;n-- >t;){let t=a[n];i.containerState=t[1],t[0].exit.call(i,e)}a.length=t}function b(){t.write([null]),n=void 0,t=void 0,i.containerState._closeFlow=void 0}}},h={tokenize:function(e,t,n){return(0,c.N)(e,e.attempt(this.parser.constructs.document,t,n),"linePrefix",this.parser.constructs.disable.null.includes("codeIndented")?void 0:4)}};var m=n(33634);let g={resolve:function(e){return l(e),e},tokenize:function(e,t){let n;return function(t){return e.enter("content"),n=e.enter("chunkContent",{contentType:"content"}),r(t)};function r(t){return null===t?i(t):(0,f.HP)(t)?e.check(y,o,i)(t):(e.consume(t),r)}function i(n){return e.exit("chunkContent"),e.exit("content"),t(n)}function o(t){return e.consume(t),e.exit("chunkContent"),n.next=e.enter("chunkContent",{contentType:"content",previous:n}),n=n.next,r}}},y={partial:!0,tokenize:function(e,t,n){let r=this;return function(t){return e.exit("chunkContent"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),(0,c.N)(e,i,"linePrefix")};function i(i){if(null===i||(0,f.HP)(i))return n(i);let o=r.events[r.events.length-1];return!r.parser.constructs.disable.null.includes("codeIndented")&&o&&"linePrefix"===o[1].type&&o[2].sliceSerialize(o[1],!0).length>=4?t(i):e.interrupt(r.parser.constructs.flow,n,t)(i)}}},v={tokenize:function(e){let t=this,n=e.attempt(m.B,function(r){return null===r?void e.consume(r):(e.enter("lineEndingBlank"),e.consume(r),e.exit("lineEndingBlank"),t.currentConstruct=void 0,n)},e.attempt(this.parser.constructs.flowInitial,r,(0,c.N)(e,e.attempt(this.parser.constructs.flow,r,e.attempt(g,r)),"linePrefix")));return n;function r(r){return null===r?void e.consume(r):(e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),t.currentConstruct=void 0,n)}}},k={resolveAll:w()},b=_("string"),x=_("text");function _(e){return{resolveAll:w("text"===e?z:void 0),tokenize:function(t){let n=this,r=this.parser.constructs[e],i=t.attempt(r,o,a);return o;function o(e){return l(e)?i(e):a(e)}function a(e){return null===e?void t.consume(e):(t.enter("data"),t.consume(e),s)}function s(e){return l(e)?(t.exit("data"),i(e)):(t.consume(e),s)}function l(e){if(null===e)return!0;let t=r[e],i=-1;if(t)for(;++i<t.length;){let e=t[i];if(!e.previous||e.previous.call(n,n.previous))return!0}return!1}}}}function w(e){return function(t,n){let r,i=-1;for(;++i<=t.length;)void 0===r?t[i]&&"data"===t[i][1].type&&(r=i,i++):t[i]&&"data"===t[i][1].type||(i!==r+2&&(t[r][1].end=t[i-1][1].end,t.splice(r+2,i-r-2),i=r+2),r=void 0);return e?e(t,n):t}}function z(e,t){let n=0;for(;++n<=e.length;)if((n===e.length||"lineEnding"===e[n][1].type)&&"data"===e[n-1][1].type){let r,i=e[n-1][1],o=t.sliceStream(i),a=o.length,s=-1,l=0;for(;a--;){let e=o[a];if("string"==typeof e){for(s=e.length;32===e.charCodeAt(s-1);)l++,s--;if(s)break;s=-1}else if(-2===e)r=!0,l++;else if(-1===e);else{a++;break}}if(t._contentTypeTextTrailing&&n===e.length&&(l=0),l){let o={type:n===e.length||r||l<2?"lineSuffix":"hardBreakTrailing",start:{_bufferIndex:a?s:i.start._bufferIndex+s,_index:i.start._index+a,line:i.end.line,column:i.end.column-l,offset:i.end.offset-l},end:{...i.end}};i.end={...o.start},i.start.offset===i.end.offset?Object.assign(i,o):(e.splice(n,0,["enter",o,t],["exit",o,t]),n+=2)}n++}return e}let S={name:"thematicBreak",tokenize:function(e,t,n){let r,i=0;return function(o){var a;return e.enter("thematicBreak"),r=a=o,function o(a){return a===r?(e.enter("thematicBreakSequence"),function t(n){return n===r?(e.consume(n),i++,t):(e.exit("thematicBreakSequence"),(0,f.On)(n)?(0,c.N)(e,o,"whitespace")(n):o(n))}(a)):i>=3&&(null===a||(0,f.HP)(a))?(e.exit("thematicBreak"),t(a)):n(a)}(a)}}},A={continuation:{tokenize:function(e,t,n){let r=this;return r.containerState._closeFlow=void 0,e.check(m.B,function(n){return r.containerState.furtherBlankLines=r.containerState.furtherBlankLines||r.containerState.initialBlankLine,(0,c.N)(e,t,"listItemIndent",r.containerState.size+1)(n)},function(n){return r.containerState.furtherBlankLines||!(0,f.On)(n)?(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,i(n)):(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,e.attempt(P,t,i)(n))});function i(i){return r.containerState._closeFlow=!0,r.interrupt=void 0,(0,c.N)(e,e.attempt(A,t,n),"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(i)}}},exit:function(e){e.exit(this.containerState.type)},name:"list",tokenize:function(e,t,n){let r=this,i=r.events[r.events.length-1],o=i&&"linePrefix"===i[1].type?i[2].sliceSerialize(i[1],!0).length:0,a=0;return function(t){let i=r.containerState.type||(42===t||43===t||45===t?"listUnordered":"listOrdered");if("listUnordered"===i?!r.containerState.marker||t===r.containerState.marker:(0,f.BM)(t)){if(r.containerState.type||(r.containerState.type=i,e.enter(i,{_container:!0})),"listUnordered"===i)return e.enter("listItemPrefix"),42===t||45===t?e.check(S,n,s)(t):s(t);if(!r.interrupt||49===t)return e.enter("listItemPrefix"),e.enter("listItemValue"),function t(i){return(0,f.BM)(i)&&++a<10?(e.consume(i),t):(!r.interrupt||a<2)&&(r.containerState.marker?i===r.containerState.marker:41===i||46===i)?(e.exit("listItemValue"),s(i)):n(i)}(t)}return n(t)};function s(t){return e.enter("listItemMarker"),e.consume(t),e.exit("listItemMarker"),r.containerState.marker=r.containerState.marker||t,e.check(m.B,r.interrupt?n:l,e.attempt(I,c,u))}function l(e){return r.containerState.initialBlankLine=!0,o++,c(e)}function u(t){return(0,f.On)(t)?(e.enter("listItemPrefixWhitespace"),e.consume(t),e.exit("listItemPrefixWhitespace"),c):n(t)}function c(n){return r.containerState.size=o+r.sliceSerialize(e.exit("listItemPrefix"),!0).length,t(n)}}},I={partial:!0,tokenize:function(e,t,n){let r=this;return(0,c.N)(e,function(e){let i=r.events[r.events.length-1];return!(0,f.On)(e)&&i&&"listItemPrefixWhitespace"===i[1].type?t(e):n(e)},"listItemPrefixWhitespace",r.parser.constructs.disable.null.includes("codeIndented")?void 0:5)}},P={partial:!0,tokenize:function(e,t,n){let r=this;return(0,c.N)(e,function(e){let i=r.events[r.events.length-1];return i&&"listItemIndent"===i[1].type&&i[2].sliceSerialize(i[1],!0).length===r.containerState.size?t(e):n(e)},"listItemIndent",r.containerState.size+1)}},E={continuation:{tokenize:function(e,t,n){let r=this;return function(t){return(0,f.On)(t)?(0,c.N)(e,i,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):i(t)};function i(r){return e.attempt(E,t,n)(r)}}},exit:function(e){e.exit("blockQuote")},name:"blockQuote",tokenize:function(e,t,n){let r=this;return function(t){if(62===t){let n=r.containerState;return n.open||(e.enter("blockQuote",{_container:!0}),n.open=!0),e.enter("blockQuotePrefix"),e.enter("blockQuoteMarker"),e.consume(t),e.exit("blockQuoteMarker"),i}return n(t)};function i(n){return(0,f.On)(n)?(e.enter("blockQuotePrefixWhitespace"),e.consume(n),e.exit("blockQuotePrefixWhitespace"),e.exit("blockQuotePrefix"),t):(e.exit("blockQuotePrefix"),t(n))}}};function C(e,t,n,r,i,o,a,s,l){let u=l||1/0,c=0;return function(t){return 60===t?(e.enter(r),e.enter(i),e.enter(o),e.consume(t),e.exit(o),d):null===t||32===t||41===t||(0,f.JQ)(t)?n(t):(e.enter(r),e.enter(a),e.enter(s),e.enter("chunkString",{contentType:"string"}),m(t))};function d(n){return 62===n?(e.enter(o),e.consume(n),e.exit(o),e.exit(i),e.exit(r),t):(e.enter(s),e.enter("chunkString",{contentType:"string"}),p(n))}function p(t){return 62===t?(e.exit("chunkString"),e.exit(s),d(t)):null===t||60===t||(0,f.HP)(t)?n(t):(e.consume(t),92===t?h:p)}function h(t){return 60===t||62===t||92===t?(e.consume(t),p):p(t)}function m(i){return!c&&(null===i||41===i||(0,f.Ee)(i))?(e.exit("chunkString"),e.exit(s),e.exit(a),e.exit(r),t(i)):c<u&&40===i?(e.consume(i),c++,m):41===i?(e.consume(i),c--,m):null===i||32===i||40===i||(0,f.JQ)(i)?n(i):(e.consume(i),92===i?g:m)}function g(t){return 40===t||41===t||92===t?(e.consume(t),m):m(t)}}function T(e,t,n,r,i,o){let a,s=this,l=0;return function(t){return e.enter(r),e.enter(i),e.consume(t),e.exit(i),e.enter(o),u};function u(d){return l>999||null===d||91===d||93===d&&!a||94===d&&!l&&"_hiddenFootnoteSupport"in s.parser.constructs?n(d):93===d?(e.exit(o),e.enter(i),e.consume(d),e.exit(i),e.exit(r),t):(0,f.HP)(d)?(e.enter("lineEnding"),e.consume(d),e.exit("lineEnding"),u):(e.enter("chunkString",{contentType:"string"}),c(d))}function c(t){return null===t||91===t||93===t||(0,f.HP)(t)||l++>999?(e.exit("chunkString"),u(t)):(e.consume(t),a||(a=!(0,f.On)(t)),92===t?d:c)}function d(t){return 91===t||92===t||93===t?(e.consume(t),l++,c):c(t)}}function O(e,t,n,r,i,o){let a;return function(t){return 34===t||39===t||40===t?(e.enter(r),e.enter(i),e.consume(t),e.exit(i),a=40===t?41:t,s):n(t)};function s(n){return n===a?(e.enter(i),e.consume(n),e.exit(i),e.exit(r),t):(e.enter(o),l(n))}function l(t){return t===a?(e.exit(o),s(a)):null===t?n(t):(0,f.HP)(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),(0,c.N)(e,l,"linePrefix")):(e.enter("chunkString",{contentType:"string"}),u(t))}function u(t){return t===a||null===t||(0,f.HP)(t)?(e.exit("chunkString"),l(t)):(e.consume(t),92===t?d:u)}function d(t){return t===a||92===t?(e.consume(t),u):u(t)}}function j(e,t){let n;return function r(i){return(0,f.HP)(i)?(e.enter("lineEnding"),e.consume(i),e.exit("lineEnding"),n=!0,r):(0,f.On)(i)?(0,c.N)(e,r,n?"linePrefix":"lineSuffix")(i):t(i)}}var $=n(35983);let N={partial:!0,tokenize:function(e,t,n){return function(t){return(0,f.Ee)(t)?j(e,r)(t):n(t)};function r(t){return O(e,i,n,"definitionTitle","definitionTitleMarker","definitionTitleString")(t)}function i(t){return(0,f.On)(t)?(0,c.N)(e,o,"whitespace")(t):o(t)}function o(e){return null===e||(0,f.HP)(e)?t(e):n(e)}}},M={name:"codeIndented",tokenize:function(e,t,n){let r=this;return function(t){return e.enter("codeIndented"),(0,c.N)(e,i,"linePrefix",5)(t)};function i(t){let i=r.events[r.events.length-1];return i&&"linePrefix"===i[1].type&&i[2].sliceSerialize(i[1],!0).length>=4?function t(n){return null===n?o(n):(0,f.HP)(n)?e.attempt(F,t,o)(n):(e.enter("codeFlowValue"),function n(r){return null===r||(0,f.HP)(r)?(e.exit("codeFlowValue"),t(r)):(e.consume(r),n)}(n))}(t):n(t)}function o(n){return e.exit("codeIndented"),t(n)}}},F={partial:!0,tokenize:function(e,t,n){let r=this;return i;function i(t){return r.parser.lazy[r.now().line]?n(t):(0,f.HP)(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),i):(0,c.N)(e,o,"linePrefix",5)(t)}function o(e){let o=r.events[r.events.length-1];return o&&"linePrefix"===o[1].type&&o[2].sliceSerialize(o[1],!0).length>=4?t(e):(0,f.HP)(e)?i(e):n(e)}}},Z={name:"setextUnderline",resolveTo:function(e,t){let n,r,i,o=e.length;for(;o--;)if("enter"===e[o][0]){if("content"===e[o][1].type){n=o;break}"paragraph"===e[o][1].type&&(r=o)}else"content"===e[o][1].type&&e.splice(o,1),i||"definition"!==e[o][1].type||(i=o);let a={type:"setextHeading",start:{...e[n][1].start},end:{...e[e.length-1][1].end}};return e[r][1].type="setextHeadingText",i?(e.splice(r,0,["enter",a,t]),e.splice(i+1,0,["exit",e[n][1],t]),e[n][1].end={...e[i][1].end}):e[n][1]=a,e.push(["exit",a,t]),e},tokenize:function(e,t,n){let r,i=this;return function(t){var a;let s,l=i.events.length;for(;l--;)if("lineEnding"!==i.events[l][1].type&&"linePrefix"!==i.events[l][1].type&&"content"!==i.events[l][1].type){s="paragraph"===i.events[l][1].type;break}return!i.parser.lazy[i.now().line]&&(i.interrupt||s)?(e.enter("setextHeadingLine"),r=t,a=t,e.enter("setextHeadingLineSequence"),function t(n){return n===r?(e.consume(n),t):(e.exit("setextHeadingLineSequence"),(0,f.On)(n)?(0,c.N)(e,o,"lineSuffix")(n):o(n))}(a)):n(t)};function o(r){return null===r||(0,f.HP)(r)?(e.exit("setextHeadingLine"),t(r)):n(r)}}},D=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","search","section","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"],L=["pre","script","style","textarea"],R={partial:!0,tokenize:function(e,t,n){return function(r){return e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),e.attempt(m.B,t,n)}}},B={partial:!0,tokenize:function(e,t,n){let r=this;return function(t){return(0,f.HP)(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),i):n(t)};function i(e){return r.parser.lazy[r.now().line]?n(e):t(e)}}},V={partial:!0,tokenize:function(e,t,n){let r=this;return function(t){return null===t?n(t):(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),i)};function i(e){return r.parser.lazy[r.now().line]?n(e):t(e)}}},H={concrete:!0,name:"codeFenced",tokenize:function(e,t,n){let r,i=this,o={partial:!0,tokenize:function(e,t,n){let o=0;return function(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),a};function a(t){return e.enter("codeFencedFence"),(0,f.On)(t)?(0,c.N)(e,l,"linePrefix",i.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):l(t)}function l(t){return t===r?(e.enter("codeFencedFenceSequence"),function t(i){return i===r?(o++,e.consume(i),t):o>=s?(e.exit("codeFencedFenceSequence"),(0,f.On)(i)?(0,c.N)(e,u,"whitespace")(i):u(i)):n(i)}(t)):n(t)}function u(r){return null===r||(0,f.HP)(r)?(e.exit("codeFencedFence"),t(r)):n(r)}}},a=0,s=0;return function(t){var o=t;let u=i.events[i.events.length-1];return a=u&&"linePrefix"===u[1].type?u[2].sliceSerialize(u[1],!0).length:0,r=o,e.enter("codeFenced"),e.enter("codeFencedFence"),e.enter("codeFencedFenceSequence"),function t(i){return i===r?(s++,e.consume(i),t):s<3?n(i):(e.exit("codeFencedFenceSequence"),(0,f.On)(i)?(0,c.N)(e,l,"whitespace")(i):l(i))}(o)};function l(o){return null===o||(0,f.HP)(o)?(e.exit("codeFencedFence"),i.interrupt?t(o):e.check(V,d,g)(o)):(e.enter("codeFencedFenceInfo"),e.enter("chunkString",{contentType:"string"}),function t(i){return null===i||(0,f.HP)(i)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),l(i)):(0,f.On)(i)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),(0,c.N)(e,u,"whitespace")(i)):96===i&&i===r?n(i):(e.consume(i),t)}(o))}function u(t){return null===t||(0,f.HP)(t)?l(t):(e.enter("codeFencedFenceMeta"),e.enter("chunkString",{contentType:"string"}),function t(i){return null===i||(0,f.HP)(i)?(e.exit("chunkString"),e.exit("codeFencedFenceMeta"),l(i)):96===i&&i===r?n(i):(e.consume(i),t)}(t))}function d(t){return e.attempt(o,g,p)(t)}function p(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),h}function h(t){return a>0&&(0,f.On)(t)?(0,c.N)(e,m,"linePrefix",a+1)(t):m(t)}function m(t){return null===t||(0,f.HP)(t)?e.check(V,d,g)(t):(e.enter("codeFlowValue"),function t(n){return null===n||(0,f.HP)(n)?(e.exit("codeFlowValue"),m(n)):(e.consume(n),t)}(t))}function g(n){return e.exit("codeFenced"),t(n)}}},U=document.createElement("i");function J(e){let t="&"+e+";";U.innerHTML=t;let n=U.textContent;return(59!==n.charCodeAt(n.length-1)||"semi"===e)&&n!==t&&n}let W={name:"characterReference",tokenize:function(e,t,n){let r,i,o=this,a=0;return function(t){return e.enter("characterReference"),e.enter("characterReferenceMarker"),e.consume(t),e.exit("characterReferenceMarker"),s};function s(t){return 35===t?(e.enter("characterReferenceMarkerNumeric"),e.consume(t),e.exit("characterReferenceMarkerNumeric"),l):(e.enter("characterReferenceValue"),r=31,i=f.lV,u(t))}function l(t){return 88===t||120===t?(e.enter("characterReferenceMarkerHexadecimal"),e.consume(t),e.exit("characterReferenceMarkerHexadecimal"),e.enter("characterReferenceValue"),r=6,i=f.ok,u):(e.enter("characterReferenceValue"),r=7,i=f.BM,u(t))}function u(s){if(59===s&&a){let r=e.exit("characterReferenceValue");return i!==f.lV||J(o.sliceSerialize(r))?(e.enter("characterReferenceMarker"),e.consume(s),e.exit("characterReferenceMarker"),e.exit("characterReference"),t):n(s)}return i(s)&&a++<r?(e.consume(s),u):n(s)}}},q={name:"characterEscape",tokenize:function(e,t,n){return function(t){return e.enter("characterEscape"),e.enter("escapeMarker"),e.consume(t),e.exit("escapeMarker"),r};function r(r){return(0,f.ol)(r)?(e.enter("characterEscapeValue"),e.consume(r),e.exit("characterEscapeValue"),e.exit("characterEscape"),t):n(r)}}},Q={name:"lineEnding",tokenize:function(e,t){return function(n){return e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),(0,c.N)(e,t,"linePrefix")}}};var G=n(41006);let K={name:"labelEnd",resolveAll:function(e){let t=-1,n=[];for(;++t<e.length;){let r=e[t][1];if(n.push(e[t]),"labelImage"===r.type||"labelLink"===r.type||"labelEnd"===r.type){let e="labelImage"===r.type?4:2;r.type="data",t+=e}}return e.length!==n.length&&(0,o.m)(e,0,e.length,n),e},resolveTo:function(e,t){let n,r,i,a,s=e.length,l=0;for(;s--;)if(n=e[s][1],r){if("link"===n.type||"labelLink"===n.type&&n._inactive)break;"enter"===e[s][0]&&"labelLink"===n.type&&(n._inactive=!0)}else if(i){if("enter"===e[s][0]&&("labelImage"===n.type||"labelLink"===n.type)&&!n._balanced&&(r=s,"labelLink"!==n.type)){l=2;break}}else"labelEnd"===n.type&&(i=s);let u={type:"labelLink"===e[r][1].type?"link":"image",start:{...e[r][1].start},end:{...e[e.length-1][1].end}},c={type:"label",start:{...e[r][1].start},end:{...e[i][1].end}},f={type:"labelText",start:{...e[r+l+2][1].end},end:{...e[i-2][1].start}};return a=[["enter",u,t],["enter",c,t]],a=(0,o.V)(a,e.slice(r+1,r+l+3)),a=(0,o.V)(a,[["enter",f,t]]),a=(0,o.V)(a,(0,G.W)(t.parser.constructs.insideSpan.null,e.slice(r+l+4,i-3),t)),a=(0,o.V)(a,[["exit",f,t],e[i-2],e[i-1],["exit",c,t]]),a=(0,o.V)(a,e.slice(i+1)),a=(0,o.V)(a,[["exit",u,t]]),(0,o.m)(e,r,e.length,a),e},tokenize:function(e,t,n){let r,i,o=this,a=o.events.length;for(;a--;)if(("labelImage"===o.events[a][1].type||"labelLink"===o.events[a][1].type)&&!o.events[a][1]._balanced){r=o.events[a][1];break}return function(t){return r?r._inactive?c(t):(i=o.parser.defined.includes((0,$.B)(o.sliceSerialize({start:r.end,end:o.now()}))),e.enter("labelEnd"),e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelEnd"),s):n(t)};function s(t){return 40===t?e.attempt(Y,u,i?u:c)(t):91===t?e.attempt(X,u,i?l:c)(t):i?u(t):c(t)}function l(t){return e.attempt(ee,u,c)(t)}function u(e){return t(e)}function c(e){return r._balanced=!0,n(e)}}},Y={tokenize:function(e,t,n){return function(t){return e.enter("resource"),e.enter("resourceMarker"),e.consume(t),e.exit("resourceMarker"),r};function r(t){return(0,f.Ee)(t)?j(e,i)(t):i(t)}function i(t){return 41===t?u(t):C(e,o,a,"resourceDestination","resourceDestinationLiteral","resourceDestinationLiteralMarker","resourceDestinationRaw","resourceDestinationString",32)(t)}function o(t){return(0,f.Ee)(t)?j(e,s)(t):u(t)}function a(e){return n(e)}function s(t){return 34===t||39===t||40===t?O(e,l,n,"resourceTitle","resourceTitleMarker","resourceTitleString")(t):u(t)}function l(t){return(0,f.Ee)(t)?j(e,u)(t):u(t)}function u(r){return 41===r?(e.enter("resourceMarker"),e.consume(r),e.exit("resourceMarker"),e.exit("resource"),t):n(r)}}},X={tokenize:function(e,t,n){let r=this;return function(t){return T.call(r,e,i,o,"reference","referenceMarker","referenceString")(t)};function i(e){return r.parser.defined.includes((0,$.B)(r.sliceSerialize(r.events[r.events.length-1][1]).slice(1,-1)))?t(e):n(e)}function o(e){return n(e)}}},ee={tokenize:function(e,t,n){return function(t){return e.enter("reference"),e.enter("referenceMarker"),e.consume(t),e.exit("referenceMarker"),r};function r(r){return 93===r?(e.enter("referenceMarker"),e.consume(r),e.exit("referenceMarker"),e.exit("reference"),t):n(r)}}},et={name:"labelStartImage",resolveAll:K.resolveAll,tokenize:function(e,t,n){let r=this;return function(t){return e.enter("labelImage"),e.enter("labelImageMarker"),e.consume(t),e.exit("labelImageMarker"),i};function i(t){return 91===t?(e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelImage"),o):n(t)}function o(e){return 94===e&&"_hiddenFootnoteSupport"in r.parser.constructs?n(e):t(e)}}};var en=n(23362);let er={name:"attention",resolveAll:function(e,t){let n,r,i,a,s,l,u,c,f=-1;for(;++f<e.length;)if("enter"===e[f][0]&&"attentionSequence"===e[f][1].type&&e[f][1]._close){for(n=f;n--;)if("exit"===e[n][0]&&"attentionSequence"===e[n][1].type&&e[n][1]._open&&t.sliceSerialize(e[n][1]).charCodeAt(0)===t.sliceSerialize(e[f][1]).charCodeAt(0)){if((e[n][1]._close||e[f][1]._open)&&(e[f][1].end.offset-e[f][1].start.offset)%3&&!((e[n][1].end.offset-e[n][1].start.offset+e[f][1].end.offset-e[f][1].start.offset)%3))continue;l=e[n][1].end.offset-e[n][1].start.offset>1&&e[f][1].end.offset-e[f][1].start.offset>1?2:1;let d={...e[n][1].end},p={...e[f][1].start};ei(d,-l),ei(p,l),a={type:l>1?"strongSequence":"emphasisSequence",start:d,end:{...e[n][1].end}},s={type:l>1?"strongSequence":"emphasisSequence",start:{...e[f][1].start},end:p},i={type:l>1?"strongText":"emphasisText",start:{...e[n][1].end},end:{...e[f][1].start}},r={type:l>1?"strong":"emphasis",start:{...a.start},end:{...s.end}},e[n][1].end={...a.start},e[f][1].start={...s.end},u=[],e[n][1].end.offset-e[n][1].start.offset&&(u=(0,o.V)(u,[["enter",e[n][1],t],["exit",e[n][1],t]])),u=(0,o.V)(u,[["enter",r,t],["enter",a,t],["exit",a,t],["enter",i,t]]),u=(0,o.V)(u,(0,G.W)(t.parser.constructs.insideSpan.null,e.slice(n+1,f),t)),u=(0,o.V)(u,[["exit",i,t],["enter",s,t],["exit",s,t],["exit",r,t]]),e[f][1].end.offset-e[f][1].start.offset?(c=2,u=(0,o.V)(u,[["enter",e[f][1],t],["exit",e[f][1],t]])):c=0,(0,o.m)(e,n-1,f-n+3,u),f=n+u.length-c-2;break}}for(f=-1;++f<e.length;)"attentionSequence"===e[f][1].type&&(e[f][1].type="data");return e},tokenize:function(e,t){let n,r=this.parser.constructs.attentionMarkers.null,i=this.previous,o=(0,en.S)(i);return function(a){return n=a,e.enter("attentionSequence"),function a(s){if(s===n)return e.consume(s),a;let l=e.exit("attentionSequence"),u=(0,en.S)(s),c=!u||2===u&&o||r.includes(s),f=!o||2===o&&u||r.includes(i);return l._open=!!(42===n?c:c&&(o||!f)),l._close=!!(42===n?f:f&&(u||!c)),t(s)}(a)}}};function ei(e,t){e.column+=t,e.offset+=t,e._bufferIndex+=t}let eo={name:"labelStartLink",resolveAll:K.resolveAll,tokenize:function(e,t,n){let r=this;return function(t){return e.enter("labelLink"),e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelLink"),i};function i(e){return 94===e&&"_hiddenFootnoteSupport"in r.parser.constructs?n(e):t(e)}}},ea={42:A,43:A,45:A,48:A,49:A,50:A,51:A,52:A,53:A,54:A,55:A,56:A,57:A,62:E},es={91:{name:"definition",tokenize:function(e,t,n){let r,i=this;return function(t){var r;return e.enter("definition"),r=t,T.call(i,e,o,n,"definitionLabel","definitionLabelMarker","definitionLabelString")(r)};function o(t){return(r=(0,$.B)(i.sliceSerialize(i.events[i.events.length-1][1]).slice(1,-1)),58===t)?(e.enter("definitionMarker"),e.consume(t),e.exit("definitionMarker"),a):n(t)}function a(t){return(0,f.Ee)(t)?j(e,s)(t):s(t)}function s(t){return C(e,l,n,"definitionDestination","definitionDestinationLiteral","definitionDestinationLiteralMarker","definitionDestinationRaw","definitionDestinationString")(t)}function l(t){return e.attempt(N,u,u)(t)}function u(t){return(0,f.On)(t)?(0,c.N)(e,d,"whitespace")(t):d(t)}function d(o){return null===o||(0,f.HP)(o)?(e.exit("definition"),i.parser.defined.push(r),t(o)):n(o)}}}},el={[-2]:M,[-1]:M,32:M},eu={35:{name:"headingAtx",resolve:function(e,t){let n,r,i=e.length-2,a=3;return"whitespace"===e[3][1].type&&(a+=2),i-2>a&&"whitespace"===e[i][1].type&&(i-=2),"atxHeadingSequence"===e[i][1].type&&(a===i-1||i-4>a&&"whitespace"===e[i-2][1].type)&&(i-=a+1===i?2:4),i>a&&(n={type:"atxHeadingText",start:e[a][1].start,end:e[i][1].end},r={type:"chunkText",start:e[a][1].start,end:e[i][1].end,contentType:"text"},(0,o.m)(e,a,i-a+1,[["enter",n,t],["enter",r,t],["exit",r,t],["exit",n,t]])),e},tokenize:function(e,t,n){let r=0;return function(i){var o;return e.enter("atxHeading"),o=i,e.enter("atxHeadingSequence"),function i(o){return 35===o&&r++<6?(e.consume(o),i):null===o||(0,f.Ee)(o)?(e.exit("atxHeadingSequence"),function n(r){return 35===r?(e.enter("atxHeadingSequence"),function t(r){return 35===r?(e.consume(r),t):(e.exit("atxHeadingSequence"),n(r))}(r)):null===r||(0,f.HP)(r)?(e.exit("atxHeading"),t(r)):(0,f.On)(r)?(0,c.N)(e,n,"whitespace")(r):(e.enter("atxHeadingText"),function t(r){return null===r||35===r||(0,f.Ee)(r)?(e.exit("atxHeadingText"),n(r)):(e.consume(r),t)}(r))}(o)):n(o)}(o)}}},42:S,45:[Z,S],60:{concrete:!0,name:"htmlFlow",resolveTo:function(e){let t=e.length;for(;t--&&("enter"!==e[t][0]||"htmlFlow"!==e[t][1].type););return t>1&&"linePrefix"===e[t-2][1].type&&(e[t][1].start=e[t-2][1].start,e[t+1][1].start=e[t-2][1].start,e.splice(t-2,2)),e},tokenize:function(e,t,n){let r,i,o,a,s,l=this;return function(t){var n;return n=t,e.enter("htmlFlow"),e.enter("htmlFlowData"),e.consume(n),u};function u(a){return 33===a?(e.consume(a),c):47===a?(e.consume(a),i=!0,h):63===a?(e.consume(a),r=3,l.interrupt?t:j):(0,f.CW)(a)?(e.consume(a),o=String.fromCharCode(a),m):n(a)}function c(i){return 45===i?(e.consume(i),r=2,d):91===i?(e.consume(i),r=5,a=0,p):(0,f.CW)(i)?(e.consume(i),r=4,l.interrupt?t:j):n(i)}function d(r){return 45===r?(e.consume(r),l.interrupt?t:j):n(r)}function p(r){let i="CDATA[";return r===i.charCodeAt(a++)?(e.consume(r),a===i.length)?l.interrupt?t:S:p:n(r)}function h(t){return(0,f.CW)(t)?(e.consume(t),o=String.fromCharCode(t),m):n(t)}function m(a){if(null===a||47===a||62===a||(0,f.Ee)(a)){let s=47===a,u=o.toLowerCase();return!s&&!i&&L.includes(u)?(r=1,l.interrupt?t(a):S(a)):D.includes(o.toLowerCase())?(r=6,s)?(e.consume(a),g):l.interrupt?t(a):S(a):(r=7,l.interrupt&&!l.parser.lazy[l.now().line]?n(a):i?function t(n){return(0,f.On)(n)?(e.consume(n),t):w(n)}(a):y(a))}return 45===a||(0,f.lV)(a)?(e.consume(a),o+=String.fromCharCode(a),m):n(a)}function g(r){return 62===r?(e.consume(r),l.interrupt?t:S):n(r)}function y(t){return 47===t?(e.consume(t),w):58===t||95===t||(0,f.CW)(t)?(e.consume(t),v):(0,f.On)(t)?(e.consume(t),y):w(t)}function v(t){return 45===t||46===t||58===t||95===t||(0,f.lV)(t)?(e.consume(t),v):k(t)}function k(t){return 61===t?(e.consume(t),b):(0,f.On)(t)?(e.consume(t),k):y(t)}function b(t){return null===t||60===t||61===t||62===t||96===t?n(t):34===t||39===t?(e.consume(t),s=t,x):(0,f.On)(t)?(e.consume(t),b):function t(n){return null===n||34===n||39===n||47===n||60===n||61===n||62===n||96===n||(0,f.Ee)(n)?k(n):(e.consume(n),t)}(t)}function x(t){return t===s?(e.consume(t),s=null,_):null===t||(0,f.HP)(t)?n(t):(e.consume(t),x)}function _(e){return 47===e||62===e||(0,f.On)(e)?y(e):n(e)}function w(t){return 62===t?(e.consume(t),z):n(t)}function z(t){return null===t||(0,f.HP)(t)?S(t):(0,f.On)(t)?(e.consume(t),z):n(t)}function S(t){return 45===t&&2===r?(e.consume(t),E):60===t&&1===r?(e.consume(t),C):62===t&&4===r?(e.consume(t),$):63===t&&3===r?(e.consume(t),j):93===t&&5===r?(e.consume(t),O):(0,f.HP)(t)&&(6===r||7===r)?(e.exit("htmlFlowData"),e.check(R,N,A)(t)):null===t||(0,f.HP)(t)?(e.exit("htmlFlowData"),A(t)):(e.consume(t),S)}function A(t){return e.check(B,I,N)(t)}function I(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),P}function P(t){return null===t||(0,f.HP)(t)?A(t):(e.enter("htmlFlowData"),S(t))}function E(t){return 45===t?(e.consume(t),j):S(t)}function C(t){return 47===t?(e.consume(t),o="",T):S(t)}function T(t){if(62===t){let n=o.toLowerCase();return L.includes(n)?(e.consume(t),$):S(t)}return(0,f.CW)(t)&&o.length<8?(e.consume(t),o+=String.fromCharCode(t),T):S(t)}function O(t){return 93===t?(e.consume(t),j):S(t)}function j(t){return 62===t?(e.consume(t),$):45===t&&2===r?(e.consume(t),j):S(t)}function $(t){return null===t||(0,f.HP)(t)?(e.exit("htmlFlowData"),N(t)):(e.consume(t),$)}function N(n){return e.exit("htmlFlow"),t(n)}}},61:Z,95:S,96:H,126:H},ec={38:W,92:q},ef={[-5]:Q,[-4]:Q,[-3]:Q,33:et,38:W,42:er,60:[{name:"autolink",tokenize:function(e,t,n){let r=0;return function(t){return e.enter("autolink"),e.enter("autolinkMarker"),e.consume(t),e.exit("autolinkMarker"),e.enter("autolinkProtocol"),i};function i(t){return(0,f.CW)(t)?(e.consume(t),o):64===t?n(t):s(t)}function o(t){return 43===t||45===t||46===t||(0,f.lV)(t)?(r=1,function t(n){return 58===n?(e.consume(n),r=0,a):(43===n||45===n||46===n||(0,f.lV)(n))&&r++<32?(e.consume(n),t):(r=0,s(n))}(t)):s(t)}function a(r){return 62===r?(e.exit("autolinkProtocol"),e.enter("autolinkMarker"),e.consume(r),e.exit("autolinkMarker"),e.exit("autolink"),t):null===r||32===r||60===r||(0,f.JQ)(r)?n(r):(e.consume(r),a)}function s(t){return 64===t?(e.consume(t),l):(0,f.cx)(t)?(e.consume(t),s):n(t)}function l(i){return(0,f.lV)(i)?function i(o){return 46===o?(e.consume(o),r=0,l):62===o?(e.exit("autolinkProtocol").type="autolinkEmail",e.enter("autolinkMarker"),e.consume(o),e.exit("autolinkMarker"),e.exit("autolink"),t):function t(o){if((45===o||(0,f.lV)(o))&&r++<63){let n=45===o?t:i;return e.consume(o),n}return n(o)}(o)}(i):n(i)}}},{name:"htmlText",tokenize:function(e,t,n){let r,i,o,a=this;return function(t){return e.enter("htmlText"),e.enter("htmlTextData"),e.consume(t),s};function s(t){return 33===t?(e.consume(t),l):47===t?(e.consume(t),_):63===t?(e.consume(t),b):(0,f.CW)(t)?(e.consume(t),z):n(t)}function l(t){return 45===t?(e.consume(t),u):91===t?(e.consume(t),i=0,m):(0,f.CW)(t)?(e.consume(t),k):n(t)}function u(t){return 45===t?(e.consume(t),h):n(t)}function d(t){return null===t?n(t):45===t?(e.consume(t),p):(0,f.HP)(t)?(o=d,O(t)):(e.consume(t),d)}function p(t){return 45===t?(e.consume(t),h):d(t)}function h(e){return 62===e?T(e):45===e?p(e):d(e)}function m(t){let r="CDATA[";return t===r.charCodeAt(i++)?(e.consume(t),i===r.length?g:m):n(t)}function g(t){return null===t?n(t):93===t?(e.consume(t),y):(0,f.HP)(t)?(o=g,O(t)):(e.consume(t),g)}function y(t){return 93===t?(e.consume(t),v):g(t)}function v(t){return 62===t?T(t):93===t?(e.consume(t),v):g(t)}function k(t){return null===t||62===t?T(t):(0,f.HP)(t)?(o=k,O(t)):(e.consume(t),k)}function b(t){return null===t?n(t):63===t?(e.consume(t),x):(0,f.HP)(t)?(o=b,O(t)):(e.consume(t),b)}function x(e){return 62===e?T(e):b(e)}function _(t){return(0,f.CW)(t)?(e.consume(t),w):n(t)}function w(t){return 45===t||(0,f.lV)(t)?(e.consume(t),w):function t(n){return(0,f.HP)(n)?(o=t,O(n)):(0,f.On)(n)?(e.consume(n),t):T(n)}(t)}function z(t){return 45===t||(0,f.lV)(t)?(e.consume(t),z):47===t||62===t||(0,f.Ee)(t)?S(t):n(t)}function S(t){return 47===t?(e.consume(t),T):58===t||95===t||(0,f.CW)(t)?(e.consume(t),A):(0,f.HP)(t)?(o=S,O(t)):(0,f.On)(t)?(e.consume(t),S):T(t)}function A(t){return 45===t||46===t||58===t||95===t||(0,f.lV)(t)?(e.consume(t),A):function t(n){return 61===n?(e.consume(n),I):(0,f.HP)(n)?(o=t,O(n)):(0,f.On)(n)?(e.consume(n),t):S(n)}(t)}function I(t){return null===t||60===t||61===t||62===t||96===t?n(t):34===t||39===t?(e.consume(t),r=t,P):(0,f.HP)(t)?(o=I,O(t)):(0,f.On)(t)?(e.consume(t),I):(e.consume(t),E)}function P(t){return t===r?(e.consume(t),r=void 0,C):null===t?n(t):(0,f.HP)(t)?(o=P,O(t)):(e.consume(t),P)}function E(t){return null===t||34===t||39===t||60===t||61===t||96===t?n(t):47===t||62===t||(0,f.Ee)(t)?S(t):(e.consume(t),E)}function C(e){return 47===e||62===e||(0,f.Ee)(e)?S(e):n(e)}function T(r){return 62===r?(e.consume(r),e.exit("htmlTextData"),e.exit("htmlText"),t):n(r)}function O(t){return e.exit("htmlTextData"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),j}function j(t){return(0,f.On)(t)?(0,c.N)(e,$,"linePrefix",a.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):$(t)}function $(t){return e.enter("htmlTextData"),o(t)}}}],91:eo,92:[{name:"hardBreakEscape",tokenize:function(e,t,n){return function(t){return e.enter("hardBreakEscape"),e.consume(t),r};function r(r){return(0,f.HP)(r)?(e.exit("hardBreakEscape"),t(r)):n(r)}}},q],93:K,95:er,96:{name:"codeText",previous:function(e){return 96!==e||"characterEscape"===this.events[this.events.length-1][1].type},resolve:function(e){let t,n,r=e.length-4,i=3;if(("lineEnding"===e[3][1].type||"space"===e[i][1].type)&&("lineEnding"===e[r][1].type||"space"===e[r][1].type)){for(t=i;++t<r;)if("codeTextData"===e[t][1].type){e[i][1].type="codeTextPadding",e[r][1].type="codeTextPadding",i+=2,r-=2;break}}for(t=i-1,r++;++t<=r;)void 0===n?t!==r&&"lineEnding"!==e[t][1].type&&(n=t):(t===r||"lineEnding"===e[t][1].type)&&(e[n][1].type="codeTextData",t!==n+2&&(e[n][1].end=e[t-1][1].end,e.splice(n+2,t-n-2),r-=t-n-2,t=n+2),n=void 0);return e},tokenize:function(e,t,n){let r,i,o=0;return function(t){return e.enter("codeText"),e.enter("codeTextSequence"),function t(n){return 96===n?(e.consume(n),o++,t):(e.exit("codeTextSequence"),a(n))}(t)};function a(l){return null===l?n(l):32===l?(e.enter("space"),e.consume(l),e.exit("space"),a):96===l?(i=e.enter("codeTextSequence"),r=0,function n(a){return 96===a?(e.consume(a),r++,n):r===o?(e.exit("codeTextSequence"),e.exit("codeText"),t(a)):(i.type="codeTextData",s(a))}(l)):(0,f.HP)(l)?(e.enter("lineEnding"),e.consume(l),e.exit("lineEnding"),a):(e.enter("codeTextData"),s(l))}function s(t){return null===t||32===t||96===t||(0,f.HP)(t)?(e.exit("codeTextData"),a(t)):(e.consume(t),s)}}}},ed={null:[er,k]},ep={null:[42,95]},eh={null:[]},em=/[\0\t\n\r]/g;function eg(e,t){let n=Number.parseInt(e,t);return n<9||11===n||n>13&&n<32||n>126&&n<160||n>55295&&n<57344||n>64975&&n<65008||(65535&n)==65535||(65535&n)==65534||n>1114111?"�":String.fromCodePoint(n)}let ey=/\\([!-/:-@[-`{-~])|&(#(?:\d{1,7}|x[\da-f]{1,6})|[\da-z]{1,31});/gi;function ev(e){return e.replace(ey,ek)}function ek(e,t,n){if(t)return t;if(35===n.charCodeAt(0)){let e=n.charCodeAt(1),t=120===e||88===e;return eg(n.slice(t?2:1),t?16:10)}return J(n)||e}var eb=n(16277);let ex={}.hasOwnProperty;function e_(e){return{line:e.line,column:e.column,offset:e.offset}}function ew(e,t){if(e)throw Error("Cannot close `"+e.type+"` ("+(0,eb.L)({start:e.start,end:e.end})+"): a different token (`"+t.type+"`, "+(0,eb.L)({start:t.start,end:t.end})+") is open");throw Error("Cannot close document, a token (`"+t.type+"`, "+(0,eb.L)({start:t.start,end:t.end})+") is still open")}var ez=n(75164);let eS={}.hasOwnProperty;var eA=n(62737),eI=n(69512),eP=n(48411);let eE=[function(e,t,n,r){if("code"===t.type&&(0,eI.m)(t,r)&&("list"===e.type||e.type===t.type&&(0,eI.m)(e,r)))return!1;if("spread"in n&&"boolean"==typeof n.spread){if("paragraph"===e.type&&(e.type===t.type||"definition"===t.type||"heading"===t.type&&(0,eP.f)(t,r)))return;return+!!n.spread}}],eC=["autolink","destinationLiteral","destinationRaw","reference","titleQuote","titleApostrophe"],eT=[{character:"	",after:"[\\r\\n]",inConstruct:"phrasing"},{character:"	",before:"[\\r\\n]",inConstruct:"phrasing"},{character:"	",inConstruct:["codeFencedLangGraveAccent","codeFencedLangTilde"]},{character:"\r",inConstruct:["codeFencedLangGraveAccent","codeFencedLangTilde","codeFencedMetaGraveAccent","codeFencedMetaTilde","destinationLiteral","headingAtx"]},{character:"\n",inConstruct:["codeFencedLangGraveAccent","codeFencedLangTilde","codeFencedMetaGraveAccent","codeFencedMetaTilde","destinationLiteral","headingAtx"]},{character:" ",after:"[\\r\\n]",inConstruct:"phrasing"},{character:" ",before:"[\\r\\n]",inConstruct:"phrasing"},{character:" ",inConstruct:["codeFencedLangGraveAccent","codeFencedLangTilde"]},{character:"!",after:"\\[",inConstruct:"phrasing",notInConstruct:eC},{character:'"',inConstruct:"titleQuote"},{atBreak:!0,character:"#"},{character:"#",inConstruct:"headingAtx",after:"(?:[\r\n]|$)"},{character:"&",after:"[#A-Za-z]",inConstruct:"phrasing"},{character:"'",inConstruct:"titleApostrophe"},{character:"(",inConstruct:"destinationRaw"},{before:"\\]",character:"(",inConstruct:"phrasing",notInConstruct:eC},{atBreak:!0,before:"\\d+",character:")"},{character:")",inConstruct:"destinationRaw"},{atBreak:!0,character:"*",after:"(?:[ 	\r\n*])"},{character:"*",inConstruct:"phrasing",notInConstruct:eC},{atBreak:!0,character:"+",after:"(?:[ 	\r\n])"},{atBreak:!0,character:"-",after:"(?:[ 	\r\n-])"},{atBreak:!0,before:"\\d+",character:".",after:"(?:[ 	\r\n]|$)"},{atBreak:!0,character:"<",after:"[!/?A-Za-z]"},{character:"<",after:"[!/?A-Za-z]",inConstruct:"phrasing",notInConstruct:eC},{character:"<",inConstruct:"destinationLiteral"},{atBreak:!0,character:"="},{atBreak:!0,character:">"},{character:">",inConstruct:"destinationLiteral"},{atBreak:!0,character:"["},{character:"[",inConstruct:"phrasing",notInConstruct:eC},{character:"[",inConstruct:["label","reference"]},{character:"\\",after:"[\\r\\n]",inConstruct:"phrasing"},{character:"]",inConstruct:["label","reference"]},{atBreak:!0,character:"_"},{character:"_",inConstruct:"phrasing",notInConstruct:eC},{atBreak:!0,character:"`"},{character:"`",inConstruct:["codeFencedLangGraveAccent","codeFencedMetaGraveAccent"]},{character:"`",inConstruct:"phrasing",notInConstruct:eC},{atBreak:!0,character:"~"}];function eO(e){return e.label||!e.identifier?e.label||"":ev(e.identifier)}function ej(e){if(!e._compiled){let t=(e.atBreak?"[\\r\\n][\\t ]*":"")+(e.before?"(?:"+e.before+")":"");e._compiled=RegExp((t?"("+t+")":"")+(/[|\\{}()[\]^$+*?.-]/.test(e.character)?"\\":"")+e.character+(e.after?"(?:"+e.after+")":""),"g")}return e._compiled}var e$=n(9038);let eN=/\r?\n|\r/g;function eM(e,t){let n,r=[],i=0,o=0;for(;n=eN.exec(e);)a(e.slice(i,n.index)),r.push(n[0]),i=n.index+n[0].length,o++;return a(e.slice(i)),r.join("");function a(e){r.push(t(e,o,!e))}}var eF=n(91305);function eZ(e,t){return e-t}function eD(e,t){let n,r=/\\(?=[!-/:-@[-`{-~])/g,i=[],o=[],a=e+t,s=-1,l=0;for(;n=r.exec(a);)i.push(n.index);for(;++s<i.length;)l!==i[s]&&o.push(e.slice(l,i[s])),o.push("\\"),l=i[s];return o.push(e.slice(l)),o.join("")}function eL(e){let t=e||{},n=t.now||{},r=t.lineShift||0,i=n.line||1,o=n.column||1;return{move:function(e){let t=e||"",n=t.split(/\r?\n|\r/g),a=n[n.length-1];return i+=n.length-1,o=1===n.length?o+a.length:1+a.length+r,t},current:function(){return{now:{line:i,column:o},lineShift:r}},shift:function(e){r+=e}}}function eR(e){throw Error("Cannot handle value `"+e+"`, expected node")}function eB(e){throw Error("Cannot handle unknown node `"+e.type+"`")}function eV(e,t){if("definition"===e.type&&e.type===t.type)return 0}function eH(e,t){return function(e,t,n){let r,i=t.indexStack,o=e.children||[],a=[],s=-1,l=n.before;i.push(-1);let u=t.createTracker(n);for(;++s<o.length;){let c,f=o[s];if(i[i.length-1]=s,s+1<o.length){let n=t.handle.handlers[o[s+1].type];n&&n.peek&&(n=n.peek),c=n?n(o[s+1],e,t,{before:"",after:"",...u.current()}).charAt(0):""}else c=n.after;a.length>0&&("\r"===l||"\n"===l)&&"html"===f.type&&(a[a.length-1]=a[a.length-1].replace(/(\r?\n|\r)$/," "),l=" ",(u=t.createTracker(n)).move(a.join("")));let d=t.handle(f,e,t,{...u.current(),after:c,before:l});r&&r===d.slice(0,1)&&(d=(0,e$.T)(r.charCodeAt(0))+d.slice(1));let p=t.attentionEncodeSurroundingInfo;t.attentionEncodeSurroundingInfo=void 0,r=void 0,p&&(a.length>0&&p.before&&l===a[a.length-1].slice(-1)&&(a[a.length-1]=a[a.length-1].slice(0,-1)+(0,e$.T)(l.charCodeAt(0))),p.after&&(r=c)),u.move(d),a.push(d),l=d.slice(-1)}return i.pop(),a.join("")}(e,this,t)}function eU(e,t){return function(e,t,n){let r=t.indexStack,i=e.children||[],o=t.createTracker(n),a=[],s=-1;for(r.push(-1);++s<i.length;){let n=i[s];r[r.length-1]=s,a.push(o.move(t.handle(n,e,t,{before:"\n",after:"\n",...o.current()}))),"list"!==n.type&&(t.bulletLastUsed=void 0),s<i.length-1&&a.push(o.move(function(e,t,n,r){let i=r.join.length;for(;i--;){let o=r.join[i](e,t,n,r);if(!0===o||1===o)break;if("number"==typeof o)return"\n".repeat(1+o);if(!1===o)return"\n\n\x3c!----\x3e\n\n"}return"\n\n"}(n,i[s+1],e,t)))}return r.pop(),a.join("")}(e,this,t)}function eJ(e,t){return function(e,t,n){let r=(n.before||"")+(t||"")+(n.after||""),i=[],o=[],a={},s=-1;for(;++s<e.unsafe.length;){let t,n=e.unsafe[s];if(!(0,eF.q)(e.stack,n))continue;let o=e.compilePattern(n);for(;t=o.exec(r);){let e="before"in n||!!n.atBreak,r="after"in n,o=t.index+(e?t[1].length:0);i.includes(o)?(a[o].before&&!e&&(a[o].before=!1),a[o].after&&!r&&(a[o].after=!1)):(i.push(o),a[o]={before:e,after:r})}}i.sort(eZ);let l=n.before?n.before.length:0,u=r.length-(n.after?n.after.length:0);for(s=-1;++s<i.length;){let e=i[s];!(e<l)&&!(e>=u)&&(!(e+1<u)||i[s+1]!==e+1||!a[e].after||a[e+1].before||a[e+1].after)&&(i[s-1]!==e-1||!a[e].before||a[e-1].before||a[e-1].after)&&(l!==e&&o.push(eD(r.slice(l,e),"\\")),l=e,!/[!-/:-@[-`{-~]/.test(r.charAt(e))||n.encode&&n.encode.includes(r.charAt(e))?(o.push((0,e$.T)(r.charCodeAt(e))),l++):o.push("\\"))}return o.push(eD(r.slice(l,u),n.after)),o.join("")}(this,e,t)}function eW(e){if(e)throw e}var eq=n(70011),eQ=n(63758);function eG(e){if("object"!=typeof e||null===e)return!1;let t=Object.getPrototypeOf(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)}var eK=n(18283);let eY={basename:function(e,t){let n;if(void 0!==t&&"string"!=typeof t)throw TypeError('"ext" argument must be a string');eX(e);let r=0,i=-1,o=e.length;if(void 0===t||0===t.length||t.length>e.length){for(;o--;)if(47===e.codePointAt(o)){if(n){r=o+1;break}}else i<0&&(n=!0,i=o+1);return i<0?"":e.slice(r,i)}if(t===e)return"";let a=-1,s=t.length-1;for(;o--;)if(47===e.codePointAt(o)){if(n){r=o+1;break}}else a<0&&(n=!0,a=o+1),s>-1&&(e.codePointAt(o)===t.codePointAt(s--)?s<0&&(i=o):(s=-1,i=a));return r===i?i=a:i<0&&(i=e.length),e.slice(r,i)},dirname:function(e){let t;if(eX(e),0===e.length)return".";let n=-1,r=e.length;for(;--r;)if(47===e.codePointAt(r)){if(t){n=r;break}}else t||(t=!0);return n<0?47===e.codePointAt(0)?"/":".":1===n&&47===e.codePointAt(0)?"//":e.slice(0,n)},extname:function(e){let t;eX(e);let n=e.length,r=-1,i=0,o=-1,a=0;for(;n--;){let s=e.codePointAt(n);if(47===s){if(t){i=n+1;break}continue}r<0&&(t=!0,r=n+1),46===s?o<0?o=n:1!==a&&(a=1):o>-1&&(a=-1)}return o<0||r<0||0===a||1===a&&o===r-1&&o===i+1?"":e.slice(o,r)},join:function(...e){let t,n=-1;for(;++n<e.length;)eX(e[n]),e[n]&&(t=void 0===t?e[n]:t+"/"+e[n]);return void 0===t?".":function(e){eX(e);let t=47===e.codePointAt(0),n=function(e,t){let n,r,i="",o=0,a=-1,s=0,l=-1;for(;++l<=e.length;){if(l<e.length)n=e.codePointAt(l);else if(47===n)break;else n=47;if(47===n){if(a===l-1||1===s);else if(a!==l-1&&2===s){if(i.length<2||2!==o||46!==i.codePointAt(i.length-1)||46!==i.codePointAt(i.length-2)){if(i.length>2){if((r=i.lastIndexOf("/"))!==i.length-1){r<0?(i="",o=0):o=(i=i.slice(0,r)).length-1-i.lastIndexOf("/"),a=l,s=0;continue}}else if(i.length>0){i="",o=0,a=l,s=0;continue}}t&&(i=i.length>0?i+"/..":"..",o=2)}else i.length>0?i+="/"+e.slice(a+1,l):i=e.slice(a+1,l),o=l-a-1;a=l,s=0}else 46===n&&s>-1?s++:s=-1}return i}(e,!t);return 0!==n.length||t||(n="."),n.length>0&&47===e.codePointAt(e.length-1)&&(n+="/"),t?"/"+n:n}(t)},sep:"/"};function eX(e){if("string"!=typeof e)throw TypeError("Path must be a string. Received "+JSON.stringify(e))}let e0={cwd:function(){return"/"}};function e1(e){return!!(null!==e&&"object"==typeof e&&"href"in e&&e.href&&"protocol"in e&&e.protocol&&void 0===e.auth)}let e2=["history","path","basename","stem","extname","dirname"];class e4{constructor(e){let t,n;t=e?e1(e)?{path:e}:"string"==typeof e||function(e){return!!(e&&"object"==typeof e&&"byteLength"in e&&"byteOffset"in e)}(e)?{value:e}:e:{},this.cwd="cwd"in t?"":e0.cwd(),this.data={},this.history=[],this.messages=[],this.value,this.map,this.result,this.stored;let r=-1;for(;++r<e2.length;){let e=e2[r];e in t&&void 0!==t[e]&&null!==t[e]&&(this[e]="history"===e?[...t[e]]:t[e])}for(n in t)e2.includes(n)||(this[n]=t[n])}get basename(){return"string"==typeof this.path?eY.basename(this.path):void 0}set basename(e){e3(e,"basename"),e9(e,"basename"),this.path=eY.join(this.dirname||"",e)}get dirname(){return"string"==typeof this.path?eY.dirname(this.path):void 0}set dirname(e){e6(this.basename,"dirname"),this.path=eY.join(e||"",this.basename)}get extname(){return"string"==typeof this.path?eY.extname(this.path):void 0}set extname(e){if(e9(e,"extname"),e6(this.dirname,"extname"),e){if(46!==e.codePointAt(0))throw Error("`extname` must start with `.`");if(e.includes(".",1))throw Error("`extname` cannot contain multiple dots")}this.path=eY.join(this.dirname,this.stem+(e||""))}get path(){return this.history[this.history.length-1]}set path(e){e1(e)&&(e=function(e){if("string"==typeof e)e=new URL(e);else if(!e1(e)){let t=TypeError('The "path" argument must be of type string or an instance of URL. Received `'+e+"`");throw t.code="ERR_INVALID_ARG_TYPE",t}if("file:"!==e.protocol){let e=TypeError("The URL must be of scheme file");throw e.code="ERR_INVALID_URL_SCHEME",e}return function(e){if(""!==e.hostname){let e=TypeError('File URL host must be "localhost" or empty on darwin');throw e.code="ERR_INVALID_FILE_URL_HOST",e}let t=e.pathname,n=-1;for(;++n<t.length;)if(37===t.codePointAt(n)&&50===t.codePointAt(n+1)){let e=t.codePointAt(n+2);if(70===e||102===e){let e=TypeError("File URL path must not include encoded / characters");throw e.code="ERR_INVALID_FILE_URL_PATH",e}}return decodeURIComponent(t)}(e)}(e)),e3(e,"path"),this.path!==e&&this.history.push(e)}get stem(){return"string"==typeof this.path?eY.basename(this.path,this.extname):void 0}set stem(e){e3(e,"stem"),e9(e,"stem"),this.path=eY.join(this.dirname||"",e+(this.extname||""))}fail(e,t,n){let r=this.message(e,t,n);throw r.fatal=!0,r}info(e,t,n){let r=this.message(e,t,n);return r.fatal=void 0,r}message(e,t,n){let r=new eK.o(e,t,n);return this.path&&(r.name=this.path+":"+r.name,r.file=this.path),r.fatal=!1,this.messages.push(r),r}toString(e){return void 0===this.value?"":"string"==typeof this.value?this.value:new TextDecoder(e||void 0).decode(this.value)}}function e9(e,t){if(e&&e.includes(eY.sep))throw Error("`"+t+"` cannot be a path: did not expect `"+eY.sep+"`")}function e3(e,t){if(!e)throw Error("`"+t+"` cannot be empty")}function e6(e,t){if(!e)throw Error("Setting `"+t+"` requires `path` to be set too")}let e5=function(e){let t=this.constructor.prototype,n=t[e],r=function(){return n.apply(r,arguments)};return Object.setPrototypeOf(r,t),r},e7={}.hasOwnProperty;class e8 extends e5{constructor(){super("copy"),this.Compiler=void 0,this.Parser=void 0,this.attachers=[],this.compiler=void 0,this.freezeIndex=-1,this.frozen=void 0,this.namespace={},this.parser=void 0,this.transformers=function(){let e=[],t={run:function(...t){let n=-1,r=t.pop();if("function"!=typeof r)throw TypeError("Expected function as last argument, not "+r);!function i(o,...a){let s=e[++n],l=-1;if(o)return void r(o);for(;++l<t.length;)(null===a[l]||void 0===a[l])&&(a[l]=t[l]);t=a,s?(function(e,t){let n;return function(...t){let o,a=e.length>t.length;a&&t.push(r);try{o=e.apply(this,t)}catch(e){if(a&&n)throw e;return r(e)}a||(o&&o.then&&"function"==typeof o.then?o.then(i,r):o instanceof Error?r(o):i(o))};function r(e,...i){n||(n=!0,t(e,...i))}function i(e){r(null,e)}})(s,i)(...a):r(null,...a)}(null,...t)},use:function(n){if("function"!=typeof n)throw TypeError("Expected `middelware` to be a function, not "+n);return e.push(n),t}};return t}()}copy(){let e=new e8,t=-1;for(;++t<this.attachers.length;){let n=this.attachers[t];e.use(...n)}return e.data(eq(!0,{},this.namespace)),e}data(e,t){return"string"==typeof e?2==arguments.length?(tn("data",this.frozen),this.namespace[e]=t,this):e7.call(this.namespace,e)&&this.namespace[e]||void 0:e?(tn("data",this.frozen),this.namespace=e,this):this.namespace}freeze(){if(this.frozen)return this;for(;++this.freezeIndex<this.attachers.length;){let[e,...t]=this.attachers[this.freezeIndex];if(!1===t[0])continue;!0===t[0]&&(t[0]=void 0);let n=e.call(this,...t);"function"==typeof n&&this.transformers.use(n)}return this.frozen=!0,this.freezeIndex=1/0,this}parse(e){this.freeze();let t=to(e),n=this.parser||this.Parser;return te("parse",n),n(String(t),t)}process(e,t){let n=this;return this.freeze(),te("process",this.parser||this.Parser),tt("process",this.compiler||this.Compiler),t?r(void 0,t):new Promise(r);function r(r,i){let o=to(e),a=n.parse(o);function s(e,n){e||!n?i(e):r?r(n):((0,eQ.ok)(t,"`done` is defined if `resolve` is not"),t(void 0,n))}n.run(a,o,function(e,t,r){var i,o;if(e||!t||!r)return s(e);let a=n.stringify(t,r);"string"==typeof(i=a)||(o=i)&&"object"==typeof o&&"byteLength"in o&&"byteOffset"in o?r.value=a:r.result=a,s(e,r)})}}processSync(e){let t,n=!1;return this.freeze(),te("processSync",this.parser||this.Parser),tt("processSync",this.compiler||this.Compiler),this.process(e,function(e,r){n=!0,eW(e),t=r}),ti("processSync","process",n),(0,eQ.ok)(t,"we either bailed on an error or have a tree"),t}run(e,t,n){tr(e),this.freeze();let r=this.transformers;return n||"function"!=typeof t||(n=t,t=void 0),n?i(void 0,n):new Promise(i);function i(i,o){(0,eQ.ok)("function"!=typeof t,"`file` can’t be a `done` anymore, we checked");let a=to(t);r.run(e,a,function(t,r,a){let s=r||e;t?o(t):i?i(s):((0,eQ.ok)(n,"`done` is defined if `resolve` is not"),n(void 0,s,a))})}}runSync(e,t){let n,r=!1;return this.run(e,t,function(e,t){eW(e),n=t,r=!0}),ti("runSync","run",r),(0,eQ.ok)(n,"we either bailed on an error or have a tree"),n}stringify(e,t){this.freeze();let n=to(t),r=this.compiler||this.Compiler;return tt("stringify",r),tr(e),r(e,n)}use(e,...t){let n=this.attachers,r=this.namespace;if(tn("use",this.frozen),null==e);else if("function"==typeof e)a(e,t);else if("object"==typeof e)Array.isArray(e)?o(e):i(e);else throw TypeError("Expected usable value, not `"+e+"`");return this;function i(e){if(!("plugins"in e)&&!("settings"in e))throw Error("Expected usable value but received an empty preset, which is probably a mistake: presets typically come with `plugins` and sometimes with `settings`, but this has neither");o(e.plugins),e.settings&&(r.settings=eq(!0,r.settings,e.settings))}function o(e){let t=-1;if(null==e);else if(Array.isArray(e))for(;++t<e.length;){var n=e[t];if("function"==typeof n)a(n,[]);else if("object"==typeof n)if(Array.isArray(n)){let[e,...t]=n;a(e,t)}else i(n);else throw TypeError("Expected usable value, not `"+n+"`")}else throw TypeError("Expected a list of plugins, not `"+e+"`")}function a(e,t){let r=-1,i=-1;for(;++r<n.length;)if(n[r][0]===e){i=r;break}if(-1===i)n.push([e,...t]);else if(t.length>0){let[r,...o]=t,a=n[i][1];eG(a)&&eG(r)&&(r=eq(!0,a,r)),n[i]=[e,r,...o]}}}}function te(e,t){if("function"!=typeof t)throw TypeError("Cannot `"+e+"` without `parser`")}function tt(e,t){if("function"!=typeof t)throw TypeError("Cannot `"+e+"` without `compiler`")}function tn(e,t){if(t)throw Error("Cannot call `"+e+"` on a frozen processor.\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.")}function tr(e){if(!eG(e)||"string"!=typeof e.type)throw TypeError("Expected node, got `"+e+"`")}function ti(e,t,n){if(!n)throw Error("`"+e+"` finished async. Use `"+t+"` instead")}function to(e){var t;return(t=e)&&"object"==typeof t&&"message"in t&&"messages"in t?e:new e4(e)}let ta=new e8().freeze()().use(function(e){let t=this;t.parser=function(n){var a,s;let c,h,m,g;return"string"!=typeof(a={...t.data("settings"),...e,extensions:t.data("micromarkExtensions")||[],mdastExtensions:t.data("fromMarkdownExtensions")||[]})&&(s=a,a=void 0),(function(e){let t={transforms:[],canContainEols:["emphasis","fragment","heading","paragraph","strong"],enter:{autolink:r(v),autolinkProtocol:c,autolinkEmail:c,atxHeading:r(m),blockQuote:r(function(){return{type:"blockquote",children:[]}}),characterEscape:c,characterReference:c,codeFenced:r(h),codeFencedFenceInfo:o,codeFencedFenceMeta:o,codeIndented:r(h,o),codeText:r(function(){return{type:"inlineCode",value:""}},o),codeTextData:c,data:c,codeFlowValue:c,definition:r(function(){return{type:"definition",identifier:"",label:null,title:null,url:""}}),definitionDestinationString:o,definitionLabelString:o,definitionTitleString:o,emphasis:r(function(){return{type:"emphasis",children:[]}}),hardBreakEscape:r(g),hardBreakTrailing:r(g),htmlFlow:r(y,o),htmlFlowData:c,htmlText:r(y,o),htmlTextData:c,image:r(function(){return{type:"image",title:null,url:"",alt:null}}),label:o,link:r(v),listItem:r(function(e){return{type:"listItem",spread:e._spread,checked:null,children:[]}}),listItemValue:function(e){this.data.expectingFirstListItemValue&&(this.stack[this.stack.length-2].start=Number.parseInt(this.sliceSerialize(e),10),this.data.expectingFirstListItemValue=void 0)},listOrdered:r(k,function(){this.data.expectingFirstListItemValue=!0}),listUnordered:r(k),paragraph:r(function(){return{type:"paragraph",children:[]}}),reference:function(){this.data.referenceType="collapsed"},referenceString:o,resourceDestinationString:o,resourceTitleString:o,setextHeading:r(m),strong:r(function(){return{type:"strong",children:[]}}),thematicBreak:r(function(){return{type:"thematicBreak"}})},exit:{atxHeading:s(),atxHeadingSequence:function(e){let t=this.stack[this.stack.length-1];t.depth||(t.depth=this.sliceSerialize(e).length)},autolink:s(),autolinkEmail:function(e){f.call(this,e),this.stack[this.stack.length-1].url="mailto:"+this.sliceSerialize(e)},autolinkProtocol:function(e){f.call(this,e),this.stack[this.stack.length-1].url=this.sliceSerialize(e)},blockQuote:s(),characterEscapeValue:f,characterReferenceMarkerHexadecimal:p,characterReferenceMarkerNumeric:p,characterReferenceValue:function(e){let t,n=this.sliceSerialize(e),r=this.data.characterReferenceType;r?(t=eg(n,"characterReferenceMarkerNumeric"===r?10:16),this.data.characterReferenceType=void 0):t=J(n);let i=this.stack[this.stack.length-1];i.value+=t},characterReference:function(e){this.stack.pop().position.end=e_(e.end)},codeFenced:s(function(){let e=this.resume();this.stack[this.stack.length-1].value=e.replace(/^(\r?\n|\r)|(\r?\n|\r)$/g,""),this.data.flowCodeInside=void 0}),codeFencedFence:function(){this.data.flowCodeInside||(this.buffer(),this.data.flowCodeInside=!0)},codeFencedFenceInfo:function(){let e=this.resume();this.stack[this.stack.length-1].lang=e},codeFencedFenceMeta:function(){let e=this.resume();this.stack[this.stack.length-1].meta=e},codeFlowValue:f,codeIndented:s(function(){let e=this.resume();this.stack[this.stack.length-1].value=e.replace(/(\r?\n|\r)$/g,"")}),codeText:s(function(){let e=this.resume();this.stack[this.stack.length-1].value=e}),codeTextData:f,data:f,definition:s(),definitionDestinationString:function(){let e=this.resume();this.stack[this.stack.length-1].url=e},definitionLabelString:function(e){let t=this.resume(),n=this.stack[this.stack.length-1];n.label=t,n.identifier=(0,$.B)(this.sliceSerialize(e)).toLowerCase()},definitionTitleString:function(){let e=this.resume();this.stack[this.stack.length-1].title=e},emphasis:s(),hardBreakEscape:s(d),hardBreakTrailing:s(d),htmlFlow:s(function(){let e=this.resume();this.stack[this.stack.length-1].value=e}),htmlFlowData:f,htmlText:s(function(){let e=this.resume();this.stack[this.stack.length-1].value=e}),htmlTextData:f,image:s(function(){let e=this.stack[this.stack.length-1];if(this.data.inReference){let t=this.data.referenceType||"shortcut";e.type+="Reference",e.referenceType=t,delete e.url,delete e.title}else delete e.identifier,delete e.label;this.data.referenceType=void 0}),label:function(){let e=this.stack[this.stack.length-1],t=this.resume(),n=this.stack[this.stack.length-1];this.data.inReference=!0,"link"===n.type?n.children=e.children:n.alt=t},labelText:function(e){let t=this.sliceSerialize(e),n=this.stack[this.stack.length-2];n.label=ev(t),n.identifier=(0,$.B)(t).toLowerCase()},lineEnding:function(e){let n=this.stack[this.stack.length-1];if(this.data.atHardBreak){n.children[n.children.length-1].position.end=e_(e.end),this.data.atHardBreak=void 0;return}!this.data.setextHeadingSlurpLineEnding&&t.canContainEols.includes(n.type)&&(c.call(this,e),f.call(this,e))},link:s(function(){let e=this.stack[this.stack.length-1];if(this.data.inReference){let t=this.data.referenceType||"shortcut";e.type+="Reference",e.referenceType=t,delete e.url,delete e.title}else delete e.identifier,delete e.label;this.data.referenceType=void 0}),listItem:s(),listOrdered:s(),listUnordered:s(),paragraph:s(),referenceString:function(e){let t=this.resume(),n=this.stack[this.stack.length-1];n.label=t,n.identifier=(0,$.B)(this.sliceSerialize(e)).toLowerCase(),this.data.referenceType="full"},resourceDestinationString:function(){let e=this.resume();this.stack[this.stack.length-1].url=e},resourceTitleString:function(){let e=this.resume();this.stack[this.stack.length-1].title=e},resource:function(){this.data.inReference=void 0},setextHeading:s(function(){this.data.setextHeadingSlurpLineEnding=void 0}),setextHeadingLineSequence:function(e){this.stack[this.stack.length-1].depth=61===this.sliceSerialize(e).codePointAt(0)?1:2},setextHeadingText:function(){this.data.setextHeadingSlurpLineEnding=!0},strong:s(),thematicBreak:s()}};!function e(t,n){let r=-1;for(;++r<n.length;){let i=n[r];Array.isArray(i)?e(t,i):function(e,t){let n;for(n in t)if(ex.call(t,n))switch(n){case"canContainEols":{let r=t[n];r&&e[n].push(...r);break}case"transforms":{let r=t[n];r&&e[n].push(...r);break}case"enter":case"exit":{let r=t[n];r&&Object.assign(e[n],r)}}}(t,i)}}(t,(e||{}).mdastExtensions||[]);let n={};return function(e){let r={type:"root",children:[]},i={stack:[r],tokenStack:[],config:t,enter:a,exit:l,buffer:o,resume:u,data:n},s=[],c=-1;for(;++c<e.length;)("listOrdered"===e[c][1].type||"listUnordered"===e[c][1].type)&&("enter"===e[c][0]?s.push(c):c=function(e,t,n){let r,i,o,a,s=t-1,l=-1,u=!1;for(;++s<=n;){let t=e[s];switch(t[1].type){case"listUnordered":case"listOrdered":case"blockQuote":"enter"===t[0]?l++:l--,a=void 0;break;case"lineEndingBlank":"enter"===t[0]&&(!r||a||l||o||(o=s),a=void 0);break;case"linePrefix":case"listItemValue":case"listItemMarker":case"listItemPrefix":case"listItemPrefixWhitespace":break;default:a=void 0}if(!l&&"enter"===t[0]&&"listItemPrefix"===t[1].type||-1===l&&"exit"===t[0]&&("listUnordered"===t[1].type||"listOrdered"===t[1].type)){if(r){let a=s;for(i=void 0;a--;){let t=e[a];if("lineEnding"===t[1].type||"lineEndingBlank"===t[1].type){if("exit"===t[0])continue;i&&(e[i][1].type="lineEndingBlank",u=!0),t[1].type="lineEnding",i=a}else if("linePrefix"===t[1].type||"blockQuotePrefix"===t[1].type||"blockQuotePrefixWhitespace"===t[1].type||"blockQuoteMarker"===t[1].type||"listItemIndent"===t[1].type);else break}o&&(!i||o<i)&&(r._spread=!0),r.end=Object.assign({},i?e[i][1].start:t[1].end),e.splice(i||s,0,["exit",r,t[2]]),s++,n++}if("listItemPrefix"===t[1].type){let i={type:"listItem",_spread:!1,start:Object.assign({},t[1].start),end:void 0};r=i,e.splice(s,0,["enter",i,t[2]]),s++,n++,o=void 0,a=!0}}}return e[t][1]._spread=u,n}(e,s.pop(),c));for(c=-1;++c<e.length;){let n=t[e[c][0]];ex.call(n,e[c][1].type)&&n[e[c][1].type].call(Object.assign({sliceSerialize:e[c][2].sliceSerialize},i),e[c][1])}if(i.tokenStack.length>0){let e=i.tokenStack[i.tokenStack.length-1];(e[1]||ew).call(i,void 0,e[0])}for(r.position={start:e_(e.length>0?e[0][1].start:{line:1,column:1,offset:0}),end:e_(e.length>0?e[e.length-2][1].end:{line:1,column:1,offset:0})},c=-1;++c<t.transforms.length;)r=t.transforms[c](r)||r;return r};function r(e,t){return function(n){a.call(this,e(n),n),t&&t.call(this,n)}}function o(){this.stack.push({type:"fragment",children:[]})}function a(e,t,n){this.stack[this.stack.length-1].children.push(e),this.stack.push(e),this.tokenStack.push([t,n||void 0]),e.position={start:e_(t.start),end:void 0}}function s(e){return function(t){e&&e.call(this,t),l.call(this,t)}}function l(e,t){let n=this.stack.pop(),r=this.tokenStack.pop();if(r)r[0].type!==e.type&&(t?t.call(this,e,r[0]):(r[1]||ew).call(this,e,r[0]));else throw Error("Cannot close `"+e.type+"` ("+(0,eb.L)({start:e.start,end:e.end})+"): it’s not open");n.position.end=e_(e.end)}function u(){return(0,i.d)(this.stack.pop())}function c(e){let t=this.stack[this.stack.length-1].children,n=t[t.length-1];n&&"text"===n.type||((n={type:"text",value:""}).position={start:e_(e.start),end:void 0},t.push(n)),this.stack.push(n)}function f(e){let t=this.stack.pop();t.value+=this.sliceSerialize(e),t.position.end=e_(e.end)}function d(){this.data.atHardBreak=!0}function p(e){this.data.characterReferenceType=e.type}function h(){return{type:"code",lang:null,meta:null,value:""}}function m(){return{type:"heading",depth:0,children:[]}}function g(){return{type:"break"}}function y(){return{type:"html",value:""}}function v(){return{type:"link",title:null,url:"",children:[]}}function k(e){return{type:"list",ordered:"listOrdered"===e.type,start:null,spread:e._spread,children:[]}}})(s)(function(e){for(;!l(e););return e}((function(e){let t={constructs:(0,u.y)([r,...(e||{}).extensions||[]]),content:n(d),defined:[],document:n(p),flow:n(v),lazy:{},string:n(b),text:n(x)};return t;function n(e){return function(n){return function(e,t,n){let r={_bufferIndex:-1,_index:0,line:n&&n.line||1,column:n&&n.column||1,offset:n&&n.offset||0},i={},a=[],s=[],l=[],u={attempt:g(function(e,t){y(e,t.from)}),check:g(m),consume:function(e){(0,f.HP)(e)?(r.line++,r.column=1,r.offset+=-3===e?2:1,v()):-1!==e&&(r.column++,r.offset++),r._bufferIndex<0?r._index++:(r._bufferIndex++,r._bufferIndex===s[r._index].length&&(r._bufferIndex=-1,r._index++)),c.previous=e},enter:function(e,t){let n=t||{};return n.type=e,n.start=h(),c.events.push(["enter",n,c]),l.push(n),n},exit:function(e){let t=l.pop();return t.end=h(),c.events.push(["exit",t,c]),t},interrupt:g(m,{interrupt:!0})},c={code:null,containerState:{},defineSkip:function(e){i[e.line]=e.column,v()},events:[],now:h,parser:e,previous:null,sliceSerialize:function(e,t){return function(e,t){let n,r=-1,i=[];for(;++r<e.length;){let o,a=e[r];if("string"==typeof a)o=a;else switch(a){case -5:o="\r";break;case -4:o="\n";break;case -3:o="\r\n";break;case -2:o=t?" ":"	";break;case -1:if(!t&&n)continue;o=" ";break;default:o=String.fromCharCode(a)}n=-2===a,i.push(o)}return i.join("")}(p(e),t)},sliceStream:p,write:function(e){return(s=(0,o.V)(s,e),function(){let e;for(;r._index<s.length;){let n=s[r._index];if("string"==typeof n)for(e=r._index,r._bufferIndex<0&&(r._bufferIndex=0);r._index===e&&r._bufferIndex<n.length;){var t;t=n.charCodeAt(r._bufferIndex),d=d(t)}else d=d(n)}}(),null!==s[s.length-1])?[]:(y(t,0),c.events=(0,G.W)(a,c.events,c),c.events)}},d=t.tokenize.call(c,u);return t.resolveAll&&a.push(t),c;function p(e){return function(e,t){let n,r=t.start._index,i=t.start._bufferIndex,o=t.end._index,a=t.end._bufferIndex;if(r===o)n=[e[r].slice(i,a)];else{if(n=e.slice(r,o),i>-1){let e=n[0];"string"==typeof e?n[0]=e.slice(i):n.shift()}a>0&&n.push(e[o].slice(0,a))}return n}(s,e)}function h(){let{_bufferIndex:e,_index:t,line:n,column:i,offset:o}=r;return{_bufferIndex:e,_index:t,line:n,column:i,offset:o}}function m(e,t){t.restore()}function g(e,t){return function(n,i,o){var a;let s,f,d,p;return Array.isArray(n)?m(n):"tokenize"in n?m([n]):(a=n,function(e){let t=null!==e&&a[e],n=null!==e&&a.null;return m([...Array.isArray(t)?t:t?[t]:[],...Array.isArray(n)?n:n?[n]:[]])(e)});function m(e){return(s=e,f=0,0===e.length)?o:g(e[f])}function g(e){return function(n){return(p=function(){let e=h(),t=c.previous,n=c.currentConstruct,i=c.events.length,o=Array.from(l);return{from:i,restore:function(){r=e,c.previous=t,c.currentConstruct=n,c.events.length=i,l=o,v()}}}(),d=e,e.partial||(c.currentConstruct=e),e.name&&c.parser.constructs.disable.null.includes(e.name))?k(n):e.tokenize.call(t?Object.assign(Object.create(c),t):c,u,y,k)(n)}}function y(t){return e(d,p),i}function k(e){return(p.restore(),++f<s.length)?g(s[f]):o}}}function y(e,t){e.resolveAll&&!a.includes(e)&&a.push(e),e.resolve&&(0,o.m)(c.events,t,c.events.length-t,e.resolve(c.events.slice(t),c)),e.resolveTo&&(c.events=e.resolveTo(c.events,c))}function v(){r.line in i&&r.column<2&&(r.column=i[r.line],r.offset+=i[r.line]-1)}}(t,e,n)}}})(s).document().write((h=1,m="",g=!0,function(e,t,n){let r,i,o,a,s,l=[];for(e=m+("string"==typeof e?e.toString():new TextDecoder(t||void 0).decode(e)),o=0,m="",g&&(65279===e.charCodeAt(0)&&o++,g=void 0);o<e.length;){if(em.lastIndex=o,a=(r=em.exec(e))&&void 0!==r.index?r.index:e.length,s=e.charCodeAt(a),!r){m=e.slice(o);break}if(10===s&&o===a&&c)l.push(-3),c=void 0;else switch(c&&(l.push(-5),c=void 0),o<a&&(l.push(e.slice(o,a)),h+=a-o),s){case 0:l.push(65533),h++;break;case 9:for(i=4*Math.ceil(h/4),l.push(-2);h++<i;)l.push(-1);break;case 10:l.push(-4),h=1;break;default:c=!0,h=1}o=a+1}return n&&(c&&l.push(-5),m&&l.push(m),l.push(null)),l})(n,a,!0))))}}).use(function(e){let t=this;t.compiler=function(n){var r={...t.data("settings"),...e,extensions:t.data("toMarkdownExtensions")||[]};let i={associationId:eO,containerPhrasing:eH,containerFlow:eU,createTracker:eL,compilePattern:ej,enter:function(e){return i.stack.push(e),function(){i.stack.pop()}},handlers:{...eA.p},handle:void 0,indentLines:eM,indexStack:[],join:[...eE],options:{},safe:eJ,stack:[],unsafe:[...eT]};!function e(t,n){var r,i,o,a;let s,l=-1;if(n.extensions)for(;++l<n.extensions.length;)e(t,n.extensions[l]);for(s in n)if(eS.call(n,s))switch(s){case"extensions":break;case"unsafe":case"join":r=t[s],(i=n[s])&&r.push(...i);break;case"handlers":o=t[s],(a=n[s])&&Object.assign(o,a);break;default:t.options[s]=n[s]}return t}(i,r||{}),i.options.tightDefinitions&&i.join.push(eV),i.handle=(0,ez.A)("type",{invalid:eR,unknown:eB,handlers:i.handlers});let o=i.handle(n,void 0,i,{before:"\n",after:"\n",now:{line:1,column:1},lineShift:0});return o&&10!==o.charCodeAt(o.length-1)&&13!==o.charCodeAt(o.length-1)&&(o+="\n"),o}}).freeze()},89511:(e,t,n)=>{"use strict";n.d(t,{Cf:()=>M,HM:()=>U,L3:()=>R,LC:()=>O,UC:()=>en,VY:()=>ei,ZJ:()=>C,ZL:()=>ee,bL:()=>Y,bm:()=>eo,hE:()=>er,hJ:()=>et,l9:()=>X,lG:()=>z,rr:()=>V});var r=n(33670),i=n(92556),o=n(94446),a=n(3468),s=n(68946),l=n(23558),u=n(44831),c=n(69666),f=n(75433),d=n(76842),p=n(97602),h=n(19526),m=n(40101),g=n(97745),y=n(32467),v=n(22342),k="Dialog",[b,x]=(0,a.A)(k),[_,w]=b(k),z=e=>{let{__scopeDialog:t,children:n,open:i,defaultOpen:o,onOpenChange:a,modal:u=!0}=e,c=r.useRef(null),f=r.useRef(null),[d,p]=(0,l.i)({prop:i,defaultProp:null!=o&&o,onChange:a,caller:k});return(0,v.jsx)(_,{scope:t,triggerRef:c,contentRef:f,contentId:(0,s.B)(),titleId:(0,s.B)(),descriptionId:(0,s.B)(),open:d,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:u,children:n})};z.displayName=k;var S="DialogTrigger",A=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=w(S,n),s=(0,o.s)(t,a.triggerRef);return(0,v.jsx)(p.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":a.open,"aria-controls":a.contentId,"data-state":J(a.open),...r,ref:s,onClick:(0,i.mK)(e.onClick,a.onOpenToggle)})});A.displayName=S;var I="DialogPortal",[P,E]=b(I,{forceMount:void 0}),C=e=>{let{__scopeDialog:t,forceMount:n,children:i,container:o}=e,a=w(I,t);return(0,v.jsx)(P,{scope:t,forceMount:n,children:r.Children.map(i,e=>(0,v.jsx)(d.C,{present:n||a.open,children:(0,v.jsx)(f.Z,{asChild:!0,container:o,children:e})}))})};C.displayName=I;var T="DialogOverlay",O=r.forwardRef((e,t)=>{let n=E(T,e.__scopeDialog),{forceMount:r=n.forceMount,...i}=e,o=w(T,e.__scopeDialog);return o.modal?(0,v.jsx)(d.C,{present:r||o.open,children:(0,v.jsx)($,{...i,ref:t})}):null});O.displayName=T;var j=(0,y.TL)("DialogOverlay.RemoveScroll"),$=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=w(T,n);return(0,v.jsx)(m.A,{as:j,allowPinchZoom:!0,shards:[i.contentRef],children:(0,v.jsx)(p.sG.div,{"data-state":J(i.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),N="DialogContent",M=r.forwardRef((e,t)=>{let n=E(N,e.__scopeDialog),{forceMount:r=n.forceMount,...i}=e,o=w(N,e.__scopeDialog);return(0,v.jsx)(d.C,{present:r||o.open,children:o.modal?(0,v.jsx)(F,{...i,ref:t}):(0,v.jsx)(Z,{...i,ref:t})})});M.displayName=N;var F=r.forwardRef((e,t)=>{let n=w(N,e.__scopeDialog),a=r.useRef(null),s=(0,o.s)(t,n.contentRef,a);return r.useEffect(()=>{let e=a.current;if(e)return(0,g.Eq)(e)},[]),(0,v.jsx)(D,{...e,ref:s,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,i.mK)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=n.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,i.mK)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,i.mK)(e.onFocusOutside,e=>e.preventDefault())})}),Z=r.forwardRef((e,t)=>{let n=w(N,e.__scopeDialog),i=r.useRef(!1),o=r.useRef(!1);return(0,v.jsx)(D,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var r,a;null==(r=e.onCloseAutoFocus)||r.call(e,t),t.defaultPrevented||(i.current||null==(a=n.triggerRef.current)||a.focus(),t.preventDefault()),i.current=!1,o.current=!1},onInteractOutside:t=>{var r,a;null==(r=e.onInteractOutside)||r.call(e,t),t.defaultPrevented||(i.current=!0,"pointerdown"===t.detail.originalEvent.type&&(o.current=!0));let s=t.target;(null==(a=n.triggerRef.current)?void 0:a.contains(s))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&o.current&&t.preventDefault()}})}),D=r.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:i,onOpenAutoFocus:a,onCloseAutoFocus:s,...l}=e,f=w(N,n),d=r.useRef(null),p=(0,o.s)(t,d);return(0,h.Oh)(),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(c.n,{asChild:!0,loop:!0,trapped:i,onMountAutoFocus:a,onUnmountAutoFocus:s,children:(0,v.jsx)(u.qW,{role:"dialog",id:f.contentId,"aria-describedby":f.descriptionId,"aria-labelledby":f.titleId,"data-state":J(f.open),...l,ref:p,onDismiss:()=>f.onOpenChange(!1)})}),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(G,{titleId:f.titleId}),(0,v.jsx)(K,{contentRef:d,descriptionId:f.descriptionId})]})]})}),L="DialogTitle",R=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=w(L,n);return(0,v.jsx)(p.sG.h2,{id:i.titleId,...r,ref:t})});R.displayName=L;var B="DialogDescription",V=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=w(B,n);return(0,v.jsx)(p.sG.p,{id:i.descriptionId,...r,ref:t})});V.displayName=B;var H="DialogClose",U=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=w(H,n);return(0,v.jsx)(p.sG.button,{type:"button",...r,ref:t,onClick:(0,i.mK)(e.onClick,()=>o.onOpenChange(!1))})});function J(e){return e?"open":"closed"}U.displayName=H;var W="DialogTitleWarning",[q,Q]=(0,a.q)(W,{contentName:N,titleName:L,docsSlug:"dialog"}),G=e=>{let{titleId:t}=e,n=Q(W),i="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return r.useEffect(()=>{t&&(document.getElementById(t)||console.error(i))},[i,t]),null},K=e=>{let{contentRef:t,descriptionId:n}=e,i=Q("DialogDescriptionWarning"),o="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(i.contentName,"}.");return r.useEffect(()=>{var e;let r=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");n&&r&&(document.getElementById(n)||console.warn(o))},[o,t,n]),null},Y=z,X=A,ee=C,et=O,en=M,er=R,ei=V,eo=U},89559:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(71847).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},91305:(e,t,n)=>{"use strict";function r(e,t){return i(e,t.inConstruct,!0)&&!i(e,t.notInConstruct,!1)}function i(e,t,n){if("string"==typeof t&&(t=[t]),!t||0===t.length)return n;let r=-1;for(;++r<t.length;)if(e.includes(t[r]))return!0;return!1}n.d(t,{q:()=>r})},93272:(e,t,n)=>{"use strict";let r;n.d(t,{mD:()=>U,Z9:()=>q,n_:()=>Q,hK:()=>j,$C:()=>$,_Z:()=>R,hd:()=>B,N8:()=>L,ZZ:()=>D,k5:()=>Z});var i,o=n(64876);class a extends Error{constructor(e,t){super(e),this.name="ParseError",this.type=t.type,this.field=t.field,this.value=t.value,this.line=t.line}}function s(e){}class l extends TransformStream{constructor({onError:e,onRetry:t,onComment:n}={}){let r;super({start(i){r=function(e){if("function"==typeof e)throw TypeError("`callbacks` must be an object, got a function instead. Did you mean `{onEvent: fn}`?");let{onEvent:t=s,onError:n=s,onRetry:r=s,onComment:i}=e,o="",l=!0,u,c="",f="";function d(e){if(""===e)return void(c.length>0&&t({id:u,event:f||void 0,data:c.endsWith(`
`)?c.slice(0,-1):c}),u=void 0,c="",f="");if(e.startsWith(":")){i&&i(e.slice(e.startsWith(": ")?2:1));return}let n=e.indexOf(":");if(-1!==n){let t=e.slice(0,n),r=" "===e[n+1]?2:1;p(t,e.slice(n+r),e);return}p(e,"",e)}function p(e,t,i){switch(e){case"event":f=t;break;case"data":c=`${c}${t}
`;break;case"id":u=t.includes("\0")?void 0:t;break;case"retry":/^\d+$/.test(t)?r(parseInt(t,10)):n(new a(`Invalid \`retry\` value: "${t}"`,{type:"invalid-retry",value:t,line:i}));break;default:n(new a(`Unknown field "${e.length>20?`${e.slice(0,20)}\u2026`:e}"`,{type:"unknown-field",field:e,value:t,line:i}))}}return{feed:function(e){let t=l?e.replace(/^\xEF\xBB\xBF/,""):e,[n,r]=function(e){let t=[],n="",r=0;for(;r<e.length;){let i=e.indexOf("\r",r),o=e.indexOf(`
`,r),a=-1;if(-1!==i&&-1!==o?a=Math.min(i,o):-1!==i?a=i===e.length-1?-1:i:-1!==o&&(a=o),-1===a){n=e.slice(r);break}{let n=e.slice(r,a);t.push(n),"\r"===e[(r=a+1)-1]&&e[r]===`
`&&r++}}return[t,n]}(`${o}${t}`);for(let e of n)d(e);o=r,l=!1},reset:function(e={}){o&&e.consume&&d(o),l=!0,u=void 0,c="",f="",o=""}}}({onEvent:e=>{i.enqueue(e)},onError(t){"terminate"===e?i.error(t):"function"==typeof e&&e(t)},onRetry:t,onComment:n})},transform(e){r.feed(e)}})}}var u=n(23978),c=n(26713);class f{constructor(e){this.counter=0,this.metadataRegistry=e?.metadata??u.fd,this.target=e?.target??"draft-2020-12",this.unrepresentable=e?.unrepresentable??"throw",this.override=e?.override??(()=>{}),this.io=e?.io??"output",this.seen=new Map}process(e,t={path:[],schemaPath:[]}){var n;let r=e._zod.def,i=this.seen.get(e);if(i)return i.count++,t.schemaPath.includes(e)&&(i.cycle=t.path),i.schema;let o={schema:{},count:1,cycle:void 0,path:t.path};this.seen.set(e,o);let a=e._zod.toJSONSchema?.();if(a)o.schema=a;else{let n={...t,schemaPath:[...t.schemaPath,e],path:t.path},i=e._zod.parent;if(i)o.ref=i,this.process(i,n),this.seen.get(i).isParent=!0;else{let t=o.schema;switch(r.type){case"string":{t.type="string";let{minimum:n,maximum:r,format:i,patterns:a,contentEncoding:s}=e._zod.bag;if("number"==typeof n&&(t.minLength=n),"number"==typeof r&&(t.maxLength=r),i&&(t.format=({guid:"uuid",url:"uri",datetime:"date-time",json_string:"json-string",regex:""})[i]??i,""===t.format&&delete t.format),s&&(t.contentEncoding=s),a&&a.size>0){let e=[...a];1===e.length?t.pattern=e[0].source:e.length>1&&(o.schema.allOf=[...e.map(e=>({..."draft-7"===this.target||"draft-4"===this.target?{type:"string"}:{},pattern:e.source}))])}break}case"number":{let{minimum:n,maximum:r,format:i,multipleOf:o,exclusiveMaximum:a,exclusiveMinimum:s}=e._zod.bag;"string"==typeof i&&i.includes("int")?t.type="integer":t.type="number","number"==typeof s&&("draft-4"===this.target?(t.minimum=s,t.exclusiveMinimum=!0):t.exclusiveMinimum=s),"number"==typeof n&&(t.minimum=n,"number"==typeof s&&"draft-4"!==this.target&&(s>=n?delete t.minimum:delete t.exclusiveMinimum)),"number"==typeof a&&("draft-4"===this.target?(t.maximum=a,t.exclusiveMaximum=!0):t.exclusiveMaximum=a),"number"==typeof r&&(t.maximum=r,"number"==typeof a&&"draft-4"!==this.target&&(a<=r?delete t.maximum:delete t.exclusiveMaximum)),"number"==typeof o&&(t.multipleOf=o);break}case"boolean":case"success":t.type="boolean";break;case"bigint":if("throw"===this.unrepresentable)throw Error("BigInt cannot be represented in JSON Schema");break;case"symbol":if("throw"===this.unrepresentable)throw Error("Symbols cannot be represented in JSON Schema");break;case"null":t.type="null";break;case"any":case"unknown":break;case"undefined":if("throw"===this.unrepresentable)throw Error("Undefined cannot be represented in JSON Schema");break;case"void":if("throw"===this.unrepresentable)throw Error("Void cannot be represented in JSON Schema");break;case"never":t.not={};break;case"date":if("throw"===this.unrepresentable)throw Error("Date cannot be represented in JSON Schema");break;case"array":{let{minimum:i,maximum:o}=e._zod.bag;"number"==typeof i&&(t.minItems=i),"number"==typeof o&&(t.maxItems=o),t.type="array",t.items=this.process(r.element,{...n,path:[...n.path,"items"]});break}case"object":{t.type="object",t.properties={};let e=r.shape;for(let r in e)t.properties[r]=this.process(e[r],{...n,path:[...n.path,"properties",r]});let i=new Set([...new Set(Object.keys(e))].filter(e=>{let t=r.shape[e]._zod;return"input"===this.io?void 0===t.optin:void 0===t.optout}));i.size>0&&(t.required=Array.from(i)),r.catchall?._zod.def.type==="never"?t.additionalProperties=!1:r.catchall?r.catchall&&(t.additionalProperties=this.process(r.catchall,{...n,path:[...n.path,"additionalProperties"]})):"output"===this.io&&(t.additionalProperties=!1);break}case"union":t.anyOf=r.options.map((e,t)=>this.process(e,{...n,path:[...n.path,"anyOf",t]}));break;case"intersection":{let e=this.process(r.left,{...n,path:[...n.path,"allOf",0]}),i=this.process(r.right,{...n,path:[...n.path,"allOf",1]}),o=e=>"allOf"in e&&1===Object.keys(e).length;t.allOf=[...o(e)?e.allOf:[e],...o(i)?i.allOf:[i]];break}case"tuple":{t.type="array";let i=r.items.map((e,t)=>this.process(e,{...n,path:[...n.path,"prefixItems",t]}));if("draft-2020-12"===this.target?t.prefixItems=i:t.items=i,r.rest){let e=this.process(r.rest,{...n,path:[...n.path,"items"]});"draft-2020-12"===this.target?t.items=e:t.additionalItems=e}r.rest&&(t.items=this.process(r.rest,{...n,path:[...n.path,"items"]}));let{minimum:o,maximum:a}=e._zod.bag;"number"==typeof o&&(t.minItems=o),"number"==typeof a&&(t.maxItems=a);break}case"record":t.type="object","draft-4"!==this.target&&(t.propertyNames=this.process(r.keyType,{...n,path:[...n.path,"propertyNames"]})),t.additionalProperties=this.process(r.valueType,{...n,path:[...n.path,"additionalProperties"]});break;case"map":if("throw"===this.unrepresentable)throw Error("Map cannot be represented in JSON Schema");break;case"set":if("throw"===this.unrepresentable)throw Error("Set cannot be represented in JSON Schema");break;case"enum":{let e=(0,c.w5)(r.entries);e.every(e=>"number"==typeof e)&&(t.type="number"),e.every(e=>"string"==typeof e)&&(t.type="string"),t.enum=e;break}case"literal":{let e=[];for(let t of r.values)if(void 0===t){if("throw"===this.unrepresentable)throw Error("Literal `undefined` cannot be represented in JSON Schema")}else if("bigint"==typeof t)if("throw"===this.unrepresentable)throw Error("BigInt literals cannot be represented in JSON Schema");else e.push(Number(t));else e.push(t);if(0===e.length);else if(1===e.length){let n=e[0];t.type=null===n?"null":typeof n,"draft-4"===this.target?t.enum=[n]:t.const=n}else e.every(e=>"number"==typeof e)&&(t.type="number"),e.every(e=>"string"==typeof e)&&(t.type="string"),e.every(e=>"boolean"==typeof e)&&(t.type="string"),e.every(e=>null===e)&&(t.type="null"),t.enum=e;break}case"file":{let n={type:"string",format:"binary",contentEncoding:"binary"},{minimum:r,maximum:i,mime:o}=e._zod.bag;void 0!==r&&(n.minLength=r),void 0!==i&&(n.maxLength=i),o?1===o.length?(n.contentMediaType=o[0],Object.assign(t,n)):t.anyOf=o.map(e=>({...n,contentMediaType:e})):Object.assign(t,n);break}case"transform":if("throw"===this.unrepresentable)throw Error("Transforms cannot be represented in JSON Schema");break;case"nullable":t.anyOf=[this.process(r.innerType,n),{type:"null"}];break;case"nonoptional":case"promise":case"optional":this.process(r.innerType,n),o.ref=r.innerType;break;case"default":this.process(r.innerType,n),o.ref=r.innerType,t.default=JSON.parse(JSON.stringify(r.defaultValue));break;case"prefault":this.process(r.innerType,n),o.ref=r.innerType,"input"===this.io&&(t._prefault=JSON.parse(JSON.stringify(r.defaultValue)));break;case"catch":{let e;this.process(r.innerType,n),o.ref=r.innerType;try{e=r.catchValue(void 0)}catch{throw Error("Dynamic catch values are not supported in JSON Schema")}t.default=e;break}case"nan":if("throw"===this.unrepresentable)throw Error("NaN cannot be represented in JSON Schema");break;case"template_literal":{let n=e._zod.pattern;if(!n)throw Error("Pattern not found in template literal");t.type="string",t.pattern=n.source;break}case"pipe":{let e="input"===this.io?"transform"===r.in._zod.def.type?r.out:r.in:r.out;this.process(e,n),o.ref=e;break}case"readonly":this.process(r.innerType,n),o.ref=r.innerType,t.readOnly=!0;break;case"lazy":{let t=e._zod.innerType;this.process(t,n),o.ref=t;break}case"custom":if("throw"===this.unrepresentable)throw Error("Custom types cannot be represented in JSON Schema")}}}let s=this.metadataRegistry.get(e);return s&&Object.assign(o.schema,s),"input"===this.io&&function e(t,n){let r=n??{seen:new Set};if(r.seen.has(t))return!1;r.seen.add(t);let i=t._zod.def;switch(i.type){case"string":case"number":case"bigint":case"boolean":case"date":case"symbol":case"undefined":case"null":case"any":case"unknown":case"never":case"void":case"literal":case"enum":case"nan":case"file":case"template_literal":case"custom":case"success":case"catch":return!1;case"array":return e(i.element,r);case"object":for(let t in i.shape)if(e(i.shape[t],r))return!0;return!1;case"union":for(let t of i.options)if(e(t,r))return!0;return!1;case"intersection":return e(i.left,r)||e(i.right,r);case"tuple":for(let t of i.items)if(e(t,r))return!0;if(i.rest&&e(i.rest,r))return!0;return!1;case"record":case"map":return e(i.keyType,r)||e(i.valueType,r);case"set":return e(i.valueType,r);case"promise":case"optional":case"nonoptional":case"nullable":case"readonly":case"default":case"prefault":return e(i.innerType,r);case"lazy":return e(i.getter(),r);case"transform":return!0;case"pipe":return e(i.in,r)||e(i.out,r)}throw Error(`Unknown schema type: ${i.type}`)}(e)&&(delete o.schema.examples,delete o.schema.default),"input"===this.io&&o.schema._prefault&&((n=o.schema).default??(n.default=o.schema._prefault)),delete o.schema._prefault,this.seen.get(e).schema}emit(e,t){let n={cycles:t?.cycles??"ref",reused:t?.reused??"inline",external:t?.external??void 0},r=this.seen.get(e);if(!r)throw Error("Unprocessed schema. This is a bug in Zod.");let i=e=>{let t="draft-2020-12"===this.target?"$defs":"definitions";if(n.external){let r=n.external.registry.get(e[0])?.id,i=n.external.uri??(e=>e);if(r)return{ref:i(r)};let o=e[1].defId??e[1].schema.id??`schema${this.counter++}`;return e[1].defId=o,{defId:o,ref:`${i("__shared")}#/${t}/${o}`}}if(e[1]===r)return{ref:"#"};let i=`#/${t}/`,o=e[1].schema.id??`__schema${this.counter++}`;return{defId:o,ref:i+o}},o=e=>{if(e[1].schema.$ref)return;let t=e[1],{ref:n,defId:r}=i(e);t.def={...t.schema},r&&(t.defId=r);let o=t.schema;for(let e in o)delete o[e];o.$ref=n};if("throw"===n.cycles)for(let e of this.seen.entries()){let t=e[1];if(t.cycle)throw Error(`Cycle detected: #/${t.cycle?.join("/")}/<root>

Set the \`cycles\` parameter to \`"ref"\` to resolve cyclical schemas with defs.`)}for(let t of this.seen.entries()){let r=t[1];if(e===t[0]){o(t);continue}if(n.external){let r=n.external.registry.get(t[0])?.id;if(e!==t[0]&&r){o(t);continue}}if(this.metadataRegistry.get(t[0])?.id||r.cycle||r.count>1&&"ref"===n.reused){o(t);continue}}let a=(e,t)=>{let n=this.seen.get(e),r=n.def??n.schema,i={...r};if(null===n.ref)return;let o=n.ref;if(n.ref=null,o){a(o,t);let e=this.seen.get(o).schema;e.$ref&&("draft-7"===t.target||"draft-4"===t.target)?(r.allOf=r.allOf??[],r.allOf.push(e)):(Object.assign(r,e),Object.assign(r,i))}n.isParent||this.override({zodSchema:e,jsonSchema:r,path:n.path??[]})};for(let e of[...this.seen.entries()].reverse())a(e[0],{target:this.target});let s={};if("draft-2020-12"===this.target?s.$schema="https://json-schema.org/draft/2020-12/schema":"draft-7"===this.target?s.$schema="http://json-schema.org/draft-07/schema#":"draft-4"===this.target?s.$schema="http://json-schema.org/draft-04/schema#":console.warn(`Invalid target: ${this.target}`),n.external?.uri){let t=n.external.registry.get(e)?.id;if(!t)throw Error("Schema is missing an `id` property");s.$id=n.external.uri(t)}Object.assign(s,r.def);let l=n.external?.defs??{};for(let e of this.seen.entries()){let t=e[1];t.def&&t.defId&&(l[t.defId]=t.def)}n.external||Object.keys(l).length>0&&("draft-2020-12"===this.target?s.$defs=l:s.definitions=l);try{return JSON.parse(JSON.stringify(s))}catch(e){throw Error("Error converting schema to JSON.")}}}var d=n(53024);let p=Symbol("Let zodToJsonSchema decide on which parser to use"),h={name:void 0,$refStrategy:"root",basePath:["#"],effectStrategy:"input",pipeStrategy:"all",dateStrategy:"format:date-time",mapStrategy:"entries",removeAdditionalStrategy:"passthrough",allowedAdditionalProperties:!0,rejectedAdditionalProperties:!1,definitionPath:"definitions",target:"jsonSchema7",strictUnions:!1,definitions:{},errorMessages:!1,markdownDescription:!1,patternStrategy:"escape",applyRegexFlags:!1,emailStrategy:"format:email",base64Strategy:"contentEncoding:base64",nameStrategy:"ref",openAiAnyTypeName:"OpenAiAnyType"};i||(i={});let m=(e,t)=>{let n=0;for(;n<e.length&&n<t.length&&e[n]===t[n];n++);return[(e.length-n).toString(),...t.slice(n)].join("/")};function g(e){if("openAi"!==e.target)return{};let t=[...e.basePath,e.definitionPath,e.openAiAnyTypeName];return e.flags.hasReferencedOpenAiAnyType=!0,{$ref:"relative"===e.$refStrategy?m(t,e.currentPath):t.join("/")}}function y(e,t,n,r){r?.errorMessages&&n&&(e.errorMessage={...e.errorMessage,[t]:n})}function v(e,t,n,r,i){e[t]=n,y(e,t,r,i)}function k(e,t){return C(e.type._def,t)}let b={cuid:/^[cC][^\s-]{8,}$/,cuid2:/^[0-9a-z]+$/,ulid:/^[0-9A-HJKMNP-TV-Z]{26}$/,email:/^(?!\.)(?!.*\.\.)([a-zA-Z0-9_'+\-\.]*)[a-zA-Z0-9_+-]@([a-zA-Z0-9][a-zA-Z0-9\-]*\.)+[a-zA-Z]{2,}$/,emoji:()=>(void 0===r&&(r=RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),r),ipv4Cidr:/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,ipv6Cidr:/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,base64:/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,base64url:/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,nanoid:/^[a-zA-Z0-9_-]{21}$/,jwt:/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/};function x(e,t){let n={type:"string"};if(e.checks)for(let r of e.checks)switch(r.kind){case"min":v(n,"minLength","number"==typeof n.minLength?Math.max(n.minLength,r.value):r.value,r.message,t);break;case"max":v(n,"maxLength","number"==typeof n.maxLength?Math.min(n.maxLength,r.value):r.value,r.message,t);break;case"email":switch(t.emailStrategy){case"format:email":z(n,"email",r.message,t);break;case"format:idn-email":z(n,"idn-email",r.message,t);break;case"pattern:zod":S(n,b.email,r.message,t)}break;case"url":z(n,"uri",r.message,t);break;case"uuid":z(n,"uuid",r.message,t);break;case"regex":S(n,r.regex,r.message,t);break;case"cuid":S(n,b.cuid,r.message,t);break;case"cuid2":S(n,b.cuid2,r.message,t);break;case"startsWith":S(n,RegExp(`^${_(r.value,t)}`),r.message,t);break;case"endsWith":S(n,RegExp(`${_(r.value,t)}$`),r.message,t);break;case"datetime":z(n,"date-time",r.message,t);break;case"date":z(n,"date",r.message,t);break;case"time":z(n,"time",r.message,t);break;case"duration":z(n,"duration",r.message,t);break;case"length":v(n,"minLength","number"==typeof n.minLength?Math.max(n.minLength,r.value):r.value,r.message,t),v(n,"maxLength","number"==typeof n.maxLength?Math.min(n.maxLength,r.value):r.value,r.message,t);break;case"includes":S(n,RegExp(_(r.value,t)),r.message,t);break;case"ip":"v6"!==r.version&&z(n,"ipv4",r.message,t),"v4"!==r.version&&z(n,"ipv6",r.message,t);break;case"base64url":S(n,b.base64url,r.message,t);break;case"jwt":S(n,b.jwt,r.message,t);break;case"cidr":"v6"!==r.version&&S(n,b.ipv4Cidr,r.message,t),"v4"!==r.version&&S(n,b.ipv6Cidr,r.message,t);break;case"emoji":S(n,b.emoji(),r.message,t);break;case"ulid":S(n,b.ulid,r.message,t);break;case"base64":switch(t.base64Strategy){case"format:binary":z(n,"binary",r.message,t);break;case"contentEncoding:base64":v(n,"contentEncoding","base64",r.message,t);break;case"pattern:zod":S(n,b.base64,r.message,t)}break;case"nanoid":S(n,b.nanoid,r.message,t)}return n}function _(e,t){return"escape"===t.patternStrategy?function(e){let t="";for(let n=0;n<e.length;n++)w.has(e[n])||(t+="\\"),t+=e[n];return t}(e):e}let w=new Set("ABCDEFGHIJKLMNOPQRSTUVXYZabcdefghijklmnopqrstuvxyz0123456789");function z(e,t,n,r){e.format||e.anyOf?.some(e=>e.format)?(e.anyOf||(e.anyOf=[]),e.format&&(e.anyOf.push({format:e.format,...e.errorMessage&&r.errorMessages&&{errorMessage:{format:e.errorMessage.format}}}),delete e.format,e.errorMessage&&(delete e.errorMessage.format,0===Object.keys(e.errorMessage).length&&delete e.errorMessage)),e.anyOf.push({format:t,...n&&r.errorMessages&&{errorMessage:{format:n}}})):v(e,"format",t,n,r)}function S(e,t,n,r){e.pattern||e.allOf?.some(e=>e.pattern)?(e.allOf||(e.allOf=[]),e.pattern&&(e.allOf.push({pattern:e.pattern,...e.errorMessage&&r.errorMessages&&{errorMessage:{pattern:e.errorMessage.pattern}}}),delete e.pattern,e.errorMessage&&(delete e.errorMessage.pattern,0===Object.keys(e.errorMessage).length&&delete e.errorMessage)),e.allOf.push({pattern:A(t,r),...n&&r.errorMessages&&{errorMessage:{pattern:n}}})):v(e,"pattern",A(t,r),n,r)}function A(e,t){if(!t.applyRegexFlags||!e.flags)return e.source;let n={i:e.flags.includes("i"),m:e.flags.includes("m"),s:e.flags.includes("s")},r=n.i?e.source.toLowerCase():e.source,i="",o=!1,a=!1,s=!1;for(let e=0;e<r.length;e++){if(o){i+=r[e],o=!1;continue}if(n.i){if(a){if(r[e].match(/[a-z]/)){s?(i+=r[e],i+=`${r[e-2]}-${r[e]}`.toUpperCase(),s=!1):"-"===r[e+1]&&r[e+2]?.match(/[a-z]/)?(i+=r[e],s=!0):i+=`${r[e]}${r[e].toUpperCase()}`;continue}}else if(r[e].match(/[a-z]/)){i+=`[${r[e]}${r[e].toUpperCase()}]`;continue}}if(n.m){if("^"===r[e]){i+=`(^|(?<=[\r
]))`;continue}else if("$"===r[e]){i+=`($|(?=[\r
]))`;continue}}if(n.s&&"."===r[e]){i+=a?`${r[e]}\r
`:`[${r[e]}\r
]`;continue}i+=r[e],"\\"===r[e]?o=!0:a&&"]"===r[e]?a=!1:a||"["!==r[e]||(a=!0)}try{new RegExp(i)}catch{return console.warn(`Could not convert regex pattern at ${t.currentPath.join("/")} to a flag-independent form! Falling back to the flag-ignorant source`),e.source}return i}function I(e,t){if("openAi"===t.target&&console.warn("Warning: OpenAI may not support records in schemas! Try an array of key-value pairs instead."),"openApi3"===t.target&&e.keyType?._def.typeName===i.ZodEnum)return{type:"object",required:e.keyType._def.values,properties:e.keyType._def.values.reduce((n,r)=>({...n,[r]:C(e.valueType._def,{...t,currentPath:[...t.currentPath,"properties",r]})??g(t)}),{}),additionalProperties:t.rejectedAdditionalProperties};let n={type:"object",additionalProperties:C(e.valueType._def,{...t,currentPath:[...t.currentPath,"additionalProperties"]})??t.allowedAdditionalProperties};if("openApi3"===t.target)return n;if(e.keyType?._def.typeName===i.ZodString&&e.keyType._def.checks?.length){let{type:r,...i}=x(e.keyType._def,t);return{...n,propertyNames:i}}if(e.keyType?._def.typeName===i.ZodEnum)return{...n,propertyNames:{enum:e.keyType._def.values}};if(e.keyType?._def.typeName===i.ZodBranded&&e.keyType._def.type._def.typeName===i.ZodString&&e.keyType._def.type._def.checks?.length){let{type:r,...i}=k(e.keyType._def,t);return{...n,propertyNames:i}}return n}let P={ZodString:"string",ZodNumber:"number",ZodBigInt:"integer",ZodBoolean:"boolean",ZodNull:"null"},E=(e,t)=>{let n=(e.options instanceof Map?Array.from(e.options.values()):e.options).map((e,n)=>C(e._def,{...t,currentPath:[...t.currentPath,"anyOf",`${n}`]})).filter(e=>!!e&&(!t.strictUnions||"object"==typeof e&&Object.keys(e).length>0));return n.length?{anyOf:n}:void 0};function C(e,t,n=!1){let r=t.seen.get(e);if(t.override){let i=t.override?.(e,t,r,n);if(i!==p)return i}if(r&&!n){let e=T(r,t);if(void 0!==e)return e}let o={def:e,path:t.currentPath,jsonSchema:void 0};t.seen.set(e,o);let a=((e,t,n)=>{switch(t){case i.ZodString:return x(e,n);case i.ZodNumber:var r,o,a,s,l,u,c,f,d,p=e,h=n;let m={type:"number"};if(!p.checks)return m;for(let e of p.checks)switch(e.kind){case"int":m.type="integer",y(m,"type",e.message,h);break;case"min":"jsonSchema7"===h.target?e.inclusive?v(m,"minimum",e.value,e.message,h):v(m,"exclusiveMinimum",e.value,e.message,h):(e.inclusive||(m.exclusiveMinimum=!0),v(m,"minimum",e.value,e.message,h));break;case"max":"jsonSchema7"===h.target?e.inclusive?v(m,"maximum",e.value,e.message,h):v(m,"exclusiveMaximum",e.value,e.message,h):(e.inclusive||(m.exclusiveMaximum=!0),v(m,"maximum",e.value,e.message,h));break;case"multipleOf":v(m,"multipleOf",e.value,e.message,h)}return m;case i.ZodObject:return function(e,t){let n="openAi"===t.target,r={type:"object",properties:{}},i=[],o=e.shape();for(let e in o){let a=o[e];if(void 0===a||void 0===a._def)continue;let s=function(e){try{return e.isOptional()}catch{return!0}}(a);s&&n&&("ZodOptional"===a._def.typeName&&(a=a._def.innerType),a.isNullable()||(a=a.nullable()),s=!1);let l=C(a._def,{...t,currentPath:[...t.currentPath,"properties",e],propertyPath:[...t.currentPath,"properties",e]});void 0!==l&&(r.properties[e]=l,s||i.push(e))}i.length&&(r.required=i);let a=function(e,t){if("ZodNever"!==e.catchall._def.typeName)return C(e.catchall._def,{...t,currentPath:[...t.currentPath,"additionalProperties"]});switch(e.unknownKeys){case"passthrough":return t.allowedAdditionalProperties;case"strict":return t.rejectedAdditionalProperties;case"strip":return"strict"===t.removeAdditionalStrategy?t.allowedAdditionalProperties:t.rejectedAdditionalProperties}}(e,t);return void 0!==a&&(r.additionalProperties=a),r}(e,n);case i.ZodBigInt:var b=e,_=n;let w={type:"integer",format:"int64"};if(!b.checks)return w;for(let e of b.checks)switch(e.kind){case"min":"jsonSchema7"===_.target?e.inclusive?v(w,"minimum",e.value,e.message,_):v(w,"exclusiveMinimum",e.value,e.message,_):(e.inclusive||(w.exclusiveMinimum=!0),v(w,"minimum",e.value,e.message,_));break;case"max":"jsonSchema7"===_.target?e.inclusive?v(w,"maximum",e.value,e.message,_):v(w,"exclusiveMaximum",e.value,e.message,_):(e.inclusive||(w.exclusiveMaximum=!0),v(w,"maximum",e.value,e.message,_));break;case"multipleOf":v(w,"multipleOf",e.value,e.message,_)}return w;case i.ZodBoolean:return{type:"boolean"};case i.ZodDate:return function e(t,n,r){let i=r??n.dateStrategy;if(Array.isArray(i))return{anyOf:i.map((r,i)=>e(t,n,r))};switch(i){case"string":case"format:date-time":return{type:"string",format:"date-time"};case"format:date":return{type:"string",format:"date"};case"integer":var o=t,a=n;let s={type:"integer",format:"unix-time"};if("openApi3"===a.target)return s;for(let e of o.checks)switch(e.kind){case"min":v(s,"minimum",e.value,e.message,a);break;case"max":v(s,"maximum",e.value,e.message,a)}return s}}(e,n);case i.ZodUndefined:return{not:g(n)};case i.ZodNull:return"openApi3"===n.target?{enum:["null"],nullable:!0}:{type:"null"};case i.ZodArray:var z=e,S=n;let A={type:"array"};return z.type?._def&&z.type?._def?.typeName!==i.ZodAny&&(A.items=C(z.type._def,{...S,currentPath:[...S.currentPath,"items"]})),z.minLength&&v(A,"minItems",z.minLength.value,z.minLength.message,S),z.maxLength&&v(A,"maxItems",z.maxLength.value,z.maxLength.message,S),z.exactLength&&(v(A,"minItems",z.exactLength.value,z.exactLength.message,S),v(A,"maxItems",z.exactLength.value,z.exactLength.message,S)),A;case i.ZodUnion:case i.ZodDiscriminatedUnion:var T=e,O=n;if("openApi3"===O.target)return E(T,O);let j=T.options instanceof Map?Array.from(T.options.values()):T.options;if(j.every(e=>e._def.typeName in P&&(!e._def.checks||!e._def.checks.length))){let e=j.reduce((e,t)=>{let n=P[t._def.typeName];return n&&!e.includes(n)?[...e,n]:e},[]);return{type:e.length>1?e:e[0]}}if(j.every(e=>"ZodLiteral"===e._def.typeName&&!e.description)){let e=j.reduce((e,t)=>{let n=typeof t._def.value;switch(n){case"string":case"number":case"boolean":return[...e,n];case"bigint":return[...e,"integer"];case"object":if(null===t._def.value)return[...e,"null"];default:return e}},[]);if(e.length===j.length){let t=e.filter((e,t,n)=>n.indexOf(e)===t);return{type:t.length>1?t:t[0],enum:j.reduce((e,t)=>e.includes(t._def.value)?e:[...e,t._def.value],[])}}}else if(j.every(e=>"ZodEnum"===e._def.typeName))return{type:"string",enum:j.reduce((e,t)=>[...e,...t._def.values.filter(t=>!e.includes(t))],[])};return E(T,O);case i.ZodIntersection:var $=e,N=n;let M=[C($.left._def,{...N,currentPath:[...N.currentPath,"allOf","0"]}),C($.right._def,{...N,currentPath:[...N.currentPath,"allOf","1"]})].filter(e=>!!e),F="jsonSchema2019-09"===N.target?{unevaluatedProperties:!1}:void 0,Z=[];return M.forEach(e=>{if((!("type"in e)||"string"!==e.type)&&"allOf"in e)Z.push(...e.allOf),void 0===e.unevaluatedProperties&&(F=void 0);else{let t=e;if("additionalProperties"in e&&!1===e.additionalProperties){let{additionalProperties:n,...r}=e;t=r}else F=void 0;Z.push(t)}}),Z.length?{allOf:Z,...F}:void 0;case i.ZodTuple:return r=e,o=n,r.rest?{type:"array",minItems:r.items.length,items:r.items.map((e,t)=>C(e._def,{...o,currentPath:[...o.currentPath,"items",`${t}`]})).reduce((e,t)=>void 0===t?e:[...e,t],[]),additionalItems:C(r.rest._def,{...o,currentPath:[...o.currentPath,"additionalItems"]})}:{type:"array",minItems:r.items.length,maxItems:r.items.length,items:r.items.map((e,t)=>C(e._def,{...o,currentPath:[...o.currentPath,"items",`${t}`]})).reduce((e,t)=>void 0===t?e:[...e,t],[])};case i.ZodRecord:return I(e,n);case i.ZodLiteral:var D=e,L=n;let R=typeof D.value;return"bigint"!==R&&"number"!==R&&"boolean"!==R&&"string"!==R?{type:Array.isArray(D.value)?"array":"object"}:"openApi3"===L.target?{type:"bigint"===R?"integer":R,enum:[D.value]}:{type:"bigint"===R?"integer":R,const:D.value};case i.ZodEnum:return{type:"string",enum:Array.from(e.values)};case i.ZodNativeEnum:var B=e;let V=B.values,H=Object.keys(B.values).filter(e=>"number"!=typeof V[V[e]]).map(e=>V[e]),U=Array.from(new Set(H.map(e=>typeof e)));return{type:1===U.length?"string"===U[0]?"string":"number":["string","number"],enum:H};case i.ZodNullable:var J=e,W=n;if(["ZodString","ZodNumber","ZodBigInt","ZodBoolean","ZodNull"].includes(J.innerType._def.typeName)&&(!J.innerType._def.checks||!J.innerType._def.checks.length))return"openApi3"===W.target?{type:P[J.innerType._def.typeName],nullable:!0}:{type:[P[J.innerType._def.typeName],"null"]};if("openApi3"===W.target){let e=C(J.innerType._def,{...W,currentPath:[...W.currentPath]});return e&&"$ref"in e?{allOf:[e],nullable:!0}:e&&{...e,nullable:!0}}let q=C(J.innerType._def,{...W,currentPath:[...W.currentPath,"anyOf","0"]});return q&&{anyOf:[q,{type:"null"}]};case i.ZodOptional:var Q=e,G=n;if(G.currentPath.toString()===G.propertyPath?.toString())return C(Q.innerType._def,G);let K=C(Q.innerType._def,{...G,currentPath:[...G.currentPath,"anyOf","1"]});return K?{anyOf:[{not:g(G)},K]}:g(G);case i.ZodMap:return a=e,"record"===(s=n).mapStrategy?I(a,s):{type:"array",maxItems:125,items:{type:"array",items:[C(a.keyType._def,{...s,currentPath:[...s.currentPath,"items","items","0"]})||g(s),C(a.valueType._def,{...s,currentPath:[...s.currentPath,"items","items","1"]})||g(s)],minItems:2,maxItems:2}};case i.ZodSet:var Y=e,X=n;let ee={type:"array",uniqueItems:!0,items:C(Y.valueType._def,{...X,currentPath:[...X.currentPath,"items"]})};return Y.minSize&&v(ee,"minItems",Y.minSize.value,Y.minSize.message,X),Y.maxSize&&v(ee,"maxItems",Y.maxSize.value,Y.maxSize.message,X),ee;case i.ZodLazy:return()=>e.getter()._def;case i.ZodPromise:return C(e.type._def,n);case i.ZodNaN:case i.ZodNever:return"openAi"===(l=n).target?void 0:{not:g({...l,currentPath:[...l.currentPath,"not"]})};case i.ZodEffects:return u=e,"input"===(c=n).effectStrategy?C(u.schema._def,c):g(c);case i.ZodAny:case i.ZodUnknown:return g(n);case i.ZodDefault:return f=e,d=n,{...C(f.innerType._def,d),default:f.defaultValue()};case i.ZodBranded:return k(e,n);case i.ZodReadonly:case i.ZodCatch:return C(e.innerType._def,n);case i.ZodPipeline:var et=e,en=n;if("input"===en.pipeStrategy)return C(et.in._def,en);if("output"===en.pipeStrategy)return C(et.out._def,en);let er=C(et.in._def,{...en,currentPath:[...en.currentPath,"allOf","0"]}),ei=C(et.out._def,{...en,currentPath:[...en.currentPath,"allOf",er?"1":"0"]});return{allOf:[er,ei].filter(e=>void 0!==e)};case i.ZodFunction:case i.ZodVoid:case i.ZodSymbol:default:return}})(e,e.typeName,t),s="function"==typeof a?C(a(),t):a;if(s&&O(e,t,s),t.postProcess){let n=t.postProcess(s,e,t);return o.jsonSchema=s,n}return o.jsonSchema=s,s}let T=(e,t)=>{switch(t.$refStrategy){case"root":return{$ref:e.path.join("/")};case"relative":return{$ref:m(t.currentPath,e.path)};case"none":case"seen":if(e.path.length<t.currentPath.length&&e.path.every((e,n)=>t.currentPath[n]===e))return console.warn(`Recursive reference detected at ${t.currentPath.join("/")}! Defaulting to any`),g(t);return"seen"===t.$refStrategy?g(t):void 0}},O=(e,t,n)=>(e.description&&(n.description=e.description,t.markdownDescription&&(n.markdownDescription=e.description)),n);n(95704);var j=({prefix:e,size:t=16,alphabet:n="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",separator:r="-"}={})=>{let i=()=>{let e=n.length,r=Array(t);for(let i=0;i<t;i++)r[i]=n[Math.random()*e|0];return r.join("")};if(null==e)return i;if(n.includes(r))throw new o.Di({argument:"separator",message:`The separator "${r}" must not be part of the alphabet "${n}".`});return()=>`${e}${r}${i()}`},$=j(),N=/"__proto__"\s*:/,M=/"constructor"\s*:/,F=Symbol.for("vercel.ai.validator");async function Z({value:e,schema:t}){let n=await D({value:e,schema:t});if(!n.success)throw o.iM.wrap({value:e,cause:n.error});return n.value}async function D({value:e,schema:t}){var n;let r="object"==typeof t&&null!==t&&F in t&&!0===t[F]&&"validate"in t?t:(n=t,{[F]:!0,validate:async e=>{let t=await n["~standard"].validate(e);return null==t.issues?{success:!0,value:t.value}:{success:!1,error:new o.iM({value:e,cause:t.issues})}}});try{if(null==r.validate)return{success:!0,value:e,rawValue:e};let t=await r.validate(e);if(t.success)return{success:!0,value:t.value,rawValue:e};return{success:!1,error:o.iM.wrap({value:e,cause:t.error}),rawValue:e}}catch(t){return{success:!1,error:o.iM.wrap({value:e,cause:t}),rawValue:e}}}async function L({text:e,schema:t}){try{let n=function(e){let{stackTraceLimit:t}=Error;Error.stackTraceLimit=0;try{let t=JSON.parse(e);return null===t||"object"!=typeof t||!1===N.test(e)&&!1===M.test(e)?t:function(e){let t=[e];for(;t.length;){let e=t;for(let n of(t=[],e)){if(Object.prototype.hasOwnProperty.call(n,"__proto__")||Object.prototype.hasOwnProperty.call(n,"constructor")&&Object.prototype.hasOwnProperty.call(n.constructor,"prototype"))throw SyntaxError("Object contains forbidden prototype property");for(let e in n){let r=n[e];r&&"object"==typeof r&&t.push(r)}}}return e}(t)}finally{Error.stackTraceLimit=t}}(e);if(null==t)return{success:!0,value:n,rawValue:n};return await D({value:n,schema:t})}catch(t){return{success:!1,error:o.u6.isInstance(t)?t:new o.u6({text:e,cause:t}),rawValue:void 0}}}function R({stream:e,schema:t}){return e.pipeThrough(new TextDecoderStream).pipeThrough(new l).pipeThrough(new TransformStream({async transform({data:e},n){"[DONE]"!==e&&n.enqueue(await L({text:e,schema:t}))}}))}async function B(e){return"function"==typeof e&&(e=e()),Promise.resolve(e)}var V=Symbol.for("vercel.ai.schema");function H(e,{validate:t}={}){return{[V]:!0,_type:void 0,[F]:!0,jsonSchema:e,validate:t}}function U(e){return null==e?H({properties:{},additionalProperties:!1}):"object"==typeof e&&null!==e&&V in e&&!0===e[V]&&"jsonSchema"in e&&"validate"in e?e:function(e,t){var n;return"_zod"in e?H(function(e,t){if(e instanceof u.rs){let n=new f(t),r={};for(let t of e._idmap.entries()){let[e,r]=t;n.process(r)}let i={},o={registry:e,uri:t?.uri,defs:r};for(let r of e._idmap.entries()){let[e,a]=r;i[e]=n.emit(a,{...t,external:o})}return Object.keys(r).length>0&&(i.__shared={["draft-2020-12"===n.target?"$defs":"definitions"]:r}),{schemas:i}}let n=new f(t);return n.process(e),n.emit(e,t)}(e,{target:"draft-7",io:"output",reused:"inline"}),{validate:async t=>{let n=await d.bp(e,t);return n.success?{success:!0,value:n.data}:{success:!1,error:n.error}}}):H(((e,t)=>{let n=(e=>{let t,n="string"==typeof(t=e)?{...h,name:t}:{...h,...t},r=void 0!==n.name?[...n.basePath,n.definitionPath,n.name]:n.basePath;return{...n,flags:{hasReferencedOpenAiAnyType:!1},currentPath:r,propertyPath:void 0,seen:new Map(Object.entries(n.definitions).map(([e,t])=>[t._def,{def:t._def,path:[...n.basePath,n.definitionPath,e],jsonSchema:void 0}]))}})(t),r="object"==typeof t&&t.definitions?Object.entries(t.definitions).reduce((e,[t,r])=>({...e,[t]:C(r._def,{...n,currentPath:[...n.basePath,n.definitionPath,t]},!0)??g(n)}),{}):void 0,i="string"==typeof t?t:t?.nameStrategy==="title"?void 0:t?.name,o=C(e._def,void 0===i?n:{...n,currentPath:[...n.basePath,n.definitionPath,i]},!1)??g(n),a="object"==typeof t&&void 0!==t.name&&"title"===t.nameStrategy?t.name:void 0;void 0!==a&&(o.title=a),n.flags.hasReferencedOpenAiAnyType&&(r||(r={}),r[n.openAiAnyTypeName]||(r[n.openAiAnyTypeName]={type:["string","number","integer","boolean","array","null"],items:{$ref:"relative"===n.$refStrategy?"1":[...n.basePath,n.definitionPath,n.openAiAnyTypeName].join("/")}}));let s=void 0===i?r?{...o,[n.definitionPath]:r}:o:{$ref:[..."relative"===n.$refStrategy?[]:n.basePath,n.definitionPath,i].join("/"),[n.definitionPath]:{...r,[i]:o}};return"jsonSchema7"===n.target?s.$schema="http://json-schema.org/draft-07/schema#":("jsonSchema2019-09"===n.target||"openAi"===n.target)&&(s.$schema="https://json-schema.org/draft/2019-09/schema#"),"openAi"===n.target&&("anyOf"in s||"oneOf"in s||"allOf"in s||"type"in s&&Array.isArray(s.type))&&console.warn("Warning: OpenAI may not support schemas with unions as roots! Try wrapping it in an object property."),s})(e,{$refStrategy:(n=void 0,"none"),target:"jsonSchema7"}),{validate:async t=>{let n=await e.safeParseAsync(t);return n.success?{success:!0,value:n.data}:{success:!1,error:n.error}}})}(e)}var{btoa:J,atob:W}=globalThis;function q(e){let t=W(e.replace(/-/g,"+").replace(/_/g,"/"));return Uint8Array.from(t,e=>e.codePointAt(0))}function Q(e){let t="";for(let n=0;n<e.length;n++)t+=String.fromCodePoint(e[n]);return J(t)}},95139:(e,t,n)=>{"use strict";n.d(t,{YR:()=>i});var r=n(56522);function i(e,t,n,i){let o,a,s;"function"==typeof t&&"function"!=typeof n?(a=void 0,s=t,o=n):(a=t,s=n,o=i),(0,r.VG)(e,a,function(e,t){let n=t[t.length-1],r=n?n.children.indexOf(e):void 0;return s(e,r,n)},o)}},95502:(e,t,n)=>{"use strict";n.d(t,{C:()=>r});let r=function(e){var t,n;if(null==e)return o;if("function"==typeof e)return i(e);if("object"==typeof e){return Array.isArray(e)?function(e){let t=[],n=-1;for(;++n<e.length;)t[n]=r(e[n]);return i(function(...e){let n=-1;for(;++n<t.length;)if(t[n].apply(this,e))return!0;return!1})}(e):(t=e,i(function(e){let n;for(n in t)if(e[n]!==t[n])return!1;return!0}))}if("string"==typeof e){return n=e,i(function(e){return e&&e.type===n})}throw Error("Expected function, string, or object as test")};function i(e){return function(t,n,r){return!!(function(e){return null!==e&&"object"==typeof e&&"type"in e}(t)&&e.call(this,t,"number"==typeof n?n:void 0,r||void 0))}}function o(){return!0}}}]);