(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7813],{19274:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var r=t(22342),a=t(33670),u=t(20063);function n(){let e=(0,u.useRouter)();return(0,a.useEffect)(()=>{e.replace("/docs/app")},[e]),(0,r.jsxs)("main",{className:"container flex flex-col py-16",children:[(0,r.jsx)("h1",{className:"font-semibold text-2xl md:text-3xl",children:"Redirecting to Documentation..."}),(0,r.jsxs)("p",{className:"mt-1 text-fd-muted-foreground text-lg",children:["If you are not redirected automatically, ",(0,r.jsx)("a",{href:"/docs/app",className:"text-blue-600 hover:underline",children:"click here"}),"."]})]})}},20063:(e,s,t)=>{"use strict";var r=t(47260);t.o(r,"useParams")&&t.d(s,{useParams:function(){return r.useParams}}),t.o(r,"usePathname")&&t.d(s,{usePathname:function(){return r.usePathname}}),t.o(r,"useRouter")&&t.d(s,{useRouter:function(){return r.useRouter}}),t.o(r,"useSearchParams")&&t.d(s,{useSearchParams:function(){return r.useSearchParams}})},55404:(e,s,t)=>{Promise.resolve().then(t.bind(t,19274))}},e=>{e.O(0,[6708,4478,7358],()=>e(e.s=55404)),_N_E=e.O()}]);