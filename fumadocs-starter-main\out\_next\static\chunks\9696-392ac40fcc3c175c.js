"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9696],{1481:(e,r,t)=>{t.r(r),t.d(r,{default:()=>l});var n=t(13700),o=t(33670),a=t(22342),l=(0,o.forwardRef)(({href:e="#",external:r=!(e.startsWith("/")||e.startsWith("#")||e.startsWith(".")),prefetch:t,...o},l)=>r?(0,a.jsx)("a",{ref:l,href:e,rel:"noreferrer noopener",target:"_blank",...o,children:o.children}):(0,a.jsx)(n.N_,{ref:l,href:e,prefetch:t,...o}));l.displayName="Link",t(65156)},17714:(e,r,t)=>{t.d(r,{L:()=>d,TreeContextProvider:()=>u,t:()=>p});var n=t(22342),o=t(99987),a=t(33670),l=t(80909);let i=(0,o.q6)("TreeContext"),s=(0,o.q6)("PathContext",[]);function u(e){var r,t;let u=(0,a.useRef)(0),d=(0,o.a8)(),p=(0,a.useMemo)(()=>e.tree,[null!=(r=e.tree.$id)?r:e.tree]),c=(0,a.useMemo)(()=>{var e;return null!=(e=(0,l.oe)(p.children,d))?e:[]},[p,d]),f=null!=(t=c.findLast(e=>"folder"===e.type&&e.root))?t:p;return null!=f.$id||(f.$id=String(u.current++)),(0,n.jsx)(i.Provider,{value:(0,a.useMemo)(()=>({root:f}),[f]),children:(0,n.jsx)(s.Provider,{value:c,children:e.children})})}function d(){return s.use()}function p(){return i.use("You must wrap this component under <DocsLayout />")}},25502:(e,r,t)=>{t.d(r,{Popover:()=>i,PopoverContent:()=>u,PopoverTrigger:()=>s});var n=t(22342),o=t(82286),a=t(33670),l=t(75889);let i=o.bL,s=o.l9,u=a.forwardRef((e,r)=>{let{className:t,align:a="center",sideOffset:i=4,...s}=e;return(0,n.jsx)(o.ZL,{children:(0,n.jsx)(o.UC,{ref:r,align:a,sideOffset:i,side:"bottom",className:(0,l.QP)("z-50 origin-(--radix-popover-content-transform-origin) min-w-[240px] max-w-[98vw] rounded-xl border bg-fd-popover/60 backdrop-blur-lg p-2 text-sm text-fd-popover-foreground shadow-lg focus-visible:outline-none data-[state=closed]:animate-fd-popover-out data-[state=open]:animate-fd-popover-in",t),...s})})});u.displayName=o.UC.displayName,o.iN},29947:(e,r,t)=>{t.d(r,{Collapsible:()=>i,CollapsibleContent:()=>u,CollapsibleTrigger:()=>s});var n=t(22342),o=t(57259),a=t(33670),l=t(75889);let i=o.bL,s=o.R6,u=(0,a.forwardRef)((e,r)=>{let{children:t,...i}=e,[s,u]=(0,a.useState)(!1);return(0,a.useEffect)(()=>{u(!0)},[]),(0,n.jsx)(o.Ke,{ref:r,...i,className:(0,l.QP)("overflow-hidden",s&&"data-[state=closed]:animate-fd-collapsible-up data-[state=open]:animate-fd-collapsible-down",i.className),children:t})});u.displayName=o.Ke.displayName},34212:(e,r,t)=>{t.d(r,{q:()=>n});function n(e,[r,t]){return Math.min(t,Math.max(r,e))}},57259:(e,r,t)=>{t.d(r,{Ke:()=>R,R6:()=>b,UC:()=>A,bL:()=>j,l9:()=>N,z3:()=>h});var n=t(33670),o=t(92556),a=t(3468),l=t(23558),i=t(4129),s=t(94446),u=t(97602),d=t(76842),p=t(68946),c=t(22342),f="Collapsible",[v,h]=(0,a.A)(f),[m,x]=v(f),g=n.forwardRef((e,r)=>{let{__scopeCollapsible:t,open:o,defaultOpen:a,disabled:i,onOpenChange:s,...d}=e,[v,h]=(0,l.i)({prop:o,defaultProp:null!=a&&a,onChange:s,caller:f});return(0,c.jsx)(m,{scope:t,disabled:i,contentId:(0,p.B)(),open:v,onOpenToggle:n.useCallback(()=>h(e=>!e),[h]),children:(0,c.jsx)(u.sG.div,{"data-state":P(v),"data-disabled":i?"":void 0,...d,ref:r})})});g.displayName=f;var C="CollapsibleTrigger",b=n.forwardRef((e,r)=>{let{__scopeCollapsible:t,...n}=e,a=x(C,t);return(0,c.jsx)(u.sG.button,{type:"button","aria-controls":a.contentId,"aria-expanded":a.open||!1,"data-state":P(a.open),"data-disabled":a.disabled?"":void 0,disabled:a.disabled,...n,ref:r,onClick:(0,o.mK)(e.onClick,a.onOpenToggle)})});b.displayName=C;var y="CollapsibleContent",R=n.forwardRef((e,r)=>{let{forceMount:t,...n}=e,o=x(y,e.__scopeCollapsible);return(0,c.jsx)(d.C,{present:t||o.open,children:e=>{let{present:t}=e;return(0,c.jsx)(w,{...n,ref:r,present:t})}})});R.displayName=y;var w=n.forwardRef((e,r)=>{let{__scopeCollapsible:t,present:o,children:a,...l}=e,d=x(y,t),[p,f]=n.useState(o),v=n.useRef(null),h=(0,s.s)(r,v),m=n.useRef(0),g=m.current,C=n.useRef(0),b=C.current,R=d.open||p,w=n.useRef(R),j=n.useRef(void 0);return n.useEffect(()=>{let e=requestAnimationFrame(()=>w.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,i.N)(()=>{let e=v.current;if(e){j.current=j.current||{transitionDuration:e.style.transitionDuration,animationName:e.style.animationName},e.style.transitionDuration="0s",e.style.animationName="none";let r=e.getBoundingClientRect();m.current=r.height,C.current=r.width,w.current||(e.style.transitionDuration=j.current.transitionDuration,e.style.animationName=j.current.animationName),f(o)}},[d.open,o]),(0,c.jsx)(u.sG.div,{"data-state":P(d.open),"data-disabled":d.disabled?"":void 0,id:d.contentId,hidden:!R,...l,ref:h,style:{"--radix-collapsible-content-height":g?"".concat(g,"px"):void 0,"--radix-collapsible-content-width":b?"".concat(b,"px"):void 0,...e.style},children:R&&a})});function P(e){return e?"open":"closed"}var j=g,N=b,A=R},58950:(e,r,t)=>{t.d(r,{T:()=>o});var n=t(33670);function o(e,r,t=function e(r,t){return Array.isArray(r)&&Array.isArray(t)?t.length!==r.length||r.some((r,n)=>e(r,t[n])):r!==t}){let[a,l]=(0,n.useState)(e);t(a,e)&&(r(e,a),l(e))}},77222:(e,r,t)=>{t.d(r,{G:()=>u,c:()=>s});var n=t(22342),o=t(33670),a=t(99987),l=t(79384);let i=(0,a.q6)("SidebarContext");function s(){return i.use()}function u(e){let{children:r}=e,t=(0,o.useRef)(!0),[s,u]=(0,o.useState)(!1),[d,p]=(0,o.useState)(!1),c=(0,a.a8)();return(0,l.T)(c,()=>{t.current&&u(!1),t.current=!0}),(0,n.jsx)(i.Provider,{value:(0,o.useMemo)(()=>({open:s,setOpen:u,collapsed:d,setCollapsed:p,closeOnRedirect:t}),[s,d]),children:r})}},79384:(e,r,t)=>{t.d(r,{T:()=>n.T});var n=t(58950);t(65156)},80909:(e,r,t)=>{function n(e,r,t){let{includePage:n=!0,includeSeparator:o=!1,includeRoot:a}=t,l=[];return r.forEach((e,t)=>{if("separator"===e.type&&e.name&&o&&l.push({name:e.name}),"folder"===e.type){let n=r.at(t+1);if(n&&e.index===n)return;if(e.root){l=[];return}l.push({name:e.name,url:e.index?.url})}"page"===e.type&&n&&l.push({name:e.name,url:e.url})}),a&&l.unshift({name:e.name,url:"object"==typeof a?a.url:void 0}),l}t.d(r,{Pp:()=>n,oe:()=>function e(r,t){let n;for(let o of(t.endsWith("/")&&(t=t.slice(0,-1)),r)){if("separator"===o.type&&(n=o),"folder"===o.type){if(o.index?.url===t){let e=[];return n&&e.push(n),e.push(o,o.index),e}let r=e(o.children,t);if(r)return r.unshift(o),n&&r.unshift(n),r}if("page"===o.type&&o.url===t){let e=[];return n&&e.push(n),e.push(o),e}}return null}}),t(65156),t(33670)},82286:(e,r,t)=>{t.d(r,{AM:()=>N,UC:()=>z,Wv:()=>O,ZL:()=>$,bL:()=>U,hl:()=>L,i0:()=>F,iN:()=>q,l9:()=>B});var n=t(33670),o=t(92556),a=t(94446),l=t(3468),i=t(44831),s=t(19526),u=t(69666),d=t(68946),p=t(57641),c=t(75433),f=t(76842),v=t(97602),h=t(32467),m=t(23558),x=t(97745),g=t(40101),C=t(22342),b="Popover",[y,R]=(0,l.A)(b,[p.Bk]),w=(0,p.Bk)(),[P,j]=y(b),N=e=>{let{__scopePopover:r,children:t,open:o,defaultOpen:a,onOpenChange:l,modal:i=!1}=e,s=w(r),u=n.useRef(null),[c,f]=n.useState(!1),[v,h]=(0,m.i)({prop:o,defaultProp:null!=a&&a,onChange:l,caller:b});return(0,C.jsx)(p.bL,{...s,children:(0,C.jsx)(P,{scope:r,contentId:(0,d.B)(),triggerRef:u,open:v,onOpenChange:h,onOpenToggle:n.useCallback(()=>h(e=>!e),[h]),hasCustomAnchor:c,onCustomAnchorAdd:n.useCallback(()=>f(!0),[]),onCustomAnchorRemove:n.useCallback(()=>f(!1),[]),modal:i,children:t})})};N.displayName=b;var A="PopoverAnchor";n.forwardRef((e,r)=>{let{__scopePopover:t,...o}=e,a=j(A,t),l=w(t),{onCustomAnchorAdd:i,onCustomAnchorRemove:s}=a;return n.useEffect(()=>(i(),()=>s()),[i,s]),(0,C.jsx)(p.Mz,{...l,...o,ref:r})}).displayName=A;var k="PopoverTrigger",O=n.forwardRef((e,r)=>{let{__scopePopover:t,...n}=e,l=j(k,t),i=w(t),s=(0,a.s)(r,l.triggerRef),u=(0,C.jsx)(v.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":G(l.open),...n,ref:s,onClick:(0,o.mK)(e.onClick,l.onOpenToggle)});return l.hasCustomAnchor?u:(0,C.jsx)(p.Mz,{asChild:!0,...i,children:u})});O.displayName=k;var D="PopoverPortal",[_,T]=y(D,{forceMount:void 0}),F=e=>{let{__scopePopover:r,forceMount:t,children:n,container:o}=e,a=j(D,r);return(0,C.jsx)(_,{scope:r,forceMount:t,children:(0,C.jsx)(f.C,{present:t||a.open,children:(0,C.jsx)(c.Z,{asChild:!0,container:o,children:n})})})};F.displayName=D;var E="PopoverContent",L=n.forwardRef((e,r)=>{let t=T(E,e.__scopePopover),{forceMount:n=t.forceMount,...o}=e,a=j(E,e.__scopePopover);return(0,C.jsx)(f.C,{present:n||a.open,children:a.modal?(0,C.jsx)(K,{...o,ref:r}):(0,C.jsx)(S,{...o,ref:r})})});L.displayName=E;var M=(0,h.TL)("PopoverContent.RemoveScroll"),K=n.forwardRef((e,r)=>{let t=j(E,e.__scopePopover),l=n.useRef(null),i=(0,a.s)(r,l),s=n.useRef(!1);return n.useEffect(()=>{let e=l.current;if(e)return(0,x.Eq)(e)},[]),(0,C.jsx)(g.A,{as:M,allowPinchZoom:!0,children:(0,C.jsx)(W,{...e,ref:i,trapFocus:t.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.mK)(e.onCloseAutoFocus,e=>{var r;e.preventDefault(),s.current||null==(r=t.triggerRef.current)||r.focus()}),onPointerDownOutside:(0,o.mK)(e.onPointerDownOutside,e=>{let r=e.detail.originalEvent,t=0===r.button&&!0===r.ctrlKey;s.current=2===r.button||t},{checkForDefaultPrevented:!1}),onFocusOutside:(0,o.mK)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1})})})}),S=n.forwardRef((e,r)=>{let t=j(E,e.__scopePopover),o=n.useRef(!1),a=n.useRef(!1);return(0,C.jsx)(W,{...e,ref:r,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:r=>{var n,l;null==(n=e.onCloseAutoFocus)||n.call(e,r),r.defaultPrevented||(o.current||null==(l=t.triggerRef.current)||l.focus(),r.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:r=>{var n,l;null==(n=e.onInteractOutside)||n.call(e,r),r.defaultPrevented||(o.current=!0,"pointerdown"===r.detail.originalEvent.type&&(a.current=!0));let i=r.target;(null==(l=t.triggerRef.current)?void 0:l.contains(i))&&r.preventDefault(),"focusin"===r.detail.originalEvent.type&&a.current&&r.preventDefault()}})}),W=n.forwardRef((e,r)=>{let{__scopePopover:t,trapFocus:n,onOpenAutoFocus:o,onCloseAutoFocus:a,disableOutsidePointerEvents:l,onEscapeKeyDown:d,onPointerDownOutside:c,onFocusOutside:f,onInteractOutside:v,...h}=e,m=j(E,t),x=w(t);return(0,s.Oh)(),(0,C.jsx)(u.n,{asChild:!0,loop:!0,trapped:n,onMountAutoFocus:o,onUnmountAutoFocus:a,children:(0,C.jsx)(i.qW,{asChild:!0,disableOutsidePointerEvents:l,onInteractOutside:v,onEscapeKeyDown:d,onPointerDownOutside:c,onFocusOutside:f,onDismiss:()=>m.onOpenChange(!1),children:(0,C.jsx)(p.UC,{"data-state":G(m.open),role:"dialog",id:m.contentId,...x,...h,ref:r,style:{...h.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),I="PopoverClose",q=n.forwardRef((e,r)=>{let{__scopePopover:t,...n}=e,a=j(I,t);return(0,C.jsx)(v.sG.button,{type:"button",...n,ref:r,onClick:(0,o.mK)(e.onClick,()=>a.onOpenChange(!1))})});function G(e){return e?"open":"closed"}q.displayName=I,n.forwardRef((e,r)=>{let{__scopePopover:t,...n}=e,o=w(t);return(0,C.jsx)(p.i3,{...o,...n,ref:r})}).displayName="PopoverArrow";var U=N,B=O,$=F,z=L},89496:(e,r,t)=>{t.d(r,{$:()=>n});function n(e,r,t=!0){return e.endsWith("/")&&(e=e.slice(0,-1)),r.endsWith("/")&&(r=r.slice(0,-1)),e===r||t&&r.startsWith(`${e}/`)}}}]);