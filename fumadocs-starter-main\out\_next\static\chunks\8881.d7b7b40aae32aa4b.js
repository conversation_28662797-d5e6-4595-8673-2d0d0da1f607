"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8881],{58881:(t,e,n)=>{n.d(e,{searchDocs:()=>r});var i=n(84e3);async function r(t,{indexName:e,onSearch:n,client:r,locale:l,tag:u}){if(0===t.trim().length)return[];let s=n?await n(t,u,l):await r.searchForHits({requests:[{type:"default",indexName:e,query:t,distinct:5,hitsPerPage:10,filters:u?`tag:${u}`:void 0}]}),h=(0,i.r)(t);return(function(t){let e=[],n=new Set;for(let i of t)n.has(i.url)||(n.add(i.url),e.push({id:i.url,type:"page",url:i.url,content:i.title})),e.push({id:i.objectID,type:i.content===i.section?"heading":"text",url:i.section_id?`${i.url}#${i.section_id}`:i.url,content:i.content});return e})(s.results[0].hits).flatMap(t=>"page"===t.type?{...t,contentWithHighlights:t.contentWithHighlights??h.highlight(t.content)}:[])}n(65156)},84e3:(t,e,n)=>{function i(t){return t.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function r(t){let e="string"==typeof t?function(t){let e=t.trim();if(0===e.length)return null;let n=Array.from(new Set(e.split(/\s+/).map(t=>t.trim()).filter(Boolean)));if(0===n.length)return null;let r=n.map(i).join("|");return RegExp(`(${r})`,"gi")}(t):t;return{highlight(t){if(!e)return[{type:"text",content:t}];let n=[],i=0;for(let r of t.matchAll(e))i<r.index&&n.push({type:"text",content:t.substring(i,r.index)}),n.push({type:"text",content:r[0],styles:{highlight:!0}}),i=r.index+r[0].length;return i<t.length&&n.push({type:"text",content:t.substring(i)}),n}}}n.d(e,{r:()=>r})}}]);