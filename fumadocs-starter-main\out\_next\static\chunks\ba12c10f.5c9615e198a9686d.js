"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8803],{12507:(e,t,a)=>{a.d(t,{rL:()=>ee,vl:()=>et});var r,s=a(93272),o=a(64876),i=a(31219),l=Object.defineProperty,n=Symbol.for("vercel.ai.error.AI_NoOutputSpecifiedError");o.bD;Symbol.for("vercel.ai.error.AI_InvalidArgumentError");o.bD;Symbol.for("vercel.ai.error.AI_InvalidStreamPartError");o.bD;Symbol.for("vercel.ai.error.AI_InvalidToolInputError");o.bD;Symbol.for("vercel.ai.error.AI_MCPClientError");o.bD;Symbol.for("vercel.ai.error.AI_NoImageGeneratedError");o.bD;var p="AI_NoObjectGeneratedError",u=`vercel.ai.error.${p}`,d=Symbol.for(u),c=class extends o.bD{constructor({message:e="No object generated.",cause:t,text:a,response:s,usage:o,finishReason:i}){super({name:p,message:e,cause:t}),this[r]=!0,this.text=a,this.response=s,this.usage=o,this.finishReason=i}static isInstance(e){return o.bD.hasMarker(e,u)}};r=d;Symbol.for("vercel.ai.error.AI_NoOutputGeneratedError");o.bD;Symbol.for("vercel.ai.error.AI_NoSuchToolError");o.bD;Symbol.for("vercel.ai.error.AI_ToolCallRepairError");o.bD,o.bD;Symbol.for("vercel.ai.error.AI_InvalidDataContentError");o.bD;Symbol.for("vercel.ai.error.AI_InvalidMessageRoleError");o.bD;Symbol.for("vercel.ai.error.AI_MessageConversionError");o.bD;Symbol.for("vercel.ai.error.AI_DownloadError");o.bD;Symbol.for("vercel.ai.error.AI_RetryError");o.bD;var m=i.KC([i.Yj(),i.Nl(Uint8Array),i.Nl(ArrayBuffer),i.Ie(e=>{var t,a;return null!=(a=null==(t=globalThis.Buffer)?void 0:t.isBuffer(e))&&a},{message:"Must be a Buffer"})]),h=i.RZ(()=>i.KC([i.ch(),i.Yj(),i.ai(),i.zM(),i.g1(i.Yj(),h),i.YO(h)])),I=i.g1(i.Yj(),i.g1(i.Yj(),h)),v=i.Ik({type:i.eu("text"),text:i.Yj(),providerOptions:I.optional()}),g=i.Ik({type:i.eu("image"),image:i.KC([m,i.Nl(URL)]),mediaType:i.Yj().optional(),providerOptions:I.optional()}),y=i.Ik({type:i.eu("file"),data:i.KC([m,i.Nl(URL)]),filename:i.Yj().optional(),mediaType:i.Yj(),providerOptions:I.optional()}),E=i.Ik({type:i.eu("reasoning"),text:i.Yj(),providerOptions:I.optional()}),b=i.Ik({type:i.eu("tool-call"),toolCallId:i.Yj(),toolName:i.Yj(),input:i.L5(),providerOptions:I.optional(),providerExecuted:i.zM().optional()}),f=i.gM("type",[i.Ik({type:i.eu("text"),value:i.Yj()}),i.Ik({type:i.eu("json"),value:h}),i.Ik({type:i.eu("error-text"),value:i.Yj()}),i.Ik({type:i.eu("error-json"),value:h}),i.Ik({type:i.eu("content"),value:i.YO(i.KC([i.Ik({type:i.eu("text"),text:i.Yj()}),i.Ik({type:i.eu("media"),data:i.Yj(),mediaType:i.Yj()})]))})]),T=i.Ik({type:i.eu("tool-result"),toolCallId:i.Yj(),toolName:i.Yj(),output:f,providerOptions:I.optional()}),_=i.Ik({role:i.eu("system"),content:i.Yj(),providerOptions:I.optional()}),M=i.Ik({role:i.eu("user"),content:i.KC([i.Yj(),i.YO(i.KC([v,g,y]))]),providerOptions:I.optional()}),Y=i.Ik({role:i.eu("assistant"),content:i.KC([i.Yj(),i.YO(i.KC([v,y,E,b,T]))]),providerOptions:I.optional()}),j=i.Ik({role:i.eu("tool"),content:i.YO(T),providerOptions:I.optional()});i.KC([_,M,Y,j]),(0,s.hK)({prefix:"aitxt",size:24}),TransformStream;var x=i.KC([i.re({type:i.eu("text-start"),id:i.Yj(),providerMetadata:I.optional()}),i.re({type:i.eu("text-delta"),id:i.Yj(),delta:i.Yj(),providerMetadata:I.optional()}),i.re({type:i.eu("text-end"),id:i.Yj(),providerMetadata:I.optional()}),i.re({type:i.eu("error"),errorText:i.Yj()}),i.re({type:i.eu("tool-input-start"),toolCallId:i.Yj(),toolName:i.Yj(),providerExecuted:i.zM().optional(),dynamic:i.zM().optional()}),i.re({type:i.eu("tool-input-delta"),toolCallId:i.Yj(),inputTextDelta:i.Yj()}),i.re({type:i.eu("tool-input-available"),toolCallId:i.Yj(),toolName:i.Yj(),input:i.L5(),providerExecuted:i.zM().optional(),providerMetadata:I.optional(),dynamic:i.zM().optional()}),i.re({type:i.eu("tool-input-error"),toolCallId:i.Yj(),toolName:i.Yj(),input:i.L5(),providerExecuted:i.zM().optional(),providerMetadata:I.optional(),dynamic:i.zM().optional(),errorText:i.Yj()}),i.re({type:i.eu("tool-output-available"),toolCallId:i.Yj(),output:i.L5(),providerExecuted:i.zM().optional(),dynamic:i.zM().optional(),preliminary:i.zM().optional()}),i.re({type:i.eu("tool-output-error"),toolCallId:i.Yj(),errorText:i.Yj(),providerExecuted:i.zM().optional(),dynamic:i.zM().optional()}),i.re({type:i.eu("reasoning"),text:i.Yj(),providerMetadata:I.optional()}),i.re({type:i.eu("reasoning-start"),id:i.Yj(),providerMetadata:I.optional()}),i.re({type:i.eu("reasoning-delta"),id:i.Yj(),delta:i.Yj(),providerMetadata:I.optional()}),i.re({type:i.eu("reasoning-end"),id:i.Yj(),providerMetadata:I.optional()}),i.re({type:i.eu("reasoning-part-finish")}),i.re({type:i.eu("source-url"),sourceId:i.Yj(),url:i.Yj(),title:i.Yj().optional(),providerMetadata:I.optional()}),i.re({type:i.eu("source-document"),sourceId:i.Yj(),mediaType:i.Yj(),title:i.Yj(),filename:i.Yj().optional(),providerMetadata:I.optional()}),i.re({type:i.eu("file"),url:i.Yj(),mediaType:i.Yj(),providerMetadata:I.optional()}),i.re({type:i.Yj().startsWith("data-"),id:i.Yj().optional(),data:i.L5(),transient:i.zM().optional()}),i.re({type:i.eu("start-step")}),i.re({type:i.eu("finish-step")}),i.re({type:i.eu("start"),messageId:i.Yj().optional(),messageMetadata:i.L5().optional()}),i.re({type:i.eu("finish"),messageMetadata:i.L5().optional()}),i.re({type:i.eu("abort")}),i.re({type:i.eu("message-metadata"),messageMetadata:i.L5()})]);async function C(e){if(void 0===e)return{value:void 0,state:"undefined-input"};let t=await (0,s.N8)({text:e});return t.success?{value:t.value,state:"successful-parse"}:(t=await (0,s.N8)({text:function(e){let t=["ROOT"],a=-1,r=null;function s(e,s,o){switch(e){case'"':a=s,t.pop(),t.push(o),t.push("INSIDE_STRING");break;case"f":case"t":case"n":a=s,r=s,t.pop(),t.push(o),t.push("INSIDE_LITERAL");break;case"-":t.pop(),t.push(o),t.push("INSIDE_NUMBER");break;case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":a=s,t.pop(),t.push(o),t.push("INSIDE_NUMBER");break;case"{":a=s,t.pop(),t.push(o),t.push("INSIDE_OBJECT_START");break;case"[":a=s,t.pop(),t.push(o),t.push("INSIDE_ARRAY_START")}}function o(e,r){switch(e){case",":t.pop(),t.push("INSIDE_OBJECT_AFTER_COMMA");break;case"}":a=r,t.pop()}}function i(e,r){switch(e){case",":t.pop(),t.push("INSIDE_ARRAY_AFTER_COMMA");break;case"]":a=r,t.pop()}}for(let l=0;l<e.length;l++){let n=e[l];switch(t[t.length-1]){case"ROOT":s(n,l,"FINISH");break;case"INSIDE_OBJECT_START":switch(n){case'"':t.pop(),t.push("INSIDE_OBJECT_KEY");break;case"}":a=l,t.pop()}break;case"INSIDE_OBJECT_AFTER_COMMA":'"'===n&&(t.pop(),t.push("INSIDE_OBJECT_KEY"));break;case"INSIDE_OBJECT_KEY":'"'===n&&(t.pop(),t.push("INSIDE_OBJECT_AFTER_KEY"));break;case"INSIDE_OBJECT_AFTER_KEY":":"===n&&(t.pop(),t.push("INSIDE_OBJECT_BEFORE_VALUE"));break;case"INSIDE_OBJECT_BEFORE_VALUE":s(n,l,"INSIDE_OBJECT_AFTER_VALUE");break;case"INSIDE_OBJECT_AFTER_VALUE":o(n,l);break;case"INSIDE_STRING":switch(n){case'"':t.pop(),a=l;break;case"\\":t.push("INSIDE_STRING_ESCAPE");break;default:a=l}break;case"INSIDE_ARRAY_START":"]"===n?(a=l,t.pop()):(a=l,s(n,l,"INSIDE_ARRAY_AFTER_VALUE"));break;case"INSIDE_ARRAY_AFTER_VALUE":switch(n){case",":t.pop(),t.push("INSIDE_ARRAY_AFTER_COMMA");break;case"]":a=l,t.pop();break;default:a=l}break;case"INSIDE_ARRAY_AFTER_COMMA":s(n,l,"INSIDE_ARRAY_AFTER_VALUE");break;case"INSIDE_STRING_ESCAPE":t.pop(),a=l;break;case"INSIDE_NUMBER":switch(n){case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":a=l;break;case"e":case"E":case"-":case".":break;case",":t.pop(),"INSIDE_ARRAY_AFTER_VALUE"===t[t.length-1]&&i(n,l),"INSIDE_OBJECT_AFTER_VALUE"===t[t.length-1]&&o(n,l);break;case"}":t.pop(),"INSIDE_OBJECT_AFTER_VALUE"===t[t.length-1]&&o(n,l);break;case"]":t.pop(),"INSIDE_ARRAY_AFTER_VALUE"===t[t.length-1]&&i(n,l);break;default:t.pop()}break;case"INSIDE_LITERAL":{let s=e.substring(r,l+1);"false".startsWith(s)||"true".startsWith(s)||"null".startsWith(s)?a=l:(t.pop(),"INSIDE_OBJECT_AFTER_VALUE"===t[t.length-1]?o(n,l):"INSIDE_ARRAY_AFTER_VALUE"===t[t.length-1]&&i(n,l))}}}let l=e.slice(0,a+1);for(let a=t.length-1;a>=0;a--)switch(t[a]){case"INSIDE_STRING":l+='"';break;case"INSIDE_OBJECT_KEY":case"INSIDE_OBJECT_AFTER_KEY":case"INSIDE_OBJECT_AFTER_COMMA":case"INSIDE_OBJECT_START":case"INSIDE_OBJECT_BEFORE_VALUE":case"INSIDE_OBJECT_AFTER_VALUE":l+="}";break;case"INSIDE_ARRAY_START":case"INSIDE_ARRAY_AFTER_COMMA":case"INSIDE_ARRAY_AFTER_VALUE":l+="]";break;case"INSIDE_LITERAL":{let t=e.substring(r,e.length);"true".startsWith(t)?l+="true".slice(t.length):"false".startsWith(t)?l+="false".slice(t.length):"null".startsWith(t)&&(l+="null".slice(t.length))}}return l}(e)})).success?{value:t.value,state:"repaired-parse"}:{value:void 0,state:"failed-parse"}}function k(e){return e.type.startsWith("tool-")}function R(e){return e.type.split("-").slice(1).join("-")}async function A({stream:e,onError:t}){let a=e.getReader();try{for(;;){let{done:e}=await a.read();if(e)break}}catch(e){null==t||t(e)}finally{a.releaseLock()}}(0,s.hK)({prefix:"aitxt",size:24}),(0,s.hK)({prefix:"aiobj",size:24});var N=class{constructor(){this.queue=[],this.isProcessing=!1}async processQueue(){if(!this.isProcessing){for(this.isProcessing=!0;this.queue.length>0;)await this.queue[0](),this.queue.shift();this.isProcessing=!1}}async run(e){return new Promise((t,a)=>{this.queue.push(async()=>{try{await e(),t()}catch(e){a(e)}}),this.processQueue()})}};(0,s.hK)({prefix:"aiobj",size:24}),o.bD,((e,t)=>{for(var a in t)l(e,a,{get:t[a],enumerable:!0})})({},{object:()=>w,text:()=>S});var S=()=>({type:"text",responseFormat:{type:"text"},parsePartial:async({text:e})=>({partial:e}),parseOutput:async({text:e})=>e}),w=({schema:e})=>{let t=(0,s.mD)(e);return{type:"object",responseFormat:{type:"json",schema:t.jsonSchema},async parsePartial({text:e}){let t=await C(e);switch(t.state){case"failed-parse":case"undefined-input":return;case"repaired-parse":case"successful-parse":return{partial:t.value};default:{let e=t.state;throw Error(`Unsupported parse state: ${e}`)}}},async parseOutput({text:e},a){let r=await (0,s.N8)({text:e});if(!r.success)throw new c({message:"No object generated: could not parse the response.",cause:r.error,text:e,response:a.response,usage:a.usage,finishReason:a.finishReason});let o=await (0,s.ZZ)({value:r.value,schema:t});if(!o.success)throw new c({message:"No object generated: response did not match schema.",cause:o.error,text:e,response:a.response,usage:a.usage,finishReason:a.finishReason});return o.value}}},D=Symbol.for("vercel.ai.error.AI_NoSuchProviderError");o.eM;var O=i._H({name:i.Yj(),version:i.Yj()}),L=i._H({_meta:i.lq(i.Ik({}).loose())}),P=i.Ik({method:i.Yj(),params:i.lq(L)}),q=i._H({experimental:i.lq(i.Ik({}).loose()),logging:i.lq(i.Ik({}).loose()),prompts:i.lq(i._H({listChanged:i.lq(i.zM())})),resources:i.lq(i._H({subscribe:i.lq(i.zM()),listChanged:i.lq(i.zM())})),tools:i.lq(i._H({listChanged:i.lq(i.zM())}))});L.extend({protocolVersion:i.Yj(),capabilities:q,serverInfo:O,instructions:i.lq(i.Yj())});var F=L.extend({nextCursor:i.lq(i.Yj())}),B=i.Ik({name:i.Yj(),description:i.lq(i.Yj()),inputSchema:i.Ik({type:i.eu("object"),properties:i.lq(i.Ik({}).loose())}).loose()}).loose();F.extend({tools:i.YO(B)});var K=i.Ik({type:i.eu("text"),text:i.Yj()}).loose(),z=i.Ik({type:i.eu("image"),data:i.K3(),mimeType:i.Yj()}).loose(),U=i.Ik({uri:i.Yj(),mimeType:i.lq(i.Yj())}).loose(),J=U.extend({text:i.Yj()}),V=U.extend({blob:i.K3()}),W=i.Ik({type:i.eu("resource"),resource:i.KC([J,V])}).loose();L.extend({content:i.YO(i.KC([K,z,W])),isError:i.zM().default(!1).optional()}).or(L.extend({toolResult:i.L5()}));var Z=i.Ik({jsonrpc:i.eu("2.0"),id:i.KC([i.Yj(),i.ai().int()])}).merge(P).strict(),G=i.Ik({jsonrpc:i.eu("2.0"),id:i.KC([i.Yj(),i.ai().int()]),result:L}).strict(),$=i.Ik({jsonrpc:i.eu("2.0"),id:i.KC([i.Yj(),i.ai().int()]),error:i.Ik({code:i.ai().int(),message:i.Yj(),data:i.lq(i.L5())})}).strict(),H=i.Ik({jsonrpc:i.eu("2.0")}).merge(i.Ik({method:i.Yj(),params:i.lq(L)})).strict();async function Q(e){if(null==e)return[];if(!globalThis.FileList||!(e instanceof globalThis.FileList))throw Error("FileList is not supported in the current environment");return Promise.all(Array.from(e).map(async e=>{let{name:t,type:a}=e;return{type:"file",mediaType:a,filename:t,url:await new Promise((t,a)=>{let r=new FileReader;r.onload=e=>{var a;t(null==(a=e.target)?void 0:a.result)},r.onerror=e=>a(e),r.readAsDataURL(e)})}}))}i.KC([Z,H,G,$]),o.bD;var X=class{constructor({api:e="/api/chat",credentials:t,headers:a,body:r,fetch:s,prepareSendMessagesRequest:o,prepareReconnectToStreamRequest:i}){this.api=e,this.credentials=t,this.headers=a,this.body=r,this.fetch=s,this.prepareSendMessagesRequest=o,this.prepareReconnectToStreamRequest=i}async sendMessages({abortSignal:e,...t}){var a,r,o,i,l;let n=await (0,s.hd)(this.body),p=await (0,s.hd)(this.headers),u=await (0,s.hd)(this.credentials),d=await (null==(a=this.prepareSendMessagesRequest)?void 0:a.call(this,{api:this.api,id:t.chatId,messages:t.messages,body:{...n,...t.body},headers:{...p,...t.headers},credentials:u,requestMetadata:t.metadata,trigger:t.trigger,messageId:t.messageId})),c=null!=(r=null==d?void 0:d.api)?r:this.api,m=(null==d?void 0:d.headers)!==void 0?d.headers:{...p,...t.headers},h=(null==d?void 0:d.body)!==void 0?d.body:{...n,...t.body,id:t.chatId,messages:t.messages,trigger:t.trigger,messageId:t.messageId},I=null!=(o=null==d?void 0:d.credentials)?o:u,v=null!=(i=this.fetch)?i:globalThis.fetch,g=await v(c,{method:"POST",headers:{"Content-Type":"application/json",...m},body:JSON.stringify(h),credentials:I,signal:e});if(!g.ok)throw Error(null!=(l=await g.text())?l:"Failed to fetch the chat response.");if(!g.body)throw Error("The response body is empty.");return this.processResponseStream(g.body)}async reconnectToStream(e){var t,a,r,o,i;let l=await (0,s.hd)(this.body),n=await (0,s.hd)(this.headers),p=await (0,s.hd)(this.credentials),u=await (null==(t=this.prepareReconnectToStreamRequest)?void 0:t.call(this,{api:this.api,id:e.chatId,body:{...l,...e.body},headers:{...n,...e.headers},credentials:p,requestMetadata:e.metadata})),d=null!=(a=null==u?void 0:u.api)?a:`${this.api}/${e.chatId}/stream`,c=(null==u?void 0:u.headers)!==void 0?u.headers:{...n,...e.headers},m=null!=(r=null==u?void 0:u.credentials)?r:p,h=null!=(o=this.fetch)?o:globalThis.fetch,I=await h(d,{method:"GET",headers:c,credentials:m});if(204===I.status)return null;if(!I.ok)throw Error(null!=(i=await I.text())?i:"Failed to fetch the chat response.");if(!I.body)throw Error("The response body is empty.");return this.processResponseStream(I.body)}},ee=class extends X{constructor(e={}){super(e)}processResponseStream(e){return(0,s._Z)({stream:e,schema:x}).pipeThrough(new TransformStream({async transform(e,t){if(!e.success)throw e.error;t.enqueue(e.value)}}))}},et=class{constructor({generateId:e=s.$C,id:t=e(),transport:a=new ee,messageMetadataSchema:r,dataPartSchemas:o,state:i,onError:l,onToolCall:n,onFinish:p,onData:u,sendAutomaticallyWhen:d}){this.activeResponse=void 0,this.jobExecutor=new N,this.sendMessage=async(e,t)=>{var a,r,s,o;let i;if(null==e)return void await this.makeRequest({trigger:"submit-message",messageId:null==(a=this.lastMessage)?void 0:a.id,...t});if(i="text"in e||"files"in e?{parts:[...Array.isArray(e.files)?e.files:await Q(e.files),..."text"in e&&null!=e.text?[{type:"text",text:e.text}]:[]]}:e,null!=e.messageId){let t=this.state.messages.findIndex(t=>t.id===e.messageId);if(-1===t)throw Error(`message with id ${e.messageId} not found`);if("user"!==this.state.messages[t].role)throw Error(`message with id ${e.messageId} is not a user message`);this.state.messages=this.state.messages.slice(0,t+1),this.state.replaceMessage(t,{...i,id:e.messageId,role:null!=(r=i.role)?r:"user",metadata:e.metadata})}else this.state.pushMessage({...i,id:null!=(s=i.id)?s:this.generateId(),role:null!=(o=i.role)?o:"user",metadata:e.metadata});await this.makeRequest({trigger:"submit-message",messageId:e.messageId,...t})},this.regenerate=async({messageId:e,...t}={})=>{let a=null==e?this.state.messages.length-1:this.state.messages.findIndex(t=>t.id===e);if(-1===a)throw Error(`message ${e} not found`);this.state.messages=this.state.messages.slice(0,"assistant"===this.messages[a].role?a:a+1),await this.makeRequest({trigger:"regenerate-message",messageId:e,...t})},this.resumeStream=async(e={})=>{await this.makeRequest({trigger:"resume-stream",...e})},this.clearError=()=>{"error"===this.status&&(this.state.error=void 0,this.setStatus({status:"ready"}))},this.addToolResult=async({tool:e,toolCallId:t,output:a})=>this.jobExecutor.run(async()=>{var e,r;let s=this.state.messages,o=s[s.length-1];this.state.replaceMessage(s.length-1,{...o,parts:o.parts.map(e=>k(e)&&e.toolCallId===t?{...e,state:"output-available",output:a}:e)}),this.activeResponse&&(this.activeResponse.state.message.parts=this.activeResponse.state.message.parts.map(e=>k(e)&&e.toolCallId===t?{...e,state:"output-available",output:a,errorText:void 0}:e)),"streaming"!==this.status&&"submitted"!==this.status&&(null==(e=this.sendAutomaticallyWhen)?void 0:e.call(this,{messages:this.state.messages}))&&this.makeRequest({trigger:"submit-message",messageId:null==(r=this.lastMessage)?void 0:r.id})}),this.stop=async()=>{var e;("streaming"===this.status||"submitted"===this.status)&&(null==(e=this.activeResponse)?void 0:e.abortController)&&this.activeResponse.abortController.abort()},this.id=t,this.transport=a,this.generateId=e,this.messageMetadataSchema=r,this.dataPartSchemas=o,this.state=i,this.onError=l,this.onToolCall=n,this.onFinish=p,this.onData=u,this.sendAutomaticallyWhen=d}get status(){return this.state.status}setStatus({status:e,error:t}){this.status!==e&&(this.state.status=e,this.state.error=t)}get error(){return this.state.error}get messages(){return this.state.messages}get lastMessage(){return this.state.messages[this.state.messages.length-1]}set messages(e){this.state.messages=e}async makeRequest({trigger:e,metadata:t,headers:a,body:r,messageId:o}){var i,l,n;this.setStatus({status:"submitted",error:void 0});let p=this.lastMessage;try{let l,n={state:function({lastMessage:e,messageId:t}){return{message:(null==e?void 0:e.role)==="assistant"?e:{id:t,metadata:void 0,role:"assistant",parts:[]},activeTextParts:{},activeReasoningParts:{},partialToolCalls:{}}}({lastMessage:this.state.snapshot(p),messageId:this.generateId()}),abortController:new AbortController};if(this.activeResponse=n,"resume-stream"===e){let e=await this.transport.reconnectToStream({chatId:this.id,metadata:t,headers:a,body:r});if(null==e)return void this.setStatus({status:"ready"});l=e}else l=await this.transport.sendMessages({chatId:this.id,messages:this.state.messages,abortSignal:n.abortController.signal,metadata:t,headers:a,body:r,trigger:e,messageId:o});let u=e=>this.jobExecutor.run(()=>e({state:n.state,write:()=>{var e;this.setStatus({status:"streaming"}),n.state.message.id===(null==(e=this.lastMessage)?void 0:e.id)?this.state.replaceMessage(this.state.messages.length-1,n.state.message):this.state.pushMessage(n.state.message)}}));await A({stream:function({stream:e,messageMetadataSchema:t,dataPartSchemas:a,runUpdateMessageJob:r,onError:o,onToolCall:i,onData:l}){return e.pipeThrough(new TransformStream({async transform(e,n){await r(async({state:r,write:p})=>{var u,d,c,m;function h(e){let t=r.message.parts.filter(k).find(t=>t.toolCallId===e);if(null==t)throw Error("tool-output-error must be preceded by a tool-input-available");return t}function I(e){let t=r.message.parts.filter(e=>"dynamic-tool"===e.type).find(t=>t.toolCallId===e);if(null==t)throw Error("tool-output-error must be preceded by a tool-input-available");return t}function v(e){var t;let a=r.message.parts.find(t=>k(t)&&t.toolCallId===e.toolCallId);null!=a?(a.state=e.state,a.input=e.input,a.output=e.output,a.errorText=e.errorText,a.rawInput=e.rawInput,a.preliminary=e.preliminary,a.providerExecuted=null!=(t=e.providerExecuted)?t:a.providerExecuted,null!=e.providerMetadata&&"input-available"===a.state&&(a.callProviderMetadata=e.providerMetadata)):r.message.parts.push({type:`tool-${e.toolName}`,toolCallId:e.toolCallId,state:e.state,input:e.input,output:e.output,rawInput:e.rawInput,errorText:e.errorText,providerExecuted:e.providerExecuted,preliminary:e.preliminary,...null!=e.providerMetadata?{callProviderMetadata:e.providerMetadata}:{}})}function g(e){var t;let a=r.message.parts.find(t=>"dynamic-tool"===t.type&&t.toolCallId===e.toolCallId);null!=a?(a.state=e.state,a.toolName=e.toolName,a.input=e.input,a.output=e.output,a.errorText=e.errorText,a.rawInput=null!=(t=e.rawInput)?t:a.rawInput,a.preliminary=e.preliminary,null!=e.providerMetadata&&"input-available"===a.state&&(a.callProviderMetadata=e.providerMetadata)):r.message.parts.push({type:"dynamic-tool",toolName:e.toolName,toolCallId:e.toolCallId,state:e.state,input:e.input,output:e.output,errorText:e.errorText,preliminary:e.preliminary,...null!=e.providerMetadata?{callProviderMetadata:e.providerMetadata}:{}})}async function y(e){if(null!=e){let a=null!=r.message.metadata?function e(t,a){if(void 0===t&&void 0===a)return;if(void 0===t)return a;if(void 0===a)return t;let r={...t};for(let s in a)if(Object.prototype.hasOwnProperty.call(a,s)){let o=a[s];if(void 0===o)continue;let i=s in t?t[s]:void 0,l=null!==o&&"object"==typeof o&&!Array.isArray(o)&&!(o instanceof Date)&&!(o instanceof RegExp),n=null!=i&&"object"==typeof i&&!Array.isArray(i)&&!(i instanceof Date)&&!(i instanceof RegExp);l&&n?r[s]=e(i,o):r[s]=o}return r}(r.message.metadata,e):e;null!=t&&await (0,s.k5)({value:a,schema:t}),r.message.metadata=a}}switch(e.type){case"text-start":{let t={type:"text",text:"",providerMetadata:e.providerMetadata,state:"streaming"};r.activeTextParts[e.id]=t,r.message.parts.push(t),p();break}case"text-delta":{let t=r.activeTextParts[e.id];t.text+=e.delta,t.providerMetadata=null!=(u=e.providerMetadata)?u:t.providerMetadata,p();break}case"text-end":{let t=r.activeTextParts[e.id];t.state="done",t.providerMetadata=null!=(d=e.providerMetadata)?d:t.providerMetadata,delete r.activeTextParts[e.id],p();break}case"reasoning-start":{let t={type:"reasoning",text:"",providerMetadata:e.providerMetadata,state:"streaming"};r.activeReasoningParts[e.id]=t,r.message.parts.push(t),p();break}case"reasoning-delta":{let t=r.activeReasoningParts[e.id];t.text+=e.delta,t.providerMetadata=null!=(c=e.providerMetadata)?c:t.providerMetadata,p();break}case"reasoning-end":{let t=r.activeReasoningParts[e.id];t.providerMetadata=null!=(m=e.providerMetadata)?m:t.providerMetadata,t.state="done",delete r.activeReasoningParts[e.id],p();break}case"file":r.message.parts.push({type:"file",mediaType:e.mediaType,url:e.url}),p();break;case"source-url":r.message.parts.push({type:"source-url",sourceId:e.sourceId,url:e.url,title:e.title,providerMetadata:e.providerMetadata}),p();break;case"source-document":r.message.parts.push({type:"source-document",sourceId:e.sourceId,mediaType:e.mediaType,title:e.title,filename:e.filename,providerMetadata:e.providerMetadata}),p();break;case"tool-input-start":{let t=r.message.parts.filter(k);r.partialToolCalls[e.toolCallId]={text:"",toolName:e.toolName,index:t.length,dynamic:e.dynamic},e.dynamic?g({toolCallId:e.toolCallId,toolName:e.toolName,state:"input-streaming",input:void 0}):v({toolCallId:e.toolCallId,toolName:e.toolName,state:"input-streaming",input:void 0,providerExecuted:e.providerExecuted}),p();break}case"tool-input-delta":{let t=r.partialToolCalls[e.toolCallId];t.text+=e.inputTextDelta;let{value:a}=await C(t.text);t.dynamic?g({toolCallId:e.toolCallId,toolName:t.toolName,state:"input-streaming",input:a}):v({toolCallId:e.toolCallId,toolName:t.toolName,state:"input-streaming",input:a}),p();break}case"tool-input-available":e.dynamic?g({toolCallId:e.toolCallId,toolName:e.toolName,state:"input-available",input:e.input,providerMetadata:e.providerMetadata}):v({toolCallId:e.toolCallId,toolName:e.toolName,state:"input-available",input:e.input,providerExecuted:e.providerExecuted,providerMetadata:e.providerMetadata}),p(),i&&!e.providerExecuted&&await i({toolCall:e});break;case"tool-input-error":e.dynamic?g({toolCallId:e.toolCallId,toolName:e.toolName,state:"output-error",input:e.input,errorText:e.errorText,providerMetadata:e.providerMetadata}):v({toolCallId:e.toolCallId,toolName:e.toolName,state:"output-error",input:void 0,rawInput:e.input,errorText:e.errorText,providerExecuted:e.providerExecuted,providerMetadata:e.providerMetadata}),p();break;case"tool-output-available":if(e.dynamic){let t=I(e.toolCallId);g({toolCallId:e.toolCallId,toolName:t.toolName,state:"output-available",input:t.input,output:e.output,preliminary:e.preliminary})}else{let t=h(e.toolCallId);v({toolCallId:e.toolCallId,toolName:R(t),state:"output-available",input:t.input,output:e.output,providerExecuted:e.providerExecuted,preliminary:e.preliminary})}p();break;case"tool-output-error":if(e.dynamic){let t=I(e.toolCallId);g({toolCallId:e.toolCallId,toolName:t.toolName,state:"output-error",input:t.input,errorText:e.errorText})}else{let t=h(e.toolCallId);v({toolCallId:e.toolCallId,toolName:R(t),state:"output-error",input:t.input,rawInput:t.rawInput,errorText:e.errorText})}p();break;case"start-step":r.message.parts.push({type:"step-start"});break;case"finish-step":r.activeTextParts={},r.activeReasoningParts={};break;case"start":null!=e.messageId&&(r.message.id=e.messageId),await y(e.messageMetadata),(null!=e.messageId||null!=e.messageMetadata)&&p();break;case"finish":case"message-metadata":await y(e.messageMetadata),null!=e.messageMetadata&&p();break;case"error":null==o||o(Error(e.errorText));break;default:if(e.type.startsWith("data-")){if((null==a?void 0:a[e.type])!=null&&await (0,s.k5)({value:e.data,schema:a[e.type]}),e.transient){null==l||l(e);break}let t=null!=e.id?r.message.parts.find(t=>e.type===t.type&&e.id===t.id):void 0;null!=t?t.data=e.data:r.message.parts.push(e),null==l||l(e),p()}}n.enqueue(e)})}}))}({stream:l,onToolCall:this.onToolCall,onData:this.onData,messageMetadataSchema:this.messageMetadataSchema,dataPartSchemas:this.dataPartSchemas,runUpdateMessageJob:u,onError:e=>{throw e}}),onError:e=>{throw e}}),null==(i=this.onFinish)||i.call(this,{message:n.state.message}),this.setStatus({status:"ready"})}catch(e){if("AbortError"===e.name)return this.setStatus({status:"ready"}),null;this.onError&&e instanceof Error&&this.onError(e),this.setStatus({status:"error",error:e})}finally{this.activeResponse=void 0}(null==(l=this.sendAutomaticallyWhen)?void 0:l.call(this,{messages:this.state.messages}))&&await this.makeRequest({trigger:"submit-message",messageId:null==(n=this.lastMessage)?void 0:n.id,metadata:t,headers:a,body:r})}},ea=i.Ik({type:i.eu("text"),text:i.Yj(),state:i.k5(["streaming","done"]).optional(),providerMetadata:I.optional()}),er=i.Ik({type:i.eu("reasoning"),text:i.Yj(),state:i.k5(["streaming","done"]).optional(),providerMetadata:I.optional()}),es=i.Ik({type:i.eu("source-url"),sourceId:i.Yj(),url:i.Yj(),title:i.Yj().optional(),providerMetadata:I.optional()}),eo=i.Ik({type:i.eu("source-document"),sourceId:i.Yj(),mediaType:i.Yj(),title:i.Yj(),filename:i.Yj().optional(),providerMetadata:I.optional()}),ei=i.Ik({type:i.eu("file"),mediaType:i.Yj(),filename:i.Yj().optional(),url:i.Yj(),providerMetadata:I.optional()}),el=i.Ik({type:i.eu("step-start")}),en=i.Ik({type:i.Yj().startsWith("data-"),id:i.Yj().optional(),data:i.L5()}),ep=[i.Ik({type:i.eu("dynamic-tool"),toolName:i.Yj(),toolCallId:i.Yj(),state:i.eu("input-streaming"),input:i.L5().optional(),output:i.Zm().optional(),errorText:i.Zm().optional()}),i.Ik({type:i.eu("dynamic-tool"),toolName:i.Yj(),toolCallId:i.Yj(),state:i.eu("input-available"),input:i.L5(),output:i.Zm().optional(),errorText:i.Zm().optional(),callProviderMetadata:I.optional()}),i.Ik({type:i.eu("dynamic-tool"),toolName:i.Yj(),toolCallId:i.Yj(),state:i.eu("output-available"),input:i.L5(),output:i.L5(),errorText:i.Zm().optional(),callProviderMetadata:I.optional(),preliminary:i.zM().optional()}),i.Ik({type:i.eu("dynamic-tool"),toolName:i.Yj(),toolCallId:i.Yj(),state:i.eu("output-error"),input:i.L5(),output:i.Zm().optional(),errorText:i.Yj(),callProviderMetadata:I.optional()})],eu=[i.Ik({type:i.Yj().startsWith("tool-"),toolCallId:i.Yj(),state:i.eu("input-streaming"),input:i.L5().optional(),output:i.Zm().optional(),errorText:i.Zm().optional()}),i.Ik({type:i.Yj().startsWith("tool-"),toolCallId:i.Yj(),state:i.eu("input-available"),input:i.L5(),output:i.Zm().optional(),errorText:i.Zm().optional(),callProviderMetadata:I.optional()}),i.Ik({type:i.Yj().startsWith("tool-"),toolCallId:i.Yj(),state:i.eu("output-available"),input:i.L5(),output:i.L5(),errorText:i.Zm().optional(),callProviderMetadata:I.optional(),preliminary:i.zM().optional()}),i.Ik({type:i.Yj().startsWith("tool-"),toolCallId:i.Yj(),state:i.eu("output-error"),input:i.L5(),output:i.Zm().optional(),errorText:i.Yj(),callProviderMetadata:I.optional()})];i.Ik({id:i.Yj(),role:i.k5(["system","user","assistant"]),metadata:i.L5().optional(),parts:i.YO(i.KC([ea,er,es,eo,ei,el,en,...ep,...eu]))})}}]);