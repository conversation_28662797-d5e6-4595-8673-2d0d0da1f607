"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9250],{5009:(e,t,n)=>{n.d(t,{D:()=>i});function i(e,t){let n=String(e);if("string"!=typeof t)throw TypeError("Expected character");let i=0,r=n.indexOf(t);for(;-1!==r;)i++,r=n.indexOf(t,r+t.length);return i}},39250:(e,t,n)=>{let i;n.d(t,{createHighlighter:()=>tJ});class r extends Error{constructor(e){super(e),this.name="ShikiError"}}var a=n(95704);function s(e,...t){return t.forEach(t=>{for(let n in t)e[n]=t[n]}),e}var o=/\$(\d+)|\${(\d+):\/(downcase|upcase)}/g,l=class{static hasCaptures(e){return null!==e&&(o.lastIndex=0,o.test(e))}static replaceCaptures(e,t,n){return e.replace(o,(e,i,r,a)=>{let s=n[parseInt(i||r,10)];if(!s)return e;{let e=t.substring(s.start,s.end);for(;"."===e[0];)e=e.substring(1);switch(a){case"downcase":return e.toLowerCase();case"upcase":return e.toUpperCase();default:return e}}})}};function h(e,t){if(null===e&&null===t)return 0;if(!e)return -1;if(!t)return 1;let n=e.length,i=t.length;if(n===i){for(let i=0;i<n;i++){var r,a;let n=(r=e[i],r<(a=t[i])?-1:+(r>a));if(0!==n)return n}return 0}return n-i}function c(e){return!!(/^#[0-9a-f]{6}$/i.test(e)||/^#[0-9a-f]{8}$/i.test(e)||/^#[0-9a-f]{3}$/i.test(e)||/^#[0-9a-f]{4}$/i.test(e))}function d(e){return e.replace(/[\-\\\{\}\*\+\?\|\^\$\.\,\[\]\(\)\#\s]/g,"\\$&")}var m=class{constructor(e){this.fn=e}cache=new Map;get(e){if(this.cache.has(e))return this.cache.get(e);let t=this.fn(e);return this.cache.set(e,t),t}},p=class{constructor(e,t,n){this._colorMap=e,this._defaults=t,this._root=n}static createFromRawTheme(e,t){return this.createFromParsedTheme(function(e){if(!e||!e.settings||!Array.isArray(e.settings))return[];let t=e.settings,n=[],i=0;for(let e=0,r=t.length;e<r;e++){let r,a=t[e];if(!a.settings)continue;if("string"==typeof a.scope){let e=a.scope;r=(e=(e=e.replace(/^[,]+/,"")).replace(/[,]+$/,"")).split(",")}else r=Array.isArray(a.scope)?a.scope:[""];let s=-1;if("string"==typeof a.settings.fontStyle){s=0;let e=a.settings.fontStyle.split(" ");for(let t=0,n=e.length;t<n;t++)switch(e[t]){case"italic":s|=1;break;case"bold":s|=2;break;case"underline":s|=4;break;case"strikethrough":s|=8}}let o=null;"string"==typeof a.settings.foreground&&c(a.settings.foreground)&&(o=a.settings.foreground);let l=null;"string"==typeof a.settings.background&&c(a.settings.background)&&(l=a.settings.background);for(let t=0,a=r.length;t<a;t++){let a=r[t].trim().split(" "),h=a[a.length-1],c=null;a.length>1&&(c=a.slice(0,a.length-1)).reverse(),n[i++]=new f(h,c,e,s,o,l)}}return n}(e),t)}static createFromParsedTheme(e,t){return function(e,t){e.sort((e,t)=>{var n,i;let r=(n=e.scope,n<(i=t.scope)?-1:+(n>i));return 0!==r||0!==(r=h(e.parentScopes,t.parentScopes))?r:e.index-t.index});let n=0,i="#000000",r="#ffffff";for(;e.length>=1&&""===e[0].scope;){let t=e.shift();-1!==t.fontStyle&&(n=t.fontStyle),null!==t.foreground&&(i=t.foreground),null!==t.background&&(r=t.background)}let a=new y(t),s=new g(n,a.getId(i),a.getId(r)),o=new S(new k(0,null,-1,0,0),[]);for(let t=0,n=e.length;t<n;t++){let n=e[t];o.insert(0,n.scope,n.parentScopes,n.fontStyle,a.getId(n.foreground),a.getId(n.background))}return new p(a,s,o)}(e,t)}_cachedMatchRoot=new m(e=>this._root.match(e));getColorMap(){return this._colorMap.getColorMap()}getDefaults(){return this._defaults}match(e){if(null===e)return this._defaults;let t=e.scopeName,n=this._cachedMatchRoot.get(t).find(t=>(function(e,t){if(0===t.length)return!0;for(let r=0;r<t.length;r++){var n,i;let a=t[r],s=!1;if(">"===a){if(r===t.length-1)return!1;a=t[++r],s=!0}for(;e&&(n=e.scopeName,!((i=a)===n||n.startsWith(i)&&"."===n[i.length]));){if(s)return!1;e=e.parent}if(!e)return!1;e=e.parent}return!0})(e.parent,t.parentScopes));return n?new g(n.fontStyle,n.foreground,n.background):null}},u=class e{constructor(e,t){this.parent=e,this.scopeName=t}static push(t,n){for(let i of n)t=new e(t,i);return t}static from(...t){let n=null;for(let i=0;i<t.length;i++)n=new e(n,t[i]);return n}push(t){return new e(this,t)}getSegments(){let e=this,t=[];for(;e;)t.push(e.scopeName),e=e.parent;return t.reverse(),t}toString(){return this.getSegments().join(" ")}extends(e){return this===e||null!==this.parent&&this.parent.extends(e)}getExtensionIfDefined(e){let t=[],n=this;for(;n&&n!==e;)t.push(n.scopeName),n=n.parent;return n===e?t.reverse():void 0}},g=class{constructor(e,t,n){this.fontStyle=e,this.foregroundId=t,this.backgroundId=n}},f=class{constructor(e,t,n,i,r,a){this.scope=e,this.parentScopes=t,this.index=n,this.fontStyle=i,this.foreground=r,this.background=a}},b=(e=>(e[e.NotSet=-1]="NotSet",e[e.None=0]="None",e[e.Italic=1]="Italic",e[e.Bold=2]="Bold",e[e.Underline=4]="Underline",e[e.Strikethrough=8]="Strikethrough",e))(b||{}),y=class{_isFrozen;_lastColorId;_id2color;_color2id;constructor(e){if(this._lastColorId=0,this._id2color=[],this._color2id=Object.create(null),Array.isArray(e)){this._isFrozen=!0;for(let t=0,n=e.length;t<n;t++)this._color2id[e[t]]=t,this._id2color[t]=e[t]}else this._isFrozen=!1}getId(e){if(null===e)return 0;e=e.toUpperCase();let t=this._color2id[e];if(t)return t;if(this._isFrozen)throw Error(`Missing color in color map - ${e}`);return t=++this._lastColorId,this._color2id[e]=t,this._id2color[t]=e,t}getColorMap(){return this._id2color.slice(0)}},_=Object.freeze([]),k=class e{scopeDepth;parentScopes;fontStyle;foreground;background;constructor(e,t,n,i,r){this.scopeDepth=e,this.parentScopes=t||_,this.fontStyle=n,this.foreground=i,this.background=r}clone(){return new e(this.scopeDepth,this.parentScopes,this.fontStyle,this.foreground,this.background)}static cloneArr(e){let t=[];for(let n=0,i=e.length;n<i;n++)t[n]=e[n].clone();return t}acceptOverwrite(e,t,n,i){this.scopeDepth>e?console.log("how did this happen?"):this.scopeDepth=e,-1!==t&&(this.fontStyle=t),0!==n&&(this.foreground=n),0!==i&&(this.background=i)}},S=class e{constructor(e,t=[],n={}){this._mainRule=e,this._children=n,this._rulesWithParentScopes=t}_rulesWithParentScopes;static _cmpBySpecificity(e,t){if(e.scopeDepth!==t.scopeDepth)return t.scopeDepth-e.scopeDepth;let n=0,i=0;for(;">"===e.parentScopes[n]&&n++,">"===t.parentScopes[i]&&i++,!(n>=e.parentScopes.length)&&!(i>=t.parentScopes.length);){let r=t.parentScopes[i].length-e.parentScopes[n].length;if(0!==r)return r;n++,i++}return t.parentScopes.length-e.parentScopes.length}match(t){if(""!==t){let e,n,i=t.indexOf(".");if(-1===i?(e=t,n=""):(e=t.substring(0,i),n=t.substring(i+1)),this._children.hasOwnProperty(e))return this._children[e].match(n)}let n=this._rulesWithParentScopes.concat(this._mainRule);return n.sort(e._cmpBySpecificity),n}insert(t,n,i,r,a,s){let o,l,h;if(""===n)return void this._doInsertHere(t,i,r,a,s);let c=n.indexOf(".");-1===c?(o=n,l=""):(o=n.substring(0,c),l=n.substring(c+1)),this._children.hasOwnProperty(o)?h=this._children[o]:(h=new e(this._mainRule.clone(),k.cloneArr(this._rulesWithParentScopes)),this._children[o]=h),h.insert(t+1,l,i,r,a,s)}_doInsertHere(e,t,n,i,r){if(null===t)return void this._mainRule.acceptOverwrite(e,n,i,r);for(let a=0,s=this._rulesWithParentScopes.length;a<s;a++){let s=this._rulesWithParentScopes[a];if(0===h(s.parentScopes,t))return void s.acceptOverwrite(e,n,i,r)}-1===n&&(n=this._mainRule.fontStyle),0===i&&(i=this._mainRule.foreground),0===r&&(r=this._mainRule.background),this._rulesWithParentScopes.push(new k(e,t,n,i,r))}},N=class e{static toBinaryStr(e){return e.toString(2).padStart(32,"0")}static print(t){let n=e.getLanguageId(t),i=e.getTokenType(t),r=e.getFontStyle(t);console.log({languageId:n,tokenType:i,fontStyle:r,foreground:e.getForeground(t),background:e.getBackground(t)})}static getLanguageId(e){return(255&e)>>>0}static getTokenType(e){return(768&e)>>>8}static containsBalancedBrackets(e){return(1024&e)!=0}static getFontStyle(e){return(30720&e)>>>11}static getForeground(e){return(0xff8000&e)>>>15}static getBackground(e){return(0xff000000&e)>>>24}static set(t,n,i,r,a,s,o){let l=e.getLanguageId(t),h=e.getTokenType(t),c=+!!e.containsBalancedBrackets(t),d=e.getFontStyle(t),m=e.getForeground(t),p=e.getBackground(t);return 0!==n&&(l=n),8!==i&&(h=i),null!==r&&(c=+!!r),-1!==a&&(d=a),0!==s&&(m=s),0!==o&&(p=o),(0|l|h<<8|c<<10|d<<11|m<<15|p<<24)>>>0}};function C(e,t){var n;let i,r,a=[],s=(n=e,r=(i=/([LR]:|[\w\.:][\w\.:\-]*|[\,\|\-\(\)])/g).exec(n),{next:()=>{if(!r)return null;let e=r[0];return r=i.exec(n),e}}),o=s.next();for(;null!==o;){let e=0;if(2===o.length&&":"===o.charAt(1)){switch(o.charAt(0)){case"R":e=1;break;case"L":e=-1;break;default:console.log(`Unknown priority ${o} in scope selector`)}o=s.next()}let t=h();if(a.push({matcher:t,priority:e}),","!==o)break;o=s.next()}return a;function l(){if("-"===o){o=s.next();let e=l();return t=>!!e&&!e(t)}if("("===o){o=s.next();let e=function(){let e=[],t=h();for(;t&&(e.push(t),"|"===o||","===o);){do o=s.next();while("|"===o||","===o);t=h()}return t=>e.some(e=>e(t))}();return")"===o&&(o=s.next()),e}if(w(o)){let e=[];do e.push(o),o=s.next();while(w(o));return n=>t(e,n)}return null}function h(){let e=[],t=l();for(;t;)e.push(t),t=l();return t=>e.every(e=>e(t))}}function w(e){return!!e&&!!e.match(/[\w\.:]+/)}var v=(e=>(e[e.None=0]="None",e[e.NotBeginString=1]="NotBeginString",e[e.NotEndString=2]="NotEndString",e[e.NotBeginPosition=4]="NotBeginPosition",e[e.DebugCall=8]="DebugCall",e))(v||{});function P(e){"function"==typeof e.dispose&&e.dispose()}var A=class{constructor(e){this.scopeName=e}toKey(){return this.scopeName}},L=class{constructor(e,t){this.scopeName=e,this.ruleName=t}toKey(){return`${this.scopeName}#${this.ruleName}`}},x=class{_references=[];_seenReferenceKeys=new Set;get references(){return this._references}visitedRule=new Set;add(e){let t=e.toKey();this._seenReferenceKeys.has(t)||(this._seenReferenceKeys.add(t),this._references.push(e))}},R=class{constructor(e,t){this.repo=e,this.initialScopeName=t,this.seenFullScopeRequests.add(this.initialScopeName),this.Q=[new A(this.initialScopeName)]}seenFullScopeRequests=new Set;seenPartialScopeRequests=new Set;Q;processQueue(){let e=this.Q;this.Q=[];let t=new x;for(let n of e)!function(e,t,n,i){let r=n.lookup(e.scopeName);if(!r){if(e.scopeName===t)throw Error(`No grammar provided for <${t}>`);return}let a=n.lookup(t);e instanceof A?E({baseGrammar:a,selfGrammar:r},i):T(e.ruleName,{baseGrammar:a,selfGrammar:r,repository:r.repository},i);let s=n.injections(e.scopeName);if(s)for(let e of s)i.add(new A(e))}(n,this.initialScopeName,this.repo,t);for(let e of t.references)if(e instanceof A){if(this.seenFullScopeRequests.has(e.scopeName))continue;this.seenFullScopeRequests.add(e.scopeName),this.Q.push(e)}else{if(this.seenFullScopeRequests.has(e.scopeName)||this.seenPartialScopeRequests.has(e.toKey()))continue;this.seenPartialScopeRequests.add(e.toKey()),this.Q.push(e)}}};function T(e,t,n){t.repository&&t.repository[e]&&I([t.repository[e]],t,n)}function E(e,t){e.selfGrammar.patterns&&Array.isArray(e.selfGrammar.patterns)&&I(e.selfGrammar.patterns,{...e,repository:e.selfGrammar.repository},t),e.selfGrammar.injections&&I(Object.values(e.selfGrammar.injections),{...e,repository:e.selfGrammar.repository},t)}function I(e,t,n){for(let i of e){if(n.visitedRule.has(i))continue;n.visitedRule.add(i);let e=i.repository?s({},t.repository,i.repository):t.repository;Array.isArray(i.patterns)&&I(i.patterns,{...t,repository:e},n);let r=i.include;if(!r)continue;let a=B(r);switch(a.kind){case 0:E({...t,selfGrammar:t.baseGrammar},n);break;case 1:E(t,n);break;case 2:T(a.ruleName,{...t,repository:e},n);break;case 3:case 4:let o=a.scopeName===t.selfGrammar.scopeName?t.selfGrammar:a.scopeName===t.baseGrammar.scopeName?t.baseGrammar:void 0;if(o){let i={baseGrammar:t.baseGrammar,selfGrammar:o,repository:e};4===a.kind?T(a.ruleName,i,n):E(i,n)}else 4===a.kind?n.add(new L(a.scopeName,a.ruleName)):n.add(new A(a.scopeName))}}}var G=class{kind=0},M=class{kind=1},O=class{constructor(e){this.ruleName=e}kind=2},j=class{constructor(e){this.scopeName=e}kind=3},$=class{constructor(e,t){this.scopeName=e,this.ruleName=t}kind=4};function B(e){if("$base"===e)return new G;if("$self"===e)return new M;let t=e.indexOf("#");return -1===t?new j(e):0===t?new O(e.substring(1)):new $(e.substring(0,t),e.substring(t+1))}var D=/\\(\d+)/,F=/\\(\d+)/g;Symbol("RuleId");var q=class{$location;id;_nameIsCapturing;_name;_contentNameIsCapturing;_contentName;constructor(e,t,n,i){this.$location=e,this.id=t,this._name=n||null,this._nameIsCapturing=l.hasCaptures(this._name),this._contentName=i||null,this._contentNameIsCapturing=l.hasCaptures(this._contentName)}get debugName(){let e=this.$location?`${function e(t){let n=~t.lastIndexOf("/")||~t.lastIndexOf("\\");return 0===n?t:~n==t.length-1?e(t.substring(0,t.length-1)):t.substr(~n+1)}(this.$location.filename)}:${this.$location.line}`:"unknown";return`${this.constructor.name}#${this.id} @ ${e}`}getName(e,t){return this._nameIsCapturing&&null!==this._name&&null!==e&&null!==t?l.replaceCaptures(this._name,e,t):this._name}getContentName(e,t){return this._contentNameIsCapturing&&null!==this._contentName?l.replaceCaptures(this._contentName,e,t):this._contentName}},W=class extends q{retokenizeCapturedWithRuleId;constructor(e,t,n,i,r){super(e,t,n,i),this.retokenizeCapturedWithRuleId=r}dispose(){}collectPatterns(e,t){throw Error("Not supported!")}compile(e,t){throw Error("Not supported!")}compileAG(e,t,n,i){throw Error("Not supported!")}},H=class extends q{_match;captures;_cachedCompiledPatterns;constructor(e,t,n,i,r){super(e,t,n,null),this._match=new V(i,this.id),this.captures=r,this._cachedCompiledPatterns=null}dispose(){this._cachedCompiledPatterns&&(this._cachedCompiledPatterns.dispose(),this._cachedCompiledPatterns=null)}get debugMatchRegExp(){return`${this._match.source}`}collectPatterns(e,t){t.push(this._match)}compile(e,t){return this._getCachedCompiledPatterns(e).compile(e)}compileAG(e,t,n,i){return this._getCachedCompiledPatterns(e).compileAG(e,n,i)}_getCachedCompiledPatterns(e){return this._cachedCompiledPatterns||(this._cachedCompiledPatterns=new K,this.collectPatterns(e,this._cachedCompiledPatterns)),this._cachedCompiledPatterns}},z=class extends q{hasMissingPatterns;patterns;_cachedCompiledPatterns;constructor(e,t,n,i,r){super(e,t,n,i),this.patterns=r.patterns,this.hasMissingPatterns=r.hasMissingPatterns,this._cachedCompiledPatterns=null}dispose(){this._cachedCompiledPatterns&&(this._cachedCompiledPatterns.dispose(),this._cachedCompiledPatterns=null)}collectPatterns(e,t){for(let n of this.patterns)e.getRule(n).collectPatterns(e,t)}compile(e,t){return this._getCachedCompiledPatterns(e).compile(e)}compileAG(e,t,n,i){return this._getCachedCompiledPatterns(e).compileAG(e,n,i)}_getCachedCompiledPatterns(e){return this._cachedCompiledPatterns||(this._cachedCompiledPatterns=new K,this.collectPatterns(e,this._cachedCompiledPatterns)),this._cachedCompiledPatterns}},U=class extends q{_begin;beginCaptures;_end;endHasBackReferences;endCaptures;applyEndPatternLast;hasMissingPatterns;patterns;_cachedCompiledPatterns;constructor(e,t,n,i,r,a,s,o,l,h){super(e,t,n,i),this._begin=new V(r,this.id),this.beginCaptures=a,this._end=new V(s||"￿",-1),this.endHasBackReferences=this._end.hasBackReferences,this.endCaptures=o,this.applyEndPatternLast=l||!1,this.patterns=h.patterns,this.hasMissingPatterns=h.hasMissingPatterns,this._cachedCompiledPatterns=null}dispose(){this._cachedCompiledPatterns&&(this._cachedCompiledPatterns.dispose(),this._cachedCompiledPatterns=null)}get debugBeginRegExp(){return`${this._begin.source}`}get debugEndRegExp(){return`${this._end.source}`}getEndWithResolvedBackReferences(e,t){return this._end.resolveBackReferences(e,t)}collectPatterns(e,t){t.push(this._begin)}compile(e,t){return this._getCachedCompiledPatterns(e,t).compile(e)}compileAG(e,t,n,i){return this._getCachedCompiledPatterns(e,t).compileAG(e,n,i)}_getCachedCompiledPatterns(e,t){if(!this._cachedCompiledPatterns){for(let t of(this._cachedCompiledPatterns=new K,this.patterns))e.getRule(t).collectPatterns(e,this._cachedCompiledPatterns);this.applyEndPatternLast?this._cachedCompiledPatterns.push(this._end.hasBackReferences?this._end.clone():this._end):this._cachedCompiledPatterns.unshift(this._end.hasBackReferences?this._end.clone():this._end)}return this._end.hasBackReferences&&(this.applyEndPatternLast?this._cachedCompiledPatterns.setSource(this._cachedCompiledPatterns.length()-1,t):this._cachedCompiledPatterns.setSource(0,t)),this._cachedCompiledPatterns}},J=class extends q{_begin;beginCaptures;whileCaptures;_while;whileHasBackReferences;hasMissingPatterns;patterns;_cachedCompiledPatterns;_cachedCompiledWhilePatterns;constructor(e,t,n,i,r,a,s,o,l){super(e,t,n,i),this._begin=new V(r,this.id),this.beginCaptures=a,this.whileCaptures=o,this._while=new V(s,-2),this.whileHasBackReferences=this._while.hasBackReferences,this.patterns=l.patterns,this.hasMissingPatterns=l.hasMissingPatterns,this._cachedCompiledPatterns=null,this._cachedCompiledWhilePatterns=null}dispose(){this._cachedCompiledPatterns&&(this._cachedCompiledPatterns.dispose(),this._cachedCompiledPatterns=null),this._cachedCompiledWhilePatterns&&(this._cachedCompiledWhilePatterns.dispose(),this._cachedCompiledWhilePatterns=null)}get debugBeginRegExp(){return`${this._begin.source}`}get debugWhileRegExp(){return`${this._while.source}`}getWhileWithResolvedBackReferences(e,t){return this._while.resolveBackReferences(e,t)}collectPatterns(e,t){t.push(this._begin)}compile(e,t){return this._getCachedCompiledPatterns(e).compile(e)}compileAG(e,t,n,i){return this._getCachedCompiledPatterns(e).compileAG(e,n,i)}_getCachedCompiledPatterns(e){if(!this._cachedCompiledPatterns)for(let t of(this._cachedCompiledPatterns=new K,this.patterns))e.getRule(t).collectPatterns(e,this._cachedCompiledPatterns);return this._cachedCompiledPatterns}compileWhile(e,t){return this._getCachedCompiledWhilePatterns(e,t).compile(e)}compileWhileAG(e,t,n,i){return this._getCachedCompiledWhilePatterns(e,t).compileAG(e,n,i)}_getCachedCompiledWhilePatterns(e,t){return this._cachedCompiledWhilePatterns||(this._cachedCompiledWhilePatterns=new K,this._cachedCompiledWhilePatterns.push(this._while.hasBackReferences?this._while.clone():this._while)),this._while.hasBackReferences&&this._cachedCompiledWhilePatterns.setSource(0,t||"￿"),this._cachedCompiledWhilePatterns}},Q=class e{static createCaptureRule(e,t,n,i,r){return e.registerRule(e=>new W(t,e,n,i,r))}static getCompiledRuleId(t,n,i){return t.id||n.registerRule(r=>{if(t.id=r,t.match)return new H(t.$vscodeTextmateLocation,t.id,t.name,t.match,e._compileCaptures(t.captures,n,i));if(void 0===t.begin){t.repository&&(i=s({},i,t.repository));let r=t.patterns;return void 0===r&&t.include&&(r=[{include:t.include}]),new z(t.$vscodeTextmateLocation,t.id,t.name,t.contentName,e._compilePatterns(r,n,i))}return t.while?new J(t.$vscodeTextmateLocation,t.id,t.name,t.contentName,t.begin,e._compileCaptures(t.beginCaptures||t.captures,n,i),t.while,e._compileCaptures(t.whileCaptures||t.captures,n,i),e._compilePatterns(t.patterns,n,i)):new U(t.$vscodeTextmateLocation,t.id,t.name,t.contentName,t.begin,e._compileCaptures(t.beginCaptures||t.captures,n,i),t.end,e._compileCaptures(t.endCaptures||t.captures,n,i),t.applyEndPatternLast,e._compilePatterns(t.patterns,n,i))}),t.id}static _compileCaptures(t,n,i){let r=[];if(t){let a=0;for(let e in t){if("$vscodeTextmateLocation"===e)continue;let t=parseInt(e,10);t>a&&(a=t)}for(let e=0;e<=a;e++)r[e]=null;for(let a in t){if("$vscodeTextmateLocation"===a)continue;let s=parseInt(a,10),o=0;t[a].patterns&&(o=e.getCompiledRuleId(t[a],n,i)),r[s]=e.createCaptureRule(n,t[a].$vscodeTextmateLocation,t[a].name,t[a].contentName,o)}}return r}static _compilePatterns(t,n,i){let r=[];if(t)for(let a=0,s=t.length;a<s;a++){let s=t[a],o=-1;if(s.include){let t=B(s.include);switch(t.kind){case 0:case 1:o=e.getCompiledRuleId(i[s.include],n,i);break;case 2:let r=i[t.ruleName];r&&(o=e.getCompiledRuleId(r,n,i));break;case 3:case 4:let a=t.scopeName,l=4===t.kind?t.ruleName:null,h=n.getExternalGrammar(a,i);if(h)if(l){let t=h.repository[l];t&&(o=e.getCompiledRuleId(t,n,h.repository))}else o=e.getCompiledRuleId(h.repository.$self,n,h.repository)}}else o=e.getCompiledRuleId(s,n,i);if(-1!==o){let e=n.getRule(o),t=!1;if((e instanceof z||e instanceof U||e instanceof J)&&e.hasMissingPatterns&&0===e.patterns.length&&(t=!0),t)continue;r.push(o)}}return{patterns:r,hasMissingPatterns:(t?t.length:0)!==r.length}}},V=class e{source;ruleId;hasAnchor;hasBackReferences;_anchorCache;constructor(e,t){if(e&&"string"==typeof e){let t=e.length,n=0,i=[],r=!1;for(let a=0;a<t;a++)if("\\"===e.charAt(a)&&a+1<t){let t=e.charAt(a+1);"z"===t?(i.push(e.substring(n,a)),i.push("$(?!\\n)(?<!\\n)"),n=a+2):("A"===t||"G"===t)&&(r=!0),a++}this.hasAnchor=r,0===n?this.source=e:(i.push(e.substring(n,t)),this.source=i.join(""))}else this.hasAnchor=!1,this.source=e;this.hasAnchor?this._anchorCache=this._buildAnchorCache():this._anchorCache=null,this.ruleId=t,"string"==typeof this.source?this.hasBackReferences=D.test(this.source):this.hasBackReferences=!1}clone(){return new e(this.source,this.ruleId)}setSource(e){this.source!==e&&(this.source=e,this.hasAnchor&&(this._anchorCache=this._buildAnchorCache()))}resolveBackReferences(e,t){if("string"!=typeof this.source)throw Error("This method should only be called if the source is a string");let n=t.map(t=>e.substring(t.start,t.end));return F.lastIndex=0,this.source.replace(F,(e,t)=>d(n[parseInt(t,10)]||""))}_buildAnchorCache(){let e,t,n,i;if("string"!=typeof this.source)throw Error("This method should only be called if the source is a string");let r=[],a=[],s=[],o=[];for(e=0,t=this.source.length;e<t;e++)n=this.source.charAt(e),r[e]=n,a[e]=n,s[e]=n,o[e]=n,"\\"===n&&e+1<t&&("A"===(i=this.source.charAt(e+1))?(r[e+1]="￿",a[e+1]="￿",s[e+1]="A",o[e+1]="A"):"G"===i?(r[e+1]="￿",a[e+1]="G",s[e+1]="￿",o[e+1]="G"):(r[e+1]=i,a[e+1]=i,s[e+1]=i,o[e+1]=i),e++);return{A0_G0:r.join(""),A0_G1:a.join(""),A1_G0:s.join(""),A1_G1:o.join("")}}resolveAnchors(e,t){if(!this.hasAnchor||!this._anchorCache||"string"!=typeof this.source)return this.source;if(e)if(t)return this._anchorCache.A1_G1;else return this._anchorCache.A1_G0;return t?this._anchorCache.A0_G1:this._anchorCache.A0_G0}},K=class{_items;_hasAnchors;_cached;_anchorCache;constructor(){this._items=[],this._hasAnchors=!1,this._cached=null,this._anchorCache={A0_G0:null,A0_G1:null,A1_G0:null,A1_G1:null}}dispose(){this._disposeCaches()}_disposeCaches(){this._cached&&(this._cached.dispose(),this._cached=null),this._anchorCache.A0_G0&&(this._anchorCache.A0_G0.dispose(),this._anchorCache.A0_G0=null),this._anchorCache.A0_G1&&(this._anchorCache.A0_G1.dispose(),this._anchorCache.A0_G1=null),this._anchorCache.A1_G0&&(this._anchorCache.A1_G0.dispose(),this._anchorCache.A1_G0=null),this._anchorCache.A1_G1&&(this._anchorCache.A1_G1.dispose(),this._anchorCache.A1_G1=null)}push(e){this._items.push(e),this._hasAnchors=this._hasAnchors||e.hasAnchor}unshift(e){this._items.unshift(e),this._hasAnchors=this._hasAnchors||e.hasAnchor}length(){return this._items.length}setSource(e,t){this._items[e].source!==t&&(this._disposeCaches(),this._items[e].setSource(t))}compile(e){if(!this._cached){let t=this._items.map(e=>e.source);this._cached=new X(e,t,this._items.map(e=>e.ruleId))}return this._cached}compileAG(e,t,n){if(!this._hasAnchors)return this.compile(e);if(t)if(n)return this._anchorCache.A1_G1||(this._anchorCache.A1_G1=this._resolveAnchors(e,t,n)),this._anchorCache.A1_G1;else return this._anchorCache.A1_G0||(this._anchorCache.A1_G0=this._resolveAnchors(e,t,n)),this._anchorCache.A1_G0;return n?(this._anchorCache.A0_G1||(this._anchorCache.A0_G1=this._resolveAnchors(e,t,n)),this._anchorCache.A0_G1):(this._anchorCache.A0_G0||(this._anchorCache.A0_G0=this._resolveAnchors(e,t,n)),this._anchorCache.A0_G0)}_resolveAnchors(e,t,n){return new X(e,this._items.map(e=>e.resolveAnchors(t,n)),this._items.map(e=>e.ruleId))}},X=class{constructor(e,t,n){this.regExps=t,this.rules=n,this.scanner=e.createOnigScanner(t)}scanner;dispose(){"function"==typeof this.scanner.dispose&&this.scanner.dispose()}toString(){let e=[];for(let t=0,n=this.rules.length;t<n;t++)e.push("   - "+this.rules[t]+": "+this.regExps[t]);return e.join("\n")}findNextMatchSync(e,t,n){let i=this.scanner.findNextMatchSync(e,t,n);return i?{ruleId:this.rules[i.index],captureIndices:i.captureIndices}:null}},Y=class{constructor(e,t){this.languageId=e,this.tokenType=t}},Z=class e{_defaultAttributes;_embeddedLanguagesMatcher;constructor(e,t){this._defaultAttributes=new Y(e,8),this._embeddedLanguagesMatcher=new ee(Object.entries(t||{}))}getDefaultAttributes(){return this._defaultAttributes}getBasicScopeAttributes(t){return null===t?e._NULL_SCOPE_METADATA:this._getBasicScopeAttributes.get(t)}static _NULL_SCOPE_METADATA=new Y(0,0);_getBasicScopeAttributes=new m(e=>new Y(this._scopeToLanguage(e),this._toStandardTokenType(e)));_scopeToLanguage(e){return this._embeddedLanguagesMatcher.match(e)||0}_toStandardTokenType(t){let n=t.match(e.STANDARD_TOKEN_TYPE_REGEXP);if(!n)return 8;switch(n[1]){case"comment":return 1;case"string":return 2;case"regex":return 3;case"meta.embedded":return 0}throw Error("Unexpected match for standard token type!")}static STANDARD_TOKEN_TYPE_REGEXP=/\b(comment|string|regex|meta\.embedded)\b/},ee=class{values;scopesRegExp;constructor(e){if(0===e.length)this.values=null,this.scopesRegExp=null;else{this.values=new Map(e);let t=e.map(([e,t])=>d(e));t.sort(),t.reverse(),this.scopesRegExp=RegExp(`^((${t.join(")|(")}))($|\\.)`,"")}}match(e){if(!this.scopesRegExp)return;let t=e.match(this.scopesRegExp);if(t)return this.values.get(t[1])}};void 0!==a&&a.env.VSCODE_TEXTMATE_DEBUG;var et=class{constructor(e,t){this.stack=e,this.stoppedEarly=t}};function en(e,t,n,i,r,a,s,o){let l=t.content.length,h=!1,c=-1;if(s){let s=function(e,t,n,i,r,a){let s=r.beginRuleCapturedEOL?0:-1,o=[];for(let t=r;t;t=t.pop()){let n=t.getRule(e);n instanceof J&&o.push({rule:n,stack:t})}for(let p=o.pop();p;p=o.pop()){var l,h,c,d,m;let{ruleScanner:o,findOptions:u}=(l=p.rule,h=e,c=p.stack.endRule,d=n,m=i===s,{ruleScanner:l.compileWhileAG(h,c,d,m),findOptions:0}),g=o.findNextMatchSync(t,i,u);if(g){if(-2!==g.ruleId){r=p.stack.pop();break}g.captureIndices&&g.captureIndices.length&&(a.produce(p.stack,g.captureIndices[0].start),er(e,t,n,p.stack,a,p.rule.whileCaptures,g.captureIndices),a.produce(p.stack,g.captureIndices[0].end),s=g.captureIndices[0].end,g.captureIndices[0].end>i&&(i=g.captureIndices[0].end,n=!1))}else{r=p.stack.pop();break}}return{stack:r,linePos:i,anchorPosition:s,isFirstLine:n}}(e,t,n,i,r,a);r=s.stack,i=s.linePos,n=s.isFirstLine,c=s.anchorPosition}let d=Date.now();for(;!h;){if(0!==o&&Date.now()-d>o)return new et(r,!0);!function(){let s=function(e,t,n,i,r,a){let s=function(e,t,n,i,r,a){let{ruleScanner:s,findOptions:o}=ei(r.getRule(e),e,r.endRule,n,i===a),l=s.findNextMatchSync(t,i,o);return l?{captureIndices:l.captureIndices,matchedRuleId:l.ruleId}:null}(e,t,n,i,r,a),o=e.getInjections();if(0===o.length)return s;let l=function(e,t,n,i,r,a,s){let o,l=Number.MAX_VALUE,h=null,c=0,d=a.contentNameScopesList.getScopeNames();for(let a=0,m=e.length;a<m;a++){let m=e[a];if(!m.matcher(d))continue;let{ruleScanner:p,findOptions:u}=ei(t.getRule(m.ruleId),t,null,i,r===s),g=p.findNextMatchSync(n,r,u);if(!g)continue;let f=g.captureIndices[0].start;if(!(f>=l)&&(l=f,h=g.captureIndices,o=g.ruleId,c=m.priority,l===r))break}return h?{priorityMatch:-1===c,captureIndices:h,matchedRuleId:o}:null}(o,e,t,n,i,r,a);if(!l)return s;if(!s)return l;let h=s.captureIndices[0].start,c=l.captureIndices[0].start;return c<h||l.priorityMatch&&c===h?l:s}(e,t,n,i,r,c);if(!s){a.produce(r,l),h=!0;return}let o=s.captureIndices,d=s.matchedRuleId,m=!!o&&o.length>0&&o[0].end>i;if(-1===d){let s=r.getRule(e);a.produce(r,o[0].start),r=r.withContentNameScopesList(r.nameScopesList),er(e,t,n,r,a,s.endCaptures,o),a.produce(r,o[0].end);let d=r;if(r=r.parent,c=d.getAnchorPos(),!m&&d.getEnterPos()===i){r=d,a.produce(r,l),h=!0;return}}else{let s=e.getRule(d);a.produce(r,o[0].start);let p=r,u=s.getName(t.content,o),g=r.contentNameScopesList.pushAttributed(u,e);if(r=r.push(d,i,c,o[0].end===l,null,g,g),s instanceof U){er(e,t,n,r,a,s.beginCaptures,o),a.produce(r,o[0].end),c=o[0].end;let i=s.getContentName(t.content,o),d=g.pushAttributed(i,e);if(r=r.withContentNameScopesList(d),s.endHasBackReferences&&(r=r.withEndRule(s.getEndWithResolvedBackReferences(t.content,o))),!m&&p.hasSameRuleAs(r)){r=r.pop(),a.produce(r,l),h=!0;return}}else if(s instanceof J){er(e,t,n,r,a,s.beginCaptures,o),a.produce(r,o[0].end),c=o[0].end;let i=s.getContentName(t.content,o),d=g.pushAttributed(i,e);if(r=r.withContentNameScopesList(d),s.whileHasBackReferences&&(r=r.withEndRule(s.getWhileWithResolvedBackReferences(t.content,o))),!m&&p.hasSameRuleAs(r)){r=r.pop(),a.produce(r,l),h=!0;return}}else if(er(e,t,n,r,a,s.captures,o),a.produce(r,o[0].end),r=r.pop(),!m){r=r.safePop(),a.produce(r,l),h=!0;return}}o[0].end>i&&(i=o[0].end,n=!1)}()}return new et(r,!1)}function ei(e,t,n,i,r){return{ruleScanner:e.compileAG(t,n,i,r),findOptions:0}}function er(e,t,n,i,r,a,s){if(0===a.length)return;let o=t.content,l=Math.min(a.length,s.length),h=[],c=s[0].end;for(let t=0;t<l;t++){let l=a[t];if(null===l)continue;let d=s[t];if(0===d.length)continue;if(d.start>c)break;for(;h.length>0&&h[h.length-1].endPos<=d.start;)r.produceFromScopes(h[h.length-1].scopes,h[h.length-1].endPos),h.pop();if(h.length>0?r.produceFromScopes(h[h.length-1].scopes,d.start):r.produce(i,d.start),l.retokenizeCapturedWithRuleId){let t=l.getName(o,s),a=i.contentNameScopesList.pushAttributed(t,e),h=l.getContentName(o,s),c=a.pushAttributed(h,e),m=i.push(l.retokenizeCapturedWithRuleId,d.start,-1,!1,null,a,c),p=e.createOnigString(o.substring(0,d.end));en(e,p,n&&0===d.start,d.start,m,r,!1,0),P(p);continue}let m=l.getName(o,s);if(null!==m){let t=(h.length>0?h[h.length-1].scopes:i.contentNameScopesList).pushAttributed(m,e);h.push(new ea(t,d.end))}}for(;h.length>0;)r.produceFromScopes(h[h.length-1].scopes,h[h.length-1].endPos),h.pop()}var ea=class{scopes;endPos;constructor(e,t){this.scopes=e,this.endPos=t}};function es(e,t,n,i,r){let a=C(t,eo),s=Q.getCompiledRuleId(n,i,r.repository);for(let n of a)e.push({debugSelector:t,matcher:n.matcher,ruleId:s,grammar:r,priority:n.priority})}function eo(e,t){if(t.length<e.length)return!1;let n=0;return e.every(e=>{for(let i=n;i<t.length;i++)if(function(e,t){if(!e)return!1;if(e===t)return!0;let n=t.length;return e.length>n&&e.substr(0,n)===t&&"."===e[n]}(t[i],e))return n=i+1,!0;return!1})}var el=class{constructor(e,t,n,i,r,a,s,o){if(this._rootScopeName=e,this.balancedBracketSelectors=a,this._onigLib=o,this._basicScopeAttributesProvider=new Z(n,i),this._rootId=-1,this._lastRuleId=0,this._ruleId2desc=[null],this._includedGrammars={},this._grammarRepository=s,this._grammar=eh(t,null),this._injections=null,this._tokenTypeMatchers=[],r)for(let e of Object.keys(r))for(let t of C(e,eo))this._tokenTypeMatchers.push({matcher:t.matcher,type:r[e]})}_rootId;_lastRuleId;_ruleId2desc;_includedGrammars;_grammarRepository;_grammar;_injections;_basicScopeAttributesProvider;_tokenTypeMatchers;get themeProvider(){return this._grammarRepository}dispose(){for(let e of this._ruleId2desc)e&&e.dispose()}createOnigScanner(e){return this._onigLib.createOnigScanner(e)}createOnigString(e){return this._onigLib.createOnigString(e)}getMetadataForScope(e){return this._basicScopeAttributesProvider.getBasicScopeAttributes(e)}_collectInjections(){let e=[],t=this._rootScopeName,n=({lookup:e=>e===this._rootScopeName?this._grammar:this.getExternalGrammar(e),injections:e=>this._grammarRepository.injections(e)}).lookup(t);if(n){let i=n.injections;if(i)for(let t in i)es(e,t,i[t],this,n);let r=this._grammarRepository.injections(t);r&&r.forEach(t=>{let n=this.getExternalGrammar(t);if(n){let t=n.injectionSelector;t&&es(e,t,n,this,n)}})}return e.sort((e,t)=>e.priority-t.priority),e}getInjections(){return null===this._injections&&(this._injections=this._collectInjections()),this._injections}registerRule(e){let t=++this._lastRuleId,n=e(t);return this._ruleId2desc[t]=n,n}getRule(e){return this._ruleId2desc[e]}getExternalGrammar(e,t){if(this._includedGrammars[e])return this._includedGrammars[e];if(this._grammarRepository){let n=this._grammarRepository.lookup(e);if(n)return this._includedGrammars[e]=eh(n,t&&t.$base),this._includedGrammars[e]}}tokenizeLine(e,t,n=0){let i=this._tokenize(e,t,!1,n);return{tokens:i.lineTokens.getResult(i.ruleStack,i.lineLength),ruleStack:i.ruleStack,stoppedEarly:i.stoppedEarly}}tokenizeLine2(e,t,n=0){let i=this._tokenize(e,t,!0,n);return{tokens:i.lineTokens.getBinaryResult(i.ruleStack,i.lineLength),ruleStack:i.ruleStack,stoppedEarly:i.stoppedEarly}}_tokenize(e,t,n,i){let r;if(-1===this._rootId&&(this._rootId=Q.getCompiledRuleId(this._grammar.repository.$self,this,this._grammar.repository),this.getInjections()),t&&t!==ed.NULL)r=!1,t.reset();else{let e;r=!0;let n=this._basicScopeAttributesProvider.getDefaultAttributes(),i=this.themeProvider.getDefaults(),a=N.set(0,n.languageId,n.tokenType,null,i.fontStyle,i.foregroundId,i.backgroundId),s=this.getRule(this._rootId).getName(null,null);e=s?ec.createRootAndLookUpScopeName(s,a,this):ec.createRoot("unknown",a),t=new ed(null,this._rootId,-1,-1,!1,null,e,e)}e+="\n";let a=this.createOnigString(e),s=a.content.length,o=new ep(n,e,this._tokenTypeMatchers,this.balancedBracketSelectors),l=en(this,a,r,0,t,o,!0,i);return P(a),{lineLength:s,lineTokens:o,ruleStack:l.stack,stoppedEarly:l.stoppedEarly}}};function eh(e,t){return(e=function e(t){return Array.isArray(t)?function(t){let n=[];for(let i=0,r=t.length;i<r;i++)n[i]=e(t[i]);return n}(t):t instanceof RegExp?t:"object"==typeof t?function(t){let n={};for(let i in t)n[i]=e(t[i]);return n}(t):t}(e)).repository=e.repository||{},e.repository.$self={$vscodeTextmateLocation:e.$vscodeTextmateLocation,patterns:e.patterns,name:e.scopeName},e.repository.$base=t||e.repository.$self,e}var ec=class e{constructor(e,t,n){this.parent=e,this.scopePath=t,this.tokenAttributes=n}static fromExtension(t,n){let i=t,r=t?.scopePath??null;for(let t of n)i=new e(i,r=u.push(r,t.scopeNames),t.encodedTokenAttributes);return i}static createRoot(t,n){return new e(null,new u(null,t),n)}static createRootAndLookUpScopeName(t,n,i){let r=i.getMetadataForScope(t),a=new u(null,t),s=i.themeProvider.themeMatch(a),o=e.mergeAttributes(n,r,s);return new e(null,a,o)}get scopeName(){return this.scopePath.scopeName}toString(){return this.getScopeNames().join(" ")}equals(t){return e.equals(this,t)}static equals(e,t){for(;;){if(e===t||!e&&!t)return!0;if(!e||!t||e.scopeName!==t.scopeName||e.tokenAttributes!==t.tokenAttributes)return!1;e=e.parent,t=t.parent}}static mergeAttributes(e,t,n){let i=-1,r=0,a=0;return null!==n&&(i=n.fontStyle,r=n.foregroundId,a=n.backgroundId),N.set(e,t.languageId,t.tokenType,null,i,r,a)}pushAttributed(t,n){if(null===t)return this;if(-1===t.indexOf(" "))return e._pushAttributed(this,t,n);let i=t.split(/ /g),r=this;for(let t of i)r=e._pushAttributed(r,t,n);return r}static _pushAttributed(t,n,i){let r=i.getMetadataForScope(n),a=t.scopePath.push(n),s=i.themeProvider.themeMatch(a),o=e.mergeAttributes(t.tokenAttributes,r,s);return new e(t,a,o)}getScopeNames(){return this.scopePath.getSegments()}getExtensionIfDefined(e){let t=[],n=this;for(;n&&n!==e;)t.push({encodedTokenAttributes:n.tokenAttributes,scopeNames:n.scopePath.getExtensionIfDefined(n.parent?.scopePath??null)}),n=n.parent;return n===e?t.reverse():void 0}},ed=class e{constructor(e,t,n,i,r,a,s,o){this.parent=e,this.ruleId=t,this.beginRuleCapturedEOL=r,this.endRule=a,this.nameScopesList=s,this.contentNameScopesList=o,this.depth=this.parent?this.parent.depth+1:1,this._enterPos=n,this._anchorPos=i}_stackElementBrand=void 0;static NULL=new e(null,0,0,0,!1,null,null,null);_enterPos;_anchorPos;depth;equals(t){return null!==t&&e._equals(this,t)}static _equals(e,t){return e===t||!!this._structuralEquals(e,t)&&ec.equals(e.contentNameScopesList,t.contentNameScopesList)}static _structuralEquals(e,t){for(;;){if(e===t||!e&&!t)return!0;if(!e||!t||e.depth!==t.depth||e.ruleId!==t.ruleId||e.endRule!==t.endRule)return!1;e=e.parent,t=t.parent}}clone(){return this}static _reset(e){for(;e;)e._enterPos=-1,e._anchorPos=-1,e=e.parent}reset(){e._reset(this)}pop(){return this.parent}safePop(){return this.parent?this.parent:this}push(t,n,i,r,a,s,o){return new e(this,t,n,i,r,a,s,o)}getEnterPos(){return this._enterPos}getAnchorPos(){return this._anchorPos}getRule(e){return e.getRule(this.ruleId)}toString(){let e=[];return this._writeString(e,0),"["+e.join(",")+"]"}_writeString(e,t){return this.parent&&(t=this.parent._writeString(e,t)),e[t++]=`(${this.ruleId}, ${this.nameScopesList?.toString()}, ${this.contentNameScopesList?.toString()})`,t}withContentNameScopesList(e){return this.contentNameScopesList===e?this:this.parent.push(this.ruleId,this._enterPos,this._anchorPos,this.beginRuleCapturedEOL,this.endRule,this.nameScopesList,e)}withEndRule(t){return this.endRule===t?this:new e(this.parent,this.ruleId,this._enterPos,this._anchorPos,this.beginRuleCapturedEOL,t,this.nameScopesList,this.contentNameScopesList)}hasSameRuleAs(e){let t=this;for(;t&&t._enterPos===e._enterPos;){if(t.ruleId===e.ruleId)return!0;t=t.parent}return!1}toStateStackFrame(){return{ruleId:this.ruleId,beginRuleCapturedEOL:this.beginRuleCapturedEOL,endRule:this.endRule,nameScopesList:this.nameScopesList?.getExtensionIfDefined(this.parent?.nameScopesList??null)??[],contentNameScopesList:this.contentNameScopesList?.getExtensionIfDefined(this.nameScopesList)??[]}}static pushFrame(t,n){let i=ec.fromExtension(t?.nameScopesList??null,n.nameScopesList);return new e(t,n.ruleId,n.enterPos??-1,n.anchorPos??-1,n.beginRuleCapturedEOL,n.endRule,i,ec.fromExtension(i,n.contentNameScopesList))}},em=class{balancedBracketScopes;unbalancedBracketScopes;allowAny=!1;constructor(e,t){this.balancedBracketScopes=e.flatMap(e=>"*"===e?(this.allowAny=!0,[]):C(e,eo).map(e=>e.matcher)),this.unbalancedBracketScopes=t.flatMap(e=>C(e,eo).map(e=>e.matcher))}get matchesAlways(){return this.allowAny&&0===this.unbalancedBracketScopes.length}get matchesNever(){return 0===this.balancedBracketScopes.length&&!this.allowAny}match(e){for(let t of this.unbalancedBracketScopes)if(t(e))return!1;for(let t of this.balancedBracketScopes)if(t(e))return!0;return this.allowAny}},ep=class{constructor(e,t,n,i){this.balancedBracketSelectors=i,this._emitBinaryTokens=e,this._tokenTypeOverrides=n,this._lineText=null,this._tokens=[],this._binaryTokens=[],this._lastTokenEndIndex=0}_emitBinaryTokens;_lineText;_tokens;_binaryTokens;_lastTokenEndIndex;_tokenTypeOverrides;produce(e,t){this.produceFromScopes(e.contentNameScopesList,t)}produceFromScopes(e,t){if(this._lastTokenEndIndex>=t)return;if(this._emitBinaryTokens){let n=e?.tokenAttributes??0,i=!1;if(this.balancedBracketSelectors?.matchesAlways&&(i=!0),this._tokenTypeOverrides.length>0||this.balancedBracketSelectors&&!this.balancedBracketSelectors.matchesAlways&&!this.balancedBracketSelectors.matchesNever){let t=e?.getScopeNames()??[];for(let e of this._tokenTypeOverrides)e.matcher(t)&&(n=N.set(n,0,e.type,null,-1,0,0));this.balancedBracketSelectors&&(i=this.balancedBracketSelectors.match(t))}if(i&&(n=N.set(n,0,8,i,-1,0,0)),this._binaryTokens.length>0&&this._binaryTokens[this._binaryTokens.length-1]===n){this._lastTokenEndIndex=t;return}this._binaryTokens.push(this._lastTokenEndIndex),this._binaryTokens.push(n),this._lastTokenEndIndex=t;return}let n=e?.getScopeNames()??[];this._tokens.push({startIndex:this._lastTokenEndIndex,endIndex:t,scopes:n}),this._lastTokenEndIndex=t}getResult(e,t){return this._tokens.length>0&&this._tokens[this._tokens.length-1].startIndex===t-1&&this._tokens.pop(),0===this._tokens.length&&(this._lastTokenEndIndex=-1,this.produce(e,t),this._tokens[this._tokens.length-1].startIndex=0),this._tokens}getBinaryResult(e,t){this._binaryTokens.length>0&&this._binaryTokens[this._binaryTokens.length-2]===t-1&&(this._binaryTokens.pop(),this._binaryTokens.pop()),0===this._binaryTokens.length&&(this._lastTokenEndIndex=-1,this.produce(e,t),this._binaryTokens[this._binaryTokens.length-2]=0);let n=new Uint32Array(this._binaryTokens.length);for(let e=0,t=this._binaryTokens.length;e<t;e++)n[e]=this._binaryTokens[e];return n}},eu=class{constructor(e,t){this._onigLib=t,this._theme=e}_grammars=new Map;_rawGrammars=new Map;_injectionGrammars=new Map;_theme;dispose(){for(let e of this._grammars.values())e.dispose()}setTheme(e){this._theme=e}getColorMap(){return this._theme.getColorMap()}addGrammar(e,t){this._rawGrammars.set(e.scopeName,e),t&&this._injectionGrammars.set(e.scopeName,t)}lookup(e){return this._rawGrammars.get(e)}injections(e){return this._injectionGrammars.get(e)}getDefaults(){return this._theme.getDefaults()}themeMatch(e){return this._theme.match(e)}grammarForScopeName(e,t,n,i,r){if(!this._grammars.has(e)){let a=this._rawGrammars.get(e);if(!a)return null;this._grammars.set(e,new el(e,a,t,n,i,r,this,this._onigLib))}return this._grammars.get(e)}},eg=class{_options;_syncRegistry;_ensureGrammarCache;constructor(e){this._options=e,this._syncRegistry=new eu(p.createFromRawTheme(e.theme,e.colorMap),e.onigLib),this._ensureGrammarCache=new Map}dispose(){this._syncRegistry.dispose()}setTheme(e,t){this._syncRegistry.setTheme(p.createFromRawTheme(e,t))}getColorMap(){return this._syncRegistry.getColorMap()}loadGrammarWithEmbeddedLanguages(e,t,n){return this.loadGrammarWithConfiguration(e,t,{embeddedLanguages:n})}loadGrammarWithConfiguration(e,t,n){return this._loadGrammar(e,t,n.embeddedLanguages,n.tokenTypes,new em(n.balancedBracketSelectors||[],n.unbalancedBracketSelectors||[]))}loadGrammar(e){return this._loadGrammar(e,0,null,null,null)}_loadGrammar(e,t,n,i,r){let a=new R(this._syncRegistry,e);for(;a.Q.length>0;)a.Q.map(e=>this._loadSingleGrammar(e.scopeName)),a.processQueue();return this._grammarForScopeName(e,t,n,i,r)}_loadSingleGrammar(e){this._ensureGrammarCache.has(e)||(this._doLoadSingleGrammar(e),this._ensureGrammarCache.set(e,!0))}_doLoadSingleGrammar(e){let t=this._options.loadGrammar(e);if(t){let n="function"==typeof this._options.getInjections?this._options.getInjections(e):void 0;this._syncRegistry.addGrammar(t,n)}}addGrammar(e,t=[],n=0,i=null){return this._syncRegistry.addGrammar(e,t),this._grammarForScopeName(e.scopeName,n,i)}_grammarForScopeName(e,t=0,n=null,i=null,r=null){return this._syncRegistry.grammarForScopeName(e,t,n,i,r)}},ef=ed.NULL;let eb=["area","base","basefont","bgsound","br","col","command","embed","frame","hr","image","img","input","keygen","link","meta","param","source","track","wbr"];var ey=n(26868),e_=n(75164);let ek=/["&'<>`]/g,eS=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,eN=/[\x01-\t\v\f\x0E-\x1F\x7F\x81\x8D\x8F\x90\x9D\xA0-\uFFFF]/g,eC=/[|\\{}()[\]^$+*?.]/g,ew=new WeakMap,ev=/[\dA-Fa-f]/,eP=/\d/,eA=["AElig","AMP","Aacute","Acirc","Agrave","Aring","Atilde","Auml","COPY","Ccedil","ETH","Eacute","Ecirc","Egrave","Euml","GT","Iacute","Icirc","Igrave","Iuml","LT","Ntilde","Oacute","Ocirc","Ograve","Oslash","Otilde","Ouml","QUOT","REG","THORN","Uacute","Ucirc","Ugrave","Uuml","Yacute","aacute","acirc","acute","aelig","agrave","amp","aring","atilde","auml","brvbar","ccedil","cedil","cent","copy","curren","deg","divide","eacute","ecirc","egrave","eth","euml","frac12","frac14","frac34","gt","iacute","icirc","iexcl","igrave","iquest","iuml","laquo","lt","macr","micro","middot","nbsp","not","ntilde","oacute","ocirc","ograve","ordf","ordm","oslash","otilde","ouml","para","plusmn","pound","quot","raquo","reg","sect","shy","sup1","sup2","sup3","szlig","thorn","times","uacute","ucirc","ugrave","uml","uuml","yacute","yen","yuml"],eL={nbsp:"\xa0",iexcl:"\xa1",cent:"\xa2",pound:"\xa3",curren:"\xa4",yen:"\xa5",brvbar:"\xa6",sect:"\xa7",uml:"\xa8",copy:"\xa9",ordf:"\xaa",laquo:"\xab",not:"\xac",shy:"\xad",reg:"\xae",macr:"\xaf",deg:"\xb0",plusmn:"\xb1",sup2:"\xb2",sup3:"\xb3",acute:"\xb4",micro:"\xb5",para:"\xb6",middot:"\xb7",cedil:"\xb8",sup1:"\xb9",ordm:"\xba",raquo:"\xbb",frac14:"\xbc",frac12:"\xbd",frac34:"\xbe",iquest:"\xbf",Agrave:"\xc0",Aacute:"\xc1",Acirc:"\xc2",Atilde:"\xc3",Auml:"\xc4",Aring:"\xc5",AElig:"\xc6",Ccedil:"\xc7",Egrave:"\xc8",Eacute:"\xc9",Ecirc:"\xca",Euml:"\xcb",Igrave:"\xcc",Iacute:"\xcd",Icirc:"\xce",Iuml:"\xcf",ETH:"\xd0",Ntilde:"\xd1",Ograve:"\xd2",Oacute:"\xd3",Ocirc:"\xd4",Otilde:"\xd5",Ouml:"\xd6",times:"\xd7",Oslash:"\xd8",Ugrave:"\xd9",Uacute:"\xda",Ucirc:"\xdb",Uuml:"\xdc",Yacute:"\xdd",THORN:"\xde",szlig:"\xdf",agrave:"\xe0",aacute:"\xe1",acirc:"\xe2",atilde:"\xe3",auml:"\xe4",aring:"\xe5",aelig:"\xe6",ccedil:"\xe7",egrave:"\xe8",eacute:"\xe9",ecirc:"\xea",euml:"\xeb",igrave:"\xec",iacute:"\xed",icirc:"\xee",iuml:"\xef",eth:"\xf0",ntilde:"\xf1",ograve:"\xf2",oacute:"\xf3",ocirc:"\xf4",otilde:"\xf5",ouml:"\xf6",divide:"\xf7",oslash:"\xf8",ugrave:"\xf9",uacute:"\xfa",ucirc:"\xfb",uuml:"\xfc",yacute:"\xfd",thorn:"\xfe",yuml:"\xff",fnof:"ƒ",Alpha:"Α",Beta:"Β",Gamma:"Γ",Delta:"Δ",Epsilon:"Ε",Zeta:"Ζ",Eta:"Η",Theta:"Θ",Iota:"Ι",Kappa:"Κ",Lambda:"Λ",Mu:"Μ",Nu:"Ν",Xi:"Ξ",Omicron:"Ο",Pi:"Π",Rho:"Ρ",Sigma:"Σ",Tau:"Τ",Upsilon:"Υ",Phi:"Φ",Chi:"Χ",Psi:"Ψ",Omega:"Ω",alpha:"α",beta:"β",gamma:"γ",delta:"δ",epsilon:"ε",zeta:"ζ",eta:"η",theta:"θ",iota:"ι",kappa:"κ",lambda:"λ",mu:"μ",nu:"ν",xi:"ξ",omicron:"ο",pi:"π",rho:"ρ",sigmaf:"ς",sigma:"σ",tau:"τ",upsilon:"υ",phi:"φ",chi:"χ",psi:"ψ",omega:"ω",thetasym:"ϑ",upsih:"ϒ",piv:"ϖ",bull:"•",hellip:"…",prime:"′",Prime:"″",oline:"‾",frasl:"⁄",weierp:"℘",image:"ℑ",real:"ℜ",trade:"™",alefsym:"ℵ",larr:"←",uarr:"↑",rarr:"→",darr:"↓",harr:"↔",crarr:"↵",lArr:"⇐",uArr:"⇑",rArr:"⇒",dArr:"⇓",hArr:"⇔",forall:"∀",part:"∂",exist:"∃",empty:"∅",nabla:"∇",isin:"∈",notin:"∉",ni:"∋",prod:"∏",sum:"∑",minus:"−",lowast:"∗",radic:"√",prop:"∝",infin:"∞",ang:"∠",and:"∧",or:"∨",cap:"∩",cup:"∪",int:"∫",there4:"∴",sim:"∼",cong:"≅",asymp:"≈",ne:"≠",equiv:"≡",le:"≤",ge:"≥",sub:"⊂",sup:"⊃",nsub:"⊄",sube:"⊆",supe:"⊇",oplus:"⊕",otimes:"⊗",perp:"⊥",sdot:"⋅",lceil:"⌈",rceil:"⌉",lfloor:"⌊",rfloor:"⌋",lang:"〈",rang:"〉",loz:"◊",spades:"♠",clubs:"♣",hearts:"♥",diams:"♦",quot:'"',amp:"&",lt:"<",gt:">",OElig:"Œ",oelig:"œ",Scaron:"Š",scaron:"š",Yuml:"Ÿ",circ:"ˆ",tilde:"˜",ensp:" ",emsp:" ",thinsp:" ",zwnj:"‌",zwj:"‍",lrm:"‎",rlm:"‏",ndash:"–",mdash:"—",lsquo:"‘",rsquo:"’",sbquo:"‚",ldquo:"“",rdquo:"”",bdquo:"„",dagger:"†",Dagger:"‡",permil:"‰",lsaquo:"‹",rsaquo:"›",euro:"€"},ex=["cent","copy","divide","gt","lt","not","para","times"],eR={}.hasOwnProperty,eT={};for(i in eL)eR.call(eL,i)&&(eT[eL[i]]=i);let eE=/[^\dA-Za-z]/;function eI(e,t,n){let i,r=function(e,t,n){let i="&#x"+e.toString(16).toUpperCase();return n&&t&&!ev.test(String.fromCharCode(t))?i:i+";"}(e,t,n.omitOptionalSemicolons);if((n.useNamedReferences||n.useShortestReferences)&&(i=function(e,t,n,i){let r=String.fromCharCode(e);if(eR.call(eT,r)){let e=eT[r],a="&"+e;return n&&eA.includes(e)&&!ex.includes(e)&&(!i||t&&61!==t&&eE.test(String.fromCharCode(t)))?a:a+";"}return""}(e,t,n.omitOptionalSemicolons,n.attribute)),(n.useShortestReferences||!i)&&n.useShortestReferences){let i=function(e,t,n){let i="&#"+String(e);return n&&t&&!eP.test(String.fromCharCode(t))?i:i+";"}(e,t,n.omitOptionalSemicolons);i.length<r.length&&(r=i)}return i&&(!n.useShortestReferences||i.length<r.length)?i:r}function eG(e,t){let n;var i,r=e,a=Object.assign({format:eI},t);if(r=r.replace(a.subset?(i=a.subset,(n=ew.get(i))||(n=function(e){let t=[],n=-1;for(;++n<e.length;)t.push(e[n].replace(eC,"\\$&"));return RegExp("(?:"+t.join("|")+")","g")}(i),ew.set(i,n)),n):ek,s),a.subset||a.escapeOnly)return r;return r.replace(eS,function(e,t,n){return a.format((e.charCodeAt(0)-55296)*1024+e.charCodeAt(1)-56320+65536,n.charCodeAt(t+2),a)}).replace(eN,s);function s(e,t,n){return a.format(e.charCodeAt(0),n.charCodeAt(t+1),a)}}let eM=/^>|^->|<!--|-->|--!>|<!-$/g,eO=[">"],ej=["<",">"];var e$=n(5009),eB=n(52371),eD=n(94094),eF=n(59292),eq=n(39754);let eW=eU(1),eH=eU(-1),ez=[];function eU(e){return function(t,n,i){let r=t?t.children:ez,a=(n||0)+e,s=r[a];if(!i)for(;s&&(0,eq.m)(s);)a+=e,s=r[a];return s}}let eJ={}.hasOwnProperty;function eQ(e){return function(t,n,i){return eJ.call(e,t.tagName)&&e[t.tagName](t,n,i)}}let eV=eQ({body:function(e,t,n){let i=eW(n,t);return!i||"comment"!==i.type},caption:eK,colgroup:eK,dd:function(e,t,n){let i=eW(n,t);return!i||"element"===i.type&&("dt"===i.tagName||"dd"===i.tagName)},dt:function(e,t,n){let i=eW(n,t);return!!(i&&"element"===i.type&&("dt"===i.tagName||"dd"===i.tagName))},head:eK,html:function(e,t,n){let i=eW(n,t);return!i||"comment"!==i.type},li:function(e,t,n){let i=eW(n,t);return!i||"element"===i.type&&"li"===i.tagName},optgroup:function(e,t,n){let i=eW(n,t);return!i||"element"===i.type&&"optgroup"===i.tagName},option:function(e,t,n){let i=eW(n,t);return!i||"element"===i.type&&("option"===i.tagName||"optgroup"===i.tagName)},p:function(e,t,n){let i=eW(n,t);return i?"element"===i.type&&("address"===i.tagName||"article"===i.tagName||"aside"===i.tagName||"blockquote"===i.tagName||"details"===i.tagName||"div"===i.tagName||"dl"===i.tagName||"fieldset"===i.tagName||"figcaption"===i.tagName||"figure"===i.tagName||"footer"===i.tagName||"form"===i.tagName||"h1"===i.tagName||"h2"===i.tagName||"h3"===i.tagName||"h4"===i.tagName||"h5"===i.tagName||"h6"===i.tagName||"header"===i.tagName||"hgroup"===i.tagName||"hr"===i.tagName||"main"===i.tagName||"menu"===i.tagName||"nav"===i.tagName||"ol"===i.tagName||"p"===i.tagName||"pre"===i.tagName||"section"===i.tagName||"table"===i.tagName||"ul"===i.tagName):!n||"element"!==n.type||"a"!==n.tagName&&"audio"!==n.tagName&&"del"!==n.tagName&&"ins"!==n.tagName&&"map"!==n.tagName&&"noscript"!==n.tagName&&"video"!==n.tagName},rp:eX,rt:eX,tbody:function(e,t,n){let i=eW(n,t);return!i||"element"===i.type&&("tbody"===i.tagName||"tfoot"===i.tagName)},td:eY,tfoot:function(e,t,n){return!eW(n,t)},th:eY,thead:function(e,t,n){let i=eW(n,t);return!!(i&&"element"===i.type&&("tbody"===i.tagName||"tfoot"===i.tagName))},tr:function(e,t,n){let i=eW(n,t);return!i||"element"===i.type&&"tr"===i.tagName}});function eK(e,t,n){let i=eW(n,t,!0);return!i||"comment"!==i.type&&!("text"===i.type&&(0,eq.m)(i.value.charAt(0)))}function eX(e,t,n){let i=eW(n,t);return!i||"element"===i.type&&("rp"===i.tagName||"rt"===i.tagName)}function eY(e,t,n){let i=eW(n,t);return!i||"element"===i.type&&("td"===i.tagName||"th"===i.tagName)}let eZ=eQ({body:function(e){let t=eW(e,-1,!0);return!t||"comment"!==t.type&&!("text"===t.type&&(0,eq.m)(t.value.charAt(0)))&&("element"!==t.type||"meta"!==t.tagName&&"link"!==t.tagName&&"script"!==t.tagName&&"style"!==t.tagName&&"template"!==t.tagName)},colgroup:function(e,t,n){let i=eH(n,t),r=eW(e,-1,!0);return!(n&&i&&"element"===i.type&&"colgroup"===i.tagName&&eV(i,n.children.indexOf(i),n))&&!!(r&&"element"===r.type&&"col"===r.tagName)},head:function(e){let t=new Set;for(let n of e.children)if("element"===n.type&&("base"===n.tagName||"title"===n.tagName)){if(t.has(n.tagName))return!1;t.add(n.tagName)}let n=e.children[0];return!n||"element"===n.type},html:function(e){let t=eW(e,-1);return!t||"comment"!==t.type},tbody:function(e,t,n){let i=eH(n,t),r=eW(e,-1);return!(n&&i&&"element"===i.type&&("thead"===i.tagName||"tbody"===i.tagName)&&eV(i,n.children.indexOf(i),n))&&!!(r&&"element"===r.type&&"tr"===r.tagName)}}),e0={name:[["	\n\f\r &/=>".split(""),"	\n\f\r \"&'/=>`".split("")],["\0	\n\f\r \"&'/<=>".split(""),"\0	\n\f\r \"&'/<=>`".split("")]],unquoted:[["	\n\f\r &>".split(""),"\0	\n\f\r \"&'<=>`".split("")],["\0	\n\f\r \"&'<=>`".split(""),"\0	\n\f\r \"&'<=>`".split("")]],single:[["&'".split(""),"\"&'`".split("")],["\0&'".split(""),"\0\"&'`".split("")]],double:[['"&'.split(""),"\"&'`".split("")],['\0"&'.split(""),"\0\"&'`".split("")]]},e1=["<","&"];function e7(e,t,n,i){return n&&"element"===n.type&&("script"===n.tagName||"style"===n.tagName)?e.value:eG(e.value,Object.assign({},i.settings.characterReferences,{subset:e1}))}let e2=(0,e_.A)("type",{invalid:function(e){throw Error("Expected node, not `"+e+"`")},unknown:function(e){throw Error("Cannot compile unknown node `"+e.type+"`")},handlers:{comment:function(e,t,n,i){return i.settings.bogusComments?"<?"+eG(e.value,Object.assign({},i.settings.characterReferences,{subset:eO}))+">":"\x3c!--"+e.value.replace(eM,function(e){return eG(e,Object.assign({},i.settings.characterReferences,{subset:ej}))})+"--\x3e"},doctype:function(e,t,n,i){return"<!"+(i.settings.upperDoctype?"DOCTYPE":"doctype")+(i.settings.tightDoctype?"":" ")+"html>"},element:function(e,t,n,i){let r,a=i.schema,s="svg"!==a.space&&i.settings.omitOptionalTags,o="svg"===a.space?i.settings.closeEmptyElements:i.settings.voids.includes(e.tagName.toLowerCase()),l=[];"html"===a.space&&"svg"===e.tagName&&(i.schema=ey.JW);let h=function(e,t){let n,i=[],r=-1;if(t){for(n in t)if(null!==t[n]&&void 0!==t[n]){let r=function(e,t,n){let i,r=(0,eD.I)(e.schema,t),a=e.settings.allowParseErrors&&"html"===e.schema.space?0:1,s=+!e.settings.allowDangerousCharacters,o=e.quote;if(r.overloadedBoolean&&(n===r.attribute||""===n)?n=!0:(r.boolean||r.overloadedBoolean)&&("string"!=typeof n||n===r.attribute||""===n)&&(n=!!n),null==n||!1===n||"number"==typeof n&&Number.isNaN(n))return"";let l=eG(r.attribute,Object.assign({},e.settings.characterReferences,{subset:e0.name[a][s]}));return!0===n||(n=Array.isArray(n)?(r.commaSeparated?eB.A:eF.A)(n,{padLeft:!e.settings.tightCommaSeparatedLists}):String(n),e.settings.collapseEmptyAttributes&&!n)?l:(e.settings.preferUnquoted&&(i=eG(n,Object.assign({},e.settings.characterReferences,{attribute:!0,subset:e0.unquoted[a][s]}))),i!==n&&(e.settings.quoteSmart&&(0,e$.D)(n,o)>(0,e$.D)(n,e.alternative)&&(o=e.alternative),i=o+eG(n,Object.assign({},e.settings.characterReferences,{subset:("'"===o?e0.single:e0.double)[a][s],attribute:!0}))+o),l+(i?"="+i:i))}(e,n,t[n]);r&&i.push(r)}}for(;++r<i.length;){let t=e.settings.tightAttributes?i[r].charAt(i[r].length-1):void 0;r!==i.length-1&&'"'!==t&&"'"!==t&&(i[r]+=" ")}return i.join("")}(i,e.properties),c=i.all("html"===a.space&&"template"===e.tagName?e.content:e);return i.schema=a,c&&(o=!1),!h&&s&&eZ(e,t,n)||(l.push("<",e.tagName,h?" "+h:""),o&&("svg"===a.space||i.settings.closeSelfClosing)&&(r=h.charAt(h.length-1),(!i.settings.tightSelfClosing||"/"===r||r&&'"'!==r&&"'"!==r)&&l.push(" "),l.push("/")),l.push(">")),l.push(c),o||s&&eV(e,t,n)||l.push("</"+e.tagName+">"),l.join("")},raw:function(e,t,n,i){return i.settings.allowDangerousHtml?e.value:e7(e,t,n,i)},root:function(e,t,n,i){return i.all(e)},text:e7}}),e5={},e3={},e6=[];function e8(e,t,n){return e2(e,t,n,this)}function e9(e){let t=[],n=e&&e.children||e6,i=-1;for(;++i<n.length;)t[i]=this.one(n[i],i,e);return t.join("")}function e4(e,t){let n="string"==typeof e?{}:{...e.colorReplacements},i="string"==typeof e?e:e.name;for(let[e,r]of Object.entries(t?.colorReplacements||{}))"string"==typeof r?n[e]=r:e===i&&Object.assign(n,r);return n}function te(e,t){return e&&t?.[e?.toLowerCase()]||e}async function tt(e){return Promise.resolve("function"==typeof e?e():e).then(e=>e.default||e)}function tn(e){return!e||["plaintext","txt","text","plain"].includes(e)}function ti(e){return"ansi"===e||tn(e)}function tr(e){return"none"===e}function ta(e,t){if(!t)return e;for(let n of(e.properties||={},e.properties.class||=[],"string"==typeof e.properties.class&&(e.properties.class=e.properties.class.split(/\s+/g)),Array.isArray(e.properties.class)||(e.properties.class=[]),Array.isArray(t)?t:t.split(/\s+/g)))n&&!e.properties.class.includes(n)&&e.properties.class.push(n);return e}function ts(e,t=!1){let n=e.split(/(\r?\n)/g),i=0,r=[];for(let e=0;e<n.length;e+=2){let a=t?n[e]+(n[e+1]||""):n[e];r.push([a,i]),i+=n[e].length,i+=n[e+1]?.length||0}return r}let to="light-dark()",tl=["color","background-color"];function th(e){let t={};if(e.color&&(t.color=e.color),e.bgColor&&(t["background-color"]=e.bgColor),e.fontStyle){e.fontStyle&b.Italic&&(t["font-style"]="italic"),e.fontStyle&b.Bold&&(t["font-weight"]="bold");let n=[];e.fontStyle&b.Underline&&n.push("underline"),e.fontStyle&b.Strikethrough&&n.push("line-through"),n.length&&(t["text-decoration"]=n.join(" "))}return t}function tc(e){return"string"==typeof e?e:Object.entries(e).map(([e,t])=>`${e}:${t}`).join(";")}let td=new WeakMap;function tm(e,t){td.set(e,t)}function tp(e){return td.get(e)}class tu{_stacks={};lang;get themes(){return Object.keys(this._stacks)}get theme(){return this.themes[0]}get _stack(){return this._stacks[this.theme]}static initial(e,t){return new tu(Object.fromEntries((Array.isArray(t)?t:[t]).map(e=>[e,ef])),e)}constructor(...e){if(2===e.length){let[t,n]=e;this.lang=n,this._stacks=t}else{let[t,n,i]=e;this.lang=n,this._stacks={[i]:t}}}getInternalStack(e=this.theme){return this._stacks[e]}getScopes(e=this.theme){var t=this._stacks[e];let n=[],i=new Set;return!function e(t){if(i.has(t))return;i.add(t);let r=t?.nameScopesList?.scopeName;r&&n.push(r),t.parent&&e(t.parent)}(t),n}toJSON(){return{lang:this.lang,theme:this.theme,themes:this.themes,scopes:this.getScopes()}}}let tg=[function(){let e=new WeakMap;function t(t){if(!e.has(t.meta)){let n=function(e){if("number"==typeof e){if(e<0||e>t.source.length)throw new r(`Invalid decoration offset: ${e}. Code length: ${t.source.length}`);return{...i.indexToPos(e),offset:e}}{let t=i.lines[e.line];if(void 0===t)throw new r(`Invalid decoration position ${JSON.stringify(e)}. Lines length: ${i.lines.length}`);let n=e.character;if(n<0&&(n=t.length+n),n<0||n>t.length)throw new r(`Invalid decoration position ${JSON.stringify(e)}. Line ${e.line} length: ${t.length}`);return{...e,character:n,offset:i.posToIndex(e.line,n)}}},i=function(e){let t=ts(e,!0).map(([e])=>e);return{lines:t,indexToPos:function(n){if(n===e.length)return{line:t.length-1,character:t[t.length-1].length};let i=n,r=0;for(let e of t){if(i<e.length)break;i-=e.length,r++}return{line:r,character:i}},posToIndex:function(e,n){let i=0;for(let n=0;n<e;n++)i+=t[n].length;return i+n}}}(t.source),a=(t.options.decorations||[]).map(e=>({...e,start:n(e.start),end:n(e.end)}));(function(e){for(let t=0;t<e.length;t++){let n=e[t];if(n.start.offset>n.end.offset)throw new r(`Invalid decoration range: ${JSON.stringify(n.start)} - ${JSON.stringify(n.end)}`);for(let i=t+1;i<e.length;i++){let t=e[i],a=n.start.offset<=t.start.offset&&t.start.offset<n.end.offset,s=n.start.offset<t.end.offset&&t.end.offset<=n.end.offset,o=t.start.offset<=n.start.offset&&n.start.offset<t.end.offset,l=t.start.offset<n.end.offset&&n.end.offset<=t.end.offset;if(a||s||o||l){if(a&&s||o&&l||o&&n.start.offset===n.end.offset||s&&t.start.offset===t.end.offset)continue;throw new r(`Decorations ${JSON.stringify(n.start)} and ${JSON.stringify(t.start)} intersect.`)}}}})(a),e.set(t.meta,{decorations:a,converter:i,source:t.source})}return e.get(t.meta)}return{name:"shiki:decorations",tokens(e){if(this.options.decorations?.length){var n=e,i=t(this).decorations.flatMap(e=>[e.start.offset,e.end.offset]);let r=Array.from(i instanceof Set?i:new Set(i)).sort((e,t)=>e-t);return r.length?n.map(e=>e.flatMap(e=>{let t=r.filter(t=>e.offset<t&&t<e.offset+e.content.length).map(t=>t-e.offset).sort((e,t)=>e-t);if(!t.length)return e;let n=0,i=[];for(let r of t)r>n&&i.push({...e,content:e.content.slice(n,r),offset:e.offset+n}),n=r;return n<e.content.length&&i.push({...e,content:e.content.slice(n),offset:e.offset+n}),i})):n}},code(e){if(!this.options.decorations?.length)return;let n=t(this),i=Array.from(e.children).filter(e=>"element"===e.type&&"span"===e.tagName);if(i.length!==n.converter.lines.length)throw new r(`Number of lines in code element (${i.length}) does not match the number of lines in the source (${n.converter.lines.length}). Failed to apply decorations.`);function a(e,t,n,a){let o=i[e],l="",h=-1,c=-1;if(0===t&&(h=0),0===n&&(c=0),n===1/0&&(c=o.children.length),-1===h||-1===c)for(let e=0;e<o.children.length;e++)l+=function e(t){return"text"===t.type?t.value:"element"===t.type?t.children.map(e).join(""):""}(o.children[e]),-1===h&&l.length===t&&(h=e+1),-1===c&&l.length===n&&(c=e+1);if(-1===h)throw new r(`Failed to find start index for decoration ${JSON.stringify(a.start)}`);if(-1===c)throw new r(`Failed to find end index for decoration ${JSON.stringify(a.end)}`);let d=o.children.slice(h,c);if(a.alwaysWrap||d.length!==o.children.length)if(a.alwaysWrap||1!==d.length||"element"!==d[0].type){let e={type:"element",tagName:"span",properties:{},children:d};s(e,a,"wrapper"),o.children.splice(h,d.length,e)}else s(d[0],a,"token");else s(o,a,"line")}function s(e,t,n){let i=t.properties||{},r=t.transform||(e=>e);return e.tagName=t.tagName||"span",e.properties={...e.properties,...i,class:e.properties.class},t.properties?.class&&ta(e,t.properties.class),e=r(e,n)||e}let o=[];for(let e of n.decorations.sort((e,t)=>t.start.offset-e.start.offset||e.end.offset-t.end.offset)){let{start:t,end:n}=e;if(t.line===n.line)a(t.line,t.character,n.character,e);else if(t.line<n.line){a(t.line,t.character,1/0,e);for(let r=t.line+1;r<n.line;r++)o.unshift(()=>{var t;i[t=r]=s(i[t],e,"line")});a(n.line,0,n.character,e)}}o.forEach(e=>e())}}}()];function tf(e){let t=function(e){let t=[],n=[],i=[];for(let r of e)switch(r.enforce){case"pre":t.push(r);break;case"post":n.push(r);break;default:i.push(r)}return{pre:t,post:n,normal:i}}(e.transformers||[]);return[...t.pre,...t.normal,...t.post,...tg]}var tb=["black","red","green","yellow","blue","magenta","cyan","white","brightBlack","brightRed","brightGreen","brightYellow","brightBlue","brightMagenta","brightCyan","brightWhite"],ty={1:"bold",2:"dim",3:"italic",4:"underline",7:"reverse",8:"hidden",9:"strikethrough"};function t_(e){let t=e.shift();if("2"===t){let t=e.splice(0,3).map(e=>Number.parseInt(e));if(3!==t.length||t.some(e=>Number.isNaN(e)))return;return{type:"rgb",rgb:t}}if("5"===t){let t=e.shift();if(t)return{type:"table",index:Number(t)}}}var tk={black:"#000000",red:"#bb0000",green:"#00bb00",yellow:"#bbbb00",blue:"#0000bb",magenta:"#ff00ff",cyan:"#00bbbb",white:"#eeeeee",brightBlack:"#555555",brightRed:"#ff5555",brightGreen:"#00ff00",brightYellow:"#ffff55",brightBlue:"#5555ff",brightMagenta:"#ff55ff",brightCyan:"#55ffff",brightWhite:"#ffffff"};function tS(e,t,n={}){let{lang:i="text",theme:a=e.getLoadedThemes()[0]}=n;if(tn(i)||tr(a))return ts(t).map(e=>[{content:e[0],offset:e[1]}]);let{theme:s,colorMap:o}=e.setTheme(a);if("ansi"===i)return function(e,t,n){let i,r,a,s=e4(e,n),o=ts(t),l=function(e=tk){let t;function n(e){return`#${e.map(e=>Math.max(0,Math.min(e,255)).toString(16).padStart(2,"0")).join("")}`}return{value:function(i){switch(i.type){case"named":return e[i.name];case"rgb":return n(i.rgb);case"table":var r;return r=i.index,function(){if(t)return t;t=[];for(let n=0;n<tb.length;n++)t.push(e[tb[n]]);let i=[0,95,135,175,215,255];for(let e=0;e<6;e++)for(let r=0;r<6;r++)for(let a=0;a<6;a++)t.push(n([i[e],i[r],i[a]]));let r=8;for(let e=0;e<24;e++,r+=10)t.push(n([r,r,r]));return t}()[r]}}}}(Object.fromEntries(tb.map(t=>[t,e.colors?.[`terminal.ansi${t[0].toUpperCase()}${t.substring(1)}`]]))),h=(i=null,r=null,a=new Set,{parse(e){let t=[],n=0;do{let s=function(e,t){let n=e.indexOf("\x1b",t);if(-1!==n&&"["===e[n+1]){let t=e.indexOf("m",n);if(-1!==t)return{sequence:e.substring(n+2,t).split(";"),startPosition:n,position:t+1}}return{position:e.length}}(e,n),o=s.sequence?e.substring(n,s.startPosition):e.substring(n);if(o.length>0&&t.push({value:o,foreground:i,background:r,decorations:new Set(a)}),s.sequence){let e=function(e){let t=[];for(;e.length>0;){let n=e.shift();if(!n)continue;let i=Number.parseInt(n);if(!Number.isNaN(i))if(0===i)t.push({type:"resetAll"});else if(i<=9)ty[i]&&t.push({type:"setDecoration",value:ty[i]});else if(i<=29){let e=ty[i-20];e&&(t.push({type:"resetDecoration",value:e}),"dim"===e&&t.push({type:"resetDecoration",value:"bold"}))}else if(i<=37)t.push({type:"setForegroundColor",value:{type:"named",name:tb[i-30]}});else if(38===i){let n=t_(e);n&&t.push({type:"setForegroundColor",value:n})}else if(39===i)t.push({type:"resetForegroundColor"});else if(i<=47)t.push({type:"setBackgroundColor",value:{type:"named",name:tb[i-40]}});else if(48===i){let n=t_(e);n&&t.push({type:"setBackgroundColor",value:n})}else 49===i?t.push({type:"resetBackgroundColor"}):53===i?t.push({type:"setDecoration",value:"overline"}):55===i?t.push({type:"resetDecoration",value:"overline"}):i>=90&&i<=97?t.push({type:"setForegroundColor",value:{type:"named",name:tb[i-90+8]}}):i>=100&&i<=107&&t.push({type:"setBackgroundColor",value:{type:"named",name:tb[i-100+8]}})}return t}(s.sequence);for(let t of e)"resetAll"===t.type?(i=null,r=null,a.clear()):"resetForegroundColor"===t.type?i=null:"resetBackgroundColor"===t.type?r=null:"resetDecoration"===t.type&&a.delete(t.value);for(let t of e)"setForegroundColor"===t.type?i=t.value:"setBackgroundColor"===t.type?r=t.value:"setDecoration"===t.type&&a.add(t.value)}n=s.position}while(n<e.length);return t}});return o.map(t=>h.parse(t[0]).map(n=>{let i,r;n.decorations.has("reverse")?(i=n.background?l.value(n.background):e.bg,r=n.foreground?l.value(n.foreground):e.fg):(i=n.foreground?l.value(n.foreground):e.fg,r=n.background?l.value(n.background):void 0),i=te(i,s),r=te(r,s),n.decorations.has("dim")&&(i=function(e){let t=e.match(/#([0-9a-f]{3})([0-9a-f]{3})?([0-9a-f]{2})?/);if(t)if(t[3]){let e=Math.round(Number.parseInt(t[3],16)/2).toString(16).padStart(2,"0");return`#${t[1]}${t[2]}${e}`}else if(t[2])return`#${t[1]}${t[2]}80`;else return`#${Array.from(t[1]).map(e=>`${e}${e}`).join("")}80`;let n=e.match(/var\((--[\w-]+-ansi-[\w-]+)\)/);return n?`var(${n[1]}-dim)`:e}(i));let a=b.None;return n.decorations.has("bold")&&(a|=b.Bold),n.decorations.has("italic")&&(a|=b.Italic),n.decorations.has("underline")&&(a|=b.Underline),n.decorations.has("strikethrough")&&(a|=b.Strikethrough),{content:n.value,offset:t[1],color:i,bgColor:r,fontStyle:a}}))}(s,t,n);let l=e.getLanguage(i);if(n.grammarState){if(n.grammarState.lang!==l.name)throw new r(`Grammar state language "${n.grammarState.lang}" does not match highlight language "${l.name}"`);if(!n.grammarState.themes.includes(s.name))throw new r(`Grammar state themes "${n.grammarState.themes}" do not contain highlight theme "${s.name}"`)}var h=t,c=l,d=s,m=o,p=n;let u=tN(h,c,d,m,p),g=new tu(tN(h,c,d,m,p).stateStack,c.name,d.name);return tm(u.tokens,g),u.tokens}function tN(e,t,n,i,a){let s=e4(n,a),{tokenizeMaxLineLength:o=0,tokenizeTimeLimit:l=500}=a,h=ts(e),c=a.grammarState?function(e,t){if(!(e instanceof tu))throw new r("Invalid grammar state");return e.getInternalStack(t)}(a.grammarState,n.name)??ef:null!=a.grammarContextCode?tN(a.grammarContextCode,t,n,i,{...a,grammarState:void 0,grammarContextCode:void 0}).stateStack:ef,d=[],m=[];for(let e=0,r=h.length;e<r;e++){let r,p,[u,g]=h[e];if(""===u){d=[],m.push([]);continue}if(o>0&&u.length>=o){d=[],m.push([{content:u,offset:g,color:"",fontStyle:0}]);continue}a.includeExplanation&&(r=t.tokenizeLine(u,c,l).tokens,p=0);let f=t.tokenizeLine2(u,c,l),b=f.tokens.length/2;for(let e=0;e<b;e++){let t=f.tokens[2*e],o=e+1<b?f.tokens[2*e+2]:u.length;if(t===o)continue;let l=f.tokens[2*e+1],h=te(i[N.getForeground(l)],s),c=N.getFontStyle(l),m={content:u.substring(t,o),offset:g+t,color:h,fontStyle:c};if(a.includeExplanation){let e=[];if("scopeName"!==a.includeExplanation)for(let t of n.settings){let n;switch(typeof t.scope){case"string":n=t.scope.split(/,/).map(e=>e.trim());break;case"object":n=t.scope;break;default:continue}e.push({settings:t,selectors:n.map(e=>e.split(/ /))})}m.explanation=[];let i=0;for(;t+i<o;){let t=r[p],n=u.substring(t.startIndex,t.endIndex);i+=n.length,m.explanation.push({content:n,scopes:"scopeName"===a.includeExplanation?t.scopes.map(e=>({scopeName:e})):function(e,t){let n=[];for(let i=0,r=t.length;i<r;i++){let r=t[i];n[i]={scopeName:r,themeMatches:function(e,t,n){let i=[];for(let{selectors:r,settings:a}of e)for(let e of r)if(function(e,t,n){if(!tC(e[e.length-1],t))return!1;let i=e.length-2,r=n.length-1;for(;i>=0&&r>=0;)tC(e[i],n[r])&&(i-=1),r-=1;return -1===i}(e,t,n)){i.push(a);break}return i}(e,r,t.slice(0,i))}}return n}(e,t.scopes)}),p+=1}}d.push(m)}m.push(d),d=[],c=f.ruleStack}return{tokens:m,stateStack:c}}function tC(e,t){return e===t||t.substring(0,e.length)===e&&"."===t[e.length]}function tw(e,t,n){let i=Object.entries(n.themes).filter(e=>e[1]).map(e=>({color:e[0],theme:e[1]})),r=i.map(i=>{let r=tS(e,t,{...n,theme:i.theme}),a=tp(r);return{tokens:r,state:a,theme:"string"==typeof i.theme?i.theme:i.theme.name}}),a=function(...e){let t=e.map(()=>[]),n=e.length;for(let i=0;i<e[0].length;i++){let r=e.map(e=>e[i]),a=t.map(()=>[]);t.forEach((e,t)=>e.push(a[t]));let s=r.map(()=>0),o=r.map(e=>e[0]);for(;o.every(e=>e);){let e=Math.min(...o.map(e=>e.content.length));for(let t=0;t<n;t++){let n=o[t];n.content.length===e?(a[t].push(n),s[t]+=1,o[t]=r[t][s[t]]):(a[t].push({...n,content:n.content.slice(0,e)}),o[t]={...n,content:n.content.slice(e),offset:n.offset+e})}}}return t}(...r.map(e=>e.tokens)),s=a[0].map((e,t)=>e.map((e,r)=>{let s={content:e.content,variants:{},offset:e.offset};return"includeExplanation"in n&&n.includeExplanation&&(s.explanation=e.explanation),a.forEach((e,n)=>{let{content:a,explanation:o,offset:l,...h}=e[t][r];s.variants[i[n].color]=h}),s})),o=r[0].state?new tu(Object.fromEntries(r.map(e=>[e.theme,e.state?.getInternalStack(e.theme)])),r[0].state.lang):void 0;return o&&tm(s,o),s}function tv(e,t,n){let i,a,s,o,l,h;if("themes"in n){let{defaultColor:c="light",cssVariablePrefix:d="--shiki-",colorsRendering:m="css-vars"}=n,p=Object.entries(n.themes).filter(e=>e[1]).map(e=>({color:e[0],theme:e[1]})).sort((e,t)=>e.color===c?-1:+(t.color===c));if(0===p.length)throw new r("`themes` option must not be empty");let u=tw(e,t,n);if(h=tp(u),c&&to!==c&&!p.find(e=>e.color===c))throw new r(`\`themes\` option must contain the defaultColor key \`${c}\``);let g=p.map(t=>e.getTheme(t.theme)),f=p.map(e=>e.color);s=u.map(e=>e.map(e=>(function(e,t,n,i,a="css-vars"){let s={content:e.content,explanation:e.explanation,offset:e.offset},o=t.map(t=>th(e.variants[t])),l=new Set(o.flatMap(e=>Object.keys(e))),h={},c=(e,i)=>{let r="color"===i?"":"background-color"===i?"-bg":`-${i}`;return n+t[e]+("color"===i?"":r)};return o.forEach((e,n)=>{for(let s of l){let l=e[s]||"inherit";if(0===n&&i&&tl.includes(s))if(i===to&&o.length>1){let e=t.findIndex(e=>"light"===e),i=t.findIndex(e=>"dark"===e);if(-1===e||-1===i)throw new r('When using `defaultColor: "light-dark()"`, you must provide both `light` and `dark` themes');let d=o[e][s]||"inherit",m=o[i][s]||"inherit";h[s]=`light-dark(${d}, ${m})`,"css-vars"===a&&(h[c(n,s)]=l)}else h[s]=l;else"css-vars"===a&&(h[c(n,s)]=l)}}),s.htmlStyle=h,s})(e,f,d,c,m))),h&&tm(s,h);let b=p.map(e=>e4(e.theme,n));a=tP(p,g,b,d,c,"fg",m),i=tP(p,g,b,d,c,"bg",m),o=`shiki-themes ${g.map(e=>e.name).join(" ")}`,l=c?void 0:[a,i].join(";")}else if("theme"in n){let r=e4(n.theme,n);s=tS(e,t,n);let l=e.getTheme(n.theme);i=te(l.bg,r),a=te(l.fg,r),o=l.name,h=tp(s)}else throw new r("Invalid options, either `theme` or `themes` must be provided");return{tokens:s,fg:a,bg:i,themeName:o,rootStyle:l,grammarState:h}}function tP(e,t,n,i,a,s,o){return e.map((l,h)=>{let c=te(t[h][s],n[h])||"inherit",d=`${i+l.color}${"bg"===s?"-bg":""}:${c}`;if(0===h&&a){if(a===to&&e.length>1){let i=e.findIndex(e=>"light"===e.color),a=e.findIndex(e=>"dark"===e.color);if(-1===i||-1===a)throw new r('When using `defaultColor: "light-dark()"`, you must provide both `light` and `dark` themes');let o=te(t[i][s],n[i])||"inherit",l=te(t[a][s],n[a])||"inherit";return`light-dark(${o}, ${l});${d}`}return c}return"css-vars"===o?d:null}).filter(e=>!!e).join(";")}function tA(e,t,n,i={meta:{},options:n,codeToHast:(t,n)=>tA(e,t,n),codeToTokens:(t,n)=>tv(e,t,n)}){let r=t;for(let e of tf(n))r=e.preprocess?.call(i,r,n)||r;let{tokens:a,fg:s,bg:o,themeName:l,rootStyle:h,grammarState:c}=tv(e,r,n),{mergeWhitespaces:d=!0,mergeSameStyleTokens:m=!1}=n;!0===d?a=a.map(e=>{let t=[],n="",i=0;return e.forEach((r,a)=>{let s=!(r.fontStyle&&(r.fontStyle&b.Underline||r.fontStyle&b.Strikethrough));s&&r.content.match(/^\s+$/)&&e[a+1]?(i||(i=r.offset),n+=r.content):n?(s?t.push({...r,offset:i,content:n+r.content}):t.push({content:n,offset:i},r),i=0,n=""):t.push(r)}),t}):"never"===d&&(a=a.map(e=>e.flatMap(e=>{if(e.content.match(/^\s+$/))return e;let t=e.content.match(/^(\s*)(.*?)(\s*)$/);if(!t)return e;let[,n,i,r]=t;if(!n&&!r)return e;let a=[{...e,offset:e.offset+n.length,content:i}];return n&&a.unshift({content:n,offset:e.offset}),r&&a.push({content:r,offset:e.offset+n.length+i.length}),a}))),m&&(a=a.map(e=>{let t=[];for(let n of e){if(0===t.length){t.push({...n});continue}let e=t[t.length-1],i=tc(e.htmlStyle||th(e)),r=tc(n.htmlStyle||th(n)),a=e.fontStyle&&(e.fontStyle&b.Underline||e.fontStyle&b.Strikethrough),s=n.fontStyle&&(n.fontStyle&b.Underline||n.fontStyle&b.Strikethrough);a||s||i!==r?t.push({...n}):e.content+=n.content}return t}));let p={...i,get source(){return r}};for(let e of tf(n))a=e.tokens?.call(p,a)||a;return function(e,t,n,i=tp(e)){let r=tf(t),a=[],s={type:"root",children:[]},{structure:o="classic",tabindex:l="0"}=t,h={type:"element",tagName:"pre",properties:{class:`shiki ${t.themeName||""}`,style:t.rootStyle||`background-color:${t.bg};color:${t.fg}`,...!1!==l&&null!=l?{tabindex:l.toString()}:{},...Object.fromEntries(Array.from(Object.entries(t.meta||{})).filter(([e])=>!e.startsWith("_")))},children:[]},c={type:"element",tagName:"code",properties:{},children:a},d=[],m={...n,structure:o,addClassToHast:ta,get source(){return n.source},get tokens(){return e},get options(){return t},get root(){return s},get pre(){return h},get code(){return c},get lines(){return d}};if(e.forEach((e,t)=>{t&&("inline"===o?s.children.push({type:"element",tagName:"br",properties:{},children:[]}):"classic"===o&&a.push({type:"text",value:"\n"}));let n={type:"element",tagName:"span",properties:{class:"line"},children:[]},i=0;for(let a of e){let e={type:"element",tagName:"span",properties:{...a.htmlAttrs},children:[{type:"text",value:a.content}]},l=tc(a.htmlStyle||th(a));for(let s of(l&&(e.properties.style=l),r))e=s?.span?.call(m,e,t+1,i,n,a)||e;"inline"===o?s.children.push(e):"classic"===o&&n.children.push(e),i+=a.content.length}if("classic"===o){for(let e of r)n=e?.line?.call(m,n,t+1)||n;d.push(n),a.push(n)}}),"classic"===o){for(let e of r)c=e?.code?.call(m,c)||c;for(let e of(h.children.push(c),r))h=e?.pre?.call(m,h)||h;s.children.push(h)}let p=s;for(let e of r)p=e?.root?.call(m,p)||p;return i&&tm(p,i),p}(a,{...n,fg:s,bg:o,themeName:l,rootStyle:h},p,c)}let tL=function(e,t){let n=t||e5,i=n.quote||'"';if('"'!==i&&"'"!==i)throw Error("Invalid quote `"+i+"`, expected `'` or `\"`");return({one:e8,all:e9,settings:{omitOptionalTags:n.omitOptionalTags||!1,allowParseErrors:n.allowParseErrors||!1,allowDangerousCharacters:n.allowDangerousCharacters||!1,quoteSmart:n.quoteSmart||!1,preferUnquoted:n.preferUnquoted||!1,tightAttributes:n.tightAttributes||!1,upperDoctype:n.upperDoctype||!1,tightDoctype:n.tightDoctype||!1,bogusComments:n.bogusComments||!1,tightCommaSeparatedLists:n.tightCommaSeparatedLists||!1,tightSelfClosing:n.tightSelfClosing||!1,collapseEmptyAttributes:n.collapseEmptyAttributes||!1,allowDangerousHtml:n.allowDangerousHtml||!1,voids:n.voids||eb,characterReferences:n.characterReferences||e3,closeSelfClosing:n.closeSelfClosing||!1,closeEmptyElements:n.closeEmptyElements||!1},schema:"svg"===n.space?ey.JW:ey.qy,quote:i,alternative:'"'===i?"'":'"'}).one(Array.isArray(e)?{type:"root",children:e}:e,void 0,void 0)},tx={light:"#333333",dark:"#bbbbbb"},tR={light:"#fffffe",dark:"#1e1e1e"},tT="__shiki_resolved";function tE(e){if(e?.[tT])return e;let t={...e};t.tokenColors&&!t.settings&&(t.settings=t.tokenColors,delete t.tokenColors),t.type||="dark",t.colorReplacements={...t.colorReplacements},t.settings||=[];let{bg:n,fg:i}=t;if(!n||!i){let e=t.settings?t.settings.find(e=>!e.name&&!e.scope):void 0;e?.settings?.foreground&&(i=e.settings.foreground),e?.settings?.background&&(n=e.settings.background),!i&&t?.colors?.["editor.foreground"]&&(i=t.colors["editor.foreground"]),!n&&t?.colors?.["editor.background"]&&(n=t.colors["editor.background"]),i||(i="light"===t.type?tx.light:tx.dark),n||(n="light"===t.type?tR.light:tR.dark),t.fg=i,t.bg=n}t.settings[0]&&t.settings[0].settings&&!t.settings[0].scope||t.settings.unshift({settings:{foreground:t.fg,background:t.bg}});let r=0,a=new Map;function s(e){if(a.has(e))return a.get(e);r+=1;let n=`#${r.toString(16).padStart(8,"0").toLowerCase()}`;return t.colorReplacements?.[`#${n}`]?s(e):(a.set(e,n),n)}for(let e of(t.settings=t.settings.map(e=>{let n=e.settings?.foreground&&!e.settings.foreground.startsWith("#"),i=e.settings?.background&&!e.settings.background.startsWith("#");if(!n&&!i)return e;let r={...e,settings:{...e.settings}};if(n){let n=s(e.settings.foreground);t.colorReplacements[n]=e.settings.foreground,r.settings.foreground=n}if(i){let n=s(e.settings.background);t.colorReplacements[n]=e.settings.background,r.settings.background=n}return r}),Object.keys(t.colors||{})))if(("editor.foreground"===e||"editor.background"===e||e.startsWith("terminal.ansi"))&&!t.colors[e]?.startsWith("#")){let n=s(t.colors[e]);t.colorReplacements[n]=t.colors[e],t.colors[e]=n}return Object.defineProperty(t,tT,{enumerable:!1,writable:!1,value:!0}),t}async function tI(e){return Array.from(new Set((await Promise.all(e.filter(e=>!ti(e)).map(async e=>await tt(e).then(e=>Array.isArray(e)?e:[e])))).flat()))}async function tG(e){return(await Promise.all(e.map(async e=>tr(e)?null:tE(await tt(e))))).filter(e=>!!e)}class tM extends Error{constructor(e){super(e),this.name="ShikiError"}}class tO extends eg{constructor(e,t,n,i={}){super(e),this._resolver=e,this._themes=t,this._langs=n,this._alias=i,this._themes.map(e=>this.loadTheme(e)),this.loadLanguages(this._langs)}_resolvedThemes=new Map;_resolvedGrammars=new Map;_langMap=new Map;_langGraph=new Map;_textmateThemeCache=new WeakMap;_loadedThemesCache=null;_loadedLanguagesCache=null;getTheme(e){return"string"==typeof e?this._resolvedThemes.get(e):this.loadTheme(e)}loadTheme(e){let t=tE(e);return t.name&&(this._resolvedThemes.set(t.name,t),this._loadedThemesCache=null),t}getLoadedThemes(){return this._loadedThemesCache||(this._loadedThemesCache=[...this._resolvedThemes.keys()]),this._loadedThemesCache}setTheme(e){let t=this._textmateThemeCache.get(e);t||(t=p.createFromRawTheme(e),this._textmateThemeCache.set(e,t)),this._syncRegistry.setTheme(t)}getGrammar(e){if(this._alias[e]){let t=new Set([e]);for(;this._alias[e];){if(e=this._alias[e],t.has(e))throw new tM(`Circular alias \`${Array.from(t).join(" -> ")} -> ${e}\``);t.add(e)}}return this._resolvedGrammars.get(e)}loadLanguage(e){if(this.getGrammar(e.name))return;let t=new Set([...this._langMap.values()].filter(t=>t.embeddedLangsLazy?.includes(e.name)));this._resolver.addLanguage(e);let n={balancedBracketSelectors:e.balancedBracketSelectors||["*"],unbalancedBracketSelectors:e.unbalancedBracketSelectors||[]};this._syncRegistry._rawGrammars.set(e.scopeName,e);let i=this.loadGrammarWithConfiguration(e.scopeName,1,n);if(i.name=e.name,this._resolvedGrammars.set(e.name,i),e.aliases&&e.aliases.forEach(t=>{this._alias[t]=e.name}),this._loadedLanguagesCache=null,t.size)for(let e of t)this._resolvedGrammars.delete(e.name),this._loadedLanguagesCache=null,this._syncRegistry?._injectionGrammars?.delete(e.scopeName),this._syncRegistry?._grammars?.delete(e.scopeName),this.loadLanguage(this._langMap.get(e.name))}dispose(){super.dispose(),this._resolvedThemes.clear(),this._resolvedGrammars.clear(),this._langMap.clear(),this._langGraph.clear(),this._loadedThemesCache=null}loadLanguages(e){for(let t of e)this.resolveEmbeddedLanguages(t);let t=Array.from(this._langGraph.entries()),n=t.filter(([e,t])=>!t);if(n.length){let e=t.filter(([e,t])=>t&&t.embeddedLangs?.some(e=>n.map(([e])=>e).includes(e))).filter(e=>!n.includes(e));throw new tM(`Missing languages ${n.map(([e])=>`\`${e}\``).join(", ")}, required by ${e.map(([e])=>`\`${e}\``).join(", ")}`)}for(let[e,n]of t)this._resolver.addLanguage(n);for(let[e,n]of t)this.loadLanguage(n)}getLoadedLanguages(){return this._loadedLanguagesCache||(this._loadedLanguagesCache=[...new Set([...this._resolvedGrammars.keys(),...Object.keys(this._alias)])]),this._loadedLanguagesCache}resolveEmbeddedLanguages(e){if(this._langMap.set(e.name,e),this._langGraph.set(e.name,e),e.embeddedLangs)for(let t of e.embeddedLangs)this._langGraph.set(t,this._langMap.get(t))}}class tj{_langs=new Map;_scopeToLang=new Map;_injections=new Map;_onigLib;constructor(e,t){this._onigLib={createOnigScanner:t=>e.createScanner(t),createOnigString:t=>e.createString(t)},t.forEach(e=>this.addLanguage(e))}get onigLib(){return this._onigLib}getLangRegistration(e){return this._langs.get(e)}loadGrammar(e){return this._scopeToLang.get(e)}addLanguage(e){this._langs.set(e.name,e),e.aliases&&e.aliases.forEach(t=>{this._langs.set(t,e)}),this._scopeToLang.set(e.scopeName,e),e.injectTo&&e.injectTo.forEach(t=>{this._injections.get(t)||this._injections.set(t,[]),this._injections.get(t).push(e.scopeName)})}getInjections(e){let t=e.split("."),n=[];for(let e=1;e<=t.length;e++){let i=t.slice(0,e).join(".");n=[...n,...this._injections.get(i)||[]]}return n}}let t$=0;async function tB(e){e.engine||function(e,t=3){if(!(t>3))if(1)console.trace(`[SHIKI DEPRECATE]: ${e}`);else throw Error(`[SHIKI DEPRECATE]: ${e}`)}("`engine` option is required. Use `createOnigurumaEngine` or `createJavaScriptRegexEngine` to create an engine.");let[t,n,i]=await Promise.all([tG(e.themes||[]),tI(e.langs||[]),e.engine]);return function(e){let t;t$+=1,!1!==e.warnings&&t$>=10&&t$%10==0&&console.warn(`[Shiki] ${t$} instances have been created. Shiki is supposed to be used as a singleton, consider refactoring your code to cache your highlighter instance; Or call \`highlighter.dispose()\` to release unused instances.`);let n=!1;if(!e.engine)throw new tM("`engine` option is required for synchronous mode");let i=(e.langs||[]).flat(1),r=(e.themes||[]).flat(1).map(tE),a=new tO(new tj(e.engine,i),r,i,e.langAlias);function s(e){if("none"===e)return{bg:"",fg:"",name:"none",settings:[],type:"dark"};h();let t=a.getTheme(e);if(!t)throw new tM(`Theme \`${e}\` not found, you may need to load it first`);return t}function o(...e){h(),a.loadLanguages(e.flat(1))}function l(...e){for(let t of(h(),e.flat(1)))a.loadTheme(t)}function h(){if(n)throw new tM("Shiki instance has been disposed")}function c(){n||(n=!0,a.dispose(),t$-=1)}return{setTheme:function(e){h();let n=s(e);return t!==e&&(a.setTheme(n),t=e),{theme:n,colorMap:a.getColorMap()}},getTheme:s,getLanguage:function(e){h();let t=a.getGrammar("string"==typeof e?e:e.name);if(!t)throw new tM(`Language \`${e}\` not found, you may need to load it first`);return t},getLoadedThemes:function(){return h(),a.getLoadedThemes()},getLoadedLanguages:function(){return h(),a.getLoadedLanguages()},loadLanguage:async function(...e){return o(await tI(e))},loadLanguageSync:o,loadTheme:async function(...e){return h(),l(await tG(e))},loadThemeSync:l,dispose:c,[Symbol.dispose]:c}}({...e,themes:t,langs:n,engine:i})}async function tD(e){let t=await tB(e);return{getLastGrammarState:(...e)=>(function(...e){if(2===e.length)return tp(e[1]);let[t,n,i={}]=e,{lang:a="text",theme:s=t.getLoadedThemes()[0]}=i;if(tn(a)||tr(s))throw new r("Plain language does not have grammar state");if("ansi"===a)throw new r("ANSI language does not have grammar state");let{theme:o,colorMap:l}=t.setTheme(s),h=t.getLanguage(a);return new tu(tN(n,h,o,l,i).stateStack,h.name,o.name)})(t,...e),codeToTokensBase:(e,n)=>tS(t,e,n),codeToTokensWithThemes:(e,n)=>tw(t,e,n),codeToTokens:(e,n)=>tv(t,e,n),codeToHast:(e,n)=>tA(t,e,n),codeToHtml:(e,n)=>(function(e,t,n){let i={meta:{},options:n,codeToHast:(t,n)=>tA(e,t,n),codeToTokens:(t,n)=>tv(e,t,n)},r=tL(tA(e,t,n,i));for(let e of tf(n))r=e.postprocess?.call(i,r,n)||r;return r})(t,e,n),getBundledLanguages:()=>({}),getBundledThemes:()=>({}),...t,getInternalContext:()=>t}}let tF=[{id:"abap",name:"ABAP",import:()=>n.e(5978).then(n.bind(n,65978))},{id:"actionscript-3",name:"ActionScript",import:()=>n.e(7039).then(n.bind(n,47039))},{id:"ada",name:"Ada",import:()=>n.e(8130).then(n.bind(n,48130))},{id:"angular-html",name:"Angular HTML",import:()=>Promise.all([n.e(2577),n.e(803),n.e(2179),n.e(4560)]).then(n.bind(n,24560))},{id:"angular-ts",name:"Angular TypeScript",import:()=>Promise.all([n.e(2577),n.e(1630),n.e(803),n.e(2179),n.e(4720),n.e(8110)]).then(n.bind(n,40040))},{id:"apache",name:"Apache Conf",import:()=>n.e(1994).then(n.bind(n,91994))},{id:"apex",name:"Apex",import:()=>n.e(6760).then(n.bind(n,26760))},{id:"apl",name:"APL",import:()=>Promise.all([n.e(2577),n.e(803),n.e(2179),n.e(3492),n.e(3355)]).then(n.bind(n,33355))},{id:"applescript",name:"AppleScript",import:()=>n.e(2347).then(n.bind(n,2347))},{id:"ara",name:"Ara",import:()=>n.e(7400).then(n.bind(n,7400))},{id:"asciidoc",name:"AsciiDoc",aliases:["adoc"],import:()=>n.e(9465).then(n.bind(n,79465))},{id:"asm",name:"Assembly",import:()=>n.e(7856).then(n.bind(n,65475))},{id:"astro",name:"Astro",import:()=>Promise.all([n.e(2577),n.e(535),n.e(6814),n.e(803),n.e(391)]).then(n.bind(n,60391))},{id:"awk",name:"AWK",import:()=>n.e(3865).then(n.bind(n,13865))},{id:"ballerina",name:"Ballerina",import:()=>n.e(7066).then(n.bind(n,87066))},{id:"bat",name:"Batch File",aliases:["batch"],import:()=>n.e(7579).then(n.bind(n,37579))},{id:"beancount",name:"Beancount",import:()=>n.e(6377).then(n.bind(n,26377))},{id:"berry",name:"Berry",aliases:["be"],import:()=>n.e(1246).then(n.bind(n,41246))},{id:"bibtex",name:"BibTeX",import:()=>n.e(7718).then(n.bind(n,87718))},{id:"bicep",name:"Bicep",import:()=>n.e(1475).then(n.bind(n,71475))},{id:"blade",name:"Blade",import:()=>Promise.all([n.e(2577),n.e(803),n.e(2179),n.e(3492),n.e(7550),n.e(9288)]).then(n.bind(n,39288))},{id:"bsl",name:"1C (Enterprise)",aliases:["1c"],import:()=>n.e(8541).then(n.bind(n,78541))},{id:"c",name:"C",import:()=>n.e(661).then(n.bind(n,661))},{id:"cadence",name:"Cadence",aliases:["cdc"],import:()=>n.e(5559).then(n.bind(n,85559))},{id:"cairo",name:"Cairo",import:()=>Promise.all([n.e(6870),n.e(6242)]).then(n.bind(n,6242))},{id:"clarity",name:"Clarity",import:()=>n.e(4190).then(n.bind(n,54190))},{id:"clojure",name:"Clojure",aliases:["clj"],import:()=>n.e(8154).then(n.bind(n,48154))},{id:"cmake",name:"CMake",import:()=>n.e(6475).then(n.bind(n,66475))},{id:"cobol",name:"COBOL",import:()=>Promise.all([n.e(2577),n.e(803),n.e(2179),n.e(3492),n.e(7615)]).then(n.bind(n,97615))},{id:"codeowners",name:"CODEOWNERS",import:()=>n.e(7203).then(n.bind(n,7203))},{id:"codeql",name:"CodeQL",aliases:["ql"],import:()=>n.e(8320).then(n.bind(n,18320))},{id:"coffee",name:"CoffeeScript",aliases:["coffeescript"],import:()=>Promise.all([n.e(2577),n.e(152)]).then(n.bind(n,70152))},{id:"common-lisp",name:"Common Lisp",aliases:["lisp"],import:()=>n.e(3390).then(n.bind(n,43390))},{id:"coq",name:"Coq",import:()=>n.e(4341).then(n.bind(n,34341))},{id:"cpp",name:"C++",aliases:["c++"],import:()=>Promise.all([n.e(6666),n.e(8835),n.e(7550),n.e(661),n.e(4174)]).then(n.bind(n,35365))},{id:"crystal",name:"Crystal",import:()=>Promise.all([n.e(2577),n.e(803),n.e(2179),n.e(7550),n.e(661),n.e(4313),n.e(3888)]).then(n.bind(n,43888))},{id:"csharp",name:"C#",aliases:["c#","cs"],import:()=>n.e(4533).then(n.bind(n,4533))},{id:"css",name:"CSS",import:()=>n.e(803).then(n.bind(n,40803))},{id:"csv",name:"CSV",import:()=>n.e(286).then(n.bind(n,10286))},{id:"cue",name:"CUE",import:()=>n.e(227).then(n.bind(n,80227))},{id:"cypher",name:"Cypher",aliases:["cql"],import:()=>n.e(829).then(n.bind(n,40829))},{id:"d",name:"D",import:()=>n.e(8874).then(n.bind(n,78874))},{id:"dart",name:"Dart",import:()=>n.e(6667).then(n.bind(n,26667))},{id:"dax",name:"DAX",import:()=>n.e(3053).then(n.bind(n,83053))},{id:"desktop",name:"Desktop",import:()=>n.e(8202).then(n.bind(n,78202))},{id:"diff",name:"Diff",import:()=>n.e(77).then(n.bind(n,50077))},{id:"docker",name:"Dockerfile",aliases:["dockerfile"],import:()=>n.e(2140).then(n.bind(n,2140))},{id:"dotenv",name:"dotEnv",import:()=>n.e(9742).then(n.bind(n,19742))},{id:"dream-maker",name:"Dream Maker",import:()=>n.e(576).then(n.bind(n,60576))},{id:"edge",name:"Edge",import:()=>Promise.all([n.e(2577),n.e(535),n.e(803),n.e(2179),n.e(8999)]).then(n.bind(n,38999))},{id:"elixir",name:"Elixir",import:()=>Promise.all([n.e(2577),n.e(803),n.e(2179),n.e(9645)]).then(n.bind(n,9645))},{id:"elm",name:"Elm",import:()=>Promise.all([n.e(661),n.e(5362)]).then(n.bind(n,5362))},{id:"emacs-lisp",name:"Emacs Lisp",aliases:["elisp"],import:()=>n.e(3879).then(n.bind(n,57434))},{id:"erb",name:"ERB",import:()=>Promise.all([n.e(2577),n.e(535),n.e(6814),n.e(9312),n.e(6666),n.e(8835),n.e(803),n.e(2179),n.e(3492),n.e(7550),n.e(661),n.e(4313),n.e(1213),n.e(2351),n.e(7319)]).then(n.bind(n,7319))},{id:"erlang",name:"Erlang",aliases:["erl"],import:()=>Promise.all([n.e(6983),n.e(6397)]).then(n.bind(n,16397))},{id:"fennel",name:"Fennel",import:()=>n.e(1600).then(n.bind(n,11600))},{id:"fish",name:"Fish",import:()=>n.e(9130).then(n.bind(n,89130))},{id:"fluent",name:"Fluent",aliases:["ftl"],import:()=>n.e(770).then(n.bind(n,20770))},{id:"fortran-fixed-form",name:"Fortran (Fixed Form)",aliases:["f","for","f77"],import:()=>Promise.all([n.e(3914),n.e(7094)]).then(n.bind(n,7094))},{id:"fortran-free-form",name:"Fortran (Free Form)",aliases:["f90","f95","f03","f08","f18"],import:()=>n.e(3914).then(n.bind(n,73914))},{id:"fsharp",name:"F#",aliases:["f#","fs"],import:()=>Promise.all([n.e(6983),n.e(8714)]).then(n.bind(n,78714))},{id:"gdresource",name:"GDResource",import:()=>n.e(6713).then(n.bind(n,16713))},{id:"gdscript",name:"GDScript",import:()=>n.e(8932).then(n.bind(n,98932))},{id:"gdshader",name:"GDShader",import:()=>n.e(7662).then(n.bind(n,97662))},{id:"genie",name:"Genie",import:()=>n.e(8650).then(n.bind(n,28650))},{id:"gherkin",name:"Gherkin",import:()=>n.e(8508).then(n.bind(n,88508))},{id:"git-commit",name:"Git Commit Message",import:()=>n.e(2110).then(n.bind(n,42110))},{id:"git-rebase",name:"Git Rebase Message",import:()=>Promise.all([n.e(4313),n.e(1381)]).then(n.bind(n,41381))},{id:"gleam",name:"Gleam",import:()=>n.e(6134).then(n.bind(n,16134))},{id:"glimmer-js",name:"Glimmer JS",aliases:["gjs"],import:()=>Promise.all([n.e(2577),n.e(535),n.e(803),n.e(2179),n.e(2899)]).then(n.bind(n,22899))},{id:"glimmer-ts",name:"Glimmer TS",aliases:["gts"],import:()=>Promise.all([n.e(2577),n.e(535),n.e(803),n.e(2179),n.e(7053)]).then(n.bind(n,87053))},{id:"glsl",name:"GLSL",import:()=>Promise.all([n.e(661),n.e(7336)]).then(n.bind(n,7336))},{id:"gnuplot",name:"Gnuplot",import:()=>n.e(6840).then(n.bind(n,24459))},{id:"go",name:"Go",import:()=>n.e(5106).then(n.bind(n,5106))},{id:"graphql",name:"GraphQL",aliases:["gql"],import:()=>Promise.all([n.e(2577),n.e(535),n.e(6814),n.e(9312),n.e(1213)]).then(n.bind(n,91213))},{id:"groovy",name:"Groovy",import:()=>n.e(2558).then(n.bind(n,42558))},{id:"hack",name:"Hack",import:()=>Promise.all([n.e(2577),n.e(803),n.e(2179),n.e(7550),n.e(155)]).then(n.bind(n,90155))},{id:"haml",name:"Ruby Haml",import:()=>Promise.all([n.e(2577),n.e(803),n.e(6778)]).then(n.bind(n,96778))},{id:"handlebars",name:"Handlebars",aliases:["hbs"],import:()=>Promise.all([n.e(2577),n.e(803),n.e(2179),n.e(6188)]).then(n.bind(n,46188))},{id:"haskell",name:"Haskell",aliases:["hs"],import:()=>n.e(2310).then(n.bind(n,92310))},{id:"haxe",name:"Haxe",import:()=>n.e(9544).then(n.bind(n,99544))},{id:"hcl",name:"HashiCorp HCL",import:()=>n.e(1043).then(n.bind(n,41043))},{id:"hjson",name:"Hjson",import:()=>n.e(3252).then(n.bind(n,23252))},{id:"hlsl",name:"HLSL",import:()=>n.e(6905).then(n.bind(n,26905))},{id:"html",name:"HTML",import:()=>Promise.all([n.e(2577),n.e(803),n.e(2179)]).then(n.bind(n,72179))},{id:"html-derivative",name:"HTML (Derivative)",import:()=>Promise.all([n.e(2577),n.e(803),n.e(2179),n.e(7737)]).then(n.bind(n,57737))},{id:"http",name:"HTTP",import:()=>Promise.all([n.e(2577),n.e(535),n.e(6814),n.e(9312),n.e(3492),n.e(4313),n.e(1213),n.e(2212)]).then(n.bind(n,12212))},{id:"hxml",name:"HXML",import:()=>n.e(9263).then(n.bind(n,9263))},{id:"hy",name:"Hy",import:()=>n.e(8397).then(n.bind(n,28397))},{id:"imba",name:"Imba",import:()=>n.e(9369).then(n.bind(n,39369))},{id:"ini",name:"INI",aliases:["properties"],import:()=>n.e(244).then(n.bind(n,90244))},{id:"java",name:"Java",import:()=>n.e(3492).then(n.bind(n,33492))},{id:"javascript",name:"JavaScript",aliases:["js"],import:()=>n.e(2577).then(n.bind(n,877))},{id:"jinja",name:"Jinja",import:()=>Promise.all([n.e(2577),n.e(803),n.e(2179),n.e(9642)]).then(n.bind(n,19642))},{id:"jison",name:"Jison",import:()=>Promise.all([n.e(2577),n.e(1003)]).then(n.bind(n,1003))},{id:"json",name:"JSON",import:()=>n.e(2010).then(n.bind(n,12010))},{id:"json5",name:"JSON5",import:()=>n.e(6741).then(n.bind(n,16741))},{id:"jsonc",name:"JSON with Comments",import:()=>n.e(5168).then(n.bind(n,62787))},{id:"jsonl",name:"JSON Lines",import:()=>n.e(1564).then(n.bind(n,31564))},{id:"jsonnet",name:"Jsonnet",import:()=>n.e(8913).then(n.bind(n,8913))},{id:"jssm",name:"JSSM",aliases:["fsl"],import:()=>n.e(3205).then(n.bind(n,63205))},{id:"jsx",name:"JSX",import:()=>n.e(9312).then(n.bind(n,86433))},{id:"julia",name:"Julia",aliases:["jl"],import:()=>Promise.all([n.e(2577),n.e(6666),n.e(8835),n.e(7550),n.e(661),n.e(6870),n.e(628),n.e(4180)]).then(n.bind(n,21037))},{id:"kotlin",name:"Kotlin",aliases:["kt","kts"],import:()=>n.e(9911).then(n.bind(n,59911))},{id:"kusto",name:"Kusto",aliases:["kql"],import:()=>n.e(4142).then(n.bind(n,24142))},{id:"latex",name:"LaTeX",import:()=>Promise.all([n.e(628),n.e(3554)]).then(n.bind(n,43554))},{id:"lean",name:"Lean 4",aliases:["lean4"],import:()=>n.e(220).then(n.bind(n,20220))},{id:"less",name:"Less",import:()=>n.e(2987).then(n.bind(n,52987))},{id:"liquid",name:"Liquid",import:()=>Promise.all([n.e(2577),n.e(803),n.e(2179),n.e(8828)]).then(n.bind(n,38828))},{id:"llvm",name:"LLVM IR",import:()=>n.e(7255).then(n.bind(n,37255))},{id:"log",name:"Log file",import:()=>n.e(2168).then(n.bind(n,42168))},{id:"logo",name:"Logo",import:()=>n.e(1373).then(n.bind(n,81373))},{id:"lua",name:"Lua",import:()=>Promise.all([n.e(661),n.e(4904)]).then(n.bind(n,24904))},{id:"luau",name:"Luau",import:()=>n.e(2727).then(n.bind(n,72727))},{id:"make",name:"Makefile",aliases:["makefile"],import:()=>n.e(1950).then(n.bind(n,61950))},{id:"markdown",name:"Markdown",aliases:["md"],import:()=>n.e(6983).then(n.bind(n,86983))},{id:"marko",name:"Marko",import:()=>Promise.all([n.e(535),n.e(803),n.e(4720),n.e(2987),n.e(9212)]).then(n.bind(n,59212))},{id:"matlab",name:"MATLAB",import:()=>n.e(6279).then(n.bind(n,86279))},{id:"mdc",name:"MDC",import:()=>Promise.all([n.e(2577),n.e(803),n.e(2179),n.e(6983),n.e(6612)]).then(n.bind(n,76612))},{id:"mdx",name:"MDX",import:()=>n.e(7231).then(n.bind(n,67231))},{id:"mermaid",name:"Mermaid",aliases:["mmd"],import:()=>n.e(131).then(n.bind(n,80131))},{id:"mipsasm",name:"MIPS Assembly",aliases:["mips"],import:()=>n.e(3556).then(n.bind(n,33556))},{id:"mojo",name:"Mojo",import:()=>n.e(8097).then(n.bind(n,58097))},{id:"move",name:"Move",import:()=>n.e(623).then(n.bind(n,80623))},{id:"narrat",name:"Narrat Language",aliases:["nar"],import:()=>n.e(5344).then(n.bind(n,95344))},{id:"nextflow",name:"Nextflow",aliases:["nf"],import:()=>n.e(9235).then(n.bind(n,89235))},{id:"nginx",name:"Nginx",import:()=>Promise.all([n.e(661),n.e(6254)]).then(n.bind(n,56254))},{id:"nim",name:"Nim",import:()=>Promise.all([n.e(2577),n.e(803),n.e(2179),n.e(3492),n.e(661),n.e(6983),n.e(5486)]).then(n.bind(n,45486))},{id:"nix",name:"Nix",import:()=>n.e(7283).then(n.bind(n,27283))},{id:"nushell",name:"nushell",aliases:["nu"],import:()=>n.e(9993).then(n.bind(n,9993))},{id:"objective-c",name:"Objective-C",aliases:["objc"],import:()=>n.e(4575).then(n.bind(n,84575))},{id:"objective-cpp",name:"Objective-C++",import:()=>n.e(4763).then(n.bind(n,88095))},{id:"ocaml",name:"OCaml",import:()=>n.e(2362).then(n.bind(n,82362))},{id:"pascal",name:"Pascal",import:()=>n.e(3332).then(n.bind(n,13332))},{id:"perl",name:"Perl",import:()=>Promise.all([n.e(2577),n.e(803),n.e(2179),n.e(3492),n.e(7550),n.e(2363)]).then(n.bind(n,22363))},{id:"php",name:"PHP",import:()=>Promise.all([n.e(2577),n.e(803),n.e(2179),n.e(3492),n.e(7550),n.e(1342),n.e(5361)]).then(n.bind(n,1342))},{id:"plsql",name:"PL/SQL",import:()=>n.e(4730).then(n.bind(n,54730))},{id:"po",name:"Gettext PO",aliases:["pot","potx"],import:()=>n.e(7363).then(n.bind(n,67363))},{id:"polar",name:"Polar",import:()=>n.e(2102).then(n.bind(n,92102))},{id:"postcss",name:"PostCSS",import:()=>n.e(749).then(n.bind(n,70749))},{id:"powerquery",name:"PowerQuery",import:()=>n.e(365).then(n.bind(n,40365))},{id:"powershell",name:"PowerShell",aliases:["ps","ps1"],import:()=>n.e(6363).then(n.bind(n,86363))},{id:"prisma",name:"Prisma",import:()=>n.e(7104).then(n.bind(n,27104))},{id:"prolog",name:"Prolog",import:()=>n.e(9661).then(n.bind(n,79661))},{id:"proto",name:"Protocol Buffer 3",aliases:["protobuf"],import:()=>n.e(5576).then(n.bind(n,35576))},{id:"pug",name:"Pug",aliases:["jade"],import:()=>Promise.all([n.e(2577),n.e(803),n.e(2179),n.e(1250)]).then(n.bind(n,51250))},{id:"puppet",name:"Puppet",import:()=>n.e(7182).then(n.bind(n,17182))},{id:"purescript",name:"PureScript",import:()=>n.e(2239).then(n.bind(n,22239))},{id:"python",name:"Python",aliases:["py"],import:()=>n.e(6870).then(n.bind(n,76870))},{id:"qml",name:"QML",import:()=>Promise.all([n.e(2577),n.e(1788)]).then(n.bind(n,51788))},{id:"qmldir",name:"QML Directory",import:()=>n.e(3527).then(n.bind(n,3527))},{id:"qss",name:"Qt Style Sheets",import:()=>n.e(669).then(n.bind(n,70669))},{id:"r",name:"R",import:()=>n.e(628).then(n.bind(n,90628))},{id:"racket",name:"Racket",import:()=>n.e(5178).then(n.bind(n,85178))},{id:"raku",name:"Raku",aliases:["perl6"],import:()=>n.e(6691).then(n.bind(n,66691))},{id:"razor",name:"ASP.NET Razor",import:()=>Promise.all([n.e(2577),n.e(803),n.e(2179),n.e(4533),n.e(8558)]).then(n.bind(n,98558))},{id:"reg",name:"Windows Registry Script",import:()=>n.e(6180).then(n.bind(n,46180))},{id:"regexp",name:"RegExp",aliases:["regex"],import:()=>n.e(9611).then(n.bind(n,99611))},{id:"rel",name:"Rel",import:()=>n.e(9420).then(n.bind(n,57039))},{id:"riscv",name:"RISC-V",import:()=>n.e(7783).then(n.bind(n,47783))},{id:"rst",name:"reStructuredText",import:()=>Promise.all([n.e(2577),n.e(535),n.e(6814),n.e(9312),n.e(6666),n.e(8835),n.e(803),n.e(2179),n.e(3492),n.e(7550),n.e(661),n.e(4313),n.e(1213),n.e(6870),n.e(2351),n.e(773)]).then(n.bind(n,70773))},{id:"ruby",name:"Ruby",aliases:["rb"],import:()=>Promise.all([n.e(2577),n.e(535),n.e(6814),n.e(9312),n.e(6666),n.e(8835),n.e(803),n.e(2179),n.e(3492),n.e(7550),n.e(661),n.e(4313),n.e(1213),n.e(2351)]).then(n.bind(n,45308))},{id:"rust",name:"Rust",aliases:["rs"],import:()=>n.e(8806).then(n.bind(n,78806))},{id:"sas",name:"SAS",import:()=>Promise.all([n.e(7550),n.e(8121)]).then(n.bind(n,18121))},{id:"sass",name:"Sass",import:()=>n.e(6358).then(n.bind(n,56358))},{id:"scala",name:"Scala",import:()=>n.e(6127).then(n.bind(n,98508))},{id:"scheme",name:"Scheme",import:()=>n.e(3173).then(n.bind(n,63173))},{id:"scss",name:"SCSS",import:()=>Promise.all([n.e(803),n.e(4720)]).then(n.bind(n,64720))},{id:"sdbl",name:"1C (Query)",aliases:["1c-query"],import:()=>n.e(1919).then(n.bind(n,11919))},{id:"shaderlab",name:"ShaderLab",aliases:["shader"],import:()=>n.e(5710).then(n.bind(n,55710))},{id:"shellscript",name:"Shell",aliases:["bash","sh","shell","zsh"],import:()=>n.e(4313).then(n.bind(n,24313))},{id:"shellsession",name:"Shell Session",aliases:["console"],import:()=>Promise.all([n.e(4313),n.e(5818)]).then(n.bind(n,35818))},{id:"smalltalk",name:"Smalltalk",import:()=>n.e(7213).then(n.bind(n,47213))},{id:"solidity",name:"Solidity",import:()=>n.e(6707).then(n.bind(n,86707))},{id:"soy",name:"Closure Templates",aliases:["closure-templates"],import:()=>Promise.all([n.e(2577),n.e(803),n.e(2179),n.e(685)]).then(n.bind(n,685))},{id:"sparql",name:"SPARQL",import:()=>n.e(6147).then(n.bind(n,36147))},{id:"splunk",name:"Splunk Query Language",aliases:["spl"],import:()=>n.e(9269).then(n.bind(n,19269))},{id:"sql",name:"SQL",import:()=>n.e(7550).then(n.bind(n,37550))},{id:"ssh-config",name:"SSH Config",import:()=>n.e(2309).then(n.bind(n,92309))},{id:"stata",name:"Stata",import:()=>Promise.all([n.e(7550),n.e(8933)]).then(n.bind(n,28933))},{id:"stylus",name:"Stylus",aliases:["styl"],import:()=>n.e(5998).then(n.bind(n,75998))},{id:"svelte",name:"Svelte",import:()=>Promise.all([n.e(2577),n.e(535),n.e(803),n.e(5733)]).then(n.bind(n,45733))},{id:"swift",name:"Swift",import:()=>n.e(4123).then(n.bind(n,4123))},{id:"system-verilog",name:"SystemVerilog",import:()=>n.e(4966).then(n.bind(n,4966))},{id:"systemd",name:"Systemd Units",import:()=>n.e(1675).then(n.bind(n,81675))},{id:"talonscript",name:"TalonScript",aliases:["talon"],import:()=>n.e(2851).then(n.bind(n,12851))},{id:"tasl",name:"Tasl",import:()=>n.e(6256).then(n.bind(n,16256))},{id:"tcl",name:"Tcl",import:()=>n.e(3151).then(n.bind(n,53151))},{id:"templ",name:"Templ",import:()=>Promise.all([n.e(2577),n.e(803),n.e(5106),n.e(9528)]).then(n.bind(n,89528))},{id:"terraform",name:"Terraform",aliases:["tf","tfvars"],import:()=>n.e(1478).then(n.bind(n,61478))},{id:"tex",name:"TeX",import:()=>Promise.all([n.e(628),n.e(5449)]).then(n.bind(n,85449))},{id:"toml",name:"TOML",import:()=>n.e(2772).then(n.bind(n,62772))},{id:"ts-tags",name:"TypeScript with Tags",aliases:["lit"],import:()=>Promise.all([n.e(2577),n.e(535),n.e(803),n.e(2179),n.e(3492),n.e(7550),n.e(661),n.e(8129)]).then(n.bind(n,58129))},{id:"tsv",name:"TSV",import:()=>n.e(4053).then(n.bind(n,84053))},{id:"tsx",name:"TSX",import:()=>n.e(6814).then(n.bind(n,94651))},{id:"turtle",name:"Turtle",import:()=>n.e(3486).then(n.bind(n,93486))},{id:"twig",name:"Twig",import:()=>Promise.all([n.e(2577),n.e(535),n.e(6814),n.e(9312),n.e(6666),n.e(8835),n.e(803),n.e(2179),n.e(3492),n.e(7550),n.e(661),n.e(4313),n.e(1213),n.e(6870),n.e(4720),n.e(2351),n.e(1342),n.e(4827)]).then(n.bind(n,64827))},{id:"typescript",name:"TypeScript",aliases:["ts"],import:()=>n.e(535).then(n.bind(n,63941))},{id:"typespec",name:"TypeSpec",aliases:["tsp"],import:()=>n.e(321).then(n.bind(n,10321))},{id:"typst",name:"Typst",aliases:["typ"],import:()=>n.e(3068).then(n.bind(n,83068))},{id:"v",name:"V",import:()=>n.e(64).then(n.bind(n,10064))},{id:"vala",name:"Vala",import:()=>n.e(9126).then(n.bind(n,19126))},{id:"vb",name:"Visual Basic",aliases:["cmd"],import:()=>n.e(8584).then(n.bind(n,38584))},{id:"verilog",name:"Verilog",import:()=>n.e(6572).then(n.bind(n,36572))},{id:"vhdl",name:"VHDL",import:()=>n.e(1730).then(n.bind(n,81730))},{id:"viml",name:"Vim Script",aliases:["vim","vimscript"],import:()=>n.e(9416).then(n.bind(n,89416))},{id:"vue",name:"Vue",import:()=>Promise.all([n.e(2577),n.e(535),n.e(803),n.e(2179),n.e(5095)]).then(n.bind(n,45095))},{id:"vue-html",name:"Vue HTML",import:()=>Promise.all([n.e(2577),n.e(535),n.e(803),n.e(2179),n.e(5095),n.e(8758)]).then(n.bind(n,78758))},{id:"vue-vine",name:"Vue Vine",import:()=>Promise.all([n.e(2577),n.e(535),n.e(1290),n.e(803),n.e(2179),n.e(4720),n.e(2987),n.e(5095),n.e(3794)]).then(n.bind(n,7693))},{id:"vyper",name:"Vyper",aliases:["vy"],import:()=>n.e(6310).then(n.bind(n,26310))},{id:"wasm",name:"WebAssembly",import:()=>n.e(5052).then(n.bind(n,65052))},{id:"wenyan",name:"Wenyan",aliases:["文言"],import:()=>n.e(9770).then(n.bind(n,59770))},{id:"wgsl",name:"WGSL",import:()=>n.e(7919).then(n.bind(n,67919))},{id:"wikitext",name:"Wikitext",aliases:["mediawiki","wiki"],import:()=>n.e(5781).then(n.bind(n,5781))},{id:"wit",name:"WebAssembly Interface Types",import:()=>n.e(9730).then(n.bind(n,49730))},{id:"wolfram",name:"Wolfram",aliases:["wl"],import:()=>n.e(7442).then(n.bind(n,21030))},{id:"xml",name:"XML",import:()=>Promise.all([n.e(3492),n.e(7742)]).then(n.bind(n,55361))},{id:"xsl",name:"XSL",import:()=>Promise.all([n.e(3492),n.e(4131)]).then(n.bind(n,14131))},{id:"yaml",name:"YAML",aliases:["yml"],import:()=>n.e(681).then(n.bind(n,20681))},{id:"zenscript",name:"ZenScript",import:()=>n.e(6400).then(n.bind(n,96400))},{id:"zig",name:"Zig",import:()=>n.e(8360).then(n.bind(n,78360))}],tq=Object.fromEntries(tF.map(e=>[e.id,e.import])),tW=Object.fromEntries(tF.flatMap(e=>e.aliases?.map(t=>[t,e.import])||[])),tH={...tq,...tW},tz=Object.fromEntries([{id:"andromeeda",displayName:"Andromeeda",type:"dark",import:()=>n.e(1969).then(n.bind(n,71969))},{id:"aurora-x",displayName:"Aurora X",type:"dark",import:()=>n.e(7886).then(n.bind(n,27886))},{id:"ayu-dark",displayName:"Ayu Dark",type:"dark",import:()=>n.e(4117).then(n.bind(n,94117))},{id:"catppuccin-frappe",displayName:"Catppuccin Frapp\xe9",type:"dark",import:()=>n.e(4556).then(n.bind(n,34556))},{id:"catppuccin-latte",displayName:"Catppuccin Latte",type:"light",import:()=>n.e(1274).then(n.bind(n,61274))},{id:"catppuccin-macchiato",displayName:"Catppuccin Macchiato",type:"dark",import:()=>n.e(9461).then(n.bind(n,39461))},{id:"catppuccin-mocha",displayName:"Catppuccin Mocha",type:"dark",import:()=>n.e(4374).then(n.bind(n,84374))},{id:"dark-plus",displayName:"Dark Plus",type:"dark",import:()=>n.e(5432).then(n.bind(n,55432))},{id:"dracula",displayName:"Dracula Theme",type:"dark",import:()=>n.e(5555).then(n.bind(n,45555))},{id:"dracula-soft",displayName:"Dracula Theme Soft",type:"dark",import:()=>n.e(8418).then(n.bind(n,58418))},{id:"everforest-dark",displayName:"Everforest Dark",type:"dark",import:()=>n.e(5409).then(n.bind(n,55409))},{id:"everforest-light",displayName:"Everforest Light",type:"light",import:()=>n.e(5017).then(n.bind(n,17398))},{id:"github-dark",displayName:"GitHub Dark",type:"dark",import:()=>n.e(971).then(n.bind(n,40971))},{id:"github-dark-default",displayName:"GitHub Dark Default",type:"dark",import:()=>n.e(3899).then(n.bind(n,73899))},{id:"github-dark-dimmed",displayName:"GitHub Dark Dimmed",type:"dark",import:()=>n.e(9830).then(n.bind(n,19830))},{id:"github-dark-high-contrast",displayName:"GitHub Dark High Contrast",type:"dark",import:()=>n.e(4635).then(n.bind(n,44635))},{id:"github-light",displayName:"GitHub Light",type:"light",import:()=>n.e(9467).then(n.bind(n,59467))},{id:"github-light-default",displayName:"GitHub Light Default",type:"light",import:()=>n.e(9035).then(n.bind(n,29035))},{id:"github-light-high-contrast",displayName:"GitHub Light High Contrast",type:"light",import:()=>n.e(4891).then(n.bind(n,64891))},{id:"gruvbox-dark-hard",displayName:"Gruvbox Dark Hard",type:"dark",import:()=>n.e(5101).then(n.bind(n,45101))},{id:"gruvbox-dark-medium",displayName:"Gruvbox Dark Medium",type:"dark",import:()=>n.e(51).then(n.bind(n,51))},{id:"gruvbox-dark-soft",displayName:"Gruvbox Dark Soft",type:"dark",import:()=>n.e(2506).then(n.bind(n,92506))},{id:"gruvbox-light-hard",displayName:"Gruvbox Light Hard",type:"light",import:()=>n.e(6797).then(n.bind(n,86797))},{id:"gruvbox-light-medium",displayName:"Gruvbox Light Medium",type:"light",import:()=>n.e(8819).then(n.bind(n,68819))},{id:"gruvbox-light-soft",displayName:"Gruvbox Light Soft",type:"light",import:()=>n.e(554).then(n.bind(n,50554))},{id:"houston",displayName:"Houston",type:"dark",import:()=>n.e(9771).then(n.bind(n,9771))},{id:"kanagawa-dragon",displayName:"Kanagawa Dragon",type:"dark",import:()=>n.e(3972).then(n.bind(n,83972))},{id:"kanagawa-lotus",displayName:"Kanagawa Lotus",type:"light",import:()=>n.e(8444).then(n.bind(n,18444))},{id:"kanagawa-wave",displayName:"Kanagawa Wave",type:"dark",import:()=>n.e(9002).then(n.bind(n,79002))},{id:"laserwave",displayName:"LaserWave",type:"dark",import:()=>n.e(7779).then(n.bind(n,77779))},{id:"light-plus",displayName:"Light Plus",type:"light",import:()=>n.e(8336).then(n.bind(n,28336))},{id:"material-theme",displayName:"Material Theme",type:"dark",import:()=>n.e(4222).then(n.bind(n,64222))},{id:"material-theme-darker",displayName:"Material Theme Darker",type:"dark",import:()=>n.e(5276).then(n.bind(n,85276))},{id:"material-theme-lighter",displayName:"Material Theme Lighter",type:"light",import:()=>n.e(9372).then(n.bind(n,19372))},{id:"material-theme-ocean",displayName:"Material Theme Ocean",type:"dark",import:()=>n.e(3167).then(n.bind(n,33167))},{id:"material-theme-palenight",displayName:"Material Theme Palenight",type:"dark",import:()=>n.e(2307).then(n.bind(n,22307))},{id:"min-dark",displayName:"Min Dark",type:"dark",import:()=>n.e(1288).then(n.bind(n,91288))},{id:"min-light",displayName:"Min Light",type:"light",import:()=>n.e(2346).then(n.bind(n,2346))},{id:"monokai",displayName:"Monokai",type:"dark",import:()=>n.e(7475).then(n.bind(n,67475))},{id:"night-owl",displayName:"Night Owl",type:"dark",import:()=>n.e(2750).then(n.bind(n,82750))},{id:"nord",displayName:"Nord",type:"dark",import:()=>n.e(6974).then(n.bind(n,36974))},{id:"one-dark-pro",displayName:"One Dark Pro",type:"dark",import:()=>n.e(946).then(n.bind(n,946))},{id:"one-light",displayName:"One Light",type:"light",import:()=>n.e(6662).then(n.bind(n,96662))},{id:"plastic",displayName:"Plastic",type:"dark",import:()=>n.e(8817).then(n.bind(n,48817))},{id:"poimandres",displayName:"Poimandres",type:"dark",import:()=>n.e(1087).then(n.bind(n,1087))},{id:"red",displayName:"Red",type:"dark",import:()=>n.e(5776).then(n.bind(n,15776))},{id:"rose-pine",displayName:"Ros\xe9 Pine",type:"dark",import:()=>n.e(1).then(n.bind(n,50001))},{id:"rose-pine-dawn",displayName:"Ros\xe9 Pine Dawn",type:"light",import:()=>n.e(1394).then(n.bind(n,61394))},{id:"rose-pine-moon",displayName:"Ros\xe9 Pine Moon",type:"dark",import:()=>n.e(2807).then(n.bind(n,72807))},{id:"slack-dark",displayName:"Slack Dark",type:"dark",import:()=>n.e(3916).then(n.bind(n,33916))},{id:"slack-ochin",displayName:"Slack Ochin",type:"light",import:()=>n.e(1593).then(n.bind(n,11593))},{id:"snazzy-light",displayName:"Snazzy Light",type:"light",import:()=>n.e(4135).then(n.bind(n,54135))},{id:"solarized-dark",displayName:"Solarized Dark",type:"dark",import:()=>n.e(791).then(n.bind(n,20791))},{id:"solarized-light",displayName:"Solarized Light",type:"light",import:()=>n.e(8671).then(n.bind(n,58671))},{id:"synthwave-84",displayName:"Synthwave '84",type:"dark",import:()=>n.e(9407).then(n.bind(n,99407))},{id:"tokyo-night",displayName:"Tokyo Night",type:"dark",import:()=>n.e(8702).then(n.bind(n,8702))},{id:"vesper",displayName:"Vesper",type:"dark",import:()=>n.e(614).then(n.bind(n,90614))},{id:"vitesse-black",displayName:"Vitesse Black",type:"dark",import:()=>n.e(8210).then(n.bind(n,38210))},{id:"vitesse-dark",displayName:"Vitesse Dark",type:"dark",import:()=>n.e(5503).then(n.bind(n,25503))},{id:"vitesse-light",displayName:"Vitesse Light",type:"light",import:()=>n.e(6967).then(n.bind(n,36967))}].map(e=>[e.id,e.import]));var tU=n(83655);let tJ=function(e){let t=e.langs,n=e.themes,i=e.engine;return async function(e){function a(n){if("string"==typeof n){if(ti(n))return[];let i=t[n=e.langAlias?.[n]||n];if(!i)throw new r(`Language \`${n}\` is not included in this bundle. You may want to load it from external source.`);return i}return n}function s(e){if(tr(e))return"none";if("string"==typeof e){let t=n[e];if(!t)throw new r(`Theme \`${e}\` is not included in this bundle. You may want to load it from external source.`);return t}return e}let o=(e.themes??[]).map(e=>s(e)),l=(e.langs??[]).map(e=>a(e)),h=await tD({engine:e.engine??i(),...e,themes:o,langs:l});return{...h,loadLanguage:(...e)=>h.loadLanguage(...e.map(a)),loadTheme:(...e)=>h.loadTheme(...e.map(s)),getBundledLanguages:()=>t,getBundledThemes:()=>n}}}({langs:tH,themes:tz,engine:()=>(0,tU.Mk)(Promise.all([n.e(4814),n.e(4595)]).then(n.bind(n,54595)))})},75164:(e,t,n)=>{n.d(t,{A:()=>r});let i={}.hasOwnProperty;function r(e,t){let n=t||{};function r(t,...n){let a=r.invalid,s=r.handlers;if(t&&i.call(t,e)){let n=String(t[e]);a=i.call(s,n)?s[n]:r.unknown}if(a)return a.call(this,t,...n)}return r.handlers=n.handlers||{},r.invalid=n.invalid,r.unknown=n.unknown,r}}}]);