(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9983],{861:(e,t,r)=>{"use strict";r.d(t,{Qg:()=>l,bL:()=>i});var n=r(33670),o=r(97602),a=r(22342),l=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),s=n.forwardRef((e,t)=>(0,a.jsx)(o.sG.span,{...e,ref:t,style:{...l,...e.style}}));s.displayName="VisuallyHidden";var i=s},2421:(e,t,r)=>{"use strict";r.d(t,{Cz:()=>f,Mf:()=>d,N2:()=>u,NQ:()=>p,R3:()=>c});var n=r(58950);r(65156);var o=r(33670),a=r(26719),l=r(22342),s=(0,o.createContext)([]),i=(0,o.createContext)({current:null});function c(){return(0,o.useContext)(s).at(-1)}function d(){return(0,o.useContext)(s)}function u(e){let{containerRef:t,children:r}=e;return(0,l.jsx)(i.Provider,{value:t,children:r})}function p(e){let{toc:t,single:r=!0,children:n}=e,a=(0,o.useMemo)(()=>t.map(e=>e.url.split("#")[1]),[t]);return(0,l.jsx)(s.Provider,{value:function(e,t){let[r,n]=(0,o.useState)([]);return(0,o.useEffect)(()=>{let r=[],o=new IntersectionObserver(e=>{for(let t of e)t.isIntersecting&&!r.includes(t.target.id)?r=[...r,t.target.id]:!t.isIntersecting&&r.includes(t.target.id)&&(r=r.filter(e=>e!==t.target.id));r.length>0&&n(r)},{rootMargin:t?"-80px 0% -70% 0%":"-20px 0% -40% 0%",threshold:1});function a(){let r=document.scrollingElement;if(!r)return;let o=r.scrollTop;o<=0&&t?n(e.slice(0,1)):o+r.clientHeight>=r.scrollHeight-6&&n(r=>r.length>0&&!t?e.slice(e.indexOf(r[0])):e.slice(-1))}for(let t of e){let e=document.getElementById(t);e&&o.observe(e)}return a(),window.addEventListener("scroll",a),()=>{window.removeEventListener("scroll",a),o.disconnect()}},[t,e]),t?r.slice(0,1):r}(a,r),children:n})}var f=(0,o.forwardRef)((e,t)=>{let{onActiveChange:r,...s}=e,c=(0,o.useContext)(i),u=d(),p=(0,o.useRef)(null),f=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return e=>{t.forEach(t=>{"function"==typeof t?t(e):null!==t&&(t.current=e)})}}(p,t),h=u.includes(s.href.slice(1));return(0,n.T)(h,e=>{let t=p.current;t&&(e&&c.current&&(0,a.A)(t,{behavior:"smooth",block:"center",inline:"center",scrollMode:"always",boundary:c.current}),null==r||r(e))}),(0,l.jsx)("a",{ref:f,"data-active":h,...s,children:s.children})});f.displayName="TOCItem"},3538:(e,t,r)=>{"use strict";r.r(t),r.d(t,{Tab:()=>p,Tabs:()=>u,TabsContent:()=>f,TabsList:()=>c,TabsTrigger:()=>d});var n=r(22342),o=r(33670),a=r(75889),l=r(90290);let s=(0,o.createContext)(null);function i(){let e=(0,o.useContext)(s);if(!e)throw Error("You must wrap your component in <Tabs>");return e}let c=o.forwardRef((e,t)=>(0,n.jsx)(l.j7,{ref:t,...e,className:(0,a.QP)("flex gap-3.5 text-fd-secondary-foreground overflow-x-auto px-4 not-prose",e.className)}));c.displayName="TabsList";let d=o.forwardRef((e,t)=>(0,n.jsx)(l.Xi,{ref:t,...e,className:(0,a.QP)("inline-flex items-center gap-2 whitespace-nowrap text-fd-muted-foreground border-b border-transparent py-2 text-sm font-medium transition-colors [&_svg]:size-4 hover:text-fd-accent-foreground disabled:pointer-events-none disabled:opacity-50 data-[state=active]:border-fd-primary data-[state=active]:text-fd-primary",e.className)}));function u(e){let{ref:t,className:r,items:i,label:u,defaultIndex:p=0,defaultValue:f=i?h(i[p]):void 0,...m}=e,[x,y]=(0,o.useState)(f),g=(0,o.useMemo)(()=>[],[]);return(0,n.jsxs)(l.tU,{ref:t,className:(0,a.QP)("flex flex-col overflow-hidden rounded-xl border bg-fd-secondary my-4",r),value:x,onValueChange:e=>{(!i||i.some(t=>h(t)===e))&&y(e)},...m,children:[i&&(0,n.jsxs)(c,{children:[u&&(0,n.jsx)("span",{className:"text-sm font-medium my-auto me-auto",children:u}),i.map(e=>(0,n.jsx)(d,{value:h(e),children:e},e))]}),(0,n.jsx)(s.Provider,{value:(0,o.useMemo)(()=>({items:i,collection:g}),[g,i]),children:m.children})]})}function p(e){let{value:t,...r}=e,{items:a}=i(),l=null!=t?t:null==a?void 0:a.at(function(){let e=(0,o.useId)(),{collection:t}=i();return(0,o.useEffect)(()=>()=>{let r=t.indexOf(e);-1!==r&&t.splice(r,1)},[e,t]),t.includes(e)||t.push(e),t.indexOf(e)}());if(!l)throw Error("Failed to resolve tab `value`, please pass a `value` prop to the Tab component.");return(0,n.jsx)(f,{value:h(l),...r,children:r.children})}function f(e){let{value:t,className:r,...o}=e;return(0,n.jsx)(l.av,{value:t,forceMount:!0,className:(0,a.QP)("p-4 text-[15px] bg-fd-background rounded-xl outline-none prose-no-margin data-[state=inactive]:hidden [&>figure:only-child]:-m-4 [&>figure:only-child]:border-none",r),...o,children:o.children})}function h(e){return e.toLowerCase().replace(/\s/,"-")}d.displayName="TabsTrigger"},5917:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(71847).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},5937:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(71847).A)("message-circle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},8e3:(e,t,r)=>{"use strict";r.d(t,{Banner:()=>i});var n=r(22342),o=r(33670),a=r(13481),l=r(75889),s=r(81501);function i(e){let{id:t,variant:r="normal",changeLayout:i=!0,height:c="3rem",rainbowColors:d=["rgba(0,149,255,0.56)","rgba(231,77,255,0.77)","rgba(255,0,0,0.73)","rgba(131,255,166,0.66)"],...u}=e,[p,f]=(0,o.useState)(!0),h=t?"nd-banner-".concat(t):null;return((0,o.useEffect)(()=>{h&&f("true"!==localStorage.getItem(h))},[h]),p)?(0,n.jsxs)("div",{id:t,...u,className:(0,l.QP)("sticky top-0 z-40 flex flex-row items-center justify-center px-4 text-center text-sm font-medium","normal"===r&&"bg-fd-secondary","rainbow"===r&&"bg-fd-background",!p&&"hidden",u.className),style:{height:c},children:[i&&p?(0,n.jsx)("style",{children:h?":root:not(.".concat(h,") { --fd-banner-height: ").concat(c,"; }"):":root { --fd-banner-height: ".concat(c,"; }")}):null,h?(0,n.jsx)("style",{children:".".concat(h," #").concat(t," { display: none; }")}):null,h?(0,n.jsx)("script",{dangerouslySetInnerHTML:{__html:"if (localStorage.getItem('".concat(h,"') === 'true') document.documentElement.classList.add('").concat(h,"');")}}):null,"rainbow"===r?function(e){let{colors:t}=e;return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"absolute inset-0 z-[-1]",style:{maskImage:"linear-gradient(to bottom,white,transparent), radial-gradient(circle at top center, white, transparent)",maskComposite:"intersect",animation:"fd-moving-banner 20s linear infinite",backgroundImage:"repeating-linear-gradient(70deg, ".concat([...t,t[0]].map((e,r)=>"".concat(e," ").concat(50*r/t.length,"%")).join(", "),")"),backgroundSize:"200% 100%",filter:"saturate(2)"}}),(0,n.jsx)("style",{children:"@keyframes fd-moving-banner {\n            from { background-position: 0% 0;  }\n            to { background-position: 100% 0;  }\n         }"})]})}({colors:d}):null,u.children,t?(0,n.jsx)("button",{type:"button","aria-label":"Close Banner",onClick:()=>{f(!1),h&&localStorage.setItem(h,"true")},className:(0,l.QP)((0,s.r)({color:"ghost",className:"absolute end-2 top-1/2 -translate-y-1/2 text-fd-muted-foreground/50",size:"icon-sm"})),children:(0,n.jsx)(a.X,{})}):null]}):null}},8892:(e,t,r)=>{"use strict";r.d(t,{generator:()=>o});var n=r(69712);let o=(e,t,r)=>{let o,{mediaAdapters:a}=r,l=[],s={...t.header},i=new Set(["java.net.URI","java.net.http.HttpClient","java.net.http.HttpRequest","java.net.http.HttpResponse","java.net.http.HttpResponse.BodyHandlers","java.time.Duration"]);for(let e of(t.body&&t.bodyMediaType&&t.bodyMediaType in a&&(o=a[t.bodyMediaType].generateExample(t,{lang:"java",addImport(e){i.add(e)}})),i.values()))l.push("import ".concat(e,";"));for(let[t,r]of(l.push(""),o&&l.push(o),l.push("HttpClient client = HttpClient.newBuilder()"),l.push((0,n.b)(".connectTimeout(Duration.ofSeconds(10))")),l.push((0,n.b)(".build();")),l.push(""),l.push("HttpRequest.Builder requestBuilder = HttpRequest.newBuilder()"),l.push((0,n.b)(".uri(URI.create(".concat(JSON.stringify(e),"))"))),Object.entries(s)))l.push((0,n.b)(".header(".concat(JSON.stringify(t),", ").concat(JSON.stringify(r.value),")")));t.bodyMediaType&&l.push((0,n.b)('.header("Content-Type", "'.concat(t.bodyMediaType,'")')));let c=Object.entries(t.cookie);if(c.length>0){let e=c.map(e=>{let[t,r]=e;return"".concat(t,"=").concat(r.value)}).join("; ");l.push((0,n.b)('.header("Cookie", '.concat(JSON.stringify(e),")")))}let d=o?"body":"";return l.push((0,n.b)(".".concat(t.method.toUpperCase(),"(").concat(d,")"))),l.push((0,n.b)(".build();")),l.push(""),l.push("try {"),l.push((0,n.b)("HttpResponse<String> response = client.send(requestBuilder.build(), BodyHandlers.ofString());")),l.push((0,n.b)('System.out.println("Status code: " + response.statusCode());')),l.push((0,n.b)('System.out.println("Response body: " + response.body());')),l.push("} catch (Exception e) {"),l.push((0,n.b)("e.printStackTrace();")),l.push("}"),l.join("\n")}},15396:(e,t,r)=>{"use strict";r.d(t,{Accordion:()=>p,Accordions:()=>u});var n=r(22342),o=r(58609),a=r(13481),l=r(33670),s=r(75889),i=r(58807),c=r(81501),d=r(58670);let u=(0,l.forwardRef)((e,t)=>{let{type:r="single",className:a,defaultValue:i,...c}=e,u=(0,l.useRef)(null),p=(0,d.P)(t,u),[f,h]=(0,l.useState)(()=>"single"===r?null!=i?i:"":null!=i?i:[]);return(0,l.useEffect)(()=>{let e=window.location.hash.substring(1),t=u.current;if(!t||0===e.length)return;let r=document.getElementById(e);if(!r||!t.contains(r))return;let n=r.getAttribute("data-accordion-value");n&&h(e=>"string"==typeof e?n:[n,...e])},[]),(0,n.jsx)(o.bL,{type:r,ref:p,value:f,onValueChange:h,collapsible:"single"===r||void 0,className:(0,s.QP)("divide-y divide-fd-border overflow-hidden rounded-lg border bg-fd-card",a),...c})});u.displayName="Accordions";let p=(0,l.forwardRef)((e,t)=>{let{title:r,className:l,id:i,value:c=String(r),children:d,...u}=e;return(0,n.jsxs)(o.q7,{ref:t,value:c,className:(0,s.QP)("scroll-m-24",l),...u,children:[(0,n.jsxs)(o.Y9,{id:i,"data-accordion-value":c,className:"not-prose flex flex-row items-center text-fd-card-foreground font-medium has-focus-visible:bg-fd-accent",children:[(0,n.jsxs)(o.l9,{className:"group flex flex-1 items-center gap-2 px-3 py-2.5 text-start focus-visible:outline-none",children:[(0,n.jsx)(a.c_,{className:"size-4 shrink-0 text-fd-muted-foreground transition-transform duration-200 group-data-[state=open]:rotate-90"}),r]}),i?(0,n.jsx)(f,{id:i}):null]}),(0,n.jsx)(o.UC,{className:"overflow-hidden data-[state=closed]:animate-fd-accordion-up data-[state=open]:animate-fd-accordion-down",children:(0,n.jsx)("div",{className:"px-4 pb-2 text-[15px] prose-no-margin",children:d})})]})});function f(e){let{id:t}=e,[r,o]=(0,i.x)(()=>{let e=new URL(window.location.href);return e.hash=t,navigator.clipboard.writeText(e.toString())});return(0,n.jsx)("button",{type:"button","aria-label":"Copy Link",className:(0,s.QP)((0,c.r)({color:"ghost",className:"text-fd-muted-foreground me-2"})),onClick:o,children:r?(0,n.jsx)(a.Jl,{className:"size-3.5"}):(0,n.jsx)(a.N_,{className:"size-3.5"})})}p.displayName="Accordion"},17925:(e,t,r)=>{"use strict";r.d(t,{File:()=>u,Files:()=>d,Folder:()=>p});var n=r(22342),o=r(83101),a=r(13481),l=r(33670),s=r(75889),i=r(29947);let c=(0,o.F)("flex flex-row items-center gap-2 rounded-md px-2 py-1.5 text-sm hover:bg-fd-accent hover:text-fd-accent-foreground [&_svg]:size-4");function d(e){let{className:t,...r}=e;return(0,n.jsx)("div",{className:(0,s.QP)("not-prose rounded-md border bg-fd-card p-2",t),...r,children:r.children})}function u(e){let{name:t,icon:r=(0,n.jsx)(a.ZH,{}),className:o,...l}=e;return(0,n.jsxs)("div",{className:(0,s.QP)(c({className:o})),...l,children:[r,t]})}function p(e){let{name:t,defaultOpen:r=!1,...o}=e,[d,u]=(0,l.useState)(r);return(0,n.jsxs)(i.Collapsible,{open:d,onOpenChange:u,...o,children:[(0,n.jsxs)(i.CollapsibleTrigger,{className:(0,s.QP)(c({className:"w-full"})),children:[d?(0,n.jsx)(a.Bm,{}):(0,n.jsx)(a.vd,{}),t]}),(0,n.jsx)(i.CollapsibleContent,{children:(0,n.jsx)("div",{className:"ms-2 flex flex-col border-l ps-2",children:o.children})})]})}},18387:(e,t,r)=>{"use strict";r.d(t,{generator:()=>n});let n=(e,t,r)=>{let n,{mediaAdapters:o}=r,a=[],l=new Set(["System","System.Net.Http","System.Text"]),s={...t.header};for(let e of(t.body&&t.bodyMediaType&&t.bodyMediaType in o&&(n=o[t.bodyMediaType].generateExample(t,{lang:"csharp",addImport(e){l.add(e)}})),l))a.push("using ".concat(e,";"));a.push(""),n&&a.push(n,""),a.push("var client = new HttpClient();");let i=[];function c(e,t){i.push('client.DefaultRequestHeaders.Add("'.concat(e,'", ').concat(JSON.stringify(t),");"))}for(let e in s)c(e,s[e].value);Object.keys(t.cookie).length>0&&c("cookie",Object.entries(t.cookie).map(e=>{let[t,r]=e;return"".concat(t,"=").concat(r.value)}).join("; ")),a.push(...i);let d=t.method[0].toUpperCase()+t.method.slice(1).toLowerCase()+"Async";return n?a.push("var response = await client.".concat(d,'("').concat(e,'", body);')):a.push("var response = await client.".concat(d,'("').concat(e,'");')),a.push("var responseBody = await response.Content.ReadAsStringAsync();"),a.join("\n")}},23346:(e,t,r)=>{"use strict";r.d(t,{generator:()=>a});var n=r(71797),o=r(69712);let a=(e,t)=>{let r=[];for(let n in r.push("curl -X ".concat(t.method,' "').concat(e,'"')),t.header){let e="".concat(n,": ").concat(t.header[n].value);r.push('-H "'.concat(e,'"'))}for(let e in t.cookie){let n=t.cookie[e];r.push("--cookie ".concat(JSON.stringify("".concat(e,"=").concat(n.value))))}if(t.body&&"multipart/form-data"===t.bodyMediaType){if("object"!=typeof t.body)throw Error("[CURL] request body must be an object.");for(let[e,o]of Object.entries(t.body))r.push("-F ".concat(e,"=").concat(JSON.stringify((0,n.N)(o))))}else if(t.body&&t.bodyMediaType){let e=(0,n.$)((0,n.N)(t.body,t.bodyMediaType),"'");r.push('-H "Content-Type: '.concat(t.bodyMediaType,'"')),r.push("-d ".concat(e))}return r.flatMap((e,t)=>(0,o.b)(e,+(t>0))).join(" \\\n")}},24033:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(71847).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},26379:(e,t,r)=>{"use strict";r.d(t,{generator:()=>o});var n=r(69712);let o=(e,t,r)=>{let o,{mediaAdapters:a}=r,l=[],s=new Map,i={};for(let[e,r]of(s.set("method",JSON.stringify(t.method)),t.bodyMediaType&&(i["Content-Type"]=t.bodyMediaType),Object.entries(t.header)))i[e]=r.value;let c=Object.entries(t.cookie);c.length>0&&(i.cookie=c.map(e=>{let[t,r]=e;return"".concat(t,"=").concat(r.value)}).join("; ")),Object.keys(i).length>0&&s.set("headers",JSON.stringify(i,null,2)),t.body&&t.bodyMediaType&&t.bodyMediaType in a&&(o=a[t.bodyMediaType].generateExample(t,{lang:"js",addImport(e,t){l.unshift("import { ".concat(t,' } from "').concat(e,'"'))}})),o&&(l.push(o),s.set("body","body"));let d=[JSON.stringify(e)];if(s.size>0){let e=Array.from(s.entries()).map(e=>{let[t,r]=e;return(0,n.b)(t===r?t:"".concat(t,": ").concat(r))}).join(",\n");d.push("{\n".concat(e,"\n}"))}return l.push("fetch(".concat(d.join(", "),")")),l.join("\n\n")}},26719:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});let n=e=>"object"==typeof e&&null!=e&&1===e.nodeType,o=(e,t)=>(!t||"hidden"!==e)&&"visible"!==e&&"clip"!==e,a=(e,t)=>{if(e.clientHeight<e.scrollHeight||e.clientWidth<e.scrollWidth){let r=getComputedStyle(e,null);return o(r.overflowY,t)||o(r.overflowX,t)||(e=>{let t=(e=>{if(!e.ownerDocument||!e.ownerDocument.defaultView)return null;try{return e.ownerDocument.defaultView.frameElement}catch(e){return null}})(e);return!!t&&(t.clientHeight<e.scrollHeight||t.clientWidth<e.scrollWidth)})(e)}return!1},l=(e,t,r,n,o,a,l,s)=>a<e&&l>t||a>e&&l<t?0:a<=e&&s<=r||l>=t&&s>=r?a-e-n:l>t&&s<r||a<e&&s>r?l-t+o:0,s=e=>{let t=e.parentElement;return null==t?e.getRootNode().host||null:t},i=(e,t)=>{var r,o,i,c;if("undefined"==typeof document)return[];let{scrollMode:d,block:u,inline:p,boundary:f,skipOverflowHiddenElements:h}=t,m="function"==typeof f?f:e=>e!==f;if(!n(e))throw TypeError("Invalid target");let x=document.scrollingElement||document.documentElement,y=[],g=e;for(;n(g)&&m(g);){if((g=s(g))===x){y.push(g);break}null!=g&&g===document.body&&a(g)&&!a(document.documentElement)||null!=g&&a(g,h)&&y.push(g)}let v=null!=(o=null==(r=window.visualViewport)?void 0:r.width)?o:innerWidth,b=null!=(c=null==(i=window.visualViewport)?void 0:i.height)?c:innerHeight,{scrollX:w,scrollY:j}=window,{height:N,width:C,top:S,right:T,bottom:k,left:P}=e.getBoundingClientRect(),{top:E,right:R,bottom:A,left:M}=(e=>{let t=window.getComputedStyle(e);return{top:parseFloat(t.scrollMarginTop)||0,right:parseFloat(t.scrollMarginRight)||0,bottom:parseFloat(t.scrollMarginBottom)||0,left:parseFloat(t.scrollMarginLeft)||0}})(e),O="start"===u||"nearest"===u?S-E:"end"===u?k+A:S+N/2-E+A,F="center"===p?P+C/2-M+R:"end"===p?T+R:P-M,K=[];for(let e=0;e<y.length;e++){let t=y[e],{height:r,width:n,top:o,right:s,bottom:i,left:c}=t.getBoundingClientRect();if("if-needed"===d&&S>=0&&P>=0&&k<=b&&T<=v&&(t===x&&!a(t)||S>=o&&k<=i&&P>=c&&T<=s))break;let f=getComputedStyle(t),h=parseInt(f.borderLeftWidth,10),m=parseInt(f.borderTopWidth,10),g=parseInt(f.borderRightWidth,10),E=parseInt(f.borderBottomWidth,10),R=0,A=0,M="offsetWidth"in t?t.offsetWidth-t.clientWidth-h-g:0,I="offsetHeight"in t?t.offsetHeight-t.clientHeight-m-E:0,L="offsetWidth"in t?0===t.offsetWidth?0:n/t.offsetWidth:0,D="offsetHeight"in t?0===t.offsetHeight?0:r/t.offsetHeight:0;if(x===t)R="start"===u?O:"end"===u?O-b:"nearest"===u?l(j,j+b,b,m,E,j+O,j+O+N,N):O-b/2,A="start"===p?F:"center"===p?F-v/2:"end"===p?F-v:l(w,w+v,v,h,g,w+F,w+F+C,C),R=Math.max(0,R+j),A=Math.max(0,A+w);else{R="start"===u?O-o-m:"end"===u?O-i+E+I:"nearest"===u?l(o,i,r,m,E+I,O,O+N,N):O-(o+r/2)+I/2,A="start"===p?F-c-h:"center"===p?F-(c+n/2)+M/2:"end"===p?F-s+g+M:l(c,s,n,h,g+M,F,F+C,C);let{scrollLeft:e,scrollTop:a}=t;R=0===D?0:Math.max(0,Math.min(a+R/D,t.scrollHeight-r/D+I)),A=0===L?0:Math.max(0,Math.min(e+A/L,t.scrollWidth-n/L+M)),O+=a-R,F+=e-A}K.push({el:t,top:R,left:A})}return K};function c(e,t){if(!e.isConnected||!(e=>{let t=e;for(;t&&t.parentNode;){if(t.parentNode===document)return!0;t=t.parentNode instanceof ShadowRoot?t.parentNode.host:t.parentNode}return!1})(e))return;let r=(e=>{let t=window.getComputedStyle(e);return{top:parseFloat(t.scrollMarginTop)||0,right:parseFloat(t.scrollMarginRight)||0,bottom:parseFloat(t.scrollMarginBottom)||0,left:parseFloat(t.scrollMarginLeft)||0}})(e);if("object"==typeof t&&"function"==typeof t.behavior)return t.behavior(i(e,t));let n="boolean"==typeof t||null==t?void 0:t.behavior;for(let{el:o,top:a,left:l}of i(e,!1===t?{block:"end",inline:"nearest"}:t===Object(t)&&0!==Object.keys(t).length?t:{block:"start",inline:"nearest"})){let e=a-r.top+r.bottom,t=l-r.left+r.right;o.scroll({top:e,left:t,behavior:n})}}},33619:(e,t,r)=>{"use strict";r.r(t),r.d(t,{Info:()=>c,TypeTable:()=>p});var n=r(22342),o=r(13481),a=r(1481),l=r(83101),s=r(75889),i=r(25502);function c(e){let{children:t}=e;return(0,n.jsxs)(i.Popover,{children:[(0,n.jsx)(i.PopoverTrigger,{children:(0,n.jsx)(o.R2,{className:"size-4"})}),(0,n.jsx)(i.PopoverContent,{className:"prose max-h-[400px] min-w-[220px] max-w-[400px] overflow-auto text-sm prose-no-margin",children:t})]})}let d=(0,l.F)("inline-flex flex-row items-center gap-1"),u=(0,l.F)("rounded-md bg-fd-secondary p-1 text-fd-secondary-foreground",{variants:{color:{primary:"bg-fd-primary/10 text-fd-primary",deprecated:"line-through text-fd-primary/50"}}});function p(e){let{type:t}=e;return(0,n.jsx)("div",{className:"prose my-6 overflow-auto prose-no-margin",children:(0,n.jsxs)("table",{className:"whitespace-nowrap text-sm text-fd-muted-foreground",children:[(0,n.jsx)("thead",{children:(0,n.jsxs)("tr",{children:[(0,n.jsx)("th",{className:"w-[45%]",children:"Prop"}),(0,n.jsx)("th",{className:"w-[30%]",children:"Type"}),(0,n.jsx)("th",{className:"w-1/4",children:"Default"})]})}),(0,n.jsx)("tbody",{children:Object.entries(t).map(e=>{let[t,r]=e;return(0,n.jsxs)("tr",{children:[(0,n.jsx)("td",{children:(0,n.jsxs)("div",{className:d(),children:[(0,n.jsxs)("code",{className:(0,s.QP)(u({color:r.deprecated?"deprecated":"primary"})),children:[t,!r.required&&"?"]}),r.description?(0,n.jsx)(c,{children:r.description}):null]})}),(0,n.jsx)("td",{children:(0,n.jsxs)("div",{className:d(),children:[(0,n.jsx)("code",{className:u(),children:r.type}),r.typeDescription?(0,n.jsx)(c,{children:r.typeDescription}):null,r.typeDescriptionLink?(0,n.jsx)(a.default,{href:r.typeDescriptionLink,children:(0,n.jsx)(o.N_,{className:"size-4 text-fd-muted-foreground"})}):null]})}),(0,n.jsx)("td",{children:r.default?(0,n.jsx)("code",{className:u(),children:r.default}):"-"})]},t)})})]})})}},41017:(e,t,r)=>{var n=r(56284).isArray;e.exports={copyOptions:function(e){var t,r={};for(t in e)e.hasOwnProperty(t)&&(r[t]=e[t]);return r},ensureFlagExists:function(e,t){e in t&&"boolean"==typeof t[e]||(t[e]=!1)},ensureSpacesExists:function(e){"spaces"in e&&("number"==typeof e.spaces||"string"==typeof e.spaces)||(e.spaces=0)},ensureAlwaysArrayExists:function(e){"alwaysArray"in e&&("boolean"==typeof e.alwaysArray||n(e.alwaysArray))||(e.alwaysArray=!1)},ensureKeyExists:function(e,t){e+"Key"in t&&"string"==typeof t[e+"Key"]||(t[e+"Key"]=t.compact?"_"+e:e)},checkFnExists:function(e,t){return e+"Fn"in t}}},42738:(e,t,r)=>{"use strict";r.r(t),r.d(t,{Popup:()=>y,PopupContent:()=>v,PopupTrigger:()=>g});var n=Object.defineProperty,o=Object.defineProperties,a=Object.getOwnPropertyDescriptors,l=Object.getOwnPropertySymbols,s=Object.prototype.hasOwnProperty,i=Object.prototype.propertyIsEnumerable,c=(e,t,r)=>t in e?n(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,d=(e,t)=>{for(var r in t||(t={}))s.call(t,r)&&c(e,r,t[r]);if(l)for(var r of l(t))i.call(t,r)&&c(e,r,t[r]);return e},u=(e,t)=>{var r={};for(var n in e)s.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&l)for(var n of l(e))0>t.indexOf(n)&&i.call(e,n)&&(r[n]=e[n]);return r},p=r(33670),f=r(82286),h=r(75889),m=r(22342),x=(0,p.createContext)(void 0);function y(e){let{delay:t=300,children:r}=e,[n,o]=(0,p.useState)(!1),a=(0,p.useRef)(void 0),l=(0,p.useRef)(void 0);return(0,m.jsx)(f.AM,{open:n,onOpenChange:o,children:(0,m.jsx)(x.Provider,{value:(0,p.useMemo)(()=>({open:n,setOpen:o,handleOpen(e){"touch"!==e.pointerType&&(l.current&&clearTimeout(l.current),a.current=window.setTimeout(()=>{o(!0)},t))},handleClose(e){"touch"!==e.pointerType&&(a.current&&clearTimeout(a.current),l.current=window.setTimeout(()=>{o(!1)},t))}}),[t,n]),children:r})})}var g=(0,p.forwardRef)((e,t)=>{var{children:r}=e,n=u(e,["children"]);let l=(0,p.useContext)(x);if(!l)throw Error("Missing Popup Context");return(0,m.jsx)(f.Wv,o(d({ref:t,onPointerEnter:l.handleOpen,onPointerLeave:l.handleClose,asChild:!0},n),a({children:(0,m.jsx)("span",{className:"twoslash-hover",children:r})})))});g.displayName="PopupTrigger";var v=(0,p.forwardRef)((e,t)=>{var{className:r,side:n="bottom",align:o="center",sideOffset:a=4}=e,l=u(e,["className","side","align","sideOffset"]);let s=(0,p.useContext)(x);if(!s)throw Error("Missing Popup Context");return(0,m.jsx)(f.i0,{children:(0,m.jsx)(f.hl,d({ref:t,side:n,align:o,sideOffset:a,className:(0,h.QP)("fd-twoslash-popover",r),onPointerEnter:s.handleOpen,onPointerLeave:s.handleClose,onOpenAutoFocus:e=>{e.preventDefault()},onCloseAutoFocus:e=>{e.preventDefault()}},l))})});v.displayName="PopupContent"},47074:(e,t,r)=>{"use strict";r.d(t,{generator:()=>n});let n=(e,t,r)=>{let n,{mediaAdapters:o}=r,a={},l=['"'.concat(t.method,'"'),"url"];for(let[e,r]of(t.body&&t.bodyMediaType&&t.bodyMediaType in o&&(a["Content-Type"]=t.bodyMediaType,(n=o[t.bodyMediaType].generateExample(t,{lang:"python"}))&&"application/json"===t.bodyMediaType?l.push("json = body"):n&&l.push("data = body")),Object.entries(t.header)))a[e]=r.value;Object.keys(a).length>0&&l.push("headers = ".concat(JSON.stringify(a,null,2)));let s=Object.entries(t.cookie);if(s.length>0){let e={};for(let[t,r]of s)e[t]=r.value;l.push("cookies = ".concat(JSON.stringify(e,null,2)))}return"import requests\n\nurl = ".concat(JSON.stringify(e),"\n").concat(null!=n?n:"","\nresponse = requests.request(").concat(l.join(", "),")\n\nprint(response.text)")}},50440:(e,t,r)=>{"use strict";r.d(t,{j:()=>d});var n=r(22342),o=r(33670),a=r(2421),l=r(79384),s=r(2764);function i(e,t){if(0===t.length||0===e.clientHeight)return[0,0];let r=Number.MAX_VALUE,n=0;for(let o of t){let t=e.querySelector(`a[href="#${o}"]`);if(!t)continue;let a=getComputedStyle(t);r=Math.min(r,t.offsetTop+parseFloat(a.paddingTop)),n=Math.max(n,t.offsetTop+t.clientHeight-parseFloat(a.paddingBottom))}return[r,n-r]}function c(e,t){e.style.setProperty("--fd-top",`${t[0]}px`),e.style.setProperty("--fd-height",`${t[1]}px`)}function d({containerRef:e,...t}){let r=a.Mf(),d=(0,o.useRef)(null),u=(0,s.J)(()=>{e.current&&d.current&&c(d.current,i(e.current,r))});return(0,o.useEffect)(()=>{if(!e.current)return;let t=e.current;u();let r=new ResizeObserver(u);return r.observe(t),()=>{r.disconnect()}},[e,u]),(0,l.T)(r,()=>{e.current&&d.current&&c(d.current,i(e.current,r))}),(0,n.jsx)("div",{ref:d,role:"none",...t})}},53450:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ApiProvider:()=>d,ClientLazy:()=>c,CodeExample:()=>s,CodeExampleProvider:()=>l,CodeExampleSelector:()=>i});var n=r(22342),o=r(33670);function a(e){return function(t){return(0,n.jsx)(e,{...t})}}let l=a((0,o.lazy)(()=>Promise.all([r.e(7300),r.e(2195)]).then(r.bind(r,82195)).then(e=>({default:e.CodeExampleProvider})))),s=a((0,o.lazy)(()=>Promise.all([r.e(7300),r.e(2195)]).then(r.bind(r,82195)).then(e=>({default:e.CodeExample})))),i=a((0,o.lazy)(()=>Promise.all([r.e(7300),r.e(2195)]).then(r.bind(r,82195)).then(e=>({default:e.CodeExampleSelector})))),c=a((0,o.lazy)(()=>Promise.all([r.e(7300),r.e(290)]).then(r.bind(r,35052)))),d=a((0,o.lazy)(()=>r.e(2615).then(r.bind(r,32615)).then(e=>({default:e.ApiProvider}))))},56284:e=>{e.exports={isArray:function(e){return Array.isArray?Array.isArray(e):"[object Array]"===Object.prototype.toString.call(e)}}},58609:(e,t,r)=>{"use strict";r.d(t,{UC:()=>V,Y9:()=>W,bL:()=>z,l9:()=>U,q7:()=>q});var n=r(33670),o=r(3468),a=r(49972),l=r(94446),s=r(92556),i=r(23558),c=r(97602),d=r(57259),u=r(68946),p=r(66218),f=r(22342),h="Accordion",m=["Home","End","ArrowDown","ArrowUp","ArrowLeft","ArrowRight"],[x,y,g]=(0,a.N)(h),[v,b]=(0,o.A)(h,[g,d.z3]),w=(0,d.z3)(),j=n.forwardRef((e,t)=>{let{type:r,...n}=e;return(0,f.jsx)(x.Provider,{scope:e.__scopeAccordion,children:"multiple"===r?(0,f.jsx)(P,{...n,ref:t}):(0,f.jsx)(k,{...n,ref:t})})});j.displayName=h;var[N,C]=v(h),[S,T]=v(h,{collapsible:!1}),k=n.forwardRef((e,t)=>{let{value:r,defaultValue:o,onValueChange:a=()=>{},collapsible:l=!1,...s}=e,[c,d]=(0,i.i)({prop:r,defaultProp:null!=o?o:"",onChange:a,caller:h});return(0,f.jsx)(N,{scope:e.__scopeAccordion,value:n.useMemo(()=>c?[c]:[],[c]),onItemOpen:d,onItemClose:n.useCallback(()=>l&&d(""),[l,d]),children:(0,f.jsx)(S,{scope:e.__scopeAccordion,collapsible:l,children:(0,f.jsx)(A,{...s,ref:t})})})}),P=n.forwardRef((e,t)=>{let{value:r,defaultValue:o,onValueChange:a=()=>{},...l}=e,[s,c]=(0,i.i)({prop:r,defaultProp:null!=o?o:[],onChange:a,caller:h}),d=n.useCallback(e=>c(function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return[...t,e]}),[c]),u=n.useCallback(e=>c(function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return t.filter(t=>t!==e)}),[c]);return(0,f.jsx)(N,{scope:e.__scopeAccordion,value:s,onItemOpen:d,onItemClose:u,children:(0,f.jsx)(S,{scope:e.__scopeAccordion,collapsible:!0,children:(0,f.jsx)(A,{...l,ref:t})})})}),[E,R]=v(h),A=n.forwardRef((e,t)=>{let{__scopeAccordion:r,disabled:o,dir:a,orientation:i="vertical",...d}=e,u=n.useRef(null),h=(0,l.s)(u,t),g=y(r),v="ltr"===(0,p.jH)(a),b=(0,s.mK)(e.onKeyDown,e=>{var t;if(!m.includes(e.key))return;let r=e.target,n=g().filter(e=>{var t;return!(null==(t=e.ref.current)?void 0:t.disabled)}),o=n.findIndex(e=>e.ref.current===r),a=n.length;if(-1===o)return;e.preventDefault();let l=o,s=a-1,c=()=>{(l=o+1)>s&&(l=0)},d=()=>{(l=o-1)<0&&(l=s)};switch(e.key){case"Home":l=0;break;case"End":l=s;break;case"ArrowRight":"horizontal"===i&&(v?c():d());break;case"ArrowDown":"vertical"===i&&c();break;case"ArrowLeft":"horizontal"===i&&(v?d():c());break;case"ArrowUp":"vertical"===i&&d()}null==(t=n[l%a].ref.current)||t.focus()});return(0,f.jsx)(E,{scope:r,disabled:o,direction:a,orientation:i,children:(0,f.jsx)(x.Slot,{scope:r,children:(0,f.jsx)(c.sG.div,{...d,"data-orientation":i,ref:h,onKeyDown:o?void 0:b})})})}),M="AccordionItem",[O,F]=v(M),K=n.forwardRef((e,t)=>{let{__scopeAccordion:r,value:n,...o}=e,a=R(M,r),l=C(M,r),s=w(r),i=(0,u.B)(),c=n&&l.value.includes(n)||!1,p=a.disabled||e.disabled;return(0,f.jsx)(O,{scope:r,open:c,disabled:p,triggerId:i,children:(0,f.jsx)(d.bL,{"data-orientation":a.orientation,"data-state":B(c),...s,...o,ref:t,disabled:p,open:c,onOpenChange:e=>{e?l.onItemOpen(n):l.onItemClose(n)}})})});K.displayName=M;var I="AccordionHeader",L=n.forwardRef((e,t)=>{let{__scopeAccordion:r,...n}=e,o=R(h,r),a=F(I,r);return(0,f.jsx)(c.sG.h3,{"data-orientation":o.orientation,"data-state":B(a.open),"data-disabled":a.disabled?"":void 0,...n,ref:t})});L.displayName=I;var D="AccordionTrigger",H=n.forwardRef((e,t)=>{let{__scopeAccordion:r,...n}=e,o=R(h,r),a=F(D,r),l=T(D,r),s=w(r);return(0,f.jsx)(x.ItemSlot,{scope:r,children:(0,f.jsx)(d.l9,{"aria-disabled":a.open&&!l.collapsible||void 0,"data-orientation":o.orientation,id:a.triggerId,...s,...n,ref:t})})});H.displayName=D;var Q="AccordionContent",_=n.forwardRef((e,t)=>{let{__scopeAccordion:r,...n}=e,o=R(h,r),a=F(Q,r),l=w(r);return(0,f.jsx)(d.UC,{role:"region","aria-labelledby":a.triggerId,"data-orientation":o.orientation,...l,...n,ref:t,style:{"--radix-accordion-content-height":"var(--radix-collapsible-content-height)","--radix-accordion-content-width":"var(--radix-collapsible-content-width)",...e.style}})});function B(e){return e?"open":"closed"}_.displayName=Q;var z=j,q=K,W=L,U=H,V=_},60566:(e,t,r)=>{"use strict";r.d(t,{generator:()=>o});var n=r(69712);let o=(e,t,r)=>{let o,{mediaAdapters:a}=r,l=["fmt","net/http","io/ioutil"],s=new Map,i=new Map;for(let r in i.set("url",JSON.stringify(e)),t.header)s.set(r,JSON.stringify(t.header[r].value));let c=Object.entries(t.cookie);return c.length>0&&s.set("Cookie",JSON.stringify(c.map(e=>{let[t,r]=e;return"".concat(t,"=").concat(r.value)}).join("; "))),t.body&&t.bodyMediaType&&t.bodyMediaType in a&&(s.set("Content-Type",'"'.concat(t.bodyMediaType,'"')),o=a[t.bodyMediaType].generateExample(t,{lang:"go",addImport(e){l.push(e)}})),"package main\n\nimport (\n".concat((0,n.b)(l.map(e=>'"'.concat(e,'"')).join("\n")),"\n)\n\nfunc main() {\n").concat(Array.from(i.entries()).map(e=>{let[t,r]=e;return(0,n.b)("".concat(t," := ").concat(r))}).join("\n"),"\n").concat(o?(0,n.b)(o):"",'\n  req, _ := http.NewRequest("').concat(t.method,'", url, ').concat(o?"body":"nil",")\n").concat((0,n.b)(Array.from(s.entries()).map(e=>{let[t,r]=e;return'req.Header.Add("'.concat(t,'", ').concat(r,")")}).join("\n")),"\n  res, _ := http.DefaultClient.Do(req)\n  defer res.Body.Close()\n  body, _ := ioutil.ReadAll(res.Body)\n\n  fmt.Println(res)\n  fmt.Println(string(body))\n}")}},64675:(e,t,r)=>{"use strict";r.d(t,{SelectTab:()=>c,SelectTabTrigger:()=>d,SelectTabs:()=>i});var n=r(22342),o=r(33670),a=r(66009),l=r(75889);let s=(0,o.createContext)(null);function i(e){let{defaultValue:t,children:r}=e,[a,l]=(0,o.useState)(null!=t?t:null);return(0,n.jsx)(s,{value:(0,o.useMemo)(()=>({type:a,setType:l}),[a]),children:r})}function c(e){let{value:t,...r}=e,a=(0,o.useContext)(s);if(t===(null==a?void 0:a.type))return(0,n.jsx)("div",{...r,children:r.children})}function d(e){let{items:t,...r}=e,{type:i,setType:c}=(0,o.useContext)(s);return(0,n.jsxs)(a.l6,{value:null!=i?i:"",onValueChange:c,children:[(0,n.jsx)(a.bq,{...r,className:(0,l.QP)("not-prose w-fit",r.className),children:(0,n.jsx)(a.yv,{})}),(0,n.jsx)(a.gC,{children:t.map(e=>(0,n.jsx)(a.eb,{value:e,children:e},e))})]})}},65064:(e,t,r)=>{"use strict";r.d(t,{PageBreadcrumb:()=>P,PageFooter:()=>T,PageLastUpdate:()=>C,PageTOC:()=>E,PageTOCPopover:()=>N,PageTOCPopoverContent:()=>j,PageTOCPopoverTrigger:()=>b});var n=r(22342),o=r(33670),a=r(13481),l=r(1481),s=r(75889),i=r(53878),c=r(17714),d=r(99987),u=r(80909),p=r(67652),f=r(89496),h=r(2764),m=r(29947),x=r(77222),y=r(85765),g=r(2421);let v=(0,d.q6)("TocPopoverContext");function b(e){var t,r;let{text:l}=(0,i.s9)(),{open:d}=v.use(),u=(0,y.a)(),p=(0,g.R3)(),f=(0,o.useMemo)(()=>u.findIndex(e=>p===e.url.slice(1)),[u,p]),h=(0,c.L)().at(-1),x=-1!==f&&!d;return(0,n.jsxs)(m.CollapsibleTrigger,{...e,className:(0,s.QP)("flex w-full h-(--fd-tocnav-height) items-center text-sm text-fd-muted-foreground gap-2.5 px-4 py-2.5 text-start focus-visible:outline-none [&_svg]:size-4 md:px-6",e.className),children:[(0,n.jsx)(w,{value:(f+1)/Math.max(1,u.length),max:1,className:(0,s.QP)("shrink-0",d&&"text-fd-primary")}),(0,n.jsxs)("span",{className:"grid flex-1 *:my-auto *:row-start-1 *:col-start-1",children:[(0,n.jsx)("span",{className:(0,s.QP)("truncate transition-all",d&&"text-fd-foreground",x&&"opacity-0 -translate-y-full pointer-events-none"),children:null!=(r=null==h?void 0:h.name)?r:l.toc}),(0,n.jsx)("span",{className:(0,s.QP)("truncate transition-all",!x&&"opacity-0 translate-y-full pointer-events-none"),children:null==(t=u[f])?void 0:t.title})]}),(0,n.jsx)(a.yQ,{className:(0,s.QP)("shrink-0 transition-transform mx-0.5",d&&"rotate-180")})]})}function w(e){let{value:t,strokeWidth:r=2,size:o=24,min:a=0,max:l=100,...s}=e,i=t<a?a:t>l?l:t,c=(o-r)/2,d=2*Math.PI*c,u=i/l*d,p={cx:o/2,cy:o/2,r:c,fill:"none",strokeWidth:r};return(0,n.jsxs)("svg",{role:"progressbar",viewBox:"0 0 ".concat(o," ").concat(o),"aria-valuenow":i,"aria-valuemin":a,"aria-valuemax":l,...s,children:[(0,n.jsx)("circle",{...p,className:"stroke-current/25"}),(0,n.jsx)("circle",{...p,stroke:"currentColor",strokeDasharray:d,strokeDashoffset:d-u,strokeLinecap:"round",transform:"rotate(-90 ".concat(o/2," ").concat(o/2,")"),className:"transition-all"})]})}function j(e){return(0,n.jsx)(m.CollapsibleContent,{"data-toc-popover":"",...e,className:(0,s.QP)("flex flex-col px-4 max-h-[50vh] md:px-6",e.className),children:e.children})}function N(e){let t=(0,o.useRef)(null),[r,a]=(0,o.useState)(!1),{collapsed:l}=(0,x.c)(),{isTransparent:i}=(0,p.hI)(),c=(0,h.J)(e=>{r&&t.current&&!t.current.contains(e.target)&&a(!1)});return(0,o.useEffect)(()=>(window.addEventListener("click",c),()=>{window.removeEventListener("click",c)}),[c]),(0,n.jsx)(v.Provider,{value:(0,o.useMemo)(()=>({open:r,setOpen:a}),[a,r]),children:(0,n.jsx)(m.Collapsible,{open:r,onOpenChange:a,asChild:!0,children:(0,n.jsx)("header",{ref:t,id:"nd-tocnav",...e,className:(0,s.QP)("fixed inset-x-0 z-10 border-b backdrop-blur-sm transition-colors xl:hidden",(!i||r)&&"bg-fd-background/80",r&&"shadow-lg",e.className),style:{...e.style,top:"calc(var(--fd-banner-height) + var(--fd-nav-height))",insetInlineStart:l?"0px":"calc(var(--fd-sidebar-width) + var(--fd-layout-offset))"},children:e.children})})})}function C(e){let{date:t,...r}=e,{text:a}=(0,i.s9)(),[l,c]=(0,o.useState)("");return(0,o.useEffect)(()=>{c(new Date(t).toLocaleDateString())},[t]),(0,n.jsxs)("p",{...r,className:(0,s.QP)("text-sm text-fd-muted-foreground",r.className),children:[a.lastUpdate," ",l]})}let S=new Map;function T(e){let{items:t,...r}=e,{root:a}=(0,c.t)(),l=(0,d.a8)(),{previous:i,next:u}=(0,o.useMemo)(()=>{if(t)return t;let e=S.get(a.$id),r=null!=e?e:function e(t){let r=[];return t.forEach(t=>{if("folder"===t.type){t.index&&r.push(t.index),r.push(...e(t.children));return}"page"!==t.type||t.external||r.push(t)}),r}(a.children);S.set(a.$id,r);let n=r.findIndex(e=>(0,f.$)(e.url,l,!1));return -1===n?{}:{previous:r[n-1],next:r[n+1]}},[t,l,a]);return(0,n.jsxs)("div",{...r,className:(0,s.QP)("@container grid gap-4 pb-6",i&&u?"grid-cols-2":"grid-cols-1",r.className),children:[i?(0,n.jsx)(k,{item:i,index:0}):null,u?(0,n.jsx)(k,{item:u,index:1}):null]})}function k(e){var t;let{item:r,index:o}=e,{text:c}=(0,i.s9)(),d=0===o?a.JG:a.c_;return(0,n.jsxs)(l.default,{href:r.url,className:(0,s.QP)("flex flex-col gap-2 rounded-lg border p-4 text-sm transition-colors hover:bg-fd-accent/80 hover:text-fd-accent-foreground @max-lg:col-span-full",1===o&&"text-end"),children:[(0,n.jsxs)("div",{className:(0,s.QP)("inline-flex items-center gap-1.5 font-medium",1===o&&"flex-row-reverse"),children:[(0,n.jsx)(d,{className:"-mx-1 size-4 shrink-0 rtl:rotate-180"}),(0,n.jsx)("p",{children:r.name})]}),(0,n.jsx)("p",{className:"text-fd-muted-foreground truncate",children:null!=(t=r.description)?t:0===o?c.previousPage:c.nextPage})]})}function P(e){let{includeRoot:t=!1,includeSeparator:r,includePage:i=!1,...d}=e,p=(0,c.L)(),{root:f}=(0,c.t)(),h=(0,o.useMemo)(()=>(0,u.Pp)(f,p,{includePage:i,includeSeparator:r,includeRoot:t}),[i,t,r,p,f]);return 0===h.length?null:(0,n.jsx)("div",{...d,className:(0,s.QP)("flex items-center gap-1.5 text-sm text-fd-muted-foreground",d.className),children:h.map((e,t)=>{let r=(0,s.QP)("truncate",t===h.length-1&&"text-fd-primary font-medium");return(0,n.jsxs)(o.Fragment,{children:[0!==t&&(0,n.jsx)(a.c_,{className:"size-3.5 shrink-0"}),e.url?(0,n.jsx)(l.default,{href:e.url,className:(0,s.QP)(r,"transition-opacity hover:opacity-80"),children:e.name}):(0,n.jsx)("span",{className:r,children:e.name})]},t)})})}function E(e){return(0,n.jsx)("div",{id:"nd-toc",...e,className:(0,s.QP)("sticky pb-2 pt-12 max-xl:hidden",e.className),style:{...e.style,top:"calc(var(--fd-banner-height) + var(--fd-nav-height))",height:"calc(100dvh - var(--fd-banner-height) - var(--fd-nav-height))"},children:(0,n.jsx)("div",{className:"flex h-full w-(--fd-toc-width) max-w-full flex-col pe-4",children:e.children})})}},66009:(e,t,r)=>{"use strict";r.d(t,{l6:()=>eA,gC:()=>eI,eb:()=>eL,bq:()=>eO,yv:()=>eM});var n=r(22342),o=r(33670),a=r(59405),l=r(34212),s=r(92556),i=r(49972),c=r(94446),d=r(3468),u=r(66218),p=r(44831),f=r(19526),h=r(69666),m=r(68946),x=r(57641),y=r(75433),g=r(97602),v=r(32467),b=r(70222),w=r(23558),j=r(4129),N=r(78108),C=r(861),S=r(97745),T=r(40101),k=[" ","Enter","ArrowUp","ArrowDown"],P=[" ","Enter"],E="Select",[R,A,M]=(0,i.N)(E),[O,F]=(0,d.A)(E,[M,x.Bk]),K=(0,x.Bk)(),[I,L]=O(E),[D,H]=O(E),Q=e=>{let{__scopeSelect:t,children:r,open:a,defaultOpen:l,onOpenChange:s,value:i,defaultValue:c,onValueChange:d,dir:p,name:f,autoComplete:h,disabled:y,required:g,form:v}=e,b=K(t),[j,N]=o.useState(null),[C,S]=o.useState(null),[T,k]=o.useState(!1),P=(0,u.jH)(p),[A,M]=(0,w.i)({prop:a,defaultProp:null!=l&&l,onChange:s,caller:E}),[O,F]=(0,w.i)({prop:i,defaultProp:c,onChange:d,caller:E}),L=o.useRef(null),H=!j||v||!!j.closest("form"),[Q,_]=o.useState(new Set),B=Array.from(Q).map(e=>e.props.value).join(";");return(0,n.jsx)(x.bL,{...b,children:(0,n.jsxs)(I,{required:g,scope:t,trigger:j,onTriggerChange:N,valueNode:C,onValueNodeChange:S,valueNodeHasChildren:T,onValueNodeHasChildrenChange:k,contentId:(0,m.B)(),value:O,onValueChange:F,open:A,onOpenChange:M,dir:P,triggerPointerDownPosRef:L,disabled:y,children:[(0,n.jsx)(R.Provider,{scope:t,children:(0,n.jsx)(D,{scope:e.__scopeSelect,onNativeOptionAdd:o.useCallback(e=>{_(t=>new Set(t).add(e))},[]),onNativeOptionRemove:o.useCallback(e=>{_(t=>{let r=new Set(t);return r.delete(e),r})},[]),children:r})}),H?(0,n.jsxs)(eS,{"aria-hidden":!0,required:g,tabIndex:-1,name:f,autoComplete:h,value:O,onChange:e=>F(e.target.value),disabled:y,form:v,children:[void 0===O?(0,n.jsx)("option",{value:""}):null,Array.from(Q)]},B):null]})})};Q.displayName=E;var _="SelectTrigger",B=o.forwardRef((e,t)=>{let{__scopeSelect:r,disabled:a=!1,...l}=e,i=K(r),d=L(_,r),u=d.disabled||a,p=(0,c.s)(t,d.onTriggerChange),f=A(r),h=o.useRef("touch"),[m,y,v]=ek(e=>{let t=f().filter(e=>!e.disabled),r=t.find(e=>e.value===d.value),n=eP(t,e,r);void 0!==n&&d.onValueChange(n.value)}),b=e=>{u||(d.onOpenChange(!0),v()),e&&(d.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,n.jsx)(x.Mz,{asChild:!0,...i,children:(0,n.jsx)(g.sG.button,{type:"button",role:"combobox","aria-controls":d.contentId,"aria-expanded":d.open,"aria-required":d.required,"aria-autocomplete":"none",dir:d.dir,"data-state":d.open?"open":"closed",disabled:u,"data-disabled":u?"":void 0,"data-placeholder":eT(d.value)?"":void 0,...l,ref:p,onClick:(0,s.mK)(l.onClick,e=>{e.currentTarget.focus(),"mouse"!==h.current&&b(e)}),onPointerDown:(0,s.mK)(l.onPointerDown,e=>{h.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(b(e),e.preventDefault())}),onKeyDown:(0,s.mK)(l.onKeyDown,e=>{let t=""!==m.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||y(e.key),(!t||" "!==e.key)&&k.includes(e.key)&&(b(),e.preventDefault())})})})});B.displayName=_;var z="SelectValue",q=o.forwardRef((e,t)=>{let{__scopeSelect:r,className:o,style:a,children:l,placeholder:s="",...i}=e,d=L(z,r),{onValueNodeHasChildrenChange:u}=d,p=void 0!==l,f=(0,c.s)(t,d.onValueNodeChange);return(0,j.N)(()=>{u(p)},[u,p]),(0,n.jsx)(g.sG.span,{...i,ref:f,style:{pointerEvents:"none"},children:eT(d.value)?(0,n.jsx)(n.Fragment,{children:s}):l})});q.displayName=z;var W=o.forwardRef((e,t)=>{let{__scopeSelect:r,children:o,...a}=e;return(0,n.jsx)(g.sG.span,{"aria-hidden":!0,...a,ref:t,children:o||"▼"})});W.displayName="SelectIcon";var U=e=>(0,n.jsx)(y.Z,{asChild:!0,...e});U.displayName="SelectPortal";var V="SelectContent",J=o.forwardRef((e,t)=>{let r=L(V,e.__scopeSelect),[l,s]=o.useState();return((0,j.N)(()=>{s(new DocumentFragment)},[]),r.open)?(0,n.jsx)(Y,{...e,ref:t}):l?a.createPortal((0,n.jsx)(G,{scope:e.__scopeSelect,children:(0,n.jsx)(R.Slot,{scope:e.__scopeSelect,children:(0,n.jsx)("div",{children:e.children})})}),l):null});J.displayName=V;var[G,$]=O(V),X=(0,v.TL)("SelectContent.RemoveScroll"),Y=o.forwardRef((e,t)=>{let{__scopeSelect:r,position:a="item-aligned",onCloseAutoFocus:l,onEscapeKeyDown:i,onPointerDownOutside:d,side:u,sideOffset:m,align:x,alignOffset:y,arrowPadding:g,collisionBoundary:v,collisionPadding:b,sticky:w,hideWhenDetached:j,avoidCollisions:N,...C}=e,k=L(V,r),[P,E]=o.useState(null),[R,M]=o.useState(null),O=(0,c.s)(t,e=>E(e)),[F,K]=o.useState(null),[I,D]=o.useState(null),H=A(r),[Q,_]=o.useState(!1),B=o.useRef(!1);o.useEffect(()=>{if(P)return(0,S.Eq)(P)},[P]),(0,f.Oh)();let z=o.useCallback(e=>{let[t,...r]=H().map(e=>e.ref.current),[n]=r.slice(-1),o=document.activeElement;for(let r of e)if(r===o||(null==r||r.scrollIntoView({block:"nearest"}),r===t&&R&&(R.scrollTop=0),r===n&&R&&(R.scrollTop=R.scrollHeight),null==r||r.focus(),document.activeElement!==o))return},[H,R]),q=o.useCallback(()=>z([F,P]),[z,F,P]);o.useEffect(()=>{Q&&q()},[Q,q]);let{onOpenChange:W,triggerPointerDownPosRef:U}=k;o.useEffect(()=>{if(P){let e={x:0,y:0},t=t=>{var r,n,o,a;e={x:Math.abs(Math.round(t.pageX)-(null!=(o=null==(r=U.current)?void 0:r.x)?o:0)),y:Math.abs(Math.round(t.pageY)-(null!=(a=null==(n=U.current)?void 0:n.y)?a:0))}},r=r=>{e.x<=10&&e.y<=10?r.preventDefault():P.contains(r.target)||W(!1),document.removeEventListener("pointermove",t),U.current=null};return null!==U.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",r,{capture:!0})}}},[P,W,U]),o.useEffect(()=>{let e=()=>W(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[W]);let[J,$]=ek(e=>{let t=H().filter(e=>!e.disabled),r=t.find(e=>e.ref.current===document.activeElement),n=eP(t,e,r);n&&setTimeout(()=>n.ref.current.focus())}),Y=o.useCallback((e,t,r)=>{let n=!B.current&&!r;(void 0!==k.value&&k.value===t||n)&&(K(e),n&&(B.current=!0))},[k.value]),et=o.useCallback(()=>null==P?void 0:P.focus(),[P]),er=o.useCallback((e,t,r)=>{let n=!B.current&&!r;(void 0!==k.value&&k.value===t||n)&&D(e)},[k.value]),en="popper"===a?ee:Z,eo=en===ee?{side:u,sideOffset:m,align:x,alignOffset:y,arrowPadding:g,collisionBoundary:v,collisionPadding:b,sticky:w,hideWhenDetached:j,avoidCollisions:N}:{};return(0,n.jsx)(G,{scope:r,content:P,viewport:R,onViewportChange:M,itemRefCallback:Y,selectedItem:F,onItemLeave:et,itemTextRefCallback:er,focusSelectedItem:q,selectedItemText:I,position:a,isPositioned:Q,searchRef:J,children:(0,n.jsx)(T.A,{as:X,allowPinchZoom:!0,children:(0,n.jsx)(h.n,{asChild:!0,trapped:k.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,s.mK)(l,e=>{var t;null==(t=k.trigger)||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,n.jsx)(p.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:d,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>k.onOpenChange(!1),children:(0,n.jsx)(en,{role:"listbox",id:k.contentId,"data-state":k.open?"open":"closed",dir:k.dir,onContextMenu:e=>e.preventDefault(),...C,...eo,onPlaced:()=>_(!0),ref:O,style:{display:"flex",flexDirection:"column",outline:"none",...C.style},onKeyDown:(0,s.mK)(C.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||$(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=H().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let r=e.target,n=t.indexOf(r);t=t.slice(n+1)}setTimeout(()=>z(t)),e.preventDefault()}})})})})})})});Y.displayName="SelectContentImpl";var Z=o.forwardRef((e,t)=>{let{__scopeSelect:r,onPlaced:a,...s}=e,i=L(V,r),d=$(V,r),[u,p]=o.useState(null),[f,h]=o.useState(null),m=(0,c.s)(t,e=>h(e)),x=A(r),y=o.useRef(!1),v=o.useRef(!0),{viewport:b,selectedItem:w,selectedItemText:N,focusSelectedItem:C}=d,S=o.useCallback(()=>{if(i.trigger&&i.valueNode&&u&&f&&b&&w&&N){let e=i.trigger.getBoundingClientRect(),t=f.getBoundingClientRect(),r=i.valueNode.getBoundingClientRect(),n=N.getBoundingClientRect();if("rtl"!==i.dir){let o=n.left-t.left,a=r.left-o,s=e.left-a,i=e.width+s,c=Math.max(i,t.width),d=window.innerWidth-10,p=(0,l.q)(a,[10,Math.max(10,d-c)]);u.style.minWidth=i+"px",u.style.left=p+"px"}else{let o=t.right-n.right,a=window.innerWidth-r.right-o,s=window.innerWidth-e.right-a,i=e.width+s,c=Math.max(i,t.width),d=window.innerWidth-10,p=(0,l.q)(a,[10,Math.max(10,d-c)]);u.style.minWidth=i+"px",u.style.right=p+"px"}let o=x(),s=window.innerHeight-20,c=b.scrollHeight,d=window.getComputedStyle(f),p=parseInt(d.borderTopWidth,10),h=parseInt(d.paddingTop,10),m=parseInt(d.borderBottomWidth,10),g=p+h+c+parseInt(d.paddingBottom,10)+m,v=Math.min(5*w.offsetHeight,g),j=window.getComputedStyle(b),C=parseInt(j.paddingTop,10),S=parseInt(j.paddingBottom,10),T=e.top+e.height/2-10,k=w.offsetHeight/2,P=p+h+(w.offsetTop+k);if(P<=T){let e=o.length>0&&w===o[o.length-1].ref.current;u.style.bottom="0px";let t=Math.max(s-T,k+(e?S:0)+(f.clientHeight-b.offsetTop-b.offsetHeight)+m);u.style.height=P+t+"px"}else{let e=o.length>0&&w===o[0].ref.current;u.style.top="0px";let t=Math.max(T,p+b.offsetTop+(e?C:0)+k);u.style.height=t+(g-P)+"px",b.scrollTop=P-T+b.offsetTop}u.style.margin="".concat(10,"px 0"),u.style.minHeight=v+"px",u.style.maxHeight=s+"px",null==a||a(),requestAnimationFrame(()=>y.current=!0)}},[x,i.trigger,i.valueNode,u,f,b,w,N,i.dir,a]);(0,j.N)(()=>S(),[S]);let[T,k]=o.useState();(0,j.N)(()=>{f&&k(window.getComputedStyle(f).zIndex)},[f]);let P=o.useCallback(e=>{e&&!0===v.current&&(S(),null==C||C(),v.current=!1)},[S,C]);return(0,n.jsx)(et,{scope:r,contentWrapper:u,shouldExpandOnScrollRef:y,onScrollButtonChange:P,children:(0,n.jsx)("div",{ref:p,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:T},children:(0,n.jsx)(g.sG.div,{...s,ref:m,style:{boxSizing:"border-box",maxHeight:"100%",...s.style}})})})});Z.displayName="SelectItemAlignedPosition";var ee=o.forwardRef((e,t)=>{let{__scopeSelect:r,align:o="start",collisionPadding:a=10,...l}=e,s=K(r);return(0,n.jsx)(x.UC,{...s,...l,ref:t,align:o,collisionPadding:a,style:{boxSizing:"border-box",...l.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});ee.displayName="SelectPopperPosition";var[et,er]=O(V,{}),en="SelectViewport",eo=o.forwardRef((e,t)=>{let{__scopeSelect:r,nonce:a,...l}=e,i=$(en,r),d=er(en,r),u=(0,c.s)(t,i.onViewportChange),p=o.useRef(0);return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:a}),(0,n.jsx)(R.Slot,{scope:r,children:(0,n.jsx)(g.sG.div,{"data-radix-select-viewport":"",role:"presentation",...l,ref:u,style:{position:"relative",flex:1,overflow:"hidden auto",...l.style},onScroll:(0,s.mK)(l.onScroll,e=>{let t=e.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:n}=d;if((null==n?void 0:n.current)&&r){let e=Math.abs(p.current-t.scrollTop);if(e>0){let n=window.innerHeight-20,o=Math.max(parseFloat(r.style.minHeight),parseFloat(r.style.height));if(o<n){let a=o+e,l=Math.min(n,a),s=a-l;r.style.height=l+"px","0px"===r.style.bottom&&(t.scrollTop=s>0?s:0,r.style.justifyContent="flex-end")}}}p.current=t.scrollTop})})})]})});eo.displayName=en;var ea="SelectGroup",[el,es]=O(ea);o.forwardRef((e,t)=>{let{__scopeSelect:r,...o}=e,a=(0,m.B)();return(0,n.jsx)(el,{scope:r,id:a,children:(0,n.jsx)(g.sG.div,{role:"group","aria-labelledby":a,...o,ref:t})})}).displayName=ea;var ei="SelectLabel",ec=o.forwardRef((e,t)=>{let{__scopeSelect:r,...o}=e,a=es(ei,r);return(0,n.jsx)(g.sG.div,{id:a.id,...o,ref:t})});ec.displayName=ei;var ed="SelectItem",[eu,ep]=O(ed),ef=o.forwardRef((e,t)=>{let{__scopeSelect:r,value:a,disabled:l=!1,textValue:i,...d}=e,u=L(ed,r),p=$(ed,r),f=u.value===a,[h,x]=o.useState(null!=i?i:""),[y,v]=o.useState(!1),b=(0,c.s)(t,e=>{var t;return null==(t=p.itemRefCallback)?void 0:t.call(p,e,a,l)}),w=(0,m.B)(),j=o.useRef("touch"),N=()=>{l||(u.onValueChange(a),u.onOpenChange(!1))};if(""===a)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,n.jsx)(eu,{scope:r,value:a,disabled:l,textId:w,isSelected:f,onItemTextChange:o.useCallback(e=>{x(t=>{var r;return t||(null!=(r=null==e?void 0:e.textContent)?r:"").trim()})},[]),children:(0,n.jsx)(R.ItemSlot,{scope:r,value:a,disabled:l,textValue:h,children:(0,n.jsx)(g.sG.div,{role:"option","aria-labelledby":w,"data-highlighted":y?"":void 0,"aria-selected":f&&y,"data-state":f?"checked":"unchecked","aria-disabled":l||void 0,"data-disabled":l?"":void 0,tabIndex:l?void 0:-1,...d,ref:b,onFocus:(0,s.mK)(d.onFocus,()=>v(!0)),onBlur:(0,s.mK)(d.onBlur,()=>v(!1)),onClick:(0,s.mK)(d.onClick,()=>{"mouse"!==j.current&&N()}),onPointerUp:(0,s.mK)(d.onPointerUp,()=>{"mouse"===j.current&&N()}),onPointerDown:(0,s.mK)(d.onPointerDown,e=>{j.current=e.pointerType}),onPointerMove:(0,s.mK)(d.onPointerMove,e=>{if(j.current=e.pointerType,l){var t;null==(t=p.onItemLeave)||t.call(p)}else"mouse"===j.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,s.mK)(d.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null==(t=p.onItemLeave)||t.call(p)}}),onKeyDown:(0,s.mK)(d.onKeyDown,e=>{var t;((null==(t=p.searchRef)?void 0:t.current)===""||" "!==e.key)&&(P.includes(e.key)&&N()," "===e.key&&e.preventDefault())})})})})});ef.displayName=ed;var eh="SelectItemText",em=o.forwardRef((e,t)=>{let{__scopeSelect:r,className:l,style:s,...i}=e,d=L(eh,r),u=$(eh,r),p=ep(eh,r),f=H(eh,r),[h,m]=o.useState(null),x=(0,c.s)(t,e=>m(e),p.onItemTextChange,e=>{var t;return null==(t=u.itemTextRefCallback)?void 0:t.call(u,e,p.value,p.disabled)}),y=null==h?void 0:h.textContent,v=o.useMemo(()=>(0,n.jsx)("option",{value:p.value,disabled:p.disabled,children:y},p.value),[p.disabled,p.value,y]),{onNativeOptionAdd:b,onNativeOptionRemove:w}=f;return(0,j.N)(()=>(b(v),()=>w(v)),[b,w,v]),(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(g.sG.span,{id:p.textId,...i,ref:x}),p.isSelected&&d.valueNode&&!d.valueNodeHasChildren?a.createPortal(i.children,d.valueNode):null]})});em.displayName=eh;var ex="SelectItemIndicator",ey=o.forwardRef((e,t)=>{let{__scopeSelect:r,...o}=e;return ep(ex,r).isSelected?(0,n.jsx)(g.sG.span,{"aria-hidden":!0,...o,ref:t}):null});ey.displayName=ex;var eg="SelectScrollUpButton",ev=o.forwardRef((e,t)=>{let r=$(eg,e.__scopeSelect),a=er(eg,e.__scopeSelect),[l,s]=o.useState(!1),i=(0,c.s)(t,a.onScrollButtonChange);return(0,j.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){s(t.scrollTop>0)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),l?(0,n.jsx)(ej,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});ev.displayName=eg;var eb="SelectScrollDownButton",ew=o.forwardRef((e,t)=>{let r=$(eb,e.__scopeSelect),a=er(eb,e.__scopeSelect),[l,s]=o.useState(!1),i=(0,c.s)(t,a.onScrollButtonChange);return(0,j.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;s(Math.ceil(t.scrollTop)<e)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),l?(0,n.jsx)(ej,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});ew.displayName=eb;var ej=o.forwardRef((e,t)=>{let{__scopeSelect:r,onAutoScroll:a,...l}=e,i=$("SelectScrollButton",r),c=o.useRef(null),d=A(r),u=o.useCallback(()=>{null!==c.current&&(window.clearInterval(c.current),c.current=null)},[]);return o.useEffect(()=>()=>u(),[u]),(0,j.N)(()=>{var e;let t=d().find(e=>e.ref.current===document.activeElement);null==t||null==(e=t.ref.current)||e.scrollIntoView({block:"nearest"})},[d]),(0,n.jsx)(g.sG.div,{"aria-hidden":!0,...l,ref:t,style:{flexShrink:0,...l.style},onPointerDown:(0,s.mK)(l.onPointerDown,()=>{null===c.current&&(c.current=window.setInterval(a,50))}),onPointerMove:(0,s.mK)(l.onPointerMove,()=>{var e;null==(e=i.onItemLeave)||e.call(i),null===c.current&&(c.current=window.setInterval(a,50))}),onPointerLeave:(0,s.mK)(l.onPointerLeave,()=>{u()})})}),eN=o.forwardRef((e,t)=>{let{__scopeSelect:r,...o}=e;return(0,n.jsx)(g.sG.div,{"aria-hidden":!0,...o,ref:t})});eN.displayName="SelectSeparator";var eC="SelectArrow";o.forwardRef((e,t)=>{let{__scopeSelect:r,...o}=e,a=K(r),l=L(eC,r),s=$(eC,r);return l.open&&"popper"===s.position?(0,n.jsx)(x.i3,{...a,...o,ref:t}):null}).displayName=eC;var eS=o.forwardRef((e,t)=>{let{__scopeSelect:r,value:a,...l}=e,s=o.useRef(null),i=(0,c.s)(t,s),d=(0,N.Z)(a);return o.useEffect(()=>{let e=s.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(d!==a&&t){let r=new Event("change",{bubbles:!0});t.call(e,a),e.dispatchEvent(r)}},[d,a]),(0,n.jsx)(g.sG.select,{...l,style:{...C.Qg,...l.style},ref:i,defaultValue:a})});function eT(e){return""===e||void 0===e}function ek(e){let t=(0,b.c)(e),r=o.useRef(""),n=o.useRef(0),a=o.useCallback(e=>{let o=r.current+e;t(o),function e(t){r.current=t,window.clearTimeout(n.current),""!==t&&(n.current=window.setTimeout(()=>e(""),1e3))}(o)},[t]),l=o.useCallback(()=>{r.current="",window.clearTimeout(n.current)},[]);return o.useEffect(()=>()=>window.clearTimeout(n.current),[]),[r,a,l]}function eP(e,t,r){var n,o;let a=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,l=r?e.indexOf(r):-1,s=(n=e,o=Math.max(l,0),n.map((e,t)=>n[(o+t)%n.length]));1===a.length&&(s=s.filter(e=>e!==r));let i=s.find(e=>e.textValue.toLowerCase().startsWith(a.toLowerCase()));return i!==r?i:void 0}eS.displayName="SelectBubbleInput";var eE=r(91037),eR=r(75889);let eA=Q,eM=q,eO=(0,o.forwardRef)(({className:e,children:t,...r},o)=>(0,n.jsxs)(B,{ref:o,className:(0,eR.QP)("flex items-center w-full rounded-md border p-2 gap-2 text-start text-[13px] text-fd-secondary-foreground bg-fd-secondary hover:bg-fd-accent focus:outline-none focus:ring focus:ring-fd-ring disabled:cursor-not-allowed disabled:opacity-50",e),...r,children:[t,(0,n.jsx)(W,{asChild:!0,children:(0,n.jsx)(eE.yQ,{className:"ms-auto size-3.5 text-fd-muted-foreground shrink-0"})})]}));eO.displayName=B.displayName;let eF=(0,o.forwardRef)(({className:e,...t},r)=>(0,n.jsx)(ev,{ref:r,className:(0,eR.QP)("flex items-center justify-center py-1",e),...t,children:(0,n.jsx)(eE.rX,{className:"size-4"})}));eF.displayName=ev.displayName;let eK=(0,o.forwardRef)(({className:e,...t},r)=>(0,n.jsx)(ew,{ref:r,className:(0,eR.QP)("flex items-center justify-center py-1",e),...t,children:(0,n.jsx)(eE.yQ,{className:"size-4"})}));eK.displayName=ew.displayName;let eI=(0,o.forwardRef)(({className:e,children:t,position:r,...o},a)=>(0,n.jsx)(U,{children:(0,n.jsxs)(J,{ref:a,className:(0,eR.QP)("z-50 overflow-hidden rounded-lg border bg-fd-popover text-fd-popover-foreground shadow-md",e),position:r,...o,children:[(0,n.jsx)(eF,{}),(0,n.jsx)(eo,{className:"p-1",children:t}),(0,n.jsx)(eK,{})]})}));eI.displayName=J.displayName,(0,o.forwardRef)(({className:e,...t},r)=>(0,n.jsx)(ec,{ref:r,className:(0,eR.QP)("py-1.5 pe-2 ps-6 text-sm font-semibold",e),...t})).displayName=ec.displayName;let eL=(0,o.forwardRef)(({className:e,children:t,...r},o)=>(0,n.jsxs)(ef,{ref:o,className:(0,eR.QP)("flex select-none flex-row items-center rounded-md py-1.5 px-2 text-[13px] outline-none focus:bg-fd-accent focus:text-fd-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...r,children:[(0,n.jsx)(em,{children:t}),(0,n.jsx)(ey,{className:"ms-auto",children:(0,n.jsx)(eE.Jl,{className:"size-3.5 text-fd-primary"})})]}));eL.displayName=ef.displayName,(0,o.forwardRef)(({className:e,...t},r)=>(0,n.jsx)(eN,{ref:r,className:(0,eR.QP)("my-1 h-px bg-fd-muted",e),...t})).displayName=eN.displayName},69213:(e,t,r)=>{"use strict";r.r(t),r.d(t,{AccordionContent:()=>c,AccordionHeader:()=>d,AccordionItem:()=>i,AccordionTrigger:()=>u,Accordions:()=>s});var n=r(22342),o=r(58609),a=r(91037),l=r(75889);function s(e){return(0,n.jsx)(o.bL,{...e,className:(0,l.QP)("divide-y divide-fd-border",e.className)})}function i(e){return(0,n.jsx)(o.q7,{...e,className:(0,l.QP)("scroll-m-20",e.className),children:e.children})}function c(e){return(0,n.jsx)(o.UC,{...e,className:(0,l.QP)("overflow-hidden data-[state=closed]:animate-fd-accordion-up data-[state=open]:animate-fd-accordion-down",e.className),children:e.children})}function d(e){return(0,n.jsx)(o.Y9,{...e,className:(0,l.QP)("not-prose flex py-2 text-fd-foreground font-medium",e.className),children:e.children})}function u(e){return(0,n.jsxs)(o.l9,{...e,className:(0,l.QP)("flex flex-1 items-center gap-1 text-start group/accordion focus-visible:outline-none",e.className),children:[(0,n.jsx)(a.c_,{className:"size-3.5 text-fd-muted-foreground shrink-0 transition-transform group-data-[state=open]/accordion:rotate-90"}),e.children]})}},69712:(e,t,r)=>{"use strict";r.d(t,{S:()=>l,b:()=>a});let n={spaceDelimited:" ",pipeDelimited:"|"},o={label:".",matrix:";"};function a(e,t=1){return e.split("\n").map(e=>"  ".repeat(t)+e).join("\n")}function l(e,t,r){let a={method:e.method,body:e.body,bodyMediaType:e.bodyMediaType};for(let l of["cookie","query","header","path"]){let s={};for(let a in e[l]){let i=e[l][a];if(null==i)continue;let c=r.find(e=>e.name===a&&e.in===l);!function(e,r,a={},l){if(!l)return a[e]={value:String(r)};let s=function(e){if(e.content){for(let r in e.content)if(r in t)return e=>String(t[r].encode({body:e}))}}(l);if(s)return a[e]={value:s(r)};let i=l.explode??!0,c="",d=",";if("path"===l.in){let e=l.style??"simple";e in o&&(c=o[e],i&&(d=c))}if(Array.isArray(r)){if(i&&"header"!==l.in&&"cookie"!==l.in)return a[e]={value:c+r.map(String)};if("query"===l.in){let e=l.style??"form";e in n&&(d=n[e])}return a[e]={value:c+r.map(String).join(d)}}if("object"==typeof r&&r)return i&&("header"===l.in||"path"===l.in)?a[e]={value:c+Object.entries(r).map(([e,t])=>`${e}=${t}`).join(d)}:i||"deepObject"===l.style?!function e(t,r,n,o){for(let a in r){let l=r[a];if(null==l)continue;let s=n?`${t}[${a}]`:a;if(!n||"object"!=typeof l){o[s]={value:String(l)};continue}e(s,r,n,o)}}(e,r,"deepObject"===l.style,a):a[e]={value:c+Object.entries(r).flat().join(d)};a[e]={value:c+String(r)}}(a,i,s,c)}a[l]=s}return a}},71797:(e,t,r)=>{"use strict";r.d(t,{$:()=>a,N:()=>o});var n=r(77944);function o(e,t="application/json"){if("string"==typeof e)return e;if("application/json"===t)return JSON.stringify(e,null,2);if("application/x-ndjson"===t)return Array.isArray(e)?e.map(e=>JSON.stringify(e)).join("\n"):JSON.stringify(e,null,2);if("application/x-www-form-urlencoded"===t){let t=new URLSearchParams;if("object"!=typeof e)throw Error(`For url encoded data, \`value\` must be an object, but received: ${typeof e}`);for(let r in e)e[r]&&t.set(r,String(e[r]));return t.toString()}return n(e,{compact:!0,spaces:2})}function a(e,t){return t?`${t}${e.replaceAll(t,`\\${t}`)}${t}`:JSON.stringify(e)}},74604:(e,t,r)=>{"use strict";r.d(t,{UC:()=>K,ZL:()=>F,bL:()=>M,l9:()=>O});var n,o=r(33670),a=r(92556),l=r(3468),s=r(23558),i=r(94446),c=r(57641),d=r(75433),u=r(76842),p=r(97602),f=r(44831),h=r(22342),m="HoverCard",[x,y]=(0,l.A)(m,[c.Bk]),g=(0,c.Bk)(),[v,b]=x(m),w=e=>{let{__scopeHoverCard:t,children:r,open:n,defaultOpen:a,onOpenChange:l,openDelay:i=700,closeDelay:d=300}=e,u=g(t),p=o.useRef(0),f=o.useRef(0),x=o.useRef(!1),y=o.useRef(!1),[b,w]=(0,s.i)({prop:n,defaultProp:null!=a&&a,onChange:l,caller:m}),j=o.useCallback(()=>{clearTimeout(f.current),p.current=window.setTimeout(()=>w(!0),i)},[i,w]),N=o.useCallback(()=>{clearTimeout(p.current),x.current||y.current||(f.current=window.setTimeout(()=>w(!1),d))},[d,w]),C=o.useCallback(()=>w(!1),[w]);return o.useEffect(()=>()=>{clearTimeout(p.current),clearTimeout(f.current)},[]),(0,h.jsx)(v,{scope:t,open:b,onOpenChange:w,onOpen:j,onClose:N,onDismiss:C,hasSelectionRef:x,isPointerDownOnContentRef:y,children:(0,h.jsx)(c.bL,{...u,children:r})})};w.displayName=m;var j="HoverCardTrigger",N=o.forwardRef((e,t)=>{let{__scopeHoverCard:r,...n}=e,o=b(j,r),l=g(r);return(0,h.jsx)(c.Mz,{asChild:!0,...l,children:(0,h.jsx)(p.sG.a,{"data-state":o.open?"open":"closed",...n,ref:t,onPointerEnter:(0,a.mK)(e.onPointerEnter,A(o.onOpen)),onPointerLeave:(0,a.mK)(e.onPointerLeave,A(o.onClose)),onFocus:(0,a.mK)(e.onFocus,o.onOpen),onBlur:(0,a.mK)(e.onBlur,o.onClose),onTouchStart:(0,a.mK)(e.onTouchStart,e=>e.preventDefault())})})});N.displayName=j;var C="HoverCardPortal",[S,T]=x(C,{forceMount:void 0}),k=e=>{let{__scopeHoverCard:t,forceMount:r,children:n,container:o}=e,a=b(C,t);return(0,h.jsx)(S,{scope:t,forceMount:r,children:(0,h.jsx)(u.C,{present:r||a.open,children:(0,h.jsx)(d.Z,{asChild:!0,container:o,children:n})})})};k.displayName=C;var P="HoverCardContent",E=o.forwardRef((e,t)=>{let r=T(P,e.__scopeHoverCard),{forceMount:n=r.forceMount,...o}=e,l=b(P,e.__scopeHoverCard);return(0,h.jsx)(u.C,{present:n||l.open,children:(0,h.jsx)(R,{"data-state":l.open?"open":"closed",...o,onPointerEnter:(0,a.mK)(e.onPointerEnter,A(l.onOpen)),onPointerLeave:(0,a.mK)(e.onPointerLeave,A(l.onClose)),ref:t})})});E.displayName=P;var R=o.forwardRef((e,t)=>{let{__scopeHoverCard:r,onEscapeKeyDown:l,onPointerDownOutside:s,onFocusOutside:d,onInteractOutside:u,...p}=e,m=b(P,r),x=g(r),y=o.useRef(null),v=(0,i.s)(t,y),[w,j]=o.useState(!1);return o.useEffect(()=>{if(w){let e=document.body;return n=e.style.userSelect||e.style.webkitUserSelect,e.style.userSelect="none",e.style.webkitUserSelect="none",()=>{e.style.userSelect=n,e.style.webkitUserSelect=n}}},[w]),o.useEffect(()=>{if(y.current){let e=()=>{j(!1),m.isPointerDownOnContentRef.current=!1,setTimeout(()=>{var e;(null==(e=document.getSelection())?void 0:e.toString())!==""&&(m.hasSelectionRef.current=!0)})};return document.addEventListener("pointerup",e),()=>{document.removeEventListener("pointerup",e),m.hasSelectionRef.current=!1,m.isPointerDownOnContentRef.current=!1}}},[m.isPointerDownOnContentRef,m.hasSelectionRef]),o.useEffect(()=>{y.current&&(function(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP});for(;r.nextNode();)t.push(r.currentNode);return t})(y.current).forEach(e=>e.setAttribute("tabindex","-1"))}),(0,h.jsx)(f.qW,{asChild:!0,disableOutsidePointerEvents:!1,onInteractOutside:u,onEscapeKeyDown:l,onPointerDownOutside:s,onFocusOutside:(0,a.mK)(d,e=>{e.preventDefault()}),onDismiss:m.onDismiss,children:(0,h.jsx)(c.UC,{...x,...p,onPointerDown:(0,a.mK)(p.onPointerDown,e=>{e.currentTarget.contains(e.target)&&j(!0),m.hasSelectionRef.current=!1,m.isPointerDownOnContentRef.current=!0}),ref:v,style:{...p.style,userSelect:w?"text":void 0,WebkitUserSelect:w?"text":void 0,"--radix-hover-card-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-hover-card-content-available-width":"var(--radix-popper-available-width)","--radix-hover-card-content-available-height":"var(--radix-popper-available-height)","--radix-hover-card-trigger-width":"var(--radix-popper-anchor-width)","--radix-hover-card-trigger-height":"var(--radix-popper-anchor-height)"}})})});function A(e){return t=>"touch"===t.pointerType?void 0:e()}o.forwardRef((e,t)=>{let{__scopeHoverCard:r,...n}=e,o=g(r);return(0,h.jsx)(c.i3,{...o,...n,ref:t})}).displayName="HoverCardArrow";var M=w,O=N,F=k,K=E},75426:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(71847).A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},77944:(e,t,r)=>{var n,o,a=r(41017),l=r(56284).isArray;function s(e,t,r){return(!r&&e.spaces?"\n":"")+Array(t+1).join(e.spaces)}function i(e,t,r){if(t.ignoreAttributes)return"";"attributesFn"in t&&(e=t.attributesFn(e,o,n));var a,l,i,c,d=[];for(a in e)e.hasOwnProperty(a)&&null!==e[a]&&void 0!==e[a]&&(c=t.noQuotesForNativeAttributes&&"string"!=typeof e[a]?"":'"',l=(l=""+e[a]).replace(/"/g,"&quot;"),i="attributeNameFn"in t?t.attributeNameFn(a,l,o,n):a,d.push(t.spaces&&t.indentAttributes?s(t,r+1,!1):" "),d.push(i+"="+c+("attributeValueFn"in t?t.attributeValueFn(l,a,o,n):l)+c));return e&&Object.keys(e).length&&t.spaces&&t.indentAttributes&&d.push(s(t,r,!1)),d.join("")}function c(e,t,r){return n=e,o="xml",t.ignoreDeclaration?"":"<?xml"+i(e[t.attributesKey],t,r)+"?>"}function d(e,t,r){if(t.ignoreInstruction)return"";for(a in e)if(e.hasOwnProperty(a))break;var a,l="instructionNameFn"in t?t.instructionNameFn(a,e[a],o,n):a;if("object"==typeof e[a])return n=e,o=l,"<?"+l+i(e[a][t.attributesKey],t,r)+"?>";var s=e[a]?e[a]:"";return"instructionFn"in t&&(s=t.instructionFn(s,a,o,n)),"<?"+l+(s?" "+s:"")+"?>"}function u(e,t){return t.ignoreComment?"":"\x3c!--"+("commentFn"in t?t.commentFn(e,o,n):e)+"--\x3e"}function p(e,t){return t.ignoreCdata?"":"<![CDATA["+("cdataFn"in t?t.cdataFn(e,o,n):e.replace("]]>","]]]]><![CDATA[>"))+"]]>"}function f(e,t){return t.ignoreDoctype?"":"<!DOCTYPE "+("doctypeFn"in t?t.doctypeFn(e,o,n):e)+">"}function h(e,t){return t.ignoreText?"":(e=(e=(e=""+e).replace(/&amp;/g,"&")).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;"),"textFn"in t?t.textFn(e,o,n):e)}function m(e,t,r){var n;for(n in e)if(e.hasOwnProperty(n))switch(n){case t.parentKey:case t.attributesKey:break;case t.textKey:if(t.indentText||r)return!0;break;case t.cdataKey:if(t.indentCdata||r)return!0;break;case t.instructionKey:if(t.indentInstruction||r)return!0;break;case t.doctypeKey:case t.commentKey:default:return!0}return!1}e.exports=function(e,t){r=t,x=a.copyOptions(r),a.ensureFlagExists("ignoreDeclaration",x),a.ensureFlagExists("ignoreInstruction",x),a.ensureFlagExists("ignoreAttributes",x),a.ensureFlagExists("ignoreText",x),a.ensureFlagExists("ignoreComment",x),a.ensureFlagExists("ignoreCdata",x),a.ensureFlagExists("ignoreDoctype",x),a.ensureFlagExists("compact",x),a.ensureFlagExists("indentText",x),a.ensureFlagExists("indentCdata",x),a.ensureFlagExists("indentAttributes",x),a.ensureFlagExists("indentInstruction",x),a.ensureFlagExists("fullTagEmptyElement",x),a.ensureFlagExists("noQuotesForNativeAttributes",x),a.ensureSpacesExists(x),"number"==typeof x.spaces&&(x.spaces=Array(x.spaces+1).join(" ")),a.ensureKeyExists("declaration",x),a.ensureKeyExists("instruction",x),a.ensureKeyExists("attributes",x),a.ensureKeyExists("text",x),a.ensureKeyExists("comment",x),a.ensureKeyExists("cdata",x),a.ensureKeyExists("doctype",x),a.ensureKeyExists("type",x),a.ensureKeyExists("name",x),a.ensureKeyExists("elements",x),a.checkFnExists("doctype",x),a.checkFnExists("instruction",x),a.checkFnExists("cdata",x),a.checkFnExists("comment",x),a.checkFnExists("text",x),a.checkFnExists("instructionName",x),a.checkFnExists("elementName",x),a.checkFnExists("attributeName",x),a.checkFnExists("attributeValue",x),a.checkFnExists("attributes",x),a.checkFnExists("fullTagEmptyElement",x),t=x;var r,x,y=[];return n=e,o="_root_",t.compact?y.push(function e(t,r,a,x){var y,g,v,b=[];for(g in t)if(t.hasOwnProperty(g))for(y=0,v=l(t[g])?t[g]:[t[g]];y<v.length;++y){switch(g){case r.declarationKey:b.push(c(v[y],r,a));break;case r.instructionKey:b.push((r.indentInstruction?s(r,a,x):"")+d(v[y],r,a));break;case r.attributesKey:case r.parentKey:break;case r.textKey:b.push((r.indentText?s(r,a,x):"")+h(v[y],r));break;case r.cdataKey:b.push((r.indentCdata?s(r,a,x):"")+p(v[y],r));break;case r.doctypeKey:b.push(s(r,a,x)+f(v[y],r));break;case r.commentKey:b.push(s(r,a,x)+u(v[y],r));break;default:b.push(s(r,a,x)+function(t,r,a,l,c){n=t,o=r;var d="elementNameFn"in a?a.elementNameFn(r,t):r;if(null==t||""===t)return"fullTagEmptyElementFn"in a&&a.fullTagEmptyElementFn(r,t)||a.fullTagEmptyElement?"<"+d+"></"+d+">":"<"+d+"/>";var u=[];if(r){if(u.push("<"+d),"object"!=typeof t)return u.push(">"+h(t,a)+"</"+d+">"),u.join("");t[a.attributesKey]&&u.push(i(t[a.attributesKey],a,l));var p=m(t,a,!0)||t[a.attributesKey]&&"preserve"===t[a.attributesKey]["xml:space"];if(p||(p="fullTagEmptyElementFn"in a?a.fullTagEmptyElementFn(r,t):a.fullTagEmptyElement),!p)return u.push("/>"),u.join("");u.push(">")}return u.push(e(t,a,l+1,!1)),n=t,o=r,r&&u.push((c?s(a,l,!1):"")+"</"+d+">"),u.join("")}(v[y],g,r,a,m(v[y],r)))}x=x&&!b.length}return b.join("")}(e,t,0,!0)):(e[t.declarationKey]&&y.push(c(e[t.declarationKey],t,0)),e[t.elementsKey]&&e[t.elementsKey].length&&y.push(function e(t,r,a,l){return t.reduce(function(t,c){var m,x,y,g=s(r,a,l&&!t);switch(c.type){case"element":return t+g+(n=c,o=c.name,m=[],x="elementNameFn"in r?r.elementNameFn(c.name,c):c.name,m.push("<"+x),c[r.attributesKey]&&m.push(i(c[r.attributesKey],r,a)),(y=c[r.elementsKey]&&c[r.elementsKey].length||c[r.attributesKey]&&"preserve"===c[r.attributesKey]["xml:space"])||(y="fullTagEmptyElementFn"in r?r.fullTagEmptyElementFn(c.name,c):r.fullTagEmptyElement),y?(m.push(">"),c[r.elementsKey]&&c[r.elementsKey].length&&(m.push(e(c[r.elementsKey],r,a+1)),n=c,o=c.name),m.push(r.spaces&&function(e,t){var r;if(e.elements&&e.elements.length)for(r=0;r<e.elements.length;++r)switch(e.elements[r][t.typeKey]){case"text":if(t.indentText)return!0;break;case"cdata":if(t.indentCdata)return!0;break;case"instruction":if(t.indentInstruction)return!0;break;default:return!0}return!1}(c,r)?"\n"+Array(a+1).join(r.spaces):""),m.push("</"+x+">")):m.push("/>"),m.join(""));case"comment":return t+g+u(c[r.commentKey],r);case"doctype":return t+g+f(c[r.doctypeKey],r);case"cdata":return t+(r.indentCdata?g:"")+p(c[r.cdataKey],r);case"text":return t+(r.indentText?g:"")+h(c[r.textKey],r);case"instruction":var v={};return v[c[r.nameKey]]=c[r.attributesKey]?c:c[r.instructionKey],t+(r.indentInstruction?g:"")+d(v,r,a)}},"")}(e[t.elementsKey],t,0,!y.length))),y.join("")}},78108:(e,t,r)=>{"use strict";r.d(t,{Z:()=>o});var n=r(33670);function o(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},81351:(e,t,r)=>{"use strict";r.d(t,{CopyResponseTypeScript:()=>i});var n=r(22342),o=r(58807),a=r(81501),l=r(75889),s=r(91037);function i(e){let{code:t}=e,[r,i]=(0,o.x)(()=>{navigator.clipboard.writeText(t)});return(0,n.jsxs)("div",{className:"flex items-start justify-between gap-2 bg-fd-card text-fd-card-foreground border rounded-xl p-3 not-prose mb-4 last:mb-0",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"font-medium text-sm mb-2",children:"TypeScript Definitions"}),(0,n.jsx)("p",{className:"text-xs text-fd-muted-foreground",children:"Use the response body type in TypeScript."})]}),(0,n.jsxs)("button",{onClick:i,className:(0,l.QP)((0,a.r)({color:"secondary",className:"p-2 gap-2",size:"sm"})),children:[r?(0,n.jsx)(s.Jl,{className:"size-3.5"}):(0,n.jsx)(s.QR,{className:"size-3.5"}),"Copy"]})]})}},85765:(e,t,r)=>{"use strict";r.d(t,{TOCItems:()=>h,TOCProvider:()=>p,TOCScrollArea:()=>f,a:()=>u});var n=r(22342),o=r(2421),a=r(33670),l=r(75889),s=r(53878),i=r(50440),c=r(58670);let d=(0,a.createContext)([]);function u(){return(0,a.useContext)(d)}function p(e){let{toc:t,children:r,...a}=e;return(0,n.jsx)(d,{value:t,children:(0,n.jsx)(o.NQ,{toc:t,...a,children:r})})}function f(e){let{ref:t,className:r,...s}=e,i=(0,a.useRef)(null);return(0,n.jsx)("div",{ref:(0,c.P)(i,t),className:(0,l.QP)("relative min-h-0 text-sm ms-px overflow-auto [scrollbar-width:none] [mask-image:linear-gradient(to_bottom,transparent,white_16px,white_calc(100%-16px),transparent)] py-3",r),...s,children:(0,n.jsx)(o.N2,{containerRef:i,children:s.children})})}function h(e){let{ref:t,className:r,...o}=e,d=(0,a.useRef)(null),p=u(),{text:f}=(0,s.s9)();return 0===p.length?(0,n.jsx)("div",{className:"rounded-lg border bg-fd-card p-3 text-xs text-fd-muted-foreground",children:f.tocNoHeadings}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(i.j,{containerRef:d,className:"absolute top-(--fd-top) h-(--fd-height) w-px bg-fd-primary transition-all"}),(0,n.jsx)("div",{ref:(0,c.P)(t,d),className:(0,l.QP)("flex flex-col border-s border-fd-foreground/10",r),...o,children:p.map(e=>(0,n.jsx)(m,{item:e},e.url))})]})}function m(e){let{item:t}=e;return(0,n.jsx)(o.Cz,{href:t.url,className:(0,l.QP)("prose py-1.5 text-sm text-fd-muted-foreground transition-colors [overflow-wrap:anywhere] first:pt-0 last:pb-0 data-[active=true]:text-fd-primary",t.depth<=2&&"ps-3",3===t.depth&&"ps-6",t.depth>=4&&"ps-8"),children:t.title})}},90441:(e,t,r)=>{"use strict";r.d(t,{default:()=>u});var n=r(22342),o=r(2421),a=r(33670),l=r(75889),s=r(50440),i=r(85765),c=r(58670),d=r(53878);function u(e){let{ref:t,className:r,...o}=e,u=(0,a.useRef)(null),h=(0,i.a)(),{text:m}=(0,d.s9)(),[x,y]=(0,a.useState)();return((0,a.useEffect)(()=>{if(!u.current)return;let e=u.current;function t(){if(0===e.clientHeight)return;let t=0,r=0,n=[];for(let o=0;o<h.length;o++){let a=e.querySelector('a[href="#'.concat(h[o].url.slice(1),'"]'));if(!a)continue;let l=getComputedStyle(a),s=p(h[o].depth)+1,i=a.offsetTop+parseFloat(l.paddingTop),c=a.offsetTop+a.clientHeight-parseFloat(l.paddingBottom);t=Math.max(s,t),r=Math.max(r,c),n.push("".concat(0===o?"M":"L").concat(s," ").concat(i)),n.push("L".concat(s," ").concat(c))}y({path:n.join(" "),width:t+1,height:r})}let r=new ResizeObserver(t);return t(),r.observe(e),()=>{r.disconnect()}},[h]),0===h.length)?(0,n.jsx)("div",{className:"rounded-lg border bg-fd-card p-3 text-xs text-fd-muted-foreground",children:m.tocNoHeadings}):(0,n.jsxs)(n.Fragment,{children:[x?(0,n.jsx)("div",{className:"absolute start-0 top-0 rtl:-scale-x-100",style:{width:x.width,height:x.height,maskImage:'url("data:image/svg+xml,'.concat(encodeURIComponent('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 '.concat(x.width," ").concat(x.height,'"><path d="').concat(x.path,'" stroke="black" stroke-width="1" fill="none" /></svg>')),'")')},children:(0,n.jsx)(s.j,{containerRef:u,className:"mt-(--fd-top) h-(--fd-height) bg-fd-primary transition-all"})}):null,(0,n.jsx)("div",{ref:(0,c.P)(u,t),className:(0,l.QP)("flex flex-col",r),...o,children:h.map((e,t)=>{var r,o;return(0,n.jsx)(f,{item:e,upper:null==(r=h[t-1])?void 0:r.depth,lower:null==(o=h[t+1])?void 0:o.depth},e.url)})})]})}function p(e){return 10*(e>=3)}function f(e){var t;let{item:r,upper:a=r.depth,lower:s=r.depth}=e,i=p(r.depth),c=p(a),d=p(s);return(0,n.jsxs)(o.Cz,{href:r.url,style:{paddingInlineStart:(t=r.depth)<=2?14:3===t?26:36},className:"prose relative py-1.5 text-sm text-fd-muted-foreground hover:text-fd-accent-foreground transition-colors [overflow-wrap:anywhere] first:pt-0 last:pb-0 data-[active=true]:text-fd-primary",children:[i!==c?(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16",className:"absolute -top-1.5 start-0 size-4 rtl:-scale-x-100",children:(0,n.jsx)("line",{x1:c,y1:"0",x2:i,y2:"12",className:"stroke-fd-foreground/10",strokeWidth:"1"})}):null,(0,n.jsx)("div",{className:(0,l.QP)("absolute inset-y-0 w-px bg-fd-foreground/10",i!==c&&"top-1.5",i!==d&&"bottom-1.5"),style:{insetInlineStart:i}}),r.title]})}},91037:(e,t,r)=>{"use strict";r.d(t,{FW:()=>n.FW,Jl:()=>n.Jl,QR:()=>n.QR,RT:()=>n.RT,TB:()=>n.TB,X:()=>n.X,c_:()=>n.c_,rW:()=>n.rW,rX:()=>n.rX,wm:()=>n.wm,yQ:()=>n.yQ});var n=r(13481)},93499:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(71847).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])}}]);