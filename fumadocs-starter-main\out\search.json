{"type": "advanced", "internalDocumentIDStore": {"internalIdToId": ["/docs/api-reference", "/docs/api-reference-0", "/docs/api-reference-1", "/docs/app", "/docs/app-0", "/docs/app-1", "/docs/app-2", "/docs/app-3", "/docs/app-4", "/docs/app-5", "/docs/app-6", "/docs/app-7", "/docs/app-8", "/docs/app-9", "/docs/app-10", "/docs/app/quickstart", "/docs/app/quickstart-0", "/docs/app/quickstart-1", "/docs/app/quickstart-2", "/docs/app/quickstart-3", "/docs/app/quickstart-4", "/docs/app/quickstart-5", "/docs/app/quickstart-6", "/docs/app/quickstart-7", "/docs/app/quickstart-8", "/docs/app/quickstart-9", "/docs/app/quickstart-10", "/docs/app/quickstart-11", "/docs/app/quickstart-12", "/docs/app/quickstart-13", "/docs/app/quickstart-14", "/docs/app/quickstart-15", "/docs/app/quickstart-16", "/docs/app/quickstart-17", "/docs/app/quickstart-18", "/docs/app/quickstart-19", "/docs/app/quickstart-20", "/docs/changelog", "/docs/changelog-0", "/docs/changelog-1", "/docs/changelog-2", "/docs/changelog-3", "/docs/changelog-4", "/docs/changelog-5", "/docs/changelog-6", "/docs/changelog-7", "/docs/changelog-8", "/docs/changelog-9", "/docs/changelog-10", "/docs/changelog-11", "/docs/changelog-12", "/docs/changelog-13", "/docs/changelog-14", "/docs/changelog-15", "/docs/changelog-16", "/docs/changelog-17", "/docs/changelog-18", "/docs/changelog-19", "/docs/changelog-20", "/docs/changelog-21", "/docs/changelog-22", "/docs/changelog-23", "/docs/changelog-24", "/docs/changelog-25", "/docs/changelog-26", "/docs/changelog-27", "/docs/changelog-28", "/docs/changelog-29", "/docs/changelog-30", "/docs/changelog-31", "/docs/changelog-32", "/docs/changelog-33", "/docs/changelog-34", "/docs/changelog-35", "/docs/changelog-36", "/docs/changelog-37", "/docs/changelog-38", "/docs/changelog-39", "/docs/changelog-40", "/docs/changelog-41", "/docs/changelog-42", "/docs/changelog-43", "/docs/changelog-44", "/docs/changelog-45", "/docs/changelog-46", "/docs/changelog-47", "/docs/changelog-48", "/docs/changelog-49", "/docs/changelog-50", "/docs/changelog-51", "/docs/changelog-52", "/docs/changelog-53", "/docs/changelog-54", "/docs/changelog-55", "/docs/changelog-56", "/docs/changelog-57", "/docs/changelog-58", "/docs/changelog-59", "/docs/changelog-60", "/docs/changelog-61", "/docs/changelog-62", "/docs/changelog-63", "/docs/changelog-64", "/docs/changelog-65", "/docs/changelog-66", "/docs/app/essentials/code", "/docs/app/essentials/code-0", "/docs/app/essentials/code-1", "/docs/app/essentials/code-2", "/docs/app/essentials/code-3", "/docs/app/essentials/code-4", "/docs/app/essentials/code-5", "/docs/app/essentials/code-6", "/docs/app/essentials/code-7", "/docs/app/essentials/code-8", "/docs/app/essentials/code-9", "/docs/app/essentials/code-10", "/docs/app/essentials/code-11", "/docs/app/essentials/code-12", "/docs/app/essentials/markdown", "/docs/app/essentials/markdown-0", "/docs/app/essentials/markdown-1", "/docs/app/essentials/markdown-2", "/docs/app/essentials/markdown-3", "/docs/app/essentials/markdown-4", "/docs/app/essentials/markdown-5", "/docs/app/essentials/markdown-6", "/docs/app/essentials/markdown-7", "/docs/app/essentials/markdown-8", "/docs/app/essentials/markdown-9", "/docs/app/essentials/markdown-10", "/docs/app/essentials/markdown-11", "/docs/app/essentials/markdown-12", "/docs/app/essentials/markdown-13", "/docs/app/essentials/markdown-14", "/docs/app/essentials/markdown-15", "/docs/app/essentials/markdown-16", "/docs/app/essentials/markdown-17", "/docs/app/essentials/markdown-18", "/docs/app/essentials/markdown-19", "/docs/app/essentials/markdown-20", "/docs/app/essentials/markdown-21", "/docs/app/essentials/markdown-22", "/docs/app/essentials/markdown-23", "/docs/app/essentials/markdown-24", "/docs/app/essentials/markdown-25", "/docs/app/essentials/markdown-26", "/docs/app/essentials/markdown-27", "/docs/app/essentials/markdown-28", "/docs/app/essentials/markdown-29", "/docs/app/essentials/markdown-30", "/docs/app/essentials/markdown-31", "/docs/app/essentials/markdown-32", "/docs/app/essentials/markdown-33", "/docs/app/essentials/markdown-34", "/docs/app/essentials/markdown-35", "/docs/app/essentials/markdown-36", "/docs/app/essentials/markdown-37", "/docs/app/essentials/markdown-38", "/docs/app/essentials/markdown-39", "/docs/app/essentials/markdown-40", "/docs/app/essentials/markdown-41", "/docs/app/essentials/markdown-42", "/docs/app/essentials/markdown-43", "/docs/app/essentials/markdown-44", "/docs/app/essentials/markdown-45", "/docs/app/essentials/markdown-46", "/docs/app/essentials/markdown-47", "/docs/app/essentials/markdown-48", "/docs/app/essentials/routing", "/docs/app/essentials/routing-0", "/docs/app/essentials/routing-1", "/docs/app/essentials/routing-2", "/docs/app/essentials/routing-3", "/docs/app/essentials/routing-4", "/docs/app/essentials/routing-5", "/docs/app/essentials/routing-6", "/docs/app/essentials/routing-7", "/docs/app/essentials/routing-8", "/docs/app/essentials/routing-9", "/docs/app/essentials/routing-10", "/docs/app/essentials/routing-11", "/docs/app/essentials/routing-12", "/docs/app/essentials/routing-13", "/docs/app/essentials/routing-14", "/docs/app/essentials/routing-15", "/docs/app/essentials/routing-16", "/docs/app/essentials/routing-17", "/docs/app/essentials/routing-18", "/docs/app/essentials/routing-19", "/docs/app/essentials/routing-20", "/docs/app/essentials/routing-21", "/docs/app/essentials/routing-22", "/docs/app/essentials/routing-23", "/docs/app/essentials/routing-24", "/docs/app/essentials/routing-25", "/docs/app/essentials/routing-26", "/docs/app/essentials/routing-27", "/docs/app/essentials/routing-28", "/docs/app/essentials/routing-29", "/docs/app/essentials/routing-30", "/docs/app/essentials/routing-31", "/docs/app/essentials/routing-32", "/docs/app/essentials/routing-33", "/docs/app/essentials/routing-34", "/docs/app/essentials/routing-35", "/docs/app/essentials/routing-36", "/docs/app/essentials/routing-37", "/docs/app/essentials/routing-38", "/docs/app/essentials/routing-39", "/docs/app/essentials/routing-40", "/docs/app/essentials/routing-41", "/docs/app/essentials/routing-42", "/docs/app/essentials/routing-43", "/docs/app/essentials/routing-44", "/docs/app/essentials/routing-45", "/docs/app/essentials/routing-46", "/docs/app/essentials/routing-47", "/docs/app/essentials/routing-48", "/docs/app/essentials/routing-49", "/docs/app/essentials/routing-50", "/docs/app/essentials/routing-51", "/docs/app/essentials/routing-52", "/docs/app/essentials/routing-53", "/docs/app/essentials/routing-54", "/docs/app/essentials/routing-55", "/docs/app/essentials/routing-56", "/docs/app/essentials/routing-57", "/docs/app/essentials/routing-58", "/docs/app/essentials/routing-59", "/docs/app/features/ai-search", "/docs/app/features/ai-search-0", "/docs/app/features/ai-search-1", "/docs/app/features/ai-search-2", "/docs/app/features/ai-search-3", "/docs/app/features/ai-search-4", "/docs/app/features/ai-search-5", "/docs/app/features/ai-search-6", "/docs/app/features/ai-search-7", "/docs/app/features/ai-search-8", "/docs/app/features/ai-search-9", "/docs/app/features/async-mode", "/docs/app/features/async-mode-0", "/docs/app/features/async-mode-1", "/docs/app/features/async-mode-2", "/docs/app/features/async-mode-3", "/docs/app/features/async-mode-4", "/docs/app/features/async-mode-5", "/docs/app/features/async-mode-6", "/docs/app/features/async-mode-7", "/docs/app/features/llms", "/docs/app/features/llms-0", "/docs/app/features/llms-1", "/docs/app/features/llms-2", "/docs/app/features/llms-3", "/docs/app/features/llms-4", "/docs/app/features/llms-5", "/docs/app/features/llms-6", "/docs/app/features/llms-7", "/docs/app/features/llms-8", "/docs/app/features/llms-9", "/docs/app/features/llms-10", "/docs/app/features/llms-11", "/docs/app/features/llms-12", "/docs/app/features/llms-13", "/docs/app/features/llms-14", "/docs/app/features/llms-15", "/docs/app/features/llms-16", "/docs/app/features/llms-17", "/docs/app/features/llms-18", "/docs/app/features/llms-19", "/docs/app/features/openapi", "/docs/app/features/openapi-0", "/docs/app/features/openapi-1", "/docs/app/features/openapi-2", "/docs/app/features/openapi-3", "/docs/app/features/openapi-4", "/docs/app/features/openapi-5", "/docs/app/features/openapi-6", "/docs/app/features/openapi-7", "/docs/app/features/openapi-8", "/docs/app/features/openapi-9", "/docs/app/features/openapi-10", "/docs/app/features/openapi-11", "/docs/app/features/openapi-12", "/docs/app/features/openapi-13", "/docs/app/guides/adding-a-root-folder", "/docs/app/guides/adding-a-root-folder-0", "/docs/app/guides/adding-a-root-folder-1", "/docs/app/guides/adding-a-root-folder-2", "/docs/app/guides/adding-a-root-folder-3", "/docs/app/guides/adding-a-root-folder-4", "/docs/app/guides/adding-a-root-folder-5", "/docs/app/guides/adding-a-root-folder-6", "/docs/app/guides/adding-a-root-folder-7", "/docs/app/guides/adding-a-root-folder-8", "/docs/app/guides/adding-a-root-folder-9", "/docs/app/guides/adding-a-root-folder-10", "/docs/app/guides/adding-a-root-folder-11", "/docs/app/guides/adding-a-root-folder-12", "/docs/app/guides/adding-a-root-folder-13", "/docs/app/guides/adding-a-root-folder-14", "/docs/app/guides/adding-a-root-folder-15", "/docs/app/guides/adding-a-root-folder-16", "/docs/app/guides/adding-a-root-folder-17", "/docs/app/guides/adding-a-root-folder-18", "/docs/app/guides/adding-a-root-folder-19", "/docs/app/guides/adding-a-root-folder-20", "/docs/api-reference/events/createSpecialEvent", "/docs/api-reference/events/createSpecialEvent-0", "/docs/api-reference/events/createSpecialEvent-1", "/docs/api-reference/events/deleteSpecialEvent", "/docs/api-reference/events/deleteSpecialEvent-0", "/docs/api-reference/events/deleteSpecialEvent-1", "/docs/api-reference/events/getSpecialEvent", "/docs/api-reference/events/getSpecialEvent-0", "/docs/api-reference/events/getSpecialEvent-1", "/docs/api-reference/events/listSpecialEvents", "/docs/api-reference/events/listSpecialEvents-0", "/docs/api-reference/events/listSpecialEvents-1", "/docs/api-reference/events/publishNewEvent", "/docs/api-reference/events/publishNewEvent-0", "/docs/api-reference/events/updateSpecialEvent", "/docs/api-reference/events/updateSpecialEvent-0", "/docs/api-reference/events/updateSpecialEvent-1", "/docs/api-reference/operations/getMuseumHours", "/docs/api-reference/operations/getMuseumHours-0", "/docs/api-reference/operations/getMuseumHours-1", "/docs/api-reference/tickets/buyMuseumTickets", "/docs/api-reference/tickets/buyMuseumTickets-0", "/docs/api-reference/tickets/buyMuseumTickets-1", "/docs/api-reference/tickets/getTicketCode", "/docs/api-reference/tickets/getTicketCode-0", "/docs/api-reference/tickets/getTicketCode-1"]}, "index": {"indexes": {"content": {"type": "<PERSON><PERSON><PERSON>", "node": {"w": "", "s": "", "e": false, "k": "", "d": [], "c": [["i", {"w": "i", "s": "i", "e": false, "k": "i", "d": [], "c": [["n", {"w": "in", "s": "n", "e": true, "k": "n", "d": [3, 10, 17, 27, 30, 33, 35, 71, 72, 92, 98, 101, 103, 115, 116, 121, 136, 137, 145, 161, 162, 164, 167, 204, 209, 226, 227, 232, 239, 261, 267, 268, 281, 284, 295, 297, 298, 299, 300, 301, 303, 306], "c": [["t", {"w": "int", "s": "t", "e": false, "k": "t", "d": [], "c": [["r", {"w": "introduc", "s": "<PERSON><PERSON>", "e": false, "k": "r", "d": [], "c": [["t", {"w": "introduction", "s": "tion", "e": true, "k": "t", "d": [1, 4, 122, 135, 172, 233, 244, 253, 274, 289], "c": []}], ["e", {"w": "introduce", "s": "e", "e": false, "k": "e", "d": [], "c": [["d", {"w": "introduced", "s": "d", "e": true, "k": "d", "d": [85], "c": []}], ["s", {"w": "introduces", "s": "s", "e": true, "k": "s", "d": [248], "c": []}]]}]]}], ["e", {"w": "inte", "s": "e", "e": false, "k": "e", "d": [], "c": [["r", {"w": "inter", "s": "r", "e": false, "k": "r", "d": [], "c": [["a", {"w": "interactive", "s": "active", "e": true, "k": "a", "d": [13, 14, 280], "c": []}], ["n", {"w": "internal", "s": "nal", "e": true, "k": "n", "d": [142], "c": []}]]}], ["g", {"w": "integration", "s": "gration", "e": true, "k": "g", "d": [42, 61, 63, 102, 241, 277, 278], "c": [["s", {"w": "integrations", "s": "s", "e": true, "k": "s", "d": [52], "c": []}]]}]]}], ["o", {"w": "into", "s": "o", "e": true, "k": "o", "d": [169, 204, 297], "c": []}]]}], ["c", {"w": "inc", "s": "c", "e": false, "k": "c", "d": [], "c": [["l", {"w": "includ", "s": "lud", "e": false, "k": "l", "d": [], "c": [["e", {"w": "include", "s": "e", "e": true, "k": "e", "d": [11, 68, 88, 104, 132, 146, 162, 222], "c": [["s", {"w": "includes", "s": "s", "e": true, "k": "s", "d": [55, 63, 262], "c": []}], ["d", {"w": "included", "s": "d", "e": true, "k": "d", "d": [147, 221, 223], "c": []}]]}], ["i", {"w": "including", "s": "ing", "e": true, "k": "i", "d": [305], "c": []}]]}], ["o", {"w": "inconsistent", "s": "onsistent", "e": true, "k": "o", "d": [71], "c": []}], ["r", {"w": "increase", "s": "rease", "e": true, "k": "r", "d": [246], "c": []}]]}], ["i", {"w": "initial", "s": "itial", "e": true, "k": "i", "d": [307], "c": [["i", {"w": "initialized", "s": "ized", "e": true, "k": "i", "d": [26], "c": []}]]}], ["s", {"w": "ins", "s": "s", "e": false, "k": "s", "d": [], "c": [["i", {"w": "inside", "s": "ide", "e": true, "k": "i", "d": [29, 221, 249, 250], "c": []}], ["t", {"w": "inst", "s": "t", "e": false, "k": "t", "d": [], "c": [["a", {"w": "install", "s": "all", "e": true, "k": "a", "d": [112, 117], "c": [["a", {"w": "installation", "s": "ation", "e": true, "k": "a", "d": [117], "c": []}]]}], ["e", {"w": "instead", "s": "ead", "e": true, "k": "e", "d": [161, 240, 247, 249], "c": []}]]}], ["e", {"w": "insert", "s": "ert", "e": true, "k": "e", "d": [225], "c": []}]]}], ["-", {"w": "in-app", "s": "-app", "e": true, "k": "-", "d": [53], "c": []}], ["v", {"w": "inv", "s": "v", "e": false, "k": "v", "d": [], "c": [["i", {"w": "invite", "s": "ite", "e": true, "k": "i", "d": [81], "c": []}], ["a", {"w": "invalid", "s": "alid", "e": true, "k": "a", "d": [155], "c": []}]]}], ["l", {"w": "inline", "s": "line", "e": true, "k": "l", "d": [107, 109, 166], "c": []}], ["f", {"w": "inf", "s": "f", "e": false, "k": "f", "d": [], "c": [["o", {"w": "info", "s": "o", "e": true, "k": "o", "d": [148], "c": [["r", {"w": "information", "s": "rmation", "e": true, "k": "r", "d": [184, 240, 279], "c": []}]]}], ["r", {"w": "infrastructure", "s": "rastructure", "e": true, "k": "r", "d": [241], "c": []}]]}], ["d", {"w": "index", "s": "dex", "e": true, "k": "d", "d": [201, 306, 307], "c": []}], ["k", {"w": "inkeep", "s": "keep", "e": true, "k": "k", "d": [241], "c": []}], ["g", {"w": "ingest", "s": "gest", "e": true, "k": "g", "d": [267], "c": []}]]}], ["s", {"w": "is", "s": "s", "e": true, "k": "s", "d": [2, 8, 24, 29, 33, 116, 135, 136, 140, 147, 155, 237, 239, 264, 268], "c": [["s", {"w": "issue", "s": "sue", "e": true, "k": "s", "d": [54, 59], "c": [["s", {"w": "issues", "s": "s", "e": true, "k": "s", "d": [24], "c": []}]]}]]}], ["t", {"w": "it", "s": "t", "e": true, "k": "t", "d": [7, 29, 115, 137, 147, 155, 158, 169, 184, 226, 240, 264, 298, 308], "c": [["e", {"w": "item", "s": "em", "e": true, "k": "e", "d": [222, 223], "c": [["s", {"w": "items", "s": "s", "e": true, "k": "s", "d": [92, 217, 220, 221, 224, 227], "c": []}]]}], ["s", {"w": "its", "s": "s", "e": true, "k": "s", "d": [185, 196, 204, 305], "c": [["e", {"w": "itself", "s": "elf", "e": true, "k": "e", "d": [162], "c": []}]]}]]}], ["m", {"w": "im", "s": "m", "e": false, "k": "m", "d": [], "c": [["p", {"w": "imp", "s": "p", "e": false, "k": "p", "d": [], "c": [["r", {"w": "improve", "s": "rove", "e": true, "k": "r", "d": [14, 247], "c": [["m", {"w": "improvements", "s": "ments", "e": true, "k": "m", "d": [39, 40], "c": []}], ["d", {"w": "improved", "s": "d", "e": true, "k": "d", "d": [60, 68, 70, 86, 90, 97, 105], "c": []}]]}], ["o", {"w": "import", "s": "ort", "e": true, "k": "o", "d": [137, 249], "c": [["s", {"w": "imports", "s": "s", "e": true, "k": "s", "d": [81], "c": []}], ["i", {"w": "importing", "s": "ing", "e": true, "k": "i", "d": [161], "c": []}]]}], ["a", {"w": "impacting", "s": "acting", "e": true, "k": "a", "d": [204], "c": []}], ["l", {"w": "implementation", "s": "lementation", "e": true, "k": "l", "d": [239, 241], "c": []}]]}], ["a", {"w": "image", "s": "age", "e": true, "k": "a", "d": [141, 250, 333, 334], "c": [["s", {"w": "images", "s": "s", "e": true, "k": "s", "d": [124, 141, 250], "c": []}]]}]]}], ["d", {"w": "id", "s": "d", "e": true, "k": "d", "d": [159], "c": [["e", {"w": "ideas", "s": "eas", "e": true, "k": "e", "d": [15], "c": []}], ["l", {"w": "idle", "s": "le", "e": true, "k": "l", "d": [101], "c": []}]]}], ["f", {"w": "if", "s": "f", "e": true, "k": "f", "d": [33, 76, 238, 249], "c": []}], ["c", {"w": "icon", "s": "con", "e": true, "k": "c", "d": [192, 193, 214, 215, 225, 226, 305], "c": [["s", {"w": "icons", "s": "s", "e": true, "k": "s", "d": [146, 182, 193, 215], "c": []}]]}]]}], ["t", {"w": "t", "s": "t", "e": true, "k": "t", "d": [238], "c": [["h", {"w": "th", "s": "h", "e": false, "k": "h", "d": [], "c": [["i", {"w": "this", "s": "is", "e": true, "k": "i", "d": [2, 32, 34, 184, 226, 237, 246, 247, 264, 268, 297, 298, 305], "c": []}], ["e", {"w": "the", "s": "e", "e": true, "k": "e", "d": [3, 8, 10, 19, 24, 26, 27, 29, 30, 31, 33, 56, 116, 117, 118, 135, 136, 137, 142, 143, 147, 156, 159, 160, 162, 165, 184, 189, 191, 193, 195, 196, 204, 209, 215, 219, 221, 224, 225, 226, 227, 228, 234, 238, 239, 240, 241, 246, 249, 250, 260, 263, 264, 265, 269, 276, 278, 284, 286, 292, 293, 295, 298, 299, 300, 301, 302, 304, 305, 310, 311, 313, 314, 319, 320, 324, 325], "c": [["m", {"w": "them", "s": "m", "e": true, "k": "m", "d": [137, 161, 230, 249, 250], "c": []}], ["y", {"w": "they", "s": "y", "e": true, "k": "y", "d": [169, 184, 221], "c": []}], ["r", {"w": "there", "s": "re", "e": true, "k": "r", "d": [239], "c": []}], ["i", {"w": "their", "s": "ir", "e": true, "k": "i", "d": [250], "c": []}]]}], ["a", {"w": "that", "s": "at", "e": true, "k": "a", "d": [24, 161], "c": [["'", {"w": "that's", "s": "'s", "e": true, "k": "'", "d": [308], "c": []}]]}], ["r", {"w": "thr", "s": "r", "e": false, "k": "r", "d": [], "c": [["o", {"w": "through", "s": "ough", "e": true, "k": "o", "d": [97, 165, 249, 297], "c": []}], ["e", {"w": "three", "s": "ee", "e": true, "k": "e", "d": [116], "c": []}]]}]]}], ["o", {"w": "to", "s": "o", "e": true, "k": "o", "d": [2, 3, 5, 8, 9, 10, 11, 14, 29, 33, 36, 37, 56, 61, 76, 81, 83, 91, 100, 102, 115, 116, 118, 135, 137, 142, 155, 157, 159, 162, 184, 197, 203, 204, 205, 222, 223, 225, 226, 230, 237, 238, 239, 240, 241, 247, 248, 249, 252, 257, 264, 277, 281, 284, 288, 290, 297, 299, 301, 302, 304, 307, 308, 313, 314], "c": [["o", {"w": "too", "s": "o", "e": true, "k": "o", "d": [146], "c": [["l", {"w": "tools", "s": "ls", "e": true, "k": "l", "d": [48], "c": []}]]}], ["k", {"w": "token", "s": "ken", "e": true, "k": "k", "d": [84], "c": [["s", {"w": "tokens", "s": "s", "e": true, "k": "s", "d": [257], "c": []}]]}], ["c", {"w": "toc", "s": "c", "e": true, "k": "c", "d": [129, 156, 158], "c": []}]]}], ["e", {"w": "te", "s": "e", "e": false, "k": "e", "d": [], "c": [["c", {"w": "techwith<PERSON><PERSON><PERSON>", "s": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "e": true, "k": "c", "d": [25], "c": []}], ["m", {"w": "template", "s": "mplate", "e": true, "k": "m", "d": [26, 32, 226, 237, 247], "c": []}], ["a", {"w": "team", "s": "am", "e": true, "k": "a", "d": [66], "c": [["s", {"w": "teams", "s": "s", "e": true, "k": "s", "d": [61], "c": []}]]}], ["x", {"w": "text", "s": "xt", "e": true, "k": "x", "d": [121, 225], "c": []}], ["s", {"w": "test", "s": "st", "e": true, "k": "s", "d": [250], "c": []}]]}], ["s", {"w": "ts", "s": "s", "e": true, "k": "s", "d": [33], "c": [["x", {"w": "tsx", "s": "x", "e": true, "k": "x", "d": [300], "c": []}]]}], ["a", {"w": "ta", "s": "a", "e": false, "k": "a", "d": [], "c": [["k", {"w": "taken", "s": "ken", "e": true, "k": "k", "d": [33], "c": []}], ["s", {"w": "task", "s": "sk", "e": true, "k": "s", "d": [62, 66, 103], "c": [["s", {"w": "tasks", "s": "s", "e": true, "k": "s", "d": [64, 77, 88, 101], "c": []}]]}], ["g", {"w": "tag", "s": "g", "e": true, "k": "g", "d": [162], "c": [["s", {"w": "tags", "s": "s", "e": true, "k": "s", "d": [89], "c": []}]]}], ["b", {"w": "tab", "s": "b", "e": true, "k": "b", "d": [131, 160], "c": [["s", {"w": "tabs", "s": "s", "e": true, "k": "s", "d": [117, 230], "c": []}], ["l", {"w": "table", "s": "le", "e": true, "k": "l", "d": [156], "c": []}]]}], ["r", {"w": "target", "s": "rget", "e": true, "k": "r", "d": [143, 162], "c": []}], ["y", {"w": "taylor", "s": "ylor", "e": true, "k": "y", "d": [167], "c": []}], ["i", {"w": "tailored", "s": "ilored", "e": true, "k": "i", "d": [241], "c": []}]]}], ["r", {"w": "tr", "s": "r", "e": false, "k": "r", "d": [], "c": [["i", {"w": "triggers", "s": "iggers", "e": true, "k": "i", "d": [64], "c": []}], ["a", {"w": "tra", "s": "a", "e": false, "k": "a", "d": [], "c": [["c", {"w": "tracking", "s": "cking", "e": true, "k": "c", "d": [67], "c": []}], ["n", {"w": "transformers", "s": "nsformers", "e": true, "k": "n", "d": [113, 118], "c": []}]]}]]}], ["i", {"w": "ti", "s": "i", "e": false, "k": "i", "d": [], "c": [["m", {"w": "time", "s": "me", "e": true, "k": "m", "d": [67, 84, 246, 268], "c": [["s", {"w": "times", "s": "s", "e": true, "k": "s", "d": [97], "c": []}]]}], ["c", {"w": "tick", "s": "ck", "e": false, "k": "c", "d": [], "c": [["s", {"w": "ticks", "s": "s", "e": true, "k": "s", "d": [116], "c": []}], ["e", {"w": "ticket", "s": "et", "e": true, "k": "e", "d": [332, 333, 334], "c": [["s", {"w": "tickets", "s": "s", "e": true, "k": "s", "d": [329, 330, 331], "c": []}]]}]]}], ["t", {"w": "title", "s": "tle", "e": true, "k": "t", "d": [121, 151, 152, 168, 188, 189, 212, 229, 305], "c": [["s", {"w": "titles", "s": "s", "e": true, "k": "s", "d": [258], "c": []}]]}], ["p", {"w": "tip", "s": "p", "e": true, "k": "p", "d": [168, 241], "c": [["s", {"w": "tips", "s": "s", "e": true, "k": "s", "d": [147], "c": []}]]}]]}], ["w", {"w": "twoslash", "s": "woslash", "e": true, "k": "w", "d": [114, 119], "c": []}], ["y", {"w": "type", "s": "ype", "e": true, "k": "y", "d": [147, 153], "c": [["s", {"w": "typescript", "s": "script", "e": true, "k": "s", "d": [282], "c": []}]]}], ["x", {"w": "txt", "s": "xt", "e": true, "k": "x", "d": [254, 255, 258, 259, 264], "c": []}]]}], ["a", {"w": "a", "s": "a", "e": true, "k": "a", "d": [2, 24, 29, 32, 98, 115, 135, 137, 159, 162, 166, 169, 171, 184, 185, 196, 203, 204, 209, 222, 224, 226, 227, 228, 238, 241, 257, 258, 259, 260, 267, 269, 287, 288, 290, 291, 296, 297, 298, 300, 303, 308, 310, 311, 313, 314, 316, 317, 319, 320, 322, 324, 325], "c": [["m", {"w": "amazing", "s": "mazing", "e": true, "k": "m", "d": [8], "c": []}], ["n", {"w": "an", "s": "n", "e": true, "k": "n", "d": [59, 155, 223, 237, 267, 277, 306, 333, 334], "c": [["d", {"w": "and", "s": "d", "e": true, "k": "d", "d": [10, 11, 12, 14, 15, 27, 29, 35, 39, 53, 63, 64, 65, 66, 67, 70, 78, 80, 85, 88, 89, 94, 105, 107, 116, 117, 121, 137, 142, 184, 226, 228, 232, 241, 246, 250, 257, 258, 261, 266, 268, 270, 273, 277, 282, 283, 285, 300, 305, 308], "c": [["r", {"w": "android", "s": "roid", "e": true, "k": "r", "d": [57], "c": []}]]}], ["a", {"w": "analytics", "s": "alytics", "e": true, "k": "a", "d": [43], "c": []}], ["c", {"w": "anchor", "s": "chor", "e": true, "k": "c", "d": [130, 155], "c": [["s", {"w": "anchors", "s": "s", "e": true, "k": "s", "d": [157], "c": []}]]}], ["y", {"w": "any", "s": "y", "e": true, "k": "y", "d": [136], "c": [["t", {"w": "anything", "s": "thing", "e": true, "k": "t", "d": [298], "c": []}]]}], ["o", {"w": "another", "s": "other", "e": true, "k": "o", "d": [162], "c": []}]]}], ["p", {"w": "ap", "s": "p", "e": false, "k": "p", "d": [], "c": [["p", {"w": "app", "s": "p", "e": true, "k": "p", "d": [27, 29, 31, 33, 271, 300], "c": [["l", {"w": "appl", "s": "l", "e": false, "k": "l", "d": [], "c": [["y", {"w": "apply", "s": "y", "e": true, "k": "y", "d": [302], "c": [["i", {"w": "applying", "s": "ing", "e": true, "k": "i", "d": [12], "c": []}]]}], ["i", {"w": "appli", "s": "i", "e": false, "k": "i", "d": [], "c": [["e", {"w": "applied", "s": "ed", "e": true, "k": "e", "d": [155], "c": []}], ["c", {"w": "application", "s": "cation", "e": true, "k": "c", "d": [235], "c": []}]]}]]}], ["r", {"w": "approval", "s": "roval", "e": true, "k": "r", "d": [83], "c": []}]]}], ["i", {"w": "api", "s": "i", "e": true, "k": "i", "d": [55, 76, 97, 234, 237, 238, 240, 279, 280], "c": [["-", {"w": "api-reference", "s": "-reference", "e": true, "k": "-", "d": [31, 284], "c": []}]]}]]}], ["u", {"w": "au", "s": "u", "e": false, "k": "u", "d": [], "c": [["t", {"w": "auto", "s": "to", "e": true, "k": "t", "d": [125], "c": [["-", {"w": "auto-generate", "s": "-generate", "e": true, "k": "-", "d": [13], "c": []}], ["m", {"w": "automatically", "s": "matically", "e": true, "k": "m", "d": [65, 77, 117, 141, 155], "c": []}]]}], ["d", {"w": "audit", "s": "dit", "e": true, "k": "d", "d": [82], "c": []}]]}], ["w", {"w": "awesome", "s": "wesome", "e": true, "k": "w", "d": [17], "c": []}], ["d", {"w": "ad", "s": "d", "e": false, "k": "d", "d": [], "c": [["d", {"w": "add", "s": "d", "e": true, "k": "d", "d": [20, 29, 33, 35, 36, 37, 157, 159, 161, 221, 222, 223, 225, 238, 288, 290, 299, 300, 304, 307], "c": [["e", {"w": "added", "s": "ed", "e": true, "k": "e", "d": [56, 58, 62, 67, 78, 82, 91, 93, 99, 103, 308, 321], "c": []}], ["i", {"w": "adding", "s": "ing", "e": true, "k": "i", "d": [144, 147, 287, 297], "c": []}]]}], ["m", {"w": "admin", "s": "min", "e": false, "k": "m", "d": [], "c": [["s", {"w": "admins", "s": "s", "e": true, "k": "s", "d": [79], "c": []}], ["-", {"w": "admin-level", "s": "-level", "e": true, "k": "-", "d": [94], "c": []}]]}], ["v", {"w": "advanced", "s": "vanced", "e": true, "k": "v", "d": [111], "c": []}]]}], ["l", {"w": "al", "s": "l", "e": false, "k": "l", "d": [], "c": [["l", {"w": "all", "s": "l", "e": true, "k": "l", "d": [95, 195, 246], "c": [["o", {"w": "allow", "s": "ow", "e": true, "k": "o", "d": [81, 142], "c": [["i", {"w": "allowing", "s": "ing", "e": true, "k": "i", "d": [29, 118, 247], "c": []}], ["s", {"w": "allows", "s": "s", "e": true, "k": "s", "d": [137, 184, 230, 297, 313, 314], "c": []}], ["e", {"w": "allowed", "s": "ed", "e": true, "k": "e", "d": [249], "c": []}]]}]]}], ["e", {"w": "alert", "s": "ert", "e": false, "k": "e", "d": [], "c": [["s", {"w": "alerts", "s": "s", "e": true, "k": "s", "d": [53, 102], "c": []}], ["i", {"w": "alerting", "s": "ing", "e": true, "k": "i", "d": [75], "c": []}]]}], ["s", {"w": "also", "s": "so", "e": true, "k": "s", "d": [116, 140, 156, 158, 162], "c": []}], ["p", {"w": "alphabetically", "s": "phabetically", "e": true, "k": "p", "d": [220, 222], "c": []}], ["t", {"w": "although", "s": "though", "e": true, "k": "t", "d": [237], "c": []}], ["r", {"w": "already", "s": "ready", "e": true, "k": "r", "d": [238], "c": []}]]}], ["b", {"w": "about", "s": "bout", "e": true, "k": "b", "d": [29, 119, 145, 316, 317], "c": []}], ["c", {"w": "ac", "s": "c", "e": false, "k": "c", "d": [], "c": [["c", {"w": "acc", "s": "c", "e": false, "k": "c", "d": [], "c": [["e", {"w": "access", "s": "ess", "e": true, "k": "e", "d": [34, 83, 257], "c": [["i", {"w": "accessib", "s": "ib", "e": false, "k": "i", "d": [], "c": [["i", {"w": "accessibility", "s": "ility", "e": true, "k": "i", "d": [44], "c": []}], ["l", {"w": "accessible", "s": "le", "e": true, "k": "l", "d": [96], "c": []}]]}]]}], ["u", {"w": "accuracy", "s": "uracy", "e": true, "k": "u", "d": [86], "c": []}], ["o", {"w": "accounts", "s": "ounts", "e": true, "k": "o", "d": [105], "c": []}]]}], ["t", {"w": "act", "s": "t", "e": false, "k": "t", "d": [], "c": [["i", {"w": "acti", "s": "i", "e": false, "k": "i", "d": [], "c": [["v", {"w": "activ", "s": "v", "e": false, "k": "v", "d": [], "c": [["i", {"w": "activity", "s": "ity", "e": true, "k": "i", "d": [55, 99], "c": []}], ["a", {"w": "activate", "s": "ate", "e": true, "k": "a", "d": [91, 238], "c": []}]]}], ["o", {"w": "actions", "s": "ons", "e": true, "k": "o", "d": [78], "c": []}]]}], ["u", {"w": "actually", "s": "ually", "e": true, "k": "u", "d": [169], "c": []}]]}], ["r", {"w": "across", "s": "ross", "e": true, "k": "r", "d": [95], "c": []}]]}], ["f", {"w": "after", "s": "fter", "e": true, "k": "f", "d": [96, 116], "c": []}], ["s", {"w": "as", "s": "s", "e": true, "k": "s", "d": [99, 115, 136, 226, 227, 230, 241], "c": [["y", {"w": "async", "s": "ync", "e": true, "k": "y", "d": [242, 247, 248], "c": []}], ["s", {"w": "assist", "s": "sist", "e": true, "k": "s", "d": [252], "c": []}]]}], ["r", {"w": "ar", "s": "r", "e": false, "k": "r", "d": [], "c": [["e", {"w": "are", "s": "e", "e": true, "k": "e", "d": [141, 196, 220, 221, 228, 236, 249, 285], "c": []}], ["r", {"w": "array", "s": "ray", "e": true, "k": "r", "d": [299], "c": []}]]}], ["v", {"w": "av", "s": "v", "e": false, "k": "v", "d": [], "c": [["o", {"w": "avoid", "s": "oid", "e": true, "k": "o", "d": [142, 204, 250], "c": []}], ["a", {"w": "available", "s": "ailable", "e": true, "k": "a", "d": [195], "c": []}]]}], ["t", {"w": "at", "s": "t", "e": true, "k": "t", "d": [238, 247, 264, 268, 319, 320], "c": [["t", {"w": "attributes", "s": "tributes", "e": true, "k": "t", "d": [143], "c": []}]]}], ["i", {"w": "ai", "s": "i", "e": true, "k": "i", "d": [231, 238, 239, 240], "c": [["-", {"w": "ai-", "s": "-", "e": false, "k": "-", "d": [], "c": [["p", {"w": "ai-powered", "s": "powered", "e": true, "k": "p", "d": [232, 237], "c": []}], ["f", {"w": "ai-friendly", "s": "friendly", "e": true, "k": "f", "d": [252], "c": []}]]}]]}], ["g", {"w": "agents", "s": "gents", "e": true, "k": "g", "d": [264], "c": []}]]}], ["p", {"w": "p", "s": "p", "e": false, "k": "p", "d": [], "c": [["a", {"w": "pa", "s": "a", "e": false, "k": "a", "d": [], "c": [["g", {"w": "page", "s": "ge", "e": true, "k": "g", "d": [2, 29, 159, 189, 191, 195, 196, 199, 207, 260, 261, 265, 269, 296, 300, 306], "c": [["s", {"w": "pages", "s": "s", "e": true, "k": "s", "d": [178, 203, 216, 221, 222, 299], "c": []}]]}], ["n", {"w": "panel", "s": "nel", "e": true, "k": "n", "d": [91], "c": []}], ["s", {"w": "pas", "s": "s", "e": false, "k": "s", "d": [], "c": [["s", {"w": "pass", "s": "s", "e": true, "k": "s", "d": [249], "c": [["w", {"w": "password", "s": "word", "e": true, "k": "w", "d": [95], "c": []}]]}], ["t", {"w": "paste", "s": "te", "e": true, "k": "t", "d": [169], "c": []}]]}], ["c", {"w": "package", "s": "ckage", "e": true, "k": "c", "d": [112, 117], "c": []}], ["t", {"w": "path", "s": "th", "e": true, "k": "t", "d": [162, 196, 197, 205, 250], "c": [["s", {"w": "paths", "s": "s", "e": true, "k": "s", "d": [250], "c": []}]]}], ["r", {"w": "par", "s": "r", "e": false, "k": "r", "d": [], "c": [["e", {"w": "parentheses", "s": "entheses", "e": true, "k": "e", "d": [204], "c": []}], ["a", {"w": "para", "s": "a", "e": false, "k": "a", "d": [], "c": [["g", {"w": "paragraphs", "s": "graphs", "e": true, "k": "g", "d": [266, 270], "c": []}], ["m", {"w": "parameters", "s": "meters", "e": true, "k": "m", "d": [283], "c": []}]]}]]}]]}], ["r", {"w": "pr", "s": "r", "e": false, "k": "r", "d": [], "c": [["a", {"w": "practices", "s": "actices", "e": true, "k": "a", "d": [15], "c": []}], ["o", {"w": "pro", "s": "o", "e": false, "k": "o", "d": [], "c": [["d", {"w": "product", "s": "duct", "e": true, "k": "d", "d": [38], "c": [["i", {"w": "producti", "s": "i", "e": false, "k": "i", "d": [], "c": [["o", {"w": "production", "s": "on", "e": true, "k": "o", "d": [24], "c": []}], ["v", {"w": "productivity", "s": "vity", "e": true, "k": "v", "d": [48], "c": []}]]}]]}], ["j", {"w": "project", "s": "ject", "e": true, "k": "j", "d": [67, 238, 284], "c": [["s", {"w": "projects", "s": "s", "e": true, "k": "s", "d": [61, 83], "c": []}]]}], ["c", {"w": "process", "s": "cess", "e": true, "k": "c", "d": [240], "c": [["i", {"w": "processing", "s": "ing", "e": true, "k": "i", "d": [75], "c": []}]]}], ["g", {"w": "programming", "s": "gramming", "e": true, "k": "g", "d": [116, 281], "c": []}], ["v", {"w": "provide", "s": "vide", "e": true, "k": "v", "d": [252, 257], "c": [["s", {"w": "provides", "s": "s", "e": true, "k": "s", "d": [135, 269, 277], "c": []}]]}], ["p", {"w": "prop", "s": "p", "e": true, "k": "p", "d": [249], "c": []}]]}], ["i", {"w": "private", "s": "ivate", "e": true, "k": "i", "d": [83], "c": []}], ["e", {"w": "pre", "s": "e", "e": false, "k": "e", "d": [], "c": [["s", {"w": "pres", "s": "s", "e": false, "k": "s", "d": [], "c": [["s", {"w": "press", "s": "s", "e": true, "k": "s", "d": [91], "c": []}], ["e", {"w": "preserves", "s": "erves", "e": true, "k": "e", "d": [266, 270], "c": []}]]}], ["f", {"w": "prefetching", "s": "fetching", "e": true, "k": "f", "d": [142], "c": []}], ["v", {"w": "prevent", "s": "vent", "e": true, "k": "v", "d": [223], "c": []}], ["-", {"w": "pre-configured", "s": "-configured", "e": true, "k": "-", "d": [239], "c": []}], ["c", {"w": "precompiled", "s": "compiled", "e": true, "k": "c", "d": [246], "c": []}]]}]]}], ["e", {"w": "pe", "s": "e", "e": false, "k": "e", "d": [], "c": [["r", {"w": "per", "s": "r", "e": true, "k": "r", "d": [79], "c": [["f", {"w": "performance", "s": "formance", "e": true, "k": "f", "d": [51, 57, 66, 247], "c": []}], ["m", {"w": "permission", "s": "mission", "e": true, "k": "m", "d": [96], "c": []}]]}], ["a", {"w": "peak", "s": "ak", "e": true, "k": "a", "d": [74], "c": []}], ["n", {"w": "pending", "s": "nding", "e": true, "k": "n", "d": [83, 88], "c": []}], ["o", {"w": "people", "s": "ople", "e": true, "k": "o", "d": [159], "c": []}]]}], ["u", {"w": "pu", "s": "u", "e": false, "k": "u", "d": [], "c": [["s", {"w": "push", "s": "sh", "e": true, "k": "s", "d": [60], "c": []}], ["t", {"w": "putting", "s": "tting", "e": true, "k": "t", "d": [204], "c": []}], ["b", {"w": "publi", "s": "bli", "e": false, "k": "b", "d": [], "c": [["c", {"w": "public", "s": "c", "e": true, "k": "c", "d": [250], "c": []}], ["s", {"w": "publish", "s": "sh", "e": true, "k": "s", "d": [322], "c": []}]]}], ["r", {"w": "purchase", "s": "rchase", "e": true, "k": "r", "d": [330, 331], "c": []}]]}], ["o", {"w": "po", "s": "o", "e": false, "k": "o", "d": [], "c": [["i", {"w": "point", "s": "int", "e": true, "k": "i", "d": [63], "c": []}], ["w", {"w": "power", "s": "wer", "e": true, "k": "w", "d": [167], "c": []}]]}], ["d", {"w": "pdf", "s": "df", "e": true, "k": "d", "d": [68], "c": []}], ["h", {"w": "phrase", "s": "hrase", "e": true, "k": "h", "d": [115], "c": []}], ["n", {"w": "pn", "s": "n", "e": false, "k": "n", "d": [], "c": [["p", {"w": "pnpm", "s": "pm", "e": true, "k": "p", "d": [117], "c": []}], ["g", {"w": "png", "s": "g", "e": true, "k": "g", "d": [250], "c": []}]]}], ["m", {"w": "pm", "s": "m", "e": true, "k": "m", "d": [166], "c": []}], ["l", {"w": "pla", "s": "la", "e": false, "k": "l", "d": [], "c": [["t", {"w": "platform", "s": "tform", "e": true, "k": "t", "d": [238], "c": []}], ["c", {"w": "place", "s": "ce", "e": true, "k": "c", "d": [250], "c": []}], ["y", {"w": "playground", "s": "yground", "e": true, "k": "y", "d": [280], "c": []}], ["n", {"w": "planned", "s": "nned", "e": true, "k": "n", "d": [313, 314], "c": []}]]}]]}], ["c", {"w": "c", "s": "c", "e": true, "k": "c", "d": [166], "c": [["h", {"w": "ch", "s": "h", "e": false, "k": "h", "d": [], "c": [["e", {"w": "check", "s": "eck", "e": true, "k": "e", "d": [2, 29], "c": [["l", {"w": "checklists", "s": "lists", "e": true, "k": "l", "d": [78], "c": []}]]}], ["a", {"w": "cha", "s": "a", "e": false, "k": "a", "d": [], "c": [["n", {"w": "chang", "s": "ng", "e": false, "k": "n", "d": [], "c": [["e", {"w": "change", "s": "e", "e": true, "k": "e", "d": [204, 284], "c": [["s", {"w": "changes", "s": "s", "e": true, "k": "s", "d": [21], "c": []}], ["l", {"w": "changelog", "s": "log", "e": true, "k": "l", "d": [31], "c": []}]]}], ["i", {"w": "changing", "s": "ing", "e": true, "k": "i", "d": [276], "c": []}]]}], ["r", {"w": "char", "s": "r", "e": false, "k": "r", "d": [], "c": [["t", {"w": "charts", "s": "ts", "e": true, "k": "t", "d": [66], "c": []}], ["a", {"w": "characters", "s": "acters", "e": true, "k": "a", "d": [155], "c": []}]]}], ["i", {"w": "chain", "s": "in", "e": true, "k": "i", "d": [158], "c": []}]]}], ["i", {"w": "child", "s": "ild", "e": true, "k": "i", "d": [204], "c": []}], ["r", {"w": "chrome", "s": "rome", "e": true, "k": "r", "d": [257], "c": []}]]}], ["a", {"w": "ca", "s": "a", "e": false, "k": "a", "d": [], "c": [["n", {"w": "can", "s": "n", "e": true, "k": "n", "d": [3, 26, 32, 34, 35, 65, 79, 83, 116, 117, 136, 146, 147, 156, 157, 158, 160, 161, 162, 169, 185, 203, 204, 221, 223, 224, 238, 239, 241, 246, 257, 284, 298], "c": [["c", {"w": "cancel", "s": "cel", "e": true, "k": "c", "d": [313, 314], "c": []}]]}], ["l", {"w": "cal", "s": "l", "e": false, "k": "l", "d": [], "c": [["e", {"w": "calendar", "s": "endar", "e": true, "k": "e", "d": [58, 86], "c": []}], ["l", {"w": "callout", "s": "lout", "e": true, "k": "l", "d": [147], "c": [["s", {"w": "callouts", "s": "s", "e": true, "k": "s", "d": [127], "c": []}]]}]]}], ["u", {"w": "causing", "s": "using", "e": true, "k": "u", "d": [59], "c": []}], ["t", {"w": "categori", "s": "te<PERSON>i", "e": false, "k": "t", "d": [], "c": [["z", {"w": "categorize", "s": "ze", "e": true, "k": "z", "d": [77], "c": []}], ["e", {"w": "categories", "s": "es", "e": true, "k": "e", "d": [297], "c": []}]]}], ["c", {"w": "caching", "s": "ching", "e": true, "k": "c", "d": [97, 145], "c": []}], ["r", {"w": "cards", "s": "rds", "e": true, "k": "r", "d": [126], "c": []}], ["p", {"w": "capabilities", "s": "pabilities", "e": true, "k": "p", "d": [237], "c": []}]]}], ["r", {"w": "cr", "s": "r", "e": false, "k": "r", "d": [], "c": [["e", {"w": "creat", "s": "eat", "e": false, "k": "e", "d": [], "c": [["i", {"w": "creati", "s": "i", "e": false, "k": "i", "d": [], "c": [["n", {"w": "creating", "s": "ng", "e": true, "k": "n", "d": [8, 209], "c": []}], ["v", {"w": "creative", "s": "ve", "e": true, "k": "v", "d": [15], "c": []}]]}], ["e", {"w": "create", "s": "e", "e": true, "k": "e", "d": [30, 35, 184, 203, 291, 295, 296, 297, 298, 303, 306, 309], "c": [["-", {"w": "create-next-app", "s": "-next-app", "e": true, "k": "-", "d": [25], "c": []}], ["s", {"w": "creates", "s": "s", "e": true, "k": "s", "d": [310, 311], "c": []}]]}]]}], ["a", {"w": "crawling", "s": "awling", "e": true, "k": "a", "d": [257], "c": []}]]}], ["u", {"w": "custom", "s": "ustom", "e": true, "k": "u", "d": [65, 130, 241, 249], "c": [["i", {"w": "customize", "s": "ize", "e": true, "k": "i", "d": [11, 12, 156, 157, 185, 203, 209], "c": []}]]}], ["o", {"w": "co", "s": "o", "e": false, "k": "o", "d": [], "c": [["n", {"w": "con", "s": "n", "e": false, "k": "n", "d": [], "c": [["t", {"w": "cont", "s": "t", "e": false, "k": "t", "d": [], "c": [["e", {"w": "content", "s": "ent", "e": true, "k": "e", "d": [11, 20, 29, 31, 35, 36, 77, 197, 205, 241, 243, 247, 260, 268, 269, 284, 298, 299, 304, 307, 308], "c": [["s", {"w": "contents", "s": "s", "e": true, "k": "s", "d": [156], "c": []}]]}], ["r", {"w": "contr", "s": "r", "e": false, "k": "r", "d": [], "c": [["o", {"w": "control", "s": "ol", "e": true, "k": "o", "d": [53, 221], "c": [["s", {"w": "controls", "s": "s", "e": true, "k": "s", "d": [94], "c": []}]]}], ["a", {"w": "contrast", "s": "ast", "e": true, "k": "a", "d": [72], "c": []}]]}]]}], ["v", {"w": "conve", "s": "ve", "e": false, "k": "v", "d": [], "c": [["r", {"w": "conver", "s": "r", "e": false, "k": "r", "d": [], "c": [["s", {"w": "conversions", "s": "sions", "e": true, "k": "s", "d": [11], "c": []}], ["t", {"w": "convert", "s": "t", "e": false, "k": "t", "d": [], "c": [["e", {"w": "converted", "s": "ed", "e": true, "k": "e", "d": [169], "c": []}], ["s", {"w": "converts", "s": "s", "e": true, "k": "s", "d": [226], "c": []}]]}]]}], ["n", {"w": "convention", "s": "ntion", "e": true, "k": "n", "d": [171], "c": []}]]}], ["c", {"w": "conc", "s": "c", "e": false, "k": "c", "d": [], "c": [["e", {"w": "concerns", "s": "erns", "e": true, "k": "e", "d": [31], "c": []}], ["i", {"w": "concise", "s": "ise", "e": true, "k": "i", "d": [258], "c": []}], ["a", {"w": "concatenates", "s": "atenates", "e": true, "k": "a", "d": [265], "c": []}]]}], ["f", {"w": "config", "s": "fig", "e": true, "k": "f", "d": [33], "c": [["u", {"w": "configur", "s": "ur", "e": false, "k": "u", "d": [], "c": [["e", {"w": "configure", "s": "e", "e": true, "k": "e", "d": [232, 237], "c": [["d", {"w": "configured", "s": "d", "e": true, "k": "d", "d": [33], "c": []}]]}], ["a", {"w": "configuration", "s": "ation", "e": true, "k": "a", "d": [34, 235], "c": []}]]}]]}], ["s", {"w": "cons", "s": "s", "e": false, "k": "s", "d": [], "c": [["i", {"w": "consi", "s": "i", "e": false, "k": "i", "d": [], "c": [["s", {"w": "consistent", "s": "stent", "e": true, "k": "s", "d": [184], "c": []}], ["d", {"w": "considered", "s": "dered", "e": true, "k": "d", "d": [227], "c": []}]]}], ["t", {"w": "constraints", "s": "traints", "e": true, "k": "t", "d": [245], "c": []}]]}]]}], ["l", {"w": "col", "s": "l", "e": false, "k": "l", "d": [], "c": [["o", {"w": "color", "s": "or", "e": true, "k": "o", "d": [72, 302], "c": [["s", {"w": "colors", "s": "s", "e": true, "k": "s", "d": [12, 294, 301], "c": []}]]}], ["l", {"w": "coll", "s": "l", "e": false, "k": "l", "d": [], "c": [["a", {"w": "collapsible", "s": "apsible", "e": true, "k": "a", "d": [69], "c": []}], ["e", {"w": "collection", "s": "ection", "e": true, "k": "e", "d": [313, 314], "c": []}]]}], ["u", {"w": "column", "s": "umn", "e": true, "k": "u", "d": [80], "c": []}]]}], ["m", {"w": "com", "s": "m", "e": true, "k": "m", "d": [25], "c": [["b", {"w": "combined", "s": "bined", "e": true, "k": "b", "d": [29], "c": []}], ["p", {"w": "comp", "s": "p", "e": false, "k": "p", "d": [], "c": [["o", {"w": "component", "s": "onent", "e": true, "k": "o", "d": [142, 160, 165, 226], "c": [["s", {"w": "components", "s": "s", "e": true, "k": "s", "d": [29, 35, 100, 137, 161, 239, 249], "c": []}]]}], ["l", {"w": "compl", "s": "l", "e": false, "k": "l", "d": [], "c": [["i", {"w": "compliance", "s": "iance", "e": true, "k": "i", "d": [50, 93], "c": []}], ["e", {"w": "completed", "s": "eted", "e": true, "k": "e", "d": [64, 88], "c": []}]]}], ["i", {"w": "compil", "s": "il", "e": false, "k": "i", "d": [], "c": [["a", {"w": "compilation", "s": "ation", "e": true, "k": "a", "d": [243], "c": []}], ["e", {"w": "compiled", "s": "ed", "e": true, "k": "e", "d": [247], "c": []}]]}]]}], ["m", {"w": "comm", "s": "m", "e": false, "k": "m", "d": [], "c": [["o", {"w": "common", "s": "on", "e": true, "k": "o", "d": [117], "c": []}], ["a", {"w": "commands", "s": "ands", "e": true, "k": "a", "d": [117], "c": []}]]}], ["e", {"w": "comes", "s": "es", "e": true, "k": "e", "d": [237], "c": []}]]}], ["d", {"w": "code", "s": "de", "e": true, "k": "d", "d": [37, 106, 107, 109, 110, 115, 116, 160, 266, 270, 281, 332, 333, 334], "c": [["s", {"w": "codes", "s": "s", "e": true, "k": "s", "d": [54], "c": []}]]}], ["p", {"w": "copy", "s": "py", "e": true, "k": "p", "d": [169], "c": []}], ["r", {"w": "corpus", "s": "rpus", "e": true, "k": "r", "d": [267], "c": []}]]}], ["l", {"w": "cl", "s": "l", "e": false, "k": "l", "d": [], "c": [["i", {"w": "cli", "s": "i", "e": true, "k": "i", "d": [298, 303, 306], "c": [["c", {"w": "click", "s": "ck", "e": true, "k": "c", "d": [32], "c": []}]]}], ["e", {"w": "clea", "s": "ea", "e": false, "k": "e", "d": [], "c": [["n", {"w": "cleane", "s": "ne", "e": false, "k": "n", "d": [], "c": [["r", {"w": "cleaner", "s": "r", "e": true, "k": "r", "d": [69], "c": []}], ["d", {"w": "cleaned", "s": "d", "e": true, "k": "d", "d": [76], "c": []}]]}], ["r", {"w": "clear", "s": "r", "e": true, "k": "r", "d": [184, 297], "c": []}]]}]]}], ["s", {"w": "cs", "s": "s", "e": false, "k": "s", "d": [], "c": [["v", {"w": "csv", "s": "v", "e": true, "k": "v", "d": [81, 99], "c": []}], ["s", {"w": "css", "s": "s", "e": true, "k": "s", "d": [301], "c": []}]]}], ["m", {"w": "cms", "s": "ms", "e": true, "k": "m", "d": [136], "c": []}]]}], ["f", {"w": "f", "s": "f", "e": true, "k": "f", "d": [167], "c": [["u", {"w": "fu", "s": "u", "e": false, "k": "u", "d": [], "c": [["m", {"w": "fumadocs", "s": "madocs", "e": true, "k": "m", "d": [10, 26, 29, 33, 34, 135, 136, 165, 184, 195, 226, 229, 230, 239, 277, 297], "c": [["'", {"w": "fuma<PERSON><PERSON>'s", "s": "'s", "e": true, "k": "'", "d": [2], "c": []}], ["-", {"w": "fumadocs-starter", "s": "-starter", "e": true, "k": "-", "d": [25], "c": [["n", {"w": "fumadocs-starternpx", "s": "npx", "e": true, "k": "n", "d": [25], "c": []}]]}]]}], ["n", {"w": "function", "s": "nction", "e": true, "k": "n", "d": [167], "c": [["a", {"w": "functionality", "s": "ality", "e": true, "k": "a", "d": [237], "c": []}]]}], ["l", {"w": "full", "s": "ll", "e": true, "k": "l", "d": [194, 237, 259], "c": []}]]}], ["i", {"w": "fi", "s": "i", "e": false, "k": "i", "d": [], "c": [["l", {"w": "fil", "s": "l", "e": false, "k": "l", "d": [], "c": [["e", {"w": "file", "s": "e", "e": true, "k": "e", "d": [3, 30, 33, 34, 64, 90, 96, 162, 173, 185, 196, 203, 204, 209, 238, 250, 264, 268, 284, 292, 299, 303, 305], "c": [["s", {"w": "files", "s": "s", "e": true, "k": "s", "d": [10, 29, 35, 161, 204, 243, 246, 247, 249, 268], "c": []}], ["-", {"w": "file-system", "s": "-system", "e": true, "k": "-", "d": [184], "c": []}]]}], ["t", {"w": "filters", "s": "ters", "e": true, "k": "t", "d": [80, 89], "c": []}], ["l", {"w": "fill", "s": "l", "e": true, "k": "l", "d": [195], "c": []}]]}], ["r", {"w": "first", "s": "rst", "e": true, "k": "r", "d": [8, 30], "c": []}], ["x", {"w": "fix", "s": "x", "e": true, "k": "x", "d": [96], "c": [["e", {"w": "fixed", "s": "ed", "e": true, "k": "e", "d": [54, 71, 73, 98], "c": []}]]}], ["e", {"w": "fields", "s": "elds", "e": true, "k": "e", "d": [70], "c": []}], ["n", {"w": "find", "s": "nd", "e": true, "k": "n", "d": [184], "c": []}]]}], ["o", {"w": "fo", "s": "o", "e": false, "k": "o", "d": [], "c": [["r", {"w": "for", "s": "r", "e": true, "k": "r", "d": [9, 15, 29, 53, 55, 56, 57, 58, 60, 62, 64, 66, 69, 70, 72, 78, 85, 86, 89, 90, 94, 99, 103, 105, 117, 141, 143, 144, 147, 171, 184, 222, 228, 237, 239, 240, 241, 246, 257, 301, 305, 310, 311, 330, 331, 333, 334], "c": [["m", {"w": "form", "s": "m", "e": true, "k": "m", "d": [70], "c": [["a", {"w": "format", "s": "at", "e": true, "k": "a", "d": [68, 136], "c": [["t", {"w": "formatting", "s": "ting", "e": true, "k": "t", "d": [87], "c": []}]]}]]}]]}], ["l", {"w": "fol", "s": "l", "e": false, "k": "l", "d": [], "c": [["d", {"w": "folder", "s": "der", "e": true, "k": "d", "d": [30, 175, 176, 183, 197, 204, 205, 209, 217, 219, 220, 224, 227, 228, 250, 287, 288, 290, 291, 295, 297, 298, 299, 301, 303, 305, 306, 308], "c": [["s", {"w": "folders", "s": "s", "e": true, "k": "s", "d": [10, 203, 209, 228, 230, 297], "c": []}], ["_", {"w": "folder_name", "s": "_name", "e": true, "k": "_", "d": [224], "c": []}]]}], ["l", {"w": "follow", "s": "low", "e": true, "k": "l", "d": [116], "c": [["i", {"w": "following", "s": "ing", "e": true, "k": "i", "d": [33, 304], "c": []}]]}]]}], ["c", {"w": "focus", "s": "cus", "e": true, "k": "c", "d": [85], "c": []}]]}], ["r", {"w": "fr", "s": "r", "e": false, "k": "r", "d": [], "c": [["o", {"w": "fro", "s": "o", "e": false, "k": "o", "d": [], "c": [["m", {"w": "from", "s": "m", "e": true, "k": "m", "d": [13, 33, 81, 196, 223, 224, 262, 283, 313, 314], "c": []}], ["n", {"w": "frontmatter", "s": "ntmatter", "e": true, "k": "n", "d": [185, 262, 266, 270], "c": []}]]}], ["a", {"w": "fra", "s": "a", "e": false, "k": "a", "d": [], "c": [["g", {"w": "fragment", "s": "gment", "e": true, "k": "g", "d": [159], "c": []}], ["m", {"w": "framework", "s": "mework", "e": true, "k": "m", "d": [228], "c": []}]]}]]}], ["e", {"w": "fe", "s": "e", "e": false, "k": "e", "d": [], "c": [["a", {"w": "feature", "s": "ature", "e": true, "k": "a", "d": [46, 237], "c": [["s", {"w": "features", "s": "s", "e": true, "k": "s", "d": [248, 275], "c": []}]]}], ["n", {"w": "fenced", "s": "nced", "e": true, "k": "n", "d": [116], "c": []}], ["w", {"w": "few", "s": "w", "e": true, "k": "w", "d": [257], "c": []}], ["t", {"w": "fetch", "s": "tch", "e": true, "k": "t", "d": [267], "c": []}]]}], ["a", {"w": "fa", "s": "a", "e": false, "k": "a", "d": [], "c": [["i", {"w": "failure", "s": "ilure", "e": true, "k": "i", "d": [75], "c": []}], ["c", {"w": "fact", "s": "ct", "e": true, "k": "c", "d": [136], "c": []}]]}], ["l", {"w": "fl", "s": "l", "e": false, "k": "l", "d": [], "c": [["o", {"w": "flow", "s": "ow", "e": true, "k": "o", "d": [81, 105], "c": []}], ["a", {"w": "flavored", "s": "avored", "e": true, "k": "a", "d": [140], "c": []}]]}]]}], ["o", {"w": "o", "s": "o", "e": false, "k": "o", "d": [], "c": [["p", {"w": "op", "s": "p", "e": false, "k": "p", "d": [], "c": [["e", {"w": "ope", "s": "e", "e": false, "k": "e", "d": [], "c": [["n", {"w": "open", "s": "n", "e": true, "k": "n", "d": [27, 219, 299, 300], "c": [["a", {"w": "opena", "s": "a", "e": false, "k": "a", "d": [], "c": [["p", {"w": "openapi", "s": "pi", "e": true, "k": "p", "d": [2, 3, 13, 272, 273, 276, 277, 278, 284, 285], "c": []}], ["i", {"w": "openai", "s": "i", "e": true, "k": "i", "d": [238, 240, 241], "c": []}]]}], ["e", {"w": "opened", "s": "ed", "e": true, "k": "e", "d": [227], "c": []}], ["i", {"w": "opening", "s": "ing", "e": true, "k": "i", "d": [228], "c": []}]]}], ["r", {"w": "operating", "s": "rating", "e": true, "k": "r", "d": [327, 328], "c": []}]]}], ["t", {"w": "opti", "s": "ti", "e": false, "k": "t", "d": [], "c": [["m", {"w": "optimiz", "s": "miz", "e": false, "k": "m", "d": [], "c": [["a", {"w": "optimizations", "s": "ations", "e": true, "k": "a", "d": [57], "c": []}], ["e", {"w": "optimized", "s": "ed", "e": true, "k": "e", "d": [141], "c": []}]]}], ["o", {"w": "option", "s": "on", "e": false, "k": "o", "d": [], "c": [["s", {"w": "options", "s": "s", "e": true, "k": "s", "d": [68], "c": []}], ["a", {"w": "optional", "s": "al", "e": true, "k": "a", "d": [262, 294], "c": [["l", {"w": "optionally", "s": "ly", "e": true, "k": "l", "d": [116], "c": []}]]}]]}]]}]]}], ["u", {"w": "ou", "s": "u", "e": false, "k": "u", "d": [], "c": [["r", {"w": "our", "s": "r", "e": true, "k": "r", "d": [15], "c": []}], ["t", {"w": "out", "s": "t", "e": true, "k": "t", "d": [29], "c": [["-", {"w": "out-of-the-box", "s": "-of-the-box", "e": true, "k": "-", "d": [32], "c": []}], ["l", {"w": "outline", "s": "line", "e": true, "k": "l", "d": [263], "c": []}]]}]]}], ["f", {"w": "of", "s": "f", "e": true, "k": "f", "d": [24, 71, 116, 118, 135, 136, 137, 147, 156, 161, 189, 191, 193, 196, 204, 215, 221, 238, 240, 243, 259, 260, 264, 265, 269, 284, 319, 320, 322, 324, 325, 333, 334], "c": [["f", {"w": "official", "s": "ficial", "e": true, "k": "f", "d": [33, 277, 278], "c": []}]]}], ["r", {"w": "or", "s": "r", "e": true, "k": "r", "d": [32, 35, 115, 136, 137, 185, 221, 222, 225, 239, 263, 297, 322, 330, 331], "c": [["g", {"w": "organiz", "s": "ganiz", "e": false, "k": "g", "d": [], "c": [["e", {"w": "organize", "s": "e", "e": true, "k": "e", "d": [184, 203, 297], "c": [["s", {"w": "organizes", "s": "s", "e": true, "k": "s", "d": [31], "c": []}]]}], ["i", {"w": "organizing", "s": "ing", "e": true, "k": "i", "d": [171], "c": []}]]}], ["d", {"w": "order", "s": "der", "e": true, "k": "d", "d": [80, 221, 222, 268], "c": []}]]}], ["w", {"w": "own", "s": "wn", "e": true, "k": "w", "d": [35], "c": []}], ["v", {"w": "over", "s": "ver", "e": true, "k": "v", "d": [53], "c": [["l", {"w": "overlapping", "s": "lapping", "e": true, "k": "l", "d": [86], "c": []}]]}], ["l", {"w": "older", "s": "lder", "e": true, "k": "l", "d": [57], "c": []}], ["n", {"w": "on", "s": "n", "e": true, "k": "n", "d": [75, 77, 97, 156, 169, 195, 228, 240], "c": [["l", {"w": "only", "s": "ly", "e": true, "k": "l", "d": [136, 227, 285], "c": []}], ["e", {"w": "one", "s": "e", "e": true, "k": "e", "d": [238], "c": [["-", {"w": "one-line", "s": "-line", "e": true, "k": "-", "d": [262], "c": []}]]}]]}], ["a", {"w": "o<PERSON>h", "s": "auth", "e": true, "k": "a", "d": [105], "c": []}], ["t", {"w": "other", "s": "ther", "e": true, "k": "t", "d": [163, 228], "c": []}]]}], ["e", {"w": "e", "s": "e", "e": true, "k": "e", "d": [31, 155, 228, 250], "c": [["x", {"w": "ex", "s": "x", "e": false, "k": "x", "d": [], "c": [["a", {"w": "example", "s": "ample", "e": true, "k": "a", "d": [2, 3, 33, 228, 271, 281, 298], "c": []}], ["p", {"w": "exp", "s": "p", "e": false, "k": "p", "d": [], "c": [["o", {"w": "expo", "s": "o", "e": false, "k": "o", "d": [], "c": [["r", {"w": "export", "s": "rt", "e": true, "k": "r", "d": [68, 93, 137, 249], "c": [["i", {"w": "exporting", "s": "ing", "e": true, "k": "i", "d": [99], "c": []}]]}], ["n", {"w": "exponential", "s": "nential", "e": true, "k": "n", "d": [104], "c": []}]]}], ["i", {"w": "expiration", "s": "iration", "e": true, "k": "i", "d": [94], "c": []}], ["a", {"w": "expansion", "s": "ansion", "e": true, "k": "a", "d": [167], "c": []}], ["r", {"w": "expressing", "s": "ressing", "e": true, "k": "r", "d": [167], "c": []}], ["l", {"w": "explore", "s": "lore", "e": true, "k": "l", "d": [239], "c": []}]]}], ["t", {"w": "ext", "s": "t", "e": false, "k": "t", "d": [], "c": [["e", {"w": "exte", "s": "e", "e": false, "k": "e", "d": [], "c": [["n", {"w": "extensions", "s": "nsions", "e": true, "k": "n", "d": [135], "c": []}], ["r", {"w": "external", "s": "rnal", "e": true, "k": "r", "d": [143, 240], "c": []}]]}], ["r", {"w": "extract", "s": "ract", "e": true, "k": "r", "d": [180, 224], "c": []}]]}]]}], ["d", {"w": "ed", "s": "d", "e": false, "k": "d", "d": [], "c": [["i", {"w": "edit", "s": "it", "e": true, "k": "i", "d": [301], "c": [["i", {"w": "editing", "s": "ing", "e": true, "k": "i", "d": [8], "c": []}], ["e", {"w": "edited", "s": "ed", "e": true, "k": "e", "d": [98], "c": []}]]}], ["g", {"w": "edge-case", "s": "ge-case", "e": true, "k": "g", "d": [73], "c": []}]]}], ["n", {"w": "en", "s": "n", "e": false, "k": "n", "d": [], "c": [["v", {"w": "env", "s": "v", "e": true, "k": "v", "d": [238], "c": [["i", {"w": "environment", "s": "ironment", "e": true, "k": "i", "d": [8, 18], "c": []}]]}], ["g", {"w": "engagement", "s": "gagement", "e": true, "k": "g", "d": [11, 14], "c": []}], ["d", {"w": "endpoint", "s": "dpoint", "e": true, "k": "d", "d": [13, 279], "c": [["s", {"w": "endpoints", "s": "s", "e": true, "k": "s", "d": [76, 252, 257], "c": []}]]}], ["s", {"w": "ensures", "s": "sures", "e": true, "k": "s", "d": [34], "c": []}], ["h", {"w": "enhancements", "s": "hancements", "e": true, "k": "h", "d": [41], "c": []}], ["f", {"w": "enforced", "s": "forced", "e": true, "k": "f", "d": [95], "c": []}], ["c", {"w": "enclos", "s": "clos", "e": false, "k": "c", "d": [], "c": [["e", {"w": "enclose", "s": "e", "e": true, "k": "e", "d": [115], "c": []}], ["i", {"w": "enclosing", "s": "ing", "e": true, "k": "i", "d": [116], "c": []}]]}], ["a", {"w": "enabl", "s": "abl", "e": false, "k": "a", "d": [], "c": [["e", {"w": "enable", "s": "e", "e": false, "k": "e", "d": [], "c": [["d", {"w": "enabled", "s": "d", "e": true, "k": "d", "d": [237], "c": []}], ["s", {"w": "enables", "s": "s", "e": true, "k": "s", "d": [247], "c": []}]]}], ["i", {"w": "enabling", "s": "ing", "e": true, "k": "i", "d": [264], "c": []}]]}], ["t", {"w": "ent", "s": "t", "e": false, "k": "t", "d": [], "c": [["i", {"w": "entire", "s": "ire", "e": true, "k": "i", "d": [267], "c": []}], ["r", {"w": "entry", "s": "ry", "e": true, "k": "r", "d": [330, 331, 333, 334], "c": []}]]}]]}], ["a", {"w": "ea", "s": "a", "e": false, "k": "a", "d": [], "c": [["s", {"w": "eas", "s": "s", "e": false, "k": "s", "d": [], "c": [["y", {"w": "easy", "s": "y", "e": true, "k": "y", "d": [9, 29], "c": []}], ["i", {"w": "easier", "s": "ier", "e": true, "k": "i", "d": [184], "c": []}]]}], ["c", {"w": "each", "s": "ch", "e": true, "k": "c", "d": [117, 155, 261], "c": []}]]}], ["m", {"w": "em", "s": "m", "e": false, "k": "m", "d": [], "c": [["b", {"w": "embed", "s": "bed", "e": true, "k": "b", "d": [14], "c": []}], ["a", {"w": "email", "s": "ail", "e": true, "k": "a", "d": [53], "c": [["e", {"w": "emailed", "s": "ed", "e": true, "k": "e", "d": [65], "c": []}], ["s", {"w": "emails", "s": "s", "e": true, "k": "s", "d": [88], "c": []}]]}]]}], ["l", {"w": "elements", "s": "lements", "e": true, "k": "l", "d": [14, 226, 228], "c": []}], ["t", {"w": "etc", "s": "tc", "e": true, "k": "t", "d": [31], "c": []}], ["v", {"w": "eve", "s": "ve", "e": false, "k": "v", "d": [], "c": [["n", {"w": "even", "s": "n", "e": true, "k": "n", "d": [137, 246], "c": [["t", {"w": "event", "s": "t", "e": true, "k": "t", "d": [62, 310, 311, 312, 313, 314, 315, 316, 317, 321, 322, 323, 324, 325, 333, 334], "c": [["s", {"w": "events", "s": "s", "e": true, "k": "s", "d": [82, 86, 98, 309, 313, 314, 318, 319, 320, 330, 331], "c": []}]]}]]}], ["r", {"w": "every", "s": "ry", "e": true, "k": "r", "d": [259, 265], "c": []}]]}], ["r", {"w": "error", "s": "rror", "e": true, "k": "r", "d": [150, 153], "c": []}], ["f", {"w": "eff", "s": "ff", "e": false, "k": "f", "d": [], "c": [["e", {"w": "effects", "s": "ects", "e": true, "k": "e", "d": [156], "c": []}], ["i", {"w": "efficient", "s": "icient", "e": true, "k": "i", "d": [241, 257], "c": []}]]}], ["q", {"w": "equations", "s": "quations", "e": true, "k": "q", "d": [169], "c": []}]]}], ["w", {"w": "w", "s": "w", "e": false, "k": "w", "d": [], "c": [["e", {"w": "we", "s": "e", "e": true, "k": "e", "d": [118, 137], "c": [["l", {"w": "welcome", "s": "lcome", "e": true, "k": "l", "d": [3, 5], "c": []}], ["r", {"w": "were", "s": "re", "e": true, "k": "r", "d": [54, 98], "c": []}], ["b", {"w": "web", "s": "b", "e": true, "k": "b", "d": [240], "c": [["h", {"w": "webhook", "s": "hook", "e": true, "k": "h", "d": [62], "c": [["s", {"w": "webhooks", "s": "s", "e": true, "k": "s", "d": [104], "c": []}]]}], ["s", {"w": "website", "s": "site", "e": true, "k": "s", "d": [308], "c": []}]]}], ["e", {"w": "weekly", "s": "ekly", "e": true, "k": "e", "d": [88], "c": []}]]}], ["i", {"w": "wi", "s": "i", "e": false, "k": "i", "d": [], "c": [["t", {"w": "with", "s": "th", "e": true, "k": "t", "d": [24, 29, 32, 33, 36, 37, 68, 69, 75, 84, 104, 116, 137, 158, 160, 224, 237, 241, 258, 286, 333, 334], "c": []}], ["l", {"w": "will", "s": "ll", "e": true, "k": "l", "d": [143, 156, 169, 204, 227, 297], "c": []}], ["k", {"w": "wikipedia", "s": "kipedia", "e": true, "k": "k", "d": [169], "c": []}]]}], ["r", {"w": "wr", "s": "r", "e": false, "k": "r", "d": [], "c": [["i", {"w": "write", "s": "ite", "e": true, "k": "i", "d": [29, 116], "c": []}], ["a", {"w": "wrap", "s": "ap", "e": true, "k": "a", "d": [204], "c": []}]]}], ["o", {"w": "wor", "s": "or", "e": false, "k": "o", "d": [], "c": [["k", {"w": "work", "s": "k", "e": true, "k": "k", "d": [85], "c": [["s", {"w": "works", "s": "s", "e": true, "k": "s", "d": [32], "c": []}], ["d", {"w": "workdir", "s": "dir", "e": true, "k": "d", "d": [33], "c": []}]]}], ["d", {"w": "word", "s": "d", "e": true, "k": "d", "d": [115], "c": []}], ["l", {"w": "world", "s": "ld", "e": true, "k": "l", "d": [154, 155], "c": []}]]}], ["a", {"w": "wa", "s": "a", "e": false, "k": "a", "d": [], "c": [["n", {"w": "want", "s": "nt", "e": true, "k": "n", "d": [33], "c": []}], ["r", {"w": "warn", "s": "rn", "e": true, "k": "r", "d": [149], "c": [["i", {"w": "warnings", "s": "ings", "e": true, "k": "i", "d": [147], "c": []}]]}], ["s", {"w": "waste", "s": "ste", "e": true, "k": "s", "d": [257], "c": []}], ["y", {"w": "way", "s": "y", "e": true, "k": "y", "d": [257], "c": []}], ["l", {"w": "walk", "s": "lk", "e": true, "k": "l", "d": [297], "c": []}]]}], ["h", {"w": "wh", "s": "h", "e": false, "k": "h", "d": [], "c": [["e", {"w": "whe", "s": "e", "e": false, "k": "e", "d": [], "c": [["r", {"w": "where", "s": "re", "e": true, "k": "r", "d": [54, 98], "c": []}], ["n", {"w": "when", "s": "n", "e": true, "k": "n", "d": [59, 98, 169, 228, 246], "c": []}]]}], ["i", {"w": "which", "s": "ich", "e": true, "k": "i", "d": [230], "c": []}]]}]]}], ["y", {"w": "y", "s": "y", "e": false, "k": "y", "d": [], "c": [["o", {"w": "you", "s": "ou", "e": true, "k": "o", "d": [3, 26, 29, 32, 33, 35, 116, 118, 136, 137, 146, 147, 156, 157, 158, 160, 161, 169, 184, 185, 203, 204, 221, 223, 224, 228, 237, 238, 239, 241, 249, 257, 284, 297, 298], "c": [["r", {"w": "your", "s": "r", "e": true, "k": "r", "d": [5, 8, 9, 10, 11, 12, 13, 18, 20, 21, 23, 27, 29, 30, 32, 33, 34, 35, 36, 37, 116, 164, 171, 184, 232, 238, 239, 241, 250, 257, 261, 264, 267, 273, 277, 284, 288, 297, 299, 301, 303, 305, 306, 308, 333, 334], "c": [["s", {"w": "yours", "s": "s", "e": true, "k": "s", "d": [7], "c": []}], ["-", {"w": "your-domain", "s": "-domain", "e": true, "k": "-", "d": [264], "c": []}]]}], ["'", {"w": "you've", "s": "'ve", "e": true, "k": "'", "d": [308], "c": []}]]}], ["m", {"w": "yml'", "s": "ml'", "e": true, "k": "m", "d": [3], "c": []}], ["e", {"w": "year-end", "s": "ear-end", "e": true, "k": "e", "d": [45], "c": []}], ["a", {"w": "yarn", "s": "arn", "e": true, "k": "a", "d": [117], "c": []}]]}], ["u", {"w": "u", "s": "u", "e": false, "k": "u", "d": [], "c": [["p", {"w": "up", "s": "p", "e": true, "k": "p", "d": [8, 9, 76, 92, 234, 238], "c": [["d", {"w": "update", "s": "date", "e": true, "k": "d", "d": [3, 23, 292, 293, 294, 302, 323, 324, 325], "c": [["s", {"w": "updates", "s": "s", "e": true, "k": "s", "d": [38, 39, 46, 51, 60, 93], "c": []}], ["d", {"w": "updated", "s": "d", "e": true, "k": "d", "d": [100, 322], "c": []}]]}], ["g", {"w": "upgrades", "s": "grades", "e": true, "k": "g", "d": [42], "c": []}], ["l", {"w": "uploads", "s": "loads", "e": true, "k": "l", "d": [64], "c": []}], ["c", {"w": "upcoming", "s": "coming", "e": true, "k": "c", "d": [319, 320, 327, 328], "c": []}]]}], ["s", {"w": "us", "s": "s", "e": false, "k": "s", "d": [], "c": [["e", {"w": "use", "s": "e", "e": true, "k": "e", "d": [35, 116, 136, 137, 142, 160, 225, 232, 249], "c": [["r", {"w": "user", "s": "r", "e": true, "k": "r", "d": [11, 14, 47, 67, 79, 81, 82, 99, 230, 240], "c": [["s", {"w": "users", "s": "s", "e": true, "k": "s", "d": [54, 83, 89, 95, 117, 184], "c": []}]]}], ["s", {"w": "uses", "s": "s", "e": true, "k": "s", "d": [29, 184, 240, 284, 298], "c": []}], ["f", {"w": "useful", "s": "ful", "e": true, "k": "f", "d": [135, 144, 147], "c": []}], ["d", {"w": "used", "s": "d", "e": true, "k": "d", "d": [333, 334], "c": []}]]}], ["i", {"w": "using", "s": "ing", "e": true, "k": "i", "d": [33, 35, 116, 117, 221, 250], "c": []}], ["a", {"w": "usage", "s": "age", "e": true, "k": "a", "d": [55, 101], "c": [["s", {"w": "usages", "s": "s", "e": true, "k": "s", "d": [163], "c": []}]]}]]}], ["n", {"w": "un", "s": "n", "e": false, "k": "n", "d": [], "c": [["d", {"w": "under", "s": "der", "e": true, "k": "d", "d": [17], "c": []}], ["l", {"w": "unless", "s": "less", "e": true, "k": "l", "d": [221], "c": []}]]}], ["x", {"w": "ux", "s": "x", "e": true, "k": "x", "d": [44], "c": []}], ["i", {"w": "ui", "s": "i", "e": true, "k": "i", "d": [93, 100, 195, 229, 230], "c": []}], ["r", {"w": "url", "s": "rl", "e": true, "k": "r", "d": [225, 250], "c": [["s", {"w": "urls", "s": "s", "e": true, "k": "s", "d": [250, 258], "c": []}]]}]]}], ["'", {"w": "'", "s": "'", "e": false, "k": "'", "d": [], "c": [["o", {"w": "'openapi", "s": "openapi", "e": true, "k": "o", "d": [3], "c": []}], ["d", {"w": "'dir'", "s": "dir'", "e": true, "k": "d", "d": [200, 202], "c": []}], ["p", {"w": "'page'", "s": "page'", "e": true, "k": "p", "d": [200, 208], "c": []}]]}], ["n", {"w": "n", "s": "n", "e": false, "k": "n", "d": [], "c": [["e", {"w": "ne", "s": "e", "e": false, "k": "e", "d": [], "c": [["w", {"w": "new", "s": "w", "e": true, "k": "w", "d": [5, 39, 52, 64, 72, 77, 94, 100, 288, 290, 291, 295, 296, 297, 298, 299, 300, 301, 302, 303, 305, 306, 308, 310, 311, 321, 322], "c": []}], ["x", {"w": "next", "s": "xt", "e": true, "k": "x", "d": [24, 29, 33, 141, 142, 145], "c": [["-", {"w": "next-mdx-remote", "s": "-mdx-remote", "e": true, "k": "-", "d": [136], "c": []}]]}], ["t", {"w": "net", "s": "t", "e": false, "k": "t", "d": [], "c": [["l", {"w": "netlify", "s": "lify", "e": true, "k": "l", "d": [32], "c": []}], ["w", {"w": "networks", "s": "works", "e": true, "k": "w", "d": [59], "c": []}]]}], ["e", {"w": "need", "s": "ed", "e": true, "k": "e", "d": [184, 237, 249], "c": [["e", {"w": "needed", "s": "ed", "e": true, "k": "e", "d": [76], "c": []}], ["s", {"w": "needs", "s": "s", "e": true, "k": "s", "d": [239], "c": []}]]}]]}], ["o", {"w": "no", "s": "o", "e": true, "k": "o", "d": [96, 249, 263], "c": [["d", {"w": "node", "s": "de", "e": true, "k": "d", "d": [24], "c": []}], ["t", {"w": "not", "s": "t", "e": true, "k": "t", "d": [54, 136, 221, 228], "c": [["e", {"w": "note", "s": "e", "e": true, "k": "e", "d": [24, 161], "c": [["s", {"w": "notes", "s": "s", "e": true, "k": "s", "d": [87], "c": []}]]}], ["i", {"w": "noti", "s": "i", "e": false, "k": "i", "d": [], "c": [["f", {"w": "notification", "s": "fication", "e": true, "k": "f", "d": [53, 60], "c": [["s", {"w": "notifications", "s": "s", "e": true, "k": "s", "d": [85], "c": []}]]}], ["o", {"w": "notion", "s": "on", "e": true, "k": "o", "d": [103], "c": []}]]}], ["a", {"w": "notations", "s": "ations", "e": true, "k": "a", "d": [114, 119], "c": []}]]}], ["w", {"w": "now", "s": "w", "e": true, "k": "w", "d": [26, 55, 61, 63, 65, 66, 68, 75, 79, 80, 83, 87, 88, 89, 102, 104, 308], "c": []}], ["v", {"w": "november", "s": "vember", "e": true, "k": "v", "d": [73], "c": []}], ["r", {"w": "<PERSON><PERSON><PERSON><PERSON>", "s": "referrer", "e": true, "k": "r", "d": [143], "c": []}], ["o", {"w": "noopener", "s": "opener", "e": true, "k": "o", "d": [143], "c": []}]]}], ["p", {"w": "np", "s": "p", "e": false, "k": "p", "d": [], "c": [["x", {"w": "npx", "s": "x", "e": true, "k": "x", "d": [25], "c": []}], ["m", {"w": "npm", "s": "m", "e": true, "k": "m", "d": [28, 117], "c": []}]]}], ["a", {"w": "na", "s": "a", "e": false, "k": "a", "d": [], "c": [["v", {"w": "navigat", "s": "vigat", "e": false, "k": "v", "d": [], "c": [["e", {"w": "navigate", "s": "e", "e": true, "k": "e", "d": [29, 184, 308], "c": []}], ["i", {"w": "navigation", "s": "ion", "e": true, "k": "i", "d": [49, 56, 69, 91, 228], "c": []}]]}], ["t", {"w": "native", "s": "tive", "e": true, "k": "t", "d": [103], "c": []}], ["m", {"w": "name", "s": "me", "e": true, "k": "m", "d": [116, 186, 193, 204, 210, 213, 215, 223, 298, 299], "c": [["s", {"w": "names", "s": "s", "e": true, "k": "s", "d": [226], "c": []}]]}]]}]]}], ["d", {"w": "d", "s": "d", "e": false, "k": "d", "d": [], "c": [["o", {"w": "do", "s": "o", "e": false, "k": "o", "d": [], "c": [["c", {"w": "doc", "s": "c", "e": false, "k": "c", "d": [], "c": [["u", {"w": "document", "s": "ument", "e": true, "k": "u", "d": [90, 137, 162, 259, 273, 277], "c": [["a", {"w": "documentation", "s": "ation", "e": true, "k": "a", "d": [5, 8, 11, 12, 17, 29, 32, 184, 232, 246, 257, 261, 267, 284, 288, 297, 308], "c": [["i", {"w": "documentationitem", "s": "item", "e": true, "k": "i", "d": [300], "c": []}]]}], ["s", {"w": "documents", "s": "s", "e": true, "k": "s", "d": [171, 184], "c": []}]]}], ["s", {"w": "docs", "s": "s", "e": true, "k": "s", "d": [9, 13, 23, 27, 29, 30, 31, 36, 37, 164, 268, 284, 286, 297, 298, 299, 308], "c": []}], ["k", {"w": "docker", "s": "ker", "e": true, "k": "k", "d": [22, 33], "c": [["f", {"w": "dockerfile", "s": "file", "e": true, "k": "f", "d": [33], "c": []}]]}]]}], ["n", {"w": "don", "s": "n", "e": true, "k": "n", "d": [238], "c": []}]]}], ["e", {"w": "de", "s": "e", "e": false, "k": "e", "d": [], "c": [["v", {"w": "dev", "s": "v", "e": true, "k": "v", "d": [28], "c": [["e", {"w": "development", "s": "elopment", "e": true, "k": "e", "d": [9, 18, 19, 26, 27, 246], "c": []}], ["p", {"w": "devpnpm", "s": "pnpm", "e": true, "k": "p", "d": [28], "c": []}], ["y", {"w": "<PERSON><PERSON><PERSON><PERSON>", "s": "yarn", "e": true, "k": "y", "d": [28], "c": []}], ["b", {"w": "devbun", "s": "bun", "e": true, "k": "b", "d": [28], "c": []}], ["i", {"w": "devices", "s": "ices", "e": true, "k": "i", "d": [57], "c": []}]]}], ["f", {"w": "def", "s": "f", "e": false, "k": "f", "d": [], "c": [["i", {"w": "defin", "s": "in", "e": false, "k": "i", "d": [], "c": [["e", {"w": "define", "s": "e", "e": true, "k": "e", "d": [10, 301], "c": [["s", {"w": "defines", "s": "s", "e": true, "k": "s", "d": [305], "c": []}]]}], ["i", {"w": "definitions", "s": "itions", "e": true, "k": "i", "d": [282], "c": []}]]}], ["a", {"w": "default", "s": "ault", "e": true, "k": "a", "d": [116, 135, 143, 147, 148, 204, 219, 220, 237, 241, 246, 247], "c": [["o", {"w": "defaultopen", "s": "open", "e": true, "k": "o", "d": [218], "c": []}]]}]]}], ["p", {"w": "dep", "s": "p", "e": false, "k": "p", "d": [], "c": [["l", {"w": "deploy", "s": "loy", "e": true, "k": "l", "d": [21, 32, 33], "c": [["m", {"w": "deployment", "s": "ment", "e": true, "k": "m", "d": [22], "c": []}], ["e", {"w": "deployed", "s": "ed", "e": true, "k": "e", "d": [264], "c": []}]]}], ["r", {"w": "deprecated", "s": "recated", "e": true, "k": "r", "d": [76], "c": []}]]}], ["t", {"w": "det", "s": "t", "e": false, "k": "t", "d": [], "c": [["a", {"w": "details", "s": "ails", "e": true, "k": "a", "d": [55, 316, 317, 322, 324, 325], "c": []}], ["e", {"w": "detects", "s": "ects", "e": true, "k": "e", "d": [117], "c": []}]]}], ["l", {"w": "del", "s": "l", "e": false, "k": "l", "d": [], "c": [["a", {"w": "delays", "s": "ays", "e": true, "k": "a", "d": [59], "c": []}], ["e", {"w": "delete", "s": "ete", "e": true, "k": "e", "d": [93, 312, 313, 314], "c": []}]]}], ["e", {"w": "deep-linking", "s": "ep-linking", "e": true, "k": "e", "d": [61], "c": []}], ["a", {"w": "deactivation", "s": "activation", "e": true, "k": "a", "d": [82], "c": []}], ["s", {"w": "desc", "s": "sc", "e": false, "k": "s", "d": [], "c": [["r", {"w": "description", "s": "ription", "e": true, "k": "r", "d": [187, 190, 191, 211, 305], "c": [["s", {"w": "descriptions", "s": "s", "e": true, "k": "s", "d": [103, 262], "c": []}]]}], ["e", {"w": "descending", "s": "ending", "e": true, "k": "e", "d": [222], "c": []}]]}], ["n", {"w": "denote", "s": "note", "e": true, "k": "n", "d": [115], "c": []}]]}], ["i", {"w": "di", "s": "i", "e": false, "k": "i", "d": [], "c": [["r", {"w": "dir", "s": "r", "e": true, "k": "r", "d": [199, 201], "c": [["e", {"w": "direct", "s": "ect", "e": true, "k": "e", "d": [102], "c": [["o", {"w": "directory", "s": "ory", "e": true, "k": "o", "d": [29, 239, 298, 299, 300, 301], "c": []}], ["l", {"w": "directly", "s": "ly", "e": true, "k": "l", "d": [35, 37], "c": []}]]}]]}], ["s", {"w": "dis", "s": "s", "e": false, "k": "s", "d": [], "c": [["t", {"w": "distraction-free", "s": "traction-free", "e": true, "k": "t", "d": [85], "c": []}], ["p", {"w": "display", "s": "play", "e": true, "k": "p", "d": [107, 213], "c": [["s", {"w": "displays", "s": "s", "e": true, "k": "s", "d": [117], "c": []}]]}], ["c", {"w": "discover", "s": "cover", "e": true, "k": "c", "d": [264], "c": []}]]}], ["f", {"w": "different", "s": "fferent", "e": true, "k": "f", "d": [117, 281], "c": []}], ["a", {"w": "diagrams", "s": "agrams", "e": true, "k": "a", "d": [164], "c": []}]]}], ["u", {"w": "du", "s": "u", "e": false, "k": "u", "d": [], "c": [["r", {"w": "during", "s": "ring", "e": true, "k": "r", "d": [34, 73, 74], "c": []}], ["p", {"w": "duplicated", "s": "plicated", "e": true, "k": "p", "d": [98], "c": []}], ["m", {"w": "dump", "s": "mp", "e": true, "k": "m", "d": [259], "c": []}]]}], ["a", {"w": "da", "s": "a", "e": false, "k": "a", "d": [], "c": [["s", {"w": "dashboard", "s": "shboard", "e": true, "k": "s", "d": [56, 66, 97], "c": []}], ["t", {"w": "dat", "s": "t", "e": false, "k": "t", "d": [], "c": [["a", {"w": "data", "s": "a", "e": true, "k": "a", "d": [59, 93], "c": []}], ["e", {"w": "date", "s": "e", "e": true, "k": "e", "d": [89], "c": []}]]}], ["r", {"w": "dark", "s": "rk", "e": true, "k": "r", "d": [72], "c": []}]]}], ["r", {"w": "dr", "s": "r", "e": false, "k": "r", "d": [], "c": [["i", {"w": "drill-down", "s": "ill-down", "e": true, "k": "i", "d": [66], "c": []}], ["o", {"w": "dropdowns", "s": "opdowns", "e": true, "k": "o", "d": [71], "c": []}], ["a", {"w": "drag-and-drop", "s": "ag-and-drop", "e": true, "k": "a", "d": [86], "c": []}]]}]]}], ["g", {"w": "g", "s": "g", "e": true, "k": "g", "d": [31, 155, 228, 250], "c": [["e", {"w": "ge", "s": "e", "e": false, "k": "e", "d": [], "c": [["t", {"w": "get", "s": "t", "e": true, "k": "t", "d": [116, 143, 315, 316, 317, 326, 327, 328, 332], "c": [["t", {"w": "getting", "s": "ting", "e": true, "k": "t", "d": [6], "c": []}]]}], ["n", {"w": "genera", "s": "nera", "e": false, "k": "n", "d": [], "c": [["l", {"w": "general", "s": "l", "e": true, "k": "l", "d": [40, 330, 331], "c": []}], ["t", {"w": "generat", "s": "t", "e": false, "k": "t", "d": [], "c": [["e", {"w": "generate", "s": "e", "e": true, "k": "e", "d": [238, 273, 277, 284, 286], "c": [["d", {"w": "generated", "s": "d", "e": true, "k": "d", "d": [156, 196, 264, 268, 283], "c": []}], ["-", {"w": "generate-docs", "s": "-docs", "e": true, "k": "-", "d": [284], "c": []}]]}], ["i", {"w": "generation", "s": "ion", "e": true, "k": "i", "d": [240, 241], "c": []}]]}]]}], ["s", {"w": "gesture", "s": "sture", "e": true, "k": "s", "d": [58], "c": []}]]}], ["u", {"w": "guide", "s": "uide", "e": true, "k": "u", "d": [76, 297], "c": [["s", {"w": "guides", "s": "s", "e": true, "k": "s", "d": [14], "c": []}], ["l", {"w": "guidelines", "s": "lines", "e": true, "k": "l", "d": [100], "c": []}]]}], ["i", {"w": "github", "s": "<PERSON>hub", "e": true, "k": "i", "d": [25, 140], "c": []}], ["r", {"w": "gr", "s": "r", "e": false, "k": "r", "d": [], "c": [["a", {"w": "granular", "s": "anular", "e": true, "k": "a", "d": [55], "c": []}], ["o", {"w": "group", "s": "oup", "e": true, "k": "o", "d": [176], "c": [["s", {"w": "groups", "s": "s", "e": true, "k": "s", "d": [131], "c": []}], ["-", {"w": "group-name", "s": "-name", "e": true, "k": "-", "d": [207], "c": []}]]}]]}], ["l", {"w": "glob", "s": "lob", "e": false, "k": "l", "d": [], "c": [["a", {"w": "global", "s": "al", "e": true, "k": "a", "d": [89], "c": [["s", {"w": "globals", "s": "s", "e": true, "k": "s", "d": [301], "c": []}]]}], ["b", {"w": "globbing", "s": "bing", "e": true, "k": "b", "d": [268], "c": []}]]}], ["d", {"w": "gdpr", "s": "dpr", "e": true, "k": "d", "d": [93], "c": []}], ["o", {"w": "google", "s": "oogle", "e": true, "k": "o", "d": [105], "c": []}], ["f", {"w": "gfm", "s": "fm", "e": true, "k": "f", "d": [140], "c": []}]]}], ["s", {"w": "s", "s": "s", "e": false, "k": "s", "d": [], "c": [["t", {"w": "st", "s": "t", "e": false, "k": "t", "d": [], "c": [["a", {"w": "sta", "s": "a", "e": false, "k": "a", "d": [], "c": [["r", {"w": "start", "s": "rt", "e": true, "k": "r", "d": [17, 19, 26], "c": [["e", {"w": "started", "s": "ed", "e": true, "k": "e", "d": [6], "c": []}], ["u", {"w": "startup", "s": "up", "e": true, "k": "u", "d": [246], "c": []}]]}], ["n", {"w": "standard", "s": "ndard", "e": true, "k": "n", "d": [29, 121], "c": []}], ["b", {"w": "stability", "s": "bility", "e": true, "k": "b", "d": [45], "c": []}], ["t", {"w": "stat", "s": "t", "e": false, "k": "t", "d": [], "c": [["u", {"w": "status", "s": "us", "e": true, "k": "u", "d": [66], "c": []}], ["e", {"w": "statements", "s": "ements", "e": true, "k": "e", "d": [249], "c": []}]]}]]}], ["e", {"w": "step", "s": "ep", "e": true, "k": "e", "d": [8, 291, 292, 293, 294, 295, 296, 297], "c": [["s", {"w": "steps", "s": "s", "e": true, "k": "s", "d": [290], "c": []}]]}], ["r", {"w": "str", "s": "r", "e": false, "k": "r", "d": [], "c": [["u", {"w": "structure", "s": "ucture", "e": true, "k": "u", "d": [10, 29, 184], "c": []}], ["a", {"w": "straight", "s": "aight", "e": true, "k": "a", "d": [13], "c": []}], ["e", {"w": "strength", "s": "ength", "e": true, "k": "e", "d": [95], "c": []}], ["i", {"w": "string", "s": "ing", "e": true, "k": "i", "d": [169], "c": []}]]}], ["y", {"w": "styl", "s": "yl", "e": false, "k": "y", "d": [], "c": [["e", {"w": "style", "s": "e", "e": true, "k": "e", "d": [118], "c": [["s", {"w": "styles", "s": "s", "e": true, "k": "s", "d": [12, 301, 302], "c": []}]]}], ["i", {"w": "styling", "s": "ing", "e": true, "k": "i", "d": [68, 121, 263], "c": []}]]}], ["o", {"w": "story", "s": "ory", "e": true, "k": "o", "d": [63], "c": []}], ["i", {"w": "sticky", "s": "icky", "e": true, "k": "i", "d": [87], "c": []}]]}], ["e", {"w": "se", "s": "e", "e": false, "k": "e", "d": [], "c": [["t", {"w": "set", "s": "t", "e": true, "k": "t", "d": [9], "c": [["t", {"w": "setting", "s": "ting", "e": true, "k": "t", "d": [8, 234], "c": [["s", {"w": "settings", "s": "s", "e": true, "k": "s", "d": [53, 72, 129, 158], "c": []}]]}], ["u", {"w": "setup", "s": "up", "e": true, "k": "u", "d": [18, 241], "c": []}]]}], ["r", {"w": "ser", "s": "r", "e": false, "k": "r", "d": [], "c": [["v", {"w": "server", "s": "ver", "e": true, "k": "v", "d": [19, 74, 246], "c": []}], ["i", {"w": "series", "s": "ies", "e": true, "k": "i", "d": [167], "c": []}]]}], ["a", {"w": "search", "s": "arch", "e": true, "k": "a", "d": [49, 89, 92, 231, 232, 237, 238, 239, 240], "c": []}], ["c", {"w": "sec", "s": "c", "e": false, "k": "c", "d": [], "c": [["u", {"w": "security", "s": "urity", "e": true, "k": "u", "d": [50, 143], "c": []}], ["t", {"w": "section", "s": "tion", "e": true, "k": "t", "d": [261], "c": [["s", {"w": "sections", "s": "s", "e": true, "k": "s", "d": [69, 297], "c": []}]]}]]}], ["s", {"w": "session", "s": "ssion", "e": true, "k": "s", "d": [94], "c": []}], ["e", {"w": "see", "s": "e", "e": true, "k": "e", "d": [138, 140, 163, 193, 215, 217], "c": []}], ["n", {"w": "send", "s": "nd", "e": true, "k": "n", "d": [281], "c": []}]]}], ["i", {"w": "si", "s": "i", "e": false, "k": "i", "d": [], "c": [["d", {"w": "sidebar", "s": "debar", "e": true, "k": "d", "d": [10, 69, 85, 228, 230, 268], "c": []}], ["m", {"w": "sim", "s": "m", "e": false, "k": "m", "d": [], "c": [["i", {"w": "similar", "s": "ilar", "e": true, "k": "i", "d": [29], "c": []}], ["p", {"w": "simple", "s": "ple", "e": true, "k": "p", "d": [257], "c": []}]]}], ["t", {"w": "site", "s": "te", "e": true, "k": "t", "d": [32, 257, 264], "c": [["s", {"w": "sites", "s": "s", "e": true, "k": "s", "d": [246], "c": []}], ["m", {"w": "sitemap", "s": "map", "e": true, "k": "m", "d": [258], "c": []}]]}], ["n", {"w": "single", "s": "ngle", "e": true, "k": "n", "d": [32, 267], "c": []}], ["g", {"w": "signing", "s": "gning", "e": true, "k": "g", "d": [238], "c": []}]]}], ["p", {"w": "sp", "s": "p", "e": false, "k": "p", "d": [], "c": [["e", {"w": "spec", "s": "ec", "e": true, "k": "e", "d": [13], "c": [["i", {"w": "speci", "s": "i", "e": false, "k": "i", "d": [], "c": [["f", {"w": "specif", "s": "f", "e": false, "k": "f", "d": [], "c": [["i", {"w": "specific", "s": "ic", "e": true, "k": "i", "d": [118, 159], "c": [["a", {"w": "specification", "s": "ation", "e": true, "k": "a", "d": [140], "c": []}]]}], ["y", {"w": "specify", "s": "y", "e": true, "k": "y", "d": [147, 162], "c": []}]]}], ["a", {"w": "special", "s": "al", "e": true, "k": "a", "d": [309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 323, 324, 325, 330, 331], "c": []}]]}]]}], ["r", {"w": "sprint", "s": "rint", "e": true, "k": "r", "d": [63], "c": []}], ["a", {"w": "space", "s": "ace", "e": true, "k": "a", "d": [195], "c": [["s", {"w": "spaces", "s": "s", "e": true, "k": "s", "d": [155], "c": []}]]}]]}], ["h", {"w": "sh", "s": "h", "e": false, "k": "h", "d": [], "c": [["o", {"w": "sho", "s": "o", "e": false, "k": "o", "d": [], "c": [["w", {"w": "show", "s": "w", "e": true, "k": "w", "d": [92], "c": [["c", {"w": "showcase", "s": "case", "e": true, "k": "c", "d": [15], "c": []}], ["n", {"w": "shown", "s": "n", "e": true, "k": "n", "d": [228], "c": []}]]}], ["u", {"w": "should", "s": "uld", "e": true, "k": "u", "d": [26], "c": []}], ["r", {"w": "shortcuts", "s": "rtcuts", "e": true, "k": "r", "d": [56], "c": []}]]}], ["i", {"w": "shi", "s": "i", "e": false, "k": "i", "d": [], "c": [["k", {"w": "shiki", "s": "ki", "e": true, "k": "k", "d": [113, 118], "c": []}], ["p", {"w": "shipping", "s": "pping", "e": true, "k": "p", "d": [257], "c": []}]]}], ["a", {"w": "shared", "s": "ared", "e": true, "k": "a", "d": [171], "c": []}]]}], ["u", {"w": "su", "s": "u", "e": false, "k": "u", "d": [], "c": [["r", {"w": "sure", "s": "re", "e": true, "k": "r", "d": [33], "c": []}], ["p", {"w": "sup", "s": "p", "e": false, "k": "p", "d": [], "c": [["p", {"w": "support", "s": "port", "e": true, "k": "p", "d": [58, 70, 87, 99, 103, 118, 251], "c": [["s", {"w": "supports", "s": "s", "e": true, "k": "s", "d": [61, 66, 89, 102, 165, 278], "c": []}], ["e", {"w": "supported", "s": "ed", "e": true, "k": "e", "d": [116, 136, 140, 285], "c": []}]]}], ["e", {"w": "superset", "s": "erset", "e": true, "k": "e", "d": [137], "c": []}]]}], ["m", {"w": "summar", "s": "mmar", "e": false, "k": "m", "d": [], "c": [["y", {"w": "summary", "s": "y", "e": true, "k": "y", "d": [67, 88], "c": []}], ["i", {"w": "summaries", "s": "ies", "e": true, "k": "i", "d": [258], "c": []}]]}], ["b", {"w": "subtasks", "s": "btasks", "e": true, "k": "b", "d": [78], "c": []}], ["g", {"w": "suggestions", "s": "ggestions", "e": true, "k": "g", "d": [92], "c": []}], ["c", {"w": "suc", "s": "c", "e": false, "k": "c", "d": [], "c": [["h", {"w": "such", "s": "h", "e": true, "k": "h", "d": [136, 241], "c": []}], ["c", {"w": "successfully", "s": "cessfully", "e": true, "k": "c", "d": [308], "c": []}]]}], ["i", {"w": "suit", "s": "it", "e": true, "k": "i", "d": [239], "c": []}]]}], ["o", {"w": "so", "s": "o", "e": false, "k": "o", "d": [], "c": [["u", {"w": "source", "s": "urce", "e": true, "k": "u", "d": [33], "c": []}], ["m", {"w": "some", "s": "me", "e": true, "k": "m", "d": [54, 118, 248], "c": []}], ["r", {"w": "sorted", "s": "rted", "e": true, "k": "r", "d": [220, 222], "c": []}]]}], ["n", {"w": "snippet", "s": "nippet", "e": true, "k": "n", "d": [33, 116], "c": []}], ["y", {"w": "sy", "s": "y", "e": false, "k": "y", "d": [], "c": [["n", {"w": "syn", "s": "n", "e": false, "k": "n", "d": [], "c": [["t", {"w": "syntax", "s": "tax", "e": true, "k": "t", "d": [35, 36, 37, 116, 120, 135, 137, 139, 225], "c": []}], ["c", {"w": "sync", "s": "c", "e": true, "k": "c", "d": [59, 63, 101], "c": []}]]}], ["s", {"w": "system", "s": "stem", "e": true, "k": "s", "d": [184], "c": []}]]}], ["m", {"w": "sm", "s": "m", "e": false, "k": "m", "d": [], "c": [["s", {"w": "sms", "s": "s", "e": true, "k": "s", "d": [54], "c": []}], ["a", {"w": "smart", "s": "art", "e": true, "k": "a", "d": [77], "c": []}]]}], ["w", {"w": "switch", "s": "witch", "e": true, "k": "w", "d": [117, 230], "c": [["i", {"w": "switching", "s": "ing", "e": true, "k": "i", "d": [59], "c": []}]]}], ["c", {"w": "sc", "s": "c", "e": false, "k": "c", "d": [], "c": [["h", {"w": "sche", "s": "he", "e": false, "k": "h", "d": [], "c": [["d", {"w": "scheduled", "s": "duled", "e": true, "k": "d", "d": [65], "c": []}], ["m", {"w": "schema", "s": "ma", "e": true, "k": "m", "d": [273, 276, 277, 284], "c": [["s", {"w": "schemas", "s": "s", "e": true, "k": "s", "d": [283], "c": []}]]}]]}], ["r", {"w": "scr", "s": "r", "e": false, "k": "r", "d": [], "c": [["e", {"w": "screen", "s": "een", "e": true, "k": "e", "d": [70], "c": []}], ["i", {"w": "script", "s": "ipt", "e": true, "k": "i", "d": [284, 286], "c": []}]]}], ["a", {"w": "scannable", "s": "annable", "e": true, "k": "a", "d": [333, 334], "c": []}]]}], ["a", {"w": "sa", "s": "a", "e": false, "k": "a", "d": [], "c": [["f", {"w": "safari", "s": "fari", "e": true, "k": "f", "d": [71], "c": []}], ["n", {"w": "sanitizes", "s": "nitizes", "e": true, "k": "n", "d": [155], "c": []}], ["m", {"w": "samples", "s": "mples", "e": true, "k": "m", "d": [266, 270, 282], "c": []}]]}], ["s", {"w": "sso", "s": "so", "e": true, "k": "s", "d": [84], "c": []}], ["l", {"w": "sl", "s": "l", "e": false, "k": "l", "d": [], "c": [["a", {"w": "slack", "s": "ack", "e": true, "k": "a", "d": [102], "c": []}], ["u", {"w": "slug", "s": "ug", "e": true, "k": "u", "d": [157], "c": [["s", {"w": "slugs", "s": "s", "e": true, "k": "s", "d": [174, 196, 198, 204, 206], "c": []}]]}]]}], ["q", {"w": "sqrt", "s": "qrt", "e": true, "k": "q", "d": [166], "c": []}]]}], ["m", {"w": "m", "s": "m", "e": false, "k": "m", "d": [], "c": [["a", {"w": "ma", "s": "a", "e": false, "k": "a", "d": [], "c": [["k", {"w": "mak", "s": "k", "e": false, "k": "k", "d": [], "c": [["e", {"w": "make", "s": "e", "e": true, "k": "e", "d": [7, 33], "c": []}], ["i", {"w": "making", "s": "ing", "e": true, "k": "i", "d": [29, 184], "c": []}]]}], ["x", {"w": "maximize", "s": "ximize", "e": true, "k": "x", "d": [11], "c": []}], ["r", {"w": "mark", "s": "rk", "e": false, "k": "r", "d": [], "c": [["d", {"w": "markdown", "s": "down", "e": true, "k": "d", "d": [29, 87, 120, 121, 137, 140, 162, 185, 246, 259, 260, 265, 269], "c": []}], ["u", {"w": "markup", "s": "up", "e": true, "k": "u", "d": [135], "c": []}], ["s", {"w": "marks", "s": "s", "e": true, "k": "s", "d": [227], "c": []}]]}], ["n", {"w": "man", "s": "n", "e": false, "k": "n", "d": [], "c": [["a", {"w": "manage", "s": "age", "e": true, "k": "a", "d": [29], "c": [["m", {"w": "management", "s": "ment", "e": true, "k": "m", "d": [47], "c": []}], ["r", {"w": "managers", "s": "rs", "e": true, "k": "r", "d": [117], "c": []}]]}], ["y", {"w": "many", "s": "y", "e": true, "k": "y", "d": [135], "c": []}]]}], ["j", {"w": "major", "s": "jor", "e": true, "k": "j", "d": [57], "c": []}], ["p", {"w": "mapping", "s": "pping", "e": true, "k": "p", "d": [63], "c": []}], ["t", {"w": "match", "s": "tch", "e": true, "k": "t", "d": [100], "c": []}]]}], ["d", {"w": "mdx", "s": "dx", "e": true, "k": "d", "d": [10, 29, 30, 33, 34, 35, 36, 123, 135, 136, 137, 139, 161, 162, 185, 199, 201, 207, 246, 248, 249, 256, 260, 265, 268, 269, 271, 306, 307], "c": []}], ["e", {"w": "me", "s": "e", "e": false, "k": "e", "d": [], "c": [["a", {"w": "meaningful", "s": "aningful", "e": true, "k": "a", "d": [11], "c": []}], ["m", {"w": "memory", "s": "mory", "e": true, "k": "m", "d": [101], "c": []}], ["r", {"w": "mermaid", "s": "rmaid", "e": true, "k": "r", "d": [133], "c": []}], ["t", {"w": "meta", "s": "ta", "e": true, "k": "t", "d": [177, 203, 209, 292, 295, 299, 303, 304], "c": [["d", {"w": "metadata", "s": "data", "e": true, "k": "d", "d": [305], "c": []}]]}]]}], ["i", {"w": "mi", "s": "i", "e": false, "k": "i", "d": [], "c": [["n", {"w": "min", "s": "n", "e": false, "k": "n", "d": [], "c": [["u", {"w": "minutes", "s": "utes", "e": true, "k": "u", "d": [17], "c": []}], ["i", {"w": "minimum", "s": "imum", "e": true, "k": "i", "d": [24, 95], "c": []}]]}], ["g", {"w": "mig", "s": "g", "e": false, "k": "g", "d": [], "c": [["h", {"w": "might", "s": "ht", "e": true, "k": "h", "d": [24], "c": []}], ["r", {"w": "migration", "s": "ration", "e": true, "k": "r", "d": [76], "c": []}]]}], ["c", {"w": "microsoft", "s": "crosoft", "e": true, "k": "c", "d": [61, 105], "c": []}]]}], ["o", {"w": "mo", "s": "o", "e": false, "k": "o", "d": [], "c": [["d", {"w": "mod", "s": "d", "e": false, "k": "d", "d": [], "c": [["e", {"w": "mode", "s": "e", "e": true, "k": "e", "d": [27, 72, 85, 242, 247, 248], "c": [["l", {"w": "models", "s": "ls", "e": true, "k": "l", "d": [252, 257], "c": []}]]}], ["i", {"w": "modify", "s": "ify", "e": true, "k": "i", "d": [239], "c": [["i", {"w": "modifying", "s": "ing", "e": true, "k": "i", "d": [284], "c": []}]]}]]}], ["r", {"w": "more", "s": "re", "e": true, "k": "r", "d": [29, 55, 119, 145, 241, 257], "c": []}], ["b", {"w": "mobile", "s": "bile", "e": true, "k": "b", "d": [41], "c": []}]]}], ["y", {"w": "my-heading-id", "s": "y-heading-id", "e": true, "k": "y", "d": [159], "c": []}], ["u", {"w": "mu", "s": "u", "e": false, "k": "u", "d": [], "c": [["l", {"w": "multiple", "s": "ltiple", "e": true, "k": "l", "d": [203], "c": []}], ["s", {"w": "mus", "s": "s", "e": false, "k": "s", "d": [], "c": [["t", {"w": "must", "s": "t", "e": true, "k": "t", "d": [246, 250], "c": []}], ["e", {"w": "museum", "s": "eum", "e": true, "k": "e", "d": [310, 311, 313, 314, 319, 320, 326, 327, 328, 329, 330, 331], "c": []}]]}]]}]]}], ["l", {"w": "l", "s": "l", "e": false, "k": "l", "d": [], "c": [["e", {"w": "le", "s": "e", "e": false, "k": "e", "d": [], "c": [["a", {"w": "lea", "s": "a", "e": false, "k": "a", "d": [], "c": [["r", {"w": "learn", "s": "rn", "e": true, "k": "r", "d": [9, 10, 29, 119, 145, 288], "c": []}], ["d", {"w": "leading", "s": "ding", "e": true, "k": "d", "d": [116], "c": []}]]}], ["t", {"w": "lets", "s": "ts", "e": true, "k": "t", "d": [267], "c": []}]]}], ["o", {"w": "lo", "s": "o", "e": false, "k": "o", "d": [], "c": [["c", {"w": "loca", "s": "ca", "e": false, "k": "c", "d": [], "c": [["l", {"w": "local", "s": "l", "e": true, "k": "l", "d": [9], "c": [["h", {"w": "localhost", "s": "host", "e": true, "k": "h", "d": [27], "c": []}]]}], ["t", {"w": "located", "s": "ted", "e": true, "k": "t", "d": [299], "c": []}]]}], ["g", {"w": "log", "s": "g", "e": true, "k": "g", "d": [55], "c": [["i", {"w": "logi", "s": "i", "e": false, "k": "i", "d": [], "c": [["c", {"w": "logic", "s": "c", "e": true, "k": "c", "d": [75, 104, 239], "c": []}], ["n", {"w": "login", "s": "n", "e": true, "k": "n", "d": [79, 84, 94], "c": []}]]}], ["s", {"w": "logs", "s": "s", "e": true, "k": "s", "d": [99], "c": []}]]}], ["n", {"w": "longer", "s": "nger", "e": true, "k": "n", "d": [96], "c": []}], ["a", {"w": "load", "s": "ad", "e": true, "k": "a", "d": [97], "c": []}]]}], ["a", {"w": "la", "s": "a", "e": false, "k": "a", "d": [], "c": [["y", {"w": "layout", "s": "yout", "e": true, "k": "y", "d": [10], "c": []}], ["t", {"w": "late", "s": "te", "e": false, "k": "t", "d": [], "c": [["n", {"w": "latency", "s": "ncy", "e": true, "k": "n", "d": [74], "c": []}], ["x", {"w": "latex", "s": "x", "e": true, "k": "x", "d": [134, 165], "c": []}]]}], ["b", {"w": "labels", "s": "bels", "e": true, "k": "b", "d": [77], "c": []}], ["s", {"w": "last", "s": "st", "e": true, "k": "s", "d": [80], "c": []}], ["n", {"w": "language", "s": "nguage", "e": true, "k": "n", "d": [116, 135, 252, 257], "c": [["s", {"w": "languages", "s": "s", "e": true, "k": "s", "d": [281], "c": []}]]}], ["r", {"w": "large", "s": "rge", "e": true, "k": "r", "d": [246, 252, 257], "c": []}]]}], ["i", {"w": "li", "s": "i", "e": false, "k": "i", "d": [], "c": [["m", {"w": "limit", "s": "mit", "e": false, "k": "m", "d": [], "c": [["s", {"w": "limits", "s": "s", "e": true, "k": "s", "d": [94], "c": []}], ["a", {"w": "limitations", "s": "ations", "e": true, "k": "a", "d": [248], "c": []}]]}], ["n", {"w": "lin", "s": "n", "e": false, "k": "n", "d": [], "c": [["k", {"w": "link", "s": "k", "e": true, "k": "k", "d": [142, 159, 181], "c": [["s", {"w": "links", "s": "s", "e": true, "k": "s", "d": [96, 103, 125, 142, 143, 144, 225], "c": []}]]}], ["e", {"w": "lines", "s": "es", "e": true, "k": "e", "d": [118], "c": []}]]}], ["k", {"w": "like", "s": "ke", "e": true, "k": "k", "d": [155, 158, 250], "c": []}], ["s", {"w": "list", "s": "st", "e": true, "k": "s", "d": [318, 319, 320], "c": [["e", {"w": "listed", "s": "ed", "e": true, "k": "e", "d": [221], "c": []}], ["s", {"w": "lists", "s": "s", "e": true, "k": "s", "d": [261], "c": []}]]}]]}], ["l", {"w": "ll", "s": "l", "e": true, "k": "l", "d": [237], "c": [["m", {"w": "llm", "s": "m", "e": true, "k": "m", "d": [251, 267], "c": [["s", {"w": "llms", "s": "s", "e": true, "k": "s", "d": [254, 256, 257, 258, 260, 264, 271], "c": [["-", {"w": "llms-full", "s": "-full", "e": true, "k": "-", "d": [255, 259], "c": []}]]}]]}]]}]]}], ["h", {"w": "h", "s": "h", "e": false, "k": "h", "d": [], "c": [["o", {"w": "ho", "s": "o", "e": false, "k": "o", "d": [], "c": [["w", {"w": "how", "s": "w", "e": true, "k": "w", "d": [9, 10, 236, 288], "c": []}], ["u", {"w": "hours", "s": "urs", "e": true, "k": "u", "d": [74, 326, 327, 328], "c": []}], ["l", {"w": "holomorphic", "s": "lomorphic", "e": true, "k": "l", "d": [167], "c": []}], ["m", {"w": "home", "s": "me", "e": true, "k": "m", "d": [300], "c": [["p", {"w": "homepage", "s": "page", "e": true, "k": "p", "d": [293], "c": []}]]}]]}], ["a", {"w": "ha", "s": "a", "e": false, "k": "a", "d": [], "c": [["v", {"w": "have", "s": "ve", "e": true, "k": "v", "d": [24, 238], "c": []}], ["s", {"w": "has", "s": "s", "e": true, "k": "s", "d": [75], "c": [["h", {"w": "hash", "s": "h", "e": true, "k": "h", "d": [159], "c": []}]]}], ["r", {"w": "hard-reload", "s": "rd-reload", "e": true, "k": "r", "d": [142], "c": []}], ["n", {"w": "handled", "s": "ndled", "e": true, "k": "n", "d": [236], "c": []}]]}], ["t", {"w": "ht", "s": "t", "e": false, "k": "t", "d": [], "c": [["t", {"w": "http", "s": "tp", "e": true, "k": "t", "d": [27], "c": [["s", {"w": "https", "s": "s", "e": true, "k": "s", "d": [25, 264], "c": []}]]}], ["m", {"w": "html", "s": "ml", "e": true, "k": "m", "d": [257], "c": []}]]}], ["i", {"w": "hi", "s": "i", "e": false, "k": "i", "d": [], "c": [["g", {"w": "highlight", "s": "ghlight", "e": true, "k": "g", "d": [118], "c": [["i", {"w": "highlighting", "s": "ing", "e": true, "k": "i", "d": [37, 116], "c": []}]]}], ["s", {"w": "history", "s": "story", "e": true, "k": "s", "d": [79], "c": []}], ["d", {"w": "hides", "s": "des", "e": true, "k": "d", "d": [85], "c": []}]]}], ["e", {"w": "he", "s": "e", "e": false, "k": "e", "d": [], "c": [["a", {"w": "head", "s": "ad", "e": false, "k": "a", "d": [], "c": [["i", {"w": "heading", "s": "ing", "e": true, "k": "i", "d": [155, 157, 159], "c": [["s", {"w": "headings", "s": "s", "e": true, "k": "s", "d": [128, 156, 266, 270], "c": []}]]}], ["l", {"w": "headless", "s": "less", "e": true, "k": "l", "d": [228], "c": []}]]}], ["r", {"w": "here", "s": "re", "e": true, "k": "r", "d": [135], "c": []}], ["l", {"w": "hel", "s": "l", "e": false, "k": "l", "d": [], "c": [["l", {"w": "hello", "s": "lo", "e": true, "k": "l", "d": [154, 155], "c": [["-", {"w": "hello-world", "s": "-world", "e": true, "k": "-", "d": [155], "c": []}]]}], ["p", {"w": "helps", "s": "ps", "e": true, "k": "p", "d": [297], "c": []}]]}]]}]]}], ["r", {"w": "r", "s": "r", "e": false, "k": "r", "d": [], "c": [["e", {"w": "re", "s": "e", "e": false, "k": "e", "d": [], "c": [["f", {"w": "ref", "s": "f", "e": false, "k": "f", "d": [], "c": [["l", {"w": "reflect", "s": "lect", "e": true, "k": "l", "d": [11], "c": []}], ["e", {"w": "refer", "s": "er", "e": true, "k": "e", "d": [76], "c": [["e", {"w": "reference", "s": "ence", "e": true, "k": "e", "d": [162, 250], "c": [["d", {"w": "referenced", "s": "d", "e": true, "k": "d", "d": [250], "c": []}]]}]]}]]}], ["q", {"w": "requ", "s": "qu", "e": false, "k": "q", "d": [], "c": [["i", {"w": "require", "s": "ire", "e": false, "k": "i", "d": [], "c": [["d", {"w": "required", "s": "d", "e": true, "k": "d", "d": [24], "c": []}], ["m", {"w": "requirement", "s": "ment", "e": true, "k": "m", "d": [246], "c": [["s", {"w": "requirements", "s": "s", "e": true, "k": "s", "d": [95], "c": []}]]}]]}], ["e", {"w": "request", "s": "est", "e": true, "k": "e", "d": [83, 281, 283], "c": [["s", {"w": "requests", "s": "s", "e": true, "k": "s", "d": [93], "c": []}], ["e", {"w": "requested", "s": "ed", "e": true, "k": "e", "d": [260, 269], "c": []}]]}]]}], ["a", {"w": "rea", "s": "a", "e": false, "k": "a", "d": [], "c": [["c", {"w": "react", "s": "ct", "e": true, "k": "c", "d": [29, 35], "c": []}], ["s", {"w": "reassignment", "s": "ssignment", "e": true, "k": "s", "d": [62], "c": []}], ["d", {"w": "reader", "s": "der", "e": true, "k": "d", "d": [70], "c": []}]]}], ["p", {"w": "rep", "s": "p", "e": false, "k": "p", "d": [], "c": [["o", {"w": "report", "s": "ort", "e": false, "k": "o", "d": [], "c": [["i", {"w": "reporting", "s": "ing", "e": true, "k": "i", "d": [43], "c": []}], ["s", {"w": "reports", "s": "s", "e": true, "k": "s", "d": [65], "c": []}], ["e", {"w": "reported", "s": "ed", "e": true, "k": "e", "d": [73], "c": []}]]}], ["l", {"w": "repl", "s": "l", "e": false, "k": "l", "d": [], "c": [["i", {"w": "replies", "s": "ies", "e": true, "k": "i", "d": [102], "c": []}], ["a", {"w": "replace", "s": "ace", "e": true, "k": "a", "d": [241], "c": []}]]}]]}], ["l", {"w": "rel", "s": "l", "e": true, "k": "l", "d": [143], "c": [["e", {"w": "rele", "s": "e", "e": false, "k": "e", "d": [], "c": [["a", {"w": "release", "s": "ase", "e": true, "k": "a", "d": [45, 73], "c": []}], ["v", {"w": "relevance", "s": "vance", "e": true, "k": "v", "d": [90], "c": []}]]}], ["i", {"w": "reli", "s": "i", "e": false, "k": "i", "d": [], "c": [["a", {"w": "reliability", "s": "ability", "e": true, "k": "a", "d": [60], "c": []}], ["e", {"w": "relies", "s": "es", "e": true, "k": "e", "d": [240], "c": []}]]}], ["a", {"w": "relative", "s": "ative", "e": true, "k": "a", "d": [162, 197, 205, 250], "c": []}]]}], ["d", {"w": "red", "s": "d", "e": false, "k": "d", "d": [], "c": [["e", {"w": "redesigned", "s": "esigned", "e": true, "k": "e", "d": [53, 69, 81], "c": []}], ["u", {"w": "reduced", "s": "uced", "e": true, "k": "u", "d": [74, 84, 101], "c": []}]]}], ["c", {"w": "rec", "s": "c", "e": false, "k": "c", "d": [], "c": [["e", {"w": "rece", "s": "e", "e": false, "k": "e", "d": [], "c": [["i", {"w": "receiving", "s": "iving", "e": true, "k": "i", "d": [54], "c": []}], ["n", {"w": "recently", "s": "ntly", "e": true, "k": "n", "d": [92], "c": []}]]}], ["u", {"w": "recurring", "s": "urring", "e": true, "k": "u", "d": [98], "c": []}], ["o", {"w": "recommend", "s": "ommend", "e": true, "k": "o", "d": [137], "c": []}]]}], ["s", {"w": "res", "s": "s", "e": false, "k": "s", "d": [], "c": [["c", {"w": "rescheduling", "s": "cheduling", "e": true, "k": "c", "d": [58], "c": []}], ["o", {"w": "resolved", "s": "olved", "e": true, "k": "o", "d": [59], "c": []}], ["p", {"w": "response", "s": "ponse", "e": true, "k": "p", "d": [74, 97, 282], "c": []}], ["u", {"w": "results", "s": "ults", "e": true, "k": "u", "d": [90], "c": []}], ["t", {"w": "rest", "s": "t", "e": true, "k": "t", "d": [179], "c": []}]]}], ["t", {"w": "ret", "s": "t", "e": false, "k": "t", "d": [], "c": [["r", {"w": "retr", "s": "r", "e": false, "k": "r", "d": [], "c": [["y", {"w": "retry", "s": "y", "e": true, "k": "y", "d": [75, 104], "c": []}], ["i", {"w": "retrieval-augmented", "s": "ieval-augmented", "e": true, "k": "i", "d": [240, 241], "c": []}]]}], ["u", {"w": "return", "s": "urn", "e": true, "k": "u", "d": [319, 320, 333, 334], "c": []}]]}], ["m", {"w": "rem", "s": "m", "e": false, "k": "m", "d": [], "c": [["e", {"w": "remembers", "s": "embers", "e": true, "k": "e", "d": [80], "c": []}], ["o", {"w": "removal", "s": "oval", "e": true, "k": "o", "d": [96], "c": []}], ["a", {"w": "remaining", "s": "aining", "e": true, "k": "a", "d": [222], "c": []}]]}], ["u", {"w": "reuse", "s": "use", "e": true, "k": "u", "d": [84], "c": []}], ["v", {"w": "reviews", "s": "views", "e": true, "k": "v", "d": [88], "c": []}], ["h", {"w": "rehype", "s": "hype", "e": true, "k": "h", "d": [116], "c": []}], ["n", {"w": "render", "s": "nder", "e": false, "k": "n", "d": [], "c": [["e", {"w": "renderers", "s": "ers", "e": true, "k": "e", "d": [136], "c": []}], ["i", {"w": "rendering", "s": "ing", "e": true, "k": "i", "d": [164], "c": []}], ["s", {"w": "renders", "s": "s", "e": true, "k": "s", "d": [226, 230], "c": []}]]}]]}], ["u", {"w": "run", "s": "un", "e": true, "k": "u", "d": [27, 28], "c": [["t", {"w": "runtime", "s": "time", "e": true, "k": "t", "d": [226, 243, 247], "c": []}], ["n", {"w": "running", "s": "ning", "e": true, "k": "n", "d": [246], "c": []}]]}], ["a", {"w": "ra", "s": "a", "e": false, "k": "a", "d": [], "c": [["n", {"w": "ran", "s": "n", "e": false, "k": "n", "d": [], "c": [["g", {"w": "ranges", "s": "ges", "e": true, "k": "g", "d": [89], "c": []}], ["k", {"w": "ranking", "s": "king", "e": true, "k": "k", "d": [90], "c": []}]]}], ["g", {"w": "rag", "s": "g", "e": true, "k": "g", "d": [240, 241], "c": []}], ["w", {"w": "raw", "s": "w", "e": true, "k": "w", "d": [260, 265, 269], "c": []}]]}], ["i", {"w": "right", "s": "ight", "e": true, "k": "i", "d": [137], "c": []}], ["o", {"w": "ro", "s": "o", "e": false, "k": "o", "d": [], "c": [["u", {"w": "routing", "s": "uting", "e": true, "k": "u", "d": [170, 184], "c": []}], ["o", {"w": "root", "s": "ot", "e": true, "k": "o", "d": [183, 227, 228, 230, 238, 264, 287, 288, 290, 297, 308], "c": []}]]}]]}], ["b", {"w": "b", "s": "b", "e": true, "k": "b", "d": [166], "c": [["r", {"w": "br", "s": "r", "e": false, "k": "r", "d": [], "c": [["a", {"w": "brand", "s": "and", "e": true, "k": "a", "d": [11, 12], "c": [["i", {"w": "branding", "s": "ing", "e": true, "k": "i", "d": [100], "c": []}]]}], ["o", {"w": "browse", "s": "owse", "e": true, "k": "o", "d": [15], "c": [["r", {"w": "browser", "s": "r", "e": true, "k": "r", "d": [27], "c": []}]]}], ["i", {"w": "brief", "s": "ief", "e": true, "k": "i", "d": [135], "c": []}]]}], ["y", {"w": "by", "s": "y", "e": true, "k": "y", "d": [12, 31, 67, 74, 84, 97, 116, 147, 204, 209, 219, 220, 237, 238, 246, 247, 257, 268, 284, 297], "c": []}], ["e", {"w": "be", "s": "e", "e": true, "k": "e", "d": [26, 65, 156, 162, 169, 227, 246, 247, 250], "c": [["s", {"w": "best", "s": "st", "e": true, "k": "s", "d": [15], "c": []}], ["t", {"w": "bet", "s": "t", "e": false, "k": "t", "d": [], "c": [["t", {"w": "better", "s": "ter", "e": true, "k": "t", "d": [53, 72], "c": []}], ["w", {"w": "between", "s": "ween", "e": true, "k": "w", "d": [59, 117, 230], "c": []}]]}], ["h", {"w": "behavior", "s": "havior", "e": true, "k": "h", "d": [71], "c": []}], ["l", {"w": "below", "s": "low", "e": true, "k": "l", "d": [217], "c": []}], ["i", {"w": "being", "s": "ing", "e": true, "k": "i", "d": [223], "c": []}]]}], ["u", {"w": "bu", "s": "u", "e": false, "k": "u", "d": [], "c": [["i", {"w": "buil", "s": "il", "e": false, "k": "i", "d": [], "c": [["d", {"w": "build", "s": "d", "e": true, "k": "d", "d": [24, 268], "c": [["i", {"w": "building", "s": "ing", "e": true, "k": "i", "d": [17], "c": []}], ["s", {"w": "builds", "s": "s", "e": true, "k": "s", "d": [34], "c": []}]]}], ["t", {"w": "built-in", "s": "t-in", "e": true, "k": "t", "d": [35, 237], "c": []}]]}], ["t", {"w": "buttons", "s": "ttons", "e": true, "k": "t", "d": [70], "c": []}], ["g", {"w": "bug", "s": "g", "e": true, "k": "g", "d": [96, 98], "c": [["s", {"w": "bugs", "s": "s", "e": true, "k": "s", "d": [73], "c": []}]]}], ["l", {"w": "bulk", "s": "lk", "e": true, "k": "l", "d": [81, 98], "c": []}], ["y", {"w": "buy", "s": "y", "e": true, "k": "y", "d": [329], "c": []}]]}], ["a", {"w": "ba", "s": "a", "e": false, "k": "a", "d": [], "c": [["c", {"w": "back", "s": "ck", "e": false, "k": "c", "d": [], "c": [["g", {"w": "background", "s": "ground", "e": true, "k": "g", "d": [60, 101], "c": []}], ["e", {"w": "backend", "s": "end", "e": true, "k": "e", "d": [75], "c": []}], ["o", {"w": "backoff", "s": "off", "e": true, "k": "o", "d": [104], "c": []}], ["t", {"w": "backticks", "s": "ticks", "e": true, "k": "t", "d": [115, 116], "c": []}]]}], ["s", {"w": "bas", "s": "s", "e": false, "k": "s", "d": [], "c": [["e", {"w": "base", "s": "e", "e": true, "k": "e", "d": [302], "c": [["d", {"w": "based", "s": "d", "e": true, "k": "d", "d": [77, 156, 184], "c": []}]]}], ["i", {"w": "basic", "s": "ic", "e": true, "k": "i", "d": [108, 279], "c": []}]]}], ["t", {"w": "batch", "s": "tch", "e": true, "k": "t", "d": [78], "c": []}]]}], ["o", {"w": "bo", "s": "o", "e": false, "k": "o", "d": [], "c": [["a", {"w": "board", "s": "ard", "e": true, "k": "a", "d": [80], "c": []}], ["i", {"w": "boilerplate", "s": "ilerplate", "e": true, "k": "i", "d": [263], "c": []}], ["d", {"w": "body", "s": "dy", "e": true, "k": "d", "d": [283], "c": []}]]}], ["l", {"w": "block", "s": "lock", "e": true, "k": "l", "d": [110, 117], "c": [["s", {"w": "blocks", "s": "s", "e": true, "k": "s", "d": [106, 107, 116, 160], "c": []}]]}]]}], ["q", {"w": "q", "s": "q", "e": false, "k": "q", "d": [], "c": [["u", {"w": "qu", "s": "u", "e": false, "k": "u", "d": [], "c": [["i", {"w": "quick", "s": "ick", "e": true, "k": "i", "d": [91], "c": [["s", {"w": "quickstart", "s": "start", "e": true, "k": "s", "d": [16], "c": []}], ["e", {"w": "quicker", "s": "er", "e": true, "k": "e", "d": [56], "c": []}]]}], ["a", {"w": "quality", "s": "ality", "e": true, "k": "a", "d": [51], "c": []}], ["e", {"w": "queries", "s": "eries", "e": true, "k": "e", "d": [236, 240], "c": []}]]}], ["r", {"w": "qr", "s": "r", "e": true, "k": "r", "d": [332, 333, 334], "c": []}]]}], ["5", {"w": "5", "s": "5", "e": true, "k": "5", "d": [17, 295], "c": []}], ["v", {"w": "v", "s": "v", "e": false, "k": "v", "d": [], "c": [["e", {"w": "ver", "s": "er", "e": false, "k": "e", "d": [], "c": [["s", {"w": "version", "s": "sion", "e": true, "k": "s", "d": [24], "c": []}], ["c", {"w": "vercel", "s": "cel", "e": true, "k": "c", "d": [32], "c": []}]]}], ["i", {"w": "vi", "s": "i", "e": false, "k": "i", "d": [], "c": [["a", {"w": "via", "s": "a", "e": true, "k": "a", "d": [54, 250], "c": []}], ["s", {"w": "visibility", "s": "sibility", "e": true, "k": "s", "d": [72], "c": []}], ["e", {"w": "view", "s": "ew", "e": true, "k": "e", "d": [79, 308], "c": [["e", {"w": "viewed", "s": "ed", "e": true, "k": "e", "d": [80, 92], "c": []}]]}]]}], ["a", {"w": "values", "s": "al<PERSON>", "e": true, "k": "a", "d": [137], "c": []}]]}], ["j", {"w": "j", "s": "j", "e": false, "k": "j", "d": [], "c": [["s", {"w": "js", "s": "s", "e": true, "k": "s", "d": [24, 29, 33, 145, 257], "c": [["x", {"w": "jsx", "s": "x", "e": true, "k": "x", "d": [137, 226], "c": []}], ["o", {"w": "json", "s": "on", "e": true, "k": "o", "d": [209, 284, 292, 295, 299, 303, 304], "c": []}]]}], ["i", {"w": "jira", "s": "ira", "e": true, "k": "i", "d": [63], "c": []}], ["o", {"w": "jo", "s": "o", "e": false, "k": "o", "d": [], "c": [["b", {"w": "job", "s": "b", "e": true, "k": "b", "d": [75], "c": []}], ["i", {"w": "joining", "s": "ining", "e": true, "k": "i", "d": [268], "c": []}]]}], ["u", {"w": "just", "s": "ust", "e": true, "k": "u", "d": [263], "c": []}]]}], ["1", {"w": "1", "s": "1", "e": true, "k": "1", "d": [24, 285, 291], "c": [["8", {"w": "18", "s": "8", "e": true, "k": "8", "d": [24], "c": []}], ["5", {"w": "15", "s": "5", "e": true, "k": "5", "d": [74], "c": []}]]}], ["2", {"w": "2", "s": "2", "e": true, "k": "2", "d": [166, 292], "c": [["3", {"w": "23", "s": "3", "e": true, "k": "3", "d": [24], "c": []}], ["f", {"w": "2fa", "s": "fa", "e": true, "k": "f", "d": [54], "c": []}], ["5", {"w": "25", "s": "5", "e": true, "k": "5", "d": [97], "c": []}]]}], ["-", {"w": "-", "s": "-", "e": false, "k": "-", "d": [], "c": [["e", {"w": "-e", "s": "e", "e": true, "k": "e", "d": [25], "c": []}], ["-", {"w": "--use-", "s": "-use-", "e": false, "k": "-", "d": [], "c": [["p", {"w": "--use-pnpmnpx", "s": "pnpmnpx", "e": true, "k": "p", "d": [25], "c": []}], ["y", {"w": "--use-yarnnpx", "s": "yarnnpx", "e": true, "k": "y", "d": [25], "c": []}], ["b", {"w": "--use-bun", "s": "bun", "e": true, "k": "b", "d": [25], "c": []}]]}]]}], ["3", {"w": "3", "s": "3", "e": true, "k": "3", "d": [285, 293], "c": [["0", {"w": "3000", "s": "000", "e": true, "k": "0", "d": [27], "c": []}]]}], ["k", {"w": "k", "s": "k", "e": false, "k": "k", "d": [], "c": [["e", {"w": "key", "s": "ey", "e": true, "k": "e", "d": [234, 237, 238], "c": [["b", {"w": "keyboard", "s": "board", "e": true, "k": "b", "d": [56], "c": []}]]}], ["a", {"w": "ka", "s": "a", "e": false, "k": "a", "d": [], "c": [["n", {"w": "kanban", "s": "nban", "e": true, "k": "n", "d": [80], "c": []}], ["t", {"w": "katex", "s": "tex", "e": true, "k": "t", "d": [169], "c": []}]]}]]}], ["z", {"w": "z", "s": "z", "e": true, "k": "z", "d": [222], "c": [["a", {"w": "zapier", "s": "apier", "e": true, "k": "a", "d": [64], "c": []}]]}], ["4", {"w": "4", "s": "4", "e": true, "k": "4", "d": [294], "c": [["0", {"w": "40", "s": "0", "e": true, "k": "0", "d": [84], "c": []}]]}], ["_", {"w": "_blank", "s": "_blank", "e": true, "k": "_", "d": [143], "c": []}], ["x", {"w": "x", "s": "x", "e": true, "k": "x", "d": [167], "c": []}], ["0", {"w": "0", "s": "0", "e": true, "k": "0", "d": [285], "c": []}], ["6", {"w": "6", "s": "6", "e": true, "k": "6", "d": [296], "c": []}]]}, "isArray": false}, "page_id": {"type": "<PERSON><PERSON><PERSON>", "node": {"w": "", "s": "", "e": false, "k": "", "d": [], "c": [["d", {"w": "d", "s": "d", "e": false, "k": "d", "d": [], "c": [["o", {"w": "docs", "s": "ocs", "e": true, "k": "o", "d": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334], "c": []}], ["e", {"w": "deletespecialevent", "s": "eletespecialevent", "e": true, "k": "e", "d": [312, 313, 314], "c": []}]]}], ["a", {"w": "a", "s": "a", "e": false, "k": "a", "d": [], "c": [["p", {"w": "ap", "s": "p", "e": false, "k": "p", "d": [], "c": [["i", {"w": "api-reference", "s": "i-reference", "e": true, "k": "i", "d": [1, 2, 3, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334], "c": []}], ["p", {"w": "app", "s": "p", "e": true, "k": "p", "d": [4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308], "c": []}]]}], ["i", {"w": "ai-search", "s": "i-search", "e": true, "k": "i", "d": [231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241], "c": []}], ["s", {"w": "async-mode", "s": "sync-mode", "e": true, "k": "s", "d": [242, 243, 244, 245, 246, 247, 248, 249, 250], "c": []}], ["d", {"w": "adding-a-root-folder", "s": "dding-a-root-folder", "e": true, "k": "d", "d": [287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308], "c": []}]]}], ["q", {"w": "quickstart", "s": "quickstart", "e": true, "k": "q", "d": [16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37], "c": []}], ["c", {"w": "c", "s": "c", "e": false, "k": "c", "d": [], "c": [["h", {"w": "changelog", "s": "hangelog", "e": true, "k": "h", "d": [38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105], "c": []}], ["o", {"w": "code", "s": "ode", "e": true, "k": "o", "d": [106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119], "c": []}], ["r", {"w": "createspecialevent", "s": "reatespecialevent", "e": true, "k": "r", "d": [309, 310, 311], "c": []}]]}], ["e", {"w": "e", "s": "e", "e": false, "k": "e", "d": [], "c": [["s", {"w": "essentials", "s": "ssentials", "e": true, "k": "s", "d": [106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230], "c": []}], ["v", {"w": "events", "s": "vents", "e": true, "k": "v", "d": [309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325], "c": []}]]}], ["m", {"w": "markdown", "s": "markdown", "e": true, "k": "m", "d": [120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169], "c": []}], ["r", {"w": "routing", "s": "routing", "e": true, "k": "r", "d": [170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230], "c": []}], ["f", {"w": "features", "s": "features", "e": true, "k": "f", "d": [231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286], "c": []}], ["l", {"w": "l", "s": "l", "e": false, "k": "l", "d": [], "c": [["l", {"w": "llms", "s": "lms", "e": true, "k": "l", "d": [251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271], "c": []}], ["i", {"w": "listspecialevents", "s": "istspecialevents", "e": true, "k": "i", "d": [318, 319, 320], "c": []}]]}], ["o", {"w": "ope", "s": "ope", "e": false, "k": "o", "d": [], "c": [["n", {"w": "openapi", "s": "napi", "e": true, "k": "n", "d": [272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286], "c": []}], ["r", {"w": "operations", "s": "rations", "e": true, "k": "r", "d": [326, 327, 328], "c": []}]]}], ["g", {"w": "g", "s": "g", "e": false, "k": "g", "d": [], "c": [["u", {"w": "guides", "s": "uides", "e": true, "k": "u", "d": [287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308], "c": []}], ["e", {"w": "get", "s": "et", "e": false, "k": "e", "d": [], "c": [["s", {"w": "getspecialevent", "s": "specialevent", "e": true, "k": "s", "d": [315, 316, 317], "c": []}], ["m", {"w": "getmuseumhours", "s": "museumhours", "e": true, "k": "m", "d": [326, 327, 328], "c": []}], ["t", {"w": "getticketcode", "s": "ticketcode", "e": true, "k": "t", "d": [332, 333, 334], "c": []}]]}]]}], ["p", {"w": "publishnewevent", "s": "publishnewevent", "e": true, "k": "p", "d": [321, 322], "c": []}], ["u", {"w": "updatespecialevent", "s": "updatespecialevent", "e": true, "k": "u", "d": [323, 324, 325], "c": []}], ["t", {"w": "tickets", "s": "tickets", "e": true, "k": "t", "d": [329, 330, 331, 332, 333, 334], "c": []}], ["b", {"w": "buymuseumtickets", "s": "buymuseumtickets", "e": true, "k": "b", "d": [329, 330, 331], "c": []}]]}, "isArray": false}, "type": {"type": "<PERSON><PERSON><PERSON>", "node": {"w": "", "s": "", "e": false, "k": "", "d": [], "c": [["p", {"w": "page", "s": "page", "e": true, "k": "p", "d": [1, 4, 16, 38, 106, 120, 170, 231, 242, 251, 272, 287, 309, 312, 315, 318, 321, 323, 326, 329, 332], "c": []}], ["t", {"w": "text", "s": "text", "e": true, "k": "t", "d": [2, 3, 5, 8, 9, 10, 11, 12, 13, 14, 15, 17, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 39, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 107, 115, 116, 117, 118, 119, 121, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 171, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 232, 237, 238, 239, 240, 241, 243, 246, 247, 248, 249, 250, 252, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 273, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 288, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 310, 311, 313, 314, 316, 317, 319, 320, 322, 324, 325, 327, 328, 330, 331, 333, 334], "c": []}], ["h", {"w": "heading", "s": "heading", "e": true, "k": "h", "d": [6, 7, 18, 19, 20, 21, 22, 23, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 108, 109, 110, 111, 112, 113, 114, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 233, 234, 235, 236, 244, 245, 253, 254, 255, 256, 274, 275, 276, 289, 290, 291, 292, 293, 294, 295, 296], "c": []}]]}, "isArray": false}, "tags": {"type": "Flat", "node": {"numberToDocumentId": []}, "isArray": true}, "url": {"type": "<PERSON><PERSON><PERSON>", "node": {"w": "", "s": "", "e": false, "k": "", "d": [], "c": [["d", {"w": "d", "s": "d", "e": false, "k": "d", "d": [], "c": [["o", {"w": "doc", "s": "oc", "e": false, "k": "o", "d": [], "c": [["s", {"w": "docs", "s": "s", "e": true, "k": "s", "d": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334], "c": []}], ["k", {"w": "docker-deployment", "s": "ker-deployment", "e": true, "k": "k", "d": [22, 33, 34], "c": []}]]}], ["e", {"w": "de", "s": "e", "e": false, "k": "e", "d": [], "c": [["p", {"w": "deploy-your-changes", "s": "ploy-your-changes", "e": true, "k": "p", "d": [21, 32], "c": []}], ["l", {"w": "deletespecialevent", "s": "letespecialevent", "e": true, "k": "l", "d": [312, 313, 314], "c": []}]]}]]}], ["a", {"w": "a", "s": "a", "e": false, "k": "a", "d": [], "c": [["p", {"w": "ap", "s": "p", "e": false, "k": "p", "d": [], "c": [["i", {"w": "api-reference", "s": "i-reference", "e": true, "k": "i", "d": [1, 2, 3, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334], "c": []}], ["p", {"w": "app", "s": "p", "e": true, "k": "p", "d": [4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308], "c": [["l", {"w": "application-configuration", "s": "lication-configuration", "e": true, "k": "l", "d": [235, 239], "c": []}]]}]]}], ["d", {"w": "ad", "s": "d", "e": false, "k": "d", "d": [], "c": [["d", {"w": "add", "s": "d", "e": false, "k": "d", "d": [], "c": [["-", {"w": "add-your-content", "s": "-your-content", "e": true, "k": "-", "d": [20, 29, 30, 31], "c": []}], ["i", {"w": "adding-a-root-folder", "s": "ing-a-root-folder", "e": true, "k": "i", "d": [287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308], "c": []}]]}], ["v", {"w": "advanced", "s": "vanced", "e": true, "k": "v", "d": [111], "c": []}]]}], ["n", {"w": "analytics--reporting", "s": "nalytics--reporting", "e": true, "k": "n", "d": [43, 65, 66, 67, 68], "c": []}], ["u", {"w": "auto-links", "s": "uto-links", "e": true, "k": "u", "d": [125, 142, 143], "c": []}], ["i", {"w": "ai-search", "s": "i-search", "e": true, "k": "i", "d": [231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241], "c": []}], ["s", {"w": "async-mode", "s": "sync-mode", "e": true, "k": "s", "d": [242, 243, 244, 245, 246, 247, 248, 249, 250], "c": []}]]}], ["g", {"w": "g", "s": "g", "e": false, "k": "g", "d": [], "c": [["e", {"w": "ge", "s": "e", "e": false, "k": "e", "d": [], "c": [["t", {"w": "get", "s": "t", "e": false, "k": "t", "d": [], "c": [["t", {"w": "getti", "s": "ti", "e": false, "k": "t", "d": [], "c": [["n", {"w": "getting-started", "s": "ng-started", "e": true, "k": "n", "d": [6, 8, 9, 10], "c": []}], ["c", {"w": "getticketcode", "s": "cketcode", "e": true, "k": "c", "d": [332, 333, 334], "c": []}]]}], ["s", {"w": "getspecialevent", "s": "specialevent", "e": true, "k": "s", "d": [315, 316, 317], "c": []}], ["m", {"w": "getmuseumhours", "s": "museumhours", "e": true, "k": "m", "d": [326, 327, 328], "c": []}]]}], ["n", {"w": "general-improvements", "s": "neral-improvements", "e": true, "k": "n", "d": [40, 53, 54, 55, 56], "c": []}]]}], ["u", {"w": "guides", "s": "uides", "e": true, "k": "u", "d": [287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308], "c": []}]]}], ["m", {"w": "m", "s": "m", "e": false, "k": "m", "d": [], "c": [["a", {"w": "ma", "s": "a", "e": false, "k": "a", "d": [], "c": [["k", {"w": "make-it-yours", "s": "ke-it-yours", "e": true, "k": "k", "d": [7, 11, 12, 13, 14, 15], "c": []}], ["r", {"w": "markdown", "s": "rkdown", "e": true, "k": "r", "d": [120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169], "c": []}]]}], ["o", {"w": "mobile-enhancements", "s": "obile-enhancements", "e": true, "k": "o", "d": [41, 57, 58, 59, 60], "c": []}], ["d", {"w": "mdx", "s": "dx", "e": true, "k": "d", "d": [123, 137, 138, 139, 140], "c": []}], ["e", {"w": "me", "s": "e", "e": false, "k": "e", "d": [], "c": [["r", {"w": "mermaid", "s": "rmaid", "e": true, "k": "r", "d": [133, 164], "c": []}], ["t", {"w": "meta", "s": "ta", "e": true, "k": "t", "d": [177, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219], "c": []}]]}]]}], ["q", {"w": "quickstart", "s": "quickstart", "e": true, "k": "q", "d": [16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37], "c": []}], ["s", {"w": "s", "s": "s", "e": false, "k": "s", "d": [], "c": [["e", {"w": "se", "s": "e", "e": false, "k": "e", "d": [], "c": [["t", {"w": "set", "s": "t", "e": false, "k": "t", "d": [], "c": [["u", {"w": "setup-your-development-environment", "s": "up-your-development-environment", "e": true, "k": "u", "d": [18, 24, 25, 26], "c": []}], ["t", {"w": "setting-up-the-api-key", "s": "ting-up-the-api-key", "e": true, "k": "t", "d": [234, 238], "c": []}]]}], ["a", {"w": "search--navigation", "s": "arch--navigation", "e": true, "k": "a", "d": [49, 89, 90, 91, 92], "c": []}], ["c", {"w": "security--compliance", "s": "curity--compliance", "e": true, "k": "c", "d": [50, 93, 94, 95, 96], "c": []}]]}], ["t", {"w": "st", "s": "t", "e": false, "k": "t", "d": [], "c": [["a", {"w": "start-the-development-server", "s": "art-the-development-server", "e": true, "k": "a", "d": [19, 27, 28], "c": []}], ["e", {"w": "step", "s": "ep", "e": false, "k": "e", "d": [], "c": [["s", {"w": "steps-to-add-a-new-root-folder", "s": "s-to-add-a-new-root-folder", "e": true, "k": "s", "d": [290], "c": []}], ["-", {"w": "step-", "s": "-", "e": false, "k": "-", "d": [], "c": [["1", {"w": "step-1-create-a-new-folder", "s": "1-create-a-new-folder", "e": true, "k": "1", "d": [291, 298], "c": []}], ["2", {"w": "step-2-update-the-metajson-file", "s": "2-update-the-metajson-file", "e": true, "k": "2", "d": [292, 299], "c": []}], ["3", {"w": "step-3-update-the-homepage", "s": "3-update-the-homepage", "e": true, "k": "3", "d": [293, 300], "c": []}], ["4", {"w": "step-4-update-colors-optional", "s": "4-update-colors-optional", "e": true, "k": "4", "d": [294, 301, 302], "c": []}], ["5", {"w": "step-5-create-metajson-in-the-new-folder", "s": "5-create-metajson-in-the-new-folder", "e": true, "k": "5", "d": [295, 303, 304, 305], "c": []}], ["6", {"w": "step-6-create-a-new-page", "s": "6-create-a-new-page", "e": true, "k": "6", "d": [296, 306, 307, 308], "c": []}]]}]]}]]}], ["h", {"w": "shiki-transformers", "s": "hiki-transformers", "e": true, "k": "h", "d": [113, 118], "c": []}], ["l", {"w": "slugs", "s": "lugs", "e": true, "k": "l", "d": [174, 196, 197, 198, 199, 200, 201, 202], "c": []}]]}], ["u", {"w": "u", "s": "u", "e": false, "k": "u", "d": [], "c": [["p", {"w": "update", "s": "pdate", "e": false, "k": "p", "d": [], "c": [["-", {"w": "update-your-docs", "s": "-your-docs", "e": true, "k": "-", "d": [23, 35, 36, 37], "c": []}], ["s", {"w": "updatespecialevent", "s": "specialevent", "e": true, "k": "s", "d": [323, 324, 325], "c": []}]]}], ["x", {"w": "ux--accessibility", "s": "x--accessibility", "e": true, "k": "x", "d": [44, 69, 70, 71, 72], "c": []}], ["s", {"w": "user-management", "s": "ser-management", "e": true, "k": "s", "d": [47, 81, 82, 83, 84], "c": []}]]}], ["c", {"w": "c", "s": "c", "e": false, "k": "c", "d": [], "c": [["h", {"w": "chang", "s": "hang", "e": false, "k": "h", "d": [], "c": [["e", {"w": "changelog", "s": "elog", "e": true, "k": "e", "d": [38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105], "c": []}], ["i", {"w": "changing-the-openapi-schema", "s": "ing-the-openapi-schema", "e": true, "k": "i", "d": [276, 284, 285, 286], "c": []}]]}], ["o", {"w": "co", "s": "o", "e": false, "k": "o", "d": [], "c": [["d", {"w": "code", "s": "de", "e": true, "k": "d", "d": [106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119], "c": [["-", {"w": "code-block", "s": "-block", "e": true, "k": "-", "d": [110, 116], "c": []}]]}], ["n", {"w": "constraints", "s": "nstraints", "e": true, "k": "n", "d": [245, 248, 249, 250], "c": []}]]}], ["a", {"w": "ca", "s": "a", "e": false, "k": "a", "d": [], "c": [["r", {"w": "cards", "s": "rds", "e": true, "k": "r", "d": [126, 144, 145, 146], "c": []}], ["l", {"w": "callouts", "s": "llouts", "e": true, "k": "l", "d": [127, 147, 148, 149, 150, 151, 152, 153, 154], "c": []}]]}], ["u", {"w": "custom-anchor", "s": "ustom-anchor", "e": true, "k": "u", "d": [130, 157, 158, 159], "c": []}], ["r", {"w": "createspecialevent", "s": "reatespecialevent", "e": true, "k": "r", "d": [309, 310, 311], "c": []}]]}], ["i", {"w": "i", "s": "i", "e": false, "k": "i", "d": [], "c": [["n", {"w": "in", "s": "n", "e": false, "k": "n", "d": [], "c": [["t", {"w": "int", "s": "t", "e": false, "k": "t", "d": [], "c": [["e", {"w": "integration-upgrades", "s": "egration-upgrades", "e": true, "k": "e", "d": [42, 61, 62, 63, 64], "c": []}], ["r", {"w": "introduction", "s": "roduction", "e": true, "k": "r", "d": [122, 135, 136, 172, 184, 233, 237, 244, 246, 247, 253, 257, 258, 259, 260, 274, 277, 289, 297], "c": []}]]}], ["l", {"w": "inline-code", "s": "line-code", "e": true, "k": "l", "d": [109, 115], "c": []}], ["c", {"w": "include", "s": "clude", "e": true, "k": "c", "d": [132, 162, 163], "c": []}]]}], ["m", {"w": "images", "s": "mages", "e": true, "k": "m", "d": [124, 141], "c": []}], ["c", {"w": "icons", "s": "cons", "e": true, "k": "c", "d": [182, 226], "c": []}]]}], ["y", {"w": "year-end-stability-release", "s": "year-end-stability-release", "e": true, "k": "y", "d": [45, 73, 74, 75, 76], "c": []}], ["f", {"w": "f", "s": "f", "e": false, "k": "f", "d": [], "c": [["e", {"w": "feature", "s": "eature", "e": false, "k": "e", "d": [], "c": [["-", {"w": "feature-updates", "s": "-updates", "e": true, "k": "-", "d": [46, 77, 78, 79, 80], "c": []}], ["s", {"w": "features", "s": "s", "e": true, "k": "s", "d": [231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286], "c": []}]]}], ["i", {"w": "file", "s": "ile", "e": true, "k": "i", "d": [173, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195], "c": []}], ["o", {"w": "folder", "s": "older", "e": true, "k": "o", "d": [175, 203], "c": [["-", {"w": "folder-group", "s": "-group", "e": true, "k": "-", "d": [176, 204, 205, 206, 207, 208], "c": []}]]}]]}], ["p", {"w": "p", "s": "p", "e": false, "k": "p", "d": [], "c": [["r", {"w": "productivity-tools", "s": "roductivity-tools", "e": true, "k": "r", "d": [48, 85, 86, 87, 88], "c": []}], ["e", {"w": "performance--quality-updates", "s": "erformance--quality-updates", "e": true, "k": "e", "d": [51, 97, 98, 99, 100, 101], "c": []}], ["a", {"w": "pa", "s": "a", "e": false, "k": "a", "d": [], "c": [["c", {"w": "package-install", "s": "ckage-install", "e": true, "k": "c", "d": [112, 117], "c": []}], ["g", {"w": "pages", "s": "ges", "e": true, "k": "g", "d": [178, 220, 221], "c": []}]]}], ["u", {"w": "publishnewevent", "s": "ublishnewevent", "e": true, "k": "u", "d": [321, 322], "c": []}]]}], ["n", {"w": "new-integrations", "s": "new-integrations", "e": true, "k": "n", "d": [52, 102, 103, 104, 105], "c": []}], ["e", {"w": "e", "s": "e", "e": false, "k": "e", "d": [], "c": [["s", {"w": "essentials", "s": "ssentials", "e": true, "k": "s", "d": [106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230], "c": []}], ["x", {"w": "extract", "s": "xtract", "e": true, "k": "x", "d": [180, 224], "c": []}], ["v", {"w": "events", "s": "vents", "e": true, "k": "v", "d": [309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325], "c": []}]]}], ["b", {"w": "b", "s": "b", "e": false, "k": "b", "d": [], "c": [["a", {"w": "basic", "s": "asic", "e": true, "k": "a", "d": [108], "c": []}], ["u", {"w": "buymuseumtickets", "s": "uymuseumtickets", "e": true, "k": "u", "d": [329, 330, 331], "c": []}]]}], ["t", {"w": "t", "s": "t", "e": false, "k": "t", "d": [], "c": [["w", {"w": "twoslash-notations", "s": "woslash-notations", "e": true, "k": "w", "d": [114, 119], "c": []}], ["o", {"w": "toc-settings", "s": "oc-settings", "e": true, "k": "o", "d": [129, 156], "c": []}], ["a", {"w": "tab-groups", "s": "ab-groups", "e": true, "k": "a", "d": [131, 160, 161], "c": []}], ["i", {"w": "tickets", "s": "ickets", "e": true, "k": "i", "d": [329, 330, 331, 332, 333, 334], "c": []}]]}], ["h", {"w": "h", "s": "h", "e": false, "k": "h", "d": [], "c": [["e", {"w": "headings", "s": "eadings", "e": true, "k": "e", "d": [128, 155], "c": []}], ["o", {"w": "how-queries-are-handled", "s": "ow-queries-are-handled", "e": true, "k": "o", "d": [236, 240, 241], "c": []}]]}], ["l", {"w": "l", "s": "l", "e": false, "k": "l", "d": [], "c": [["a", {"w": "latex", "s": "atex", "e": true, "k": "a", "d": [134, 165, 166, 167, 168, 169], "c": []}], ["i", {"w": "li", "s": "i", "e": false, "k": "i", "d": [], "c": [["n", {"w": "link", "s": "nk", "e": true, "k": "n", "d": [181, 225], "c": []}], ["s", {"w": "listspecialevents", "s": "stspecialevents", "e": true, "k": "s", "d": [318, 319, 320], "c": []}]]}], ["l", {"w": "llms", "s": "lms", "e": true, "k": "l", "d": [251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271], "c": [["t", {"w": "llmstxt", "s": "txt", "e": true, "k": "t", "d": [254, 261, 262, 263, 264], "c": []}], ["-", {"w": "llms-fulltxt", "s": "-fulltxt", "e": true, "k": "-", "d": [255, 265, 266, 267, 268], "c": []}], ["m", {"w": "llmsmdx", "s": "mdx", "e": true, "k": "m", "d": [256, 269, 270, 271], "c": []}]]}]]}], ["r", {"w": "r", "s": "r", "e": false, "k": "r", "d": [], "c": [["o", {"w": "ro", "s": "o", "e": false, "k": "o", "d": [], "c": [["u", {"w": "routing", "s": "uting", "e": true, "k": "u", "d": [170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230], "c": []}], ["o", {"w": "root-folder", "s": "ot-folder", "e": true, "k": "o", "d": [183, 227, 228, 229, 230], "c": []}]]}], ["e", {"w": "rest", "s": "est", "e": true, "k": "e", "d": [179, 222, 223], "c": []}]]}], ["o", {"w": "ope", "s": "ope", "e": false, "k": "o", "d": [], "c": [["n", {"w": "openapi", "s": "napi", "e": true, "k": "n", "d": [272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286], "c": []}], ["r", {"w": "operations", "s": "rations", "e": true, "k": "r", "d": [326, 327, 328], "c": []}]]}]]}, "isArray": false}}, "vectorIndexes": {}, "searchableProperties": ["content", "page_id", "type", "tags", "url"], "searchablePropertiesWithTypes": {"content": "string", "page_id": "string", "type": "string", "tags": "enum[]", "url": "string"}, "frequencies": {"content": {"1": {"introduction": 1}, "2": {"this": 0.****************, "is": 0.****************, "a": 0.****************, "page": 0.****************, "to": 0.****************, "check": 0.****************, "fumadocs's": 0.****************, "openapi": 0.****************, "example": 0.****************}, "3": {"welcome": 0.*****************, "to": 0.*****************, "the": 0.*****************, "openapi": 0.*****************, "example": 0.*****************, "you": 0.*****************, "can": 0.*****************, "update": 0.*****************, "in": 0.*****************, "'openapi": 0.*****************, "yml'": 0.*****************, "file": 0.*****************}, "4": {"introduction": 1}, "5": {"welcome": 0.2, "to": 0.2, "your": 0.2, "new": 0.2, "documentation": 0.2}, "6": {"getting": 0.5, "started": 0.5}, "7": {"make": 0.3333333333333333, "it": 0.3333333333333333, "yours": 0.3333333333333333}, "8": {"the": 0.*********92307693, "first": 0.*********92307693, "step": 0.*********92307693, "to": 0.*********92307693, "creating": 0.*********92307693, "amazing": 0.*********92307693, "documentation": 0.*********92307693, "is": 0.*********92307693, "setting": 0.*********92307693, "up": 0.*********92307693, "your": 0.*********92307693, "editing": 0.*********92307693, "environment": 0.*********92307693}, "9": {"learn": 0.09090909090909091, "how": 0.09090909090909091, "to": 0.09090909090909091, "set": 0.09090909090909091, "up": 0.09090909090909091, "your": 0.09090909090909091, "docs": 0.09090909090909091, "for": 0.09090909090909091, "easy": 0.09090909090909091, "local": 0.09090909090909091, "development": 0.09090909090909091}, "10": {"learn": 0.06666666666666667, "how": 0.06666666666666667, "to": 0.06666666666666667, "structure": 0.06666666666666667, "your": 0.06666666666666667, "mdx": 0.06666666666666667, "files": 0.06666666666666667, "and": 0.06666666666666667, "folders": 0.06666666666666667, "define": 0.06666666666666667, "the": 0.06666666666666667, "sidebar": 0.06666666666666667, "layout": 0.06666666666666667, "in": 0.06666666666666667, "fumadocs": 0.06666666666666667}, "11": {"customize": 0.07142857142857142, "your": 0.07142857142857142, "documentation": 0.07142857142857142, "to": 0.07142857142857142, "reflect": 0.07142857142857142, "brand": 0.07142857142857142, "and": 0.07142857142857142, "include": 0.07142857142857142, "meaningful": 0.07142857142857142, "content": 0.07142857142857142, "maximize": 0.07142857142857142, "user": 0.07142857142857142, "engagement": 0.07142857142857142, "conversions": 0.07142857142857142}, "12": {"customize": 0.****************, "your": 0.****************, "documentation": 0.****************, "by": 0.****************, "applying": 0.****************, "brand": 0.****************, "colors": 0.****************, "and": 0.****************, "styles": 0.****************}, "13": {"auto-generate": 0.****************, "interactive": 0.****************, "endpoint": 0.****************, "docs": 0.****************, "straight": 0.****************, "from": 0.****************, "your": 0.****************, "openapi": 0.****************, "spec": 0.****************}, "14": {"embed": 0.****************, "interactive": 0.****************, "elements": 0.****************, "and": 0.****************, "guides": 0.****************, "to": 0.****************, "improve": 0.****************, "user": 0.****************, "engagement": 0.****************}, "15": {"browse": 0.****************, "our": 0.****************, "showcase": 0.****************, "for": 0.****************, "creative": 0.****************, "ideas": 0.****************, "and": 0.****************, "best": 0.****************, "practices": 0.****************}, "16": {"quickstart": 1}, "17": {"5": 0.125, "start": 0.125, "building": 0.125, "awesome": 0.125, "documentation": 0.125, "in": 0.125, "under": 0.125, "minutes": 0.125}, "18": {"setup": 0.25, "your": 0.25, "development": 0.25, "environment": 0.25}, "19": {"start": 0.25, "the": 0.25, "development": 0.25, "server": 0.25}, "20": {"add": 0.3333333333333333, "your": 0.3333333333333333, "content": 0.3333333333333333}, "21": {"deploy": 0.3333333333333333, "your": 0.3333333333333333, "changes": 0.3333333333333333}, "22": {"docker": 0.5, "deployment": 0.5}, "23": {"update": 0.3333333333333333, "your": 0.3333333333333333, "docs": 0.3333333333333333}, "24": {"1": 0.047619047619047616, "18": 0.047619047619047616, "23": 0.047619047619047616, "a": 0.047619047619047616, "minimum": 0.047619047619047616, "version": 0.047619047619047616, "of": 0.047619047619047616, "node": 0.047619047619047616, "js": 0.047619047619047616, "is": 0.047619047619047616, "required": 0.047619047619047616, "note": 0.047619047619047616, "that": 0.047619047619047616, "might": 0.047619047619047616, "have": 0.047619047619047616, "issues": 0.047619047619047616, "with": 0.047619047619047616, "the": 0.047619047619047616, "next": 0.047619047619047616, "production": 0.047619047619047616, "build": 0.047619047619047616}, "25": {"npx": 0.*****************, "create-next-app": 0.*****************, "-e": 0.*****************, "https": 0.*****************, "github": 0.*****************, "com": 0.*****************, "techwithanirudh": 0.*****************, "fumadocs-starternpx": 0.*****************, "fumadocs-starter": 0.*****************, "--use-pnpmnpx": 0.*****************, "--use-yarnnpx": 0.*****************, "--use-bun": 0.*****************}, "26": {"the": 0.09090909090909091, "fumadocs": 0.09090909090909091, "template": 0.09090909090909091, "should": 0.09090909090909091, "now": 0.09090909090909091, "be": 0.09090909090909091, "initialized": 0.09090909090909091, "you": 0.09090909090909091, "can": 0.09090909090909091, "start": 0.09090909090909091, "development": 0.09090909090909091}, "27": {"3000": 0.07142857142857142, "run": 0.07142857142857142, "the": 0.07142857142857142, "app": 0.07142857142857142, "in": 0.07142857142857142, "development": 0.07142857142857142, "mode": 0.07142857142857142, "and": 0.07142857142857142, "open": 0.07142857142857142, "http": 0.07142857142857142, "localhost": 0.07142857142857142, "docs": 0.07142857142857142, "your": 0.07142857142857142, "browser": 0.07142857142857142}, "28": {"npm": 0.16666666666666666, "run": 0.16666666666666666, "devpnpm": 0.16666666666666666, "devyarn": 0.16666666666666666, "devbun": 0.16666666666666666, "dev": 0.16666666666666666}, "29": {"fumadocs": 0.023809523809523808, "uses": 0.023809523809523808, "mdx": 0.023809523809523808, "for": 0.023809523809523808, "documentation": 0.023809523809523808, "allowing": 0.023809523809523808, "you": 0.023809523809523808, "to": 0.023809523809523808, "write": 0.023809523809523808, "markdown": 0.023809523809523808, "combined": 0.023809523809523808, "with": 0.023809523809523808, "react": 0.023809523809523808, "components": 0.023809523809523808, "add": 0.023809523809523808, "your": 0.023809523809523808, "content": 0.023809523809523808, "inside": 0.023809523809523808, "the": 0.023809523809523808, "docs": 0.023809523809523808, "directory": 0.023809523809523808, "structure": 0.023809523809523808, "is": 0.023809523809523808, "similar": 0.023809523809523808, "a": 0.023809523809523808, "standard": 0.023809523809523808, "next": 0.023809523809523808, "js": 0.023809523809523808, "app": 0.023809523809523808, "making": 0.023809523809523808, "it": 0.023809523809523808, "easy": 0.023809523809523808, "navigate": 0.023809523809523808, "and": 0.023809523809523808, "manage": 0.023809523809523808, "files": 0.023809523809523808, "learn": 0.023809523809523808, "more": 0.023809523809523808, "about": 0.023809523809523808, "check": 0.023809523809523808, "out": 0.023809523809523808, "page": 0.023809523809523808}, "30": {"create": 0.****************, "your": 0.****************, "first": 0.****************, "mdx": 0.****************, "file": 0.****************, "in": 0.****************, "the": 0.****************, "docs": 0.****************, "folder": 0.****************}, "31": {"the": 0.*****************, "app": 0.*****************, "organizes": 0.*****************, "content": 0.*****************, "by": 0.*****************, "concerns": 0.*****************, "e": 0.*****************, "g": 0.*****************, "docs": 0.*****************, "api-reference": 0.*****************, "changelog": 0.*****************, "etc": 0.*****************}, "32": {"this": 0.058823529411764705, "template": 0.058823529411764705, "works": 0.058823529411764705, "out-of-the-box": 0.058823529411764705, "with": 0.058823529411764705, "vercel": 0.058823529411764705, "or": 0.058823529411764705, "netlify": 0.058823529411764705, "you": 0.058823529411764705, "can": 0.058823529411764705, "deploy": 0.058823529411764705, "your": 0.058823529411764705, "documentation": 0.058823529411764705, "site": 0.058823529411764705, "a": 0.058823529411764705, "single": 0.058823529411764705, "click": 0.058823529411764705}, "33": {"if": 0.030303030303030304, "you": 0.030303030303030304, "want": 0.030303030303030304, "to": 0.030303030303030304, "deploy": 0.030303030303030304, "your": 0.030303030303030304, "fumadocs": 0.030303030303030304, "app": 0.030303030303030304, "using": 0.030303030303030304, "docker": 0.030303030303030304, "with": 0.030303030303030304, "mdx": 0.030303030303030304, "configured": 0.030303030303030304, "make": 0.030303030303030304, "sure": 0.030303030303030304, "add": 0.030303030303030304, "the": 0.030303030303030304, "source": 0.030303030303030304, "config": 0.030303030303030304, "ts": 0.030303030303030304, "file": 0.030303030303030304, "workdir": 0.030303030303030304, "in": 0.030303030303030304, "dockerfile": 0.030303030303030304, "following": 0.030303030303030304, "snippet": 0.030303030303030304, "is": 0.030303030303030304, "taken": 0.030303030303030304, "from": 0.030303030303030304, "official": 0.030303030303030304, "next": 0.030303030303030304, "js": 0.030303030303030304, "example": 0.030303030303030304}, "34": {"this": 0.09090909090909091, "ensures": 0.09090909090909091, "fumadocs": 0.09090909090909091, "mdx": 0.09090909090909091, "can": 0.09090909090909091, "access": 0.09090909090909091, "your": 0.09090909090909091, "configuration": 0.09090909090909091, "file": 0.09090909090909091, "during": 0.09090909090909091, "builds": 0.09090909090909091}, "35": {"add": 0.05263157894736842, "content": 0.05263157894736842, "directly": 0.05263157894736842, "in": 0.05263157894736842, "your": 0.05263157894736842, "files": 0.05263157894736842, "using": 0.05263157894736842, "mdx": 0.05263157894736842, "syntax": 0.05263157894736842, "and": 0.05263157894736842, "react": 0.05263157894736842, "components": 0.05263157894736842, "you": 0.05263157894736842, "can": 0.05263157894736842, "use": 0.05263157894736842, "built-in": 0.05263157894736842, "or": 0.05263157894736842, "create": 0.05263157894736842, "own": 0.05263157894736842}, "36": {"add": 0.125, "content": 0.125, "to": 0.125, "your": 0.125, "docs": 0.125, "with": 0.125, "mdx": 0.125, "syntax": 0.125}, "37": {"add": 0.****************, "code": 0.****************, "directly": 0.****************, "to": 0.****************, "your": 0.****************, "docs": 0.****************, "with": 0.****************, "syntax": 0.****************, "highlighting": 0.****************}, "38": {"product": 0.5, "updates": 0.5}, "39": {"new": 0.25, "updates": 0.25, "and": 0.25, "improvements": 0.25}, "40": {"general": 0.5, "improvements": 0.5}, "41": {"mobile": 0.5, "enhancements": 0.5}, "42": {"integration": 0.5, "upgrades": 0.5}, "43": {"analytics": 0.5, "reporting": 0.5}, "44": {"ux": 0.5, "accessibility": 0.5}, "45": {"year-end": 0.3333333333333333, "stability": 0.3333333333333333, "release": 0.3333333333333333}, "46": {"feature": 0.5, "updates": 0.5}, "47": {"user": 0.5, "management": 0.5}, "48": {"productivity": 0.5, "tools": 0.5}, "49": {"search": 0.5, "navigation": 0.5}, "50": {"security": 0.5, "compliance": 0.5}, "51": {"performance": 0.3333333333333333, "quality": 0.3333333333333333, "updates": 0.3333333333333333}, "52": {"new": 0.5, "integrations": 0.5}, "53": {"redesigned": 0.09090909090909091, "notification": 0.09090909090909091, "settings": 0.09090909090909091, "for": 0.09090909090909091, "better": 0.09090909090909091, "control": 0.09090909090909091, "over": 0.09090909090909091, "email": 0.09090909090909091, "and": 0.09090909090909091, "in-app": 0.09090909090909091, "alerts": 0.09090909090909091}, "54": {"fixed": 0.*****************, "issue": 0.*****************, "where": 0.*****************, "some": 0.*****************, "users": 0.*****************, "were": 0.*****************, "not": 0.*****************, "receiving": 0.*****************, "2fa": 0.*****************, "codes": 0.*****************, "via": 0.*****************, "sms": 0.*****************}, "55": {"activity": 0.1, "log": 0.1, "now": 0.1, "includes": 0.1, "more": 0.1, "granular": 0.1, "details": 0.1, "for": 0.1, "api": 0.1, "usage": 0.1}, "56": {"added": 0.****************, "keyboard": 0.****************, "shortcuts": 0.****************, "to": 0.****************, "the": 0.****************, "dashboard": 0.****************, "for": 0.****************, "quicker": 0.****************, "navigation": 0.****************}, "57": {"major": 0.*****************, "performance": 0.*****************, "optimizations": 0.*****************, "for": 0.*****************, "older": 0.*****************, "android": 0.*****************, "devices": 0.*****************}, "58": {"added": 0.16666666666666666, "gesture": 0.16666666666666666, "support": 0.16666666666666666, "for": 0.16666666666666666, "calendar": 0.16666666666666666, "rescheduling": 0.16666666666666666}, "59": {"resolved": 0.09090909090909091, "an": 0.09090909090909091, "issue": 0.09090909090909091, "causing": 0.09090909090909091, "data": 0.09090909090909091, "sync": 0.09090909090909091, "delays": 0.09090909090909091, "when": 0.09090909090909091, "switching": 0.09090909090909091, "between": 0.09090909090909091, "networks": 0.09090909090909091}, "60": {"push": 0.*****************, "notification": 0.*****************, "reliability": 0.*****************, "improved": 0.*****************, "for": 0.*****************, "background": 0.*****************, "updates": 0.*****************}, "61": {"microsoft": 0.125, "teams": 0.125, "integration": 0.125, "now": 0.125, "supports": 0.125, "deep-linking": 0.125, "to": 0.125, "projects": 0.125}, "62": {"added": 0.16666666666666666, "webhook": 0.16666666666666666, "event": 0.16666666666666666, "for": 0.16666666666666666, "task": 0.16666666666666666, "reassignment": 0.16666666666666666}, "63": {"jira": 0.1, "integration": 0.1, "now": 0.1, "includes": 0.1, "sprint": 0.1, "sync": 0.1, "and": 0.1, "story": 0.1, "point": 0.1, "mapping": 0.1}, "64": {"new": 0.****************, "zapier": 0.****************, "triggers": 0.****************, "for": 0.****************, "completed": 0.****************, "tasks": 0.****************, "and": 0.****************, "file": 0.****************, "uploads": 0.****************}, "65": {"custom": 0.****************, "reports": 0.****************, "can": 0.****************, "now": 0.****************, "be": 0.****************, "scheduled": 0.****************, "and": 0.****************, "emailed": 0.****************, "automatically": 0.****************}, "66": {"dashboard": 0.09090909090909091, "now": 0.09090909090909091, "supports": 0.09090909090909091, "drill-down": 0.09090909090909091, "charts": 0.09090909090909091, "for": 0.09090909090909091, "task": 0.09090909090909091, "status": 0.09090909090909091, "and": 0.09090909090909091, "team": 0.09090909090909091, "performance": 0.09090909090909091}, "67": {"added": 0.125, "time": 0.125, "tracking": 0.125, "summary": 0.125, "by": 0.125, "user": 0.125, "and": 0.125, "project": 0.125}, "68": {"export": 0.****************, "options": 0.****************, "now": 0.****************, "include": 0.****************, "pdf": 0.****************, "format": 0.****************, "with": 0.****************, "improved": 0.****************, "styling": 0.****************}, "69": {"redesigned": 0.125, "sidebar": 0.125, "with": 0.125, "collapsible": 0.125, "sections": 0.125, "for": 0.125, "cleaner": 0.125, "navigation": 0.125}, "70": {"improved": 0.****************, "screen": 0.****************, "reader": 0.****************, "support": 0.****************, "for": 0.****************, "form": 0.****************, "fields": 0.****************, "and": 0.****************, "buttons": 0.****************}, "71": {"fixed": 0.*****************, "inconsistent": 0.*****************, "behavior": 0.*****************, "of": 0.*****************, "dropdowns": 0.*****************, "in": 0.*****************, "safari": 0.*****************}, "72": {"new": 0.1, "color": 0.1, "contrast": 0.1, "settings": 0.1, "for": 0.1, "better": 0.1, "visibility": 0.1, "in": 0.1, "dark": 0.1, "mode": 0.1}, "73": {"fixed": 0.*****************, "edge-case": 0.*****************, "bugs": 0.*****************, "reported": 0.*****************, "during": 0.*****************, "november": 0.*****************, "release": 0.*****************}, "74": {"15": 0.****************, "reduced": 0.****************, "server": 0.****************, "response": 0.****************, "latency": 0.****************, "during": 0.****************, "peak": 0.****************, "hours": 0.****************, "by": 0.****************}, "75": {"backend": 0.09090909090909091, "job": 0.09090909090909091, "processing": 0.09090909090909091, "now": 0.09090909090909091, "has": 0.09090909090909091, "retry": 0.09090909090909091, "logic": 0.09090909090909091, "with": 0.09090909090909091, "alerting": 0.09090909090909091, "on": 0.09090909090909091, "failure": 0.09090909090909091}, "76": {"cleaned": 0.09090909090909091, "up": 0.09090909090909091, "deprecated": 0.09090909090909091, "api": 0.09090909090909091, "endpoints": 0.09090909090909091, "refer": 0.09090909090909091, "to": 0.09090909090909091, "migration": 0.09090909090909091, "guide": 0.09090909090909091, "if": 0.09090909090909091, "needed": 0.09090909090909091}, "77": {"new": 0.****************, "smart": 0.****************, "labels": 0.****************, "automatically": 0.****************, "categorize": 0.****************, "tasks": 0.****************, "based": 0.****************, "on": 0.****************, "content": 0.****************}, "78": {"added": 0.*****************, "batch": 0.*****************, "actions": 0.*****************, "for": 0.*****************, "checklists": 0.*****************, "and": 0.*****************, "subtasks": 0.*****************}, "79": {"admins": 0.125, "can": 0.125, "now": 0.125, "view": 0.125, "login": 0.125, "history": 0.125, "per": 0.125, "user": 0.125}, "80": {"kanban": 0.1, "board": 0.1, "now": 0.1, "remembers": 0.1, "last": 0.1, "viewed": 0.1, "filters": 0.1, "and": 0.1, "column": 0.1, "order": 0.1}, "81": {"invite": 0.1, "flow": 0.1, "redesigned": 0.1, "to": 0.1, "allow": 0.1, "bulk": 0.1, "user": 0.1, "imports": 0.1, "from": 0.1, "csv": 0.1}, "82": {"added": 0.2, "user": 0.2, "deactivation": 0.2, "audit": 0.2, "events": 0.2}, "83": {"users": 0.1, "can": 0.1, "now": 0.1, "request": 0.1, "access": 0.1, "to": 0.1, "private": 0.1, "projects": 0.1, "pending": 0.1, "approval": 0.1}, "84": {"40": 0.****************, "sso": 0.****************, "login": 0.****************, "time": 0.****************, "reduced": 0.****************, "by": 0.****************, "with": 0.****************, "token": 0.****************, "reuse": 0.****************}, "85": {"introduced": 0.1, "focus": 0.1, "mode": 0.1, "hides": 0.1, "sidebar": 0.1, "and": 0.1, "notifications": 0.1, "for": 0.1, "distraction-free": 0.1, "work": 0.1}, "86": {"improved": 0.*****************, "calendar": 0.*****************, "drag-and-drop": 0.*****************, "accuracy": 0.*****************, "for": 0.*****************, "overlapping": 0.*****************, "events": 0.*****************}, "87": {"sticky": 0.16666666666666666, "notes": 0.16666666666666666, "now": 0.16666666666666666, "support": 0.16666666666666666, "markdown": 0.16666666666666666, "formatting": 0.16666666666666666}, "88": {"weekly": 0.1, "summary": 0.1, "emails": 0.1, "now": 0.1, "include": 0.1, "completed": 0.1, "tasks": 0.1, "and": 0.1, "pending": 0.1, "reviews": 0.1}, "89": {"global": 0.09090909090909091, "search": 0.09090909090909091, "now": 0.09090909090909091, "supports": 0.09090909090909091, "filters": 0.09090909090909091, "for": 0.09090909090909091, "date": 0.09090909090909091, "ranges": 0.09090909090909091, "users": 0.09090909090909091, "and": 0.09090909090909091, "tags": 0.09090909090909091}, "90": {"improved": 0.*****************, "relevance": 0.*****************, "ranking": 0.*****************, "for": 0.*****************, "file": 0.*****************, "document": 0.*****************, "results": 0.*****************}, "91": {"quick": 0.*****************, "navigation": 0.*****************, "panel": 0.*****************, "added": 0.*****************, "press": 0.*****************, "to": 0.*****************, "activate": 0.*****************}, "92": {"recently": 0.125, "viewed": 0.125, "items": 0.125, "show": 0.125, "up": 0.125, "in": 0.125, "search": 0.125, "suggestions": 0.125}, "93": {"gdpr": 0.****************, "compliance": 0.****************, "updates": 0.****************, "added": 0.****************, "data": 0.****************, "export": 0.****************, "delete": 0.****************, "requests": 0.****************, "ui": 0.****************}, "94": {"new": 0.****************, "admin-level": 0.****************, "controls": 0.****************, "for": 0.****************, "session": 0.****************, "expiration": 0.****************, "and": 0.****************, "login": 0.****************, "limits": 0.****************}, "95": {"enforced": 0.125, "minimum": 0.125, "password": 0.125, "strength": 0.125, "requirements": 0.125, "across": 0.125, "all": 0.125, "users": 0.125}, "96": {"bug": 0.1, "fix": 0.1, "file": 0.1, "links": 0.1, "no": 0.1, "longer": 0.1, "accessible": 0.1, "after": 0.1, "permission": 0.1, "removal": 0.1}, "97": {"25": 0.09090909090909091, "improved": 0.09090909090909091, "load": 0.09090909090909091, "times": 0.09090909090909091, "on": 0.09090909090909091, "dashboard": 0.09090909090909091, "by": 0.09090909090909091, "through": 0.09090909090909091, "api": 0.09090909090909091, "response": 0.09090909090909091, "caching": 0.09090909090909091}, "98": {"fixed": 0.*****************, "a": 0.*****************, "bug": 0.*****************, "where": 0.*****************, "recurring": 0.*****************, "events": 0.*****************, "were": 0.*****************, "duplicated": 0.*****************, "when": 0.*****************, "edited": 0.*****************, "in": 0.*****************, "bulk": 0.*****************}, "99": {"added": 0.****************, "support": 0.****************, "for": 0.****************, "exporting": 0.****************, "user": 0.****************, "activity": 0.****************, "logs": 0.****************, "as": 0.****************, "csv": 0.****************}, "100": {"updated": 0.125, "ui": 0.125, "components": 0.125, "to": 0.125, "match": 0.125, "new": 0.125, "branding": 0.125, "guidelines": 0.125}, "101": {"reduced": 0.125, "idle": 0.125, "memory": 0.125, "usage": 0.125, "in": 0.125, "background": 0.125, "sync": 0.125, "tasks": 0.125}, "102": {"slack": 0.125, "integration": 0.125, "now": 0.125, "supports": 0.125, "direct": 0.125, "replies": 0.125, "to": 0.125, "alerts": 0.125}, "103": {"added": 0.****************, "native": 0.****************, "support": 0.****************, "for": 0.****************, "notion": 0.****************, "links": 0.****************, "in": 0.****************, "task": 0.****************, "descriptions": 0.****************}, "104": {"webhooks": 0.125, "now": 0.125, "include": 0.125, "retry": 0.125, "logic": 0.125, "with": 0.125, "exponential": 0.125, "backoff": 0.125}, "105": {"oauth": 0.125, "flow": 0.125, "improved": 0.125, "for": 0.125, "google": 0.125, "and": 0.125, "microsoft": 0.125, "accounts": 0.125}, "106": {"code": 0.5, "blocks": 0.5}, "107": {"display": 0.2, "inline": 0.2, "code": 0.2, "and": 0.2, "blocks": 0.2}, "108": {"basic": 1}, "109": {"inline": 0.5, "code": 0.5}, "110": {"code": 0.5, "block": 0.5}, "111": {"advanced": 1}, "112": {"package": 0.5, "install": 0.5}, "113": {"shiki": 0.5, "transformers": 0.5}, "114": {"twoslash": 0.5, "notations": 0.5}, "115": {"to": 0.*****************, "denote": 0.*****************, "a": 0.*****************, "word": 0.*****************, "or": 0.*****************, "phrase": 0.*****************, "as": 0.*****************, "code": 0.*****************, "enclose": 0.*****************, "it": 0.*****************, "in": 0.*****************, "backticks": 0.*****************}, "116": {"use": 0.027777777*********, "fenced": 0.027777777*********, "code": 0.027777777*********, "blocks": 0.027777777*********, "by": 0.027777777*********, "enclosing": 0.027777777*********, "in": 0.027777777*********, "three": 0.027777777*********, "backticks": 0.027777777*********, "and": 0.027777777*********, "follow": 0.027777777*********, "the": 0.027777777*********, "leading": 0.027777777*********, "ticks": 0.027777777*********, "with": 0.027777777*********, "programming": 0.027777777*********, "language": 0.027777777*********, "of": 0.027777777*********, "your": 0.027777777*********, "snippet": 0.027777777*********, "to": 0.027777777*********, "get": 0.027777777*********, "syntax": 0.027777777*********, "highlighting": 0.027777777*********, "optionally": 0.027777777*********, "you": 0.027777777*********, "can": 0.027777777*********, "also": 0.027777777*********, "write": 0.027777777*********, "name": 0.027777777*********, "after": 0.027777777*********, "is": 0.027777777*********, "supported": 0.027777777*********, "default": 0.027777777*********, "using": 0.027777777*********, "rehype": 0.027777777*********}, "117": {"the": 0.041666666666666664, "package": 0.041666666666666664, "install": 0.041666666666666664, "block": 0.041666666666666664, "automatically": 0.041666666666666664, "detects": 0.041666666666666664, "common": 0.041666666666666664, "managers": 0.041666666666666664, "npm": 0.041666666666666664, "yarn": 0.041666666666666664, "pnpm": 0.041666666666666664, "and": 0.041666666666666664, "displays": 0.041666666666666664, "installation": 0.041666666666666664, "commands": 0.041666666666666664, "for": 0.041666666666666664, "each": 0.041666666666666664, "users": 0.041666666666666664, "can": 0.041666666666666664, "switch": 0.041666666666666664, "between": 0.041666666666666664, "different": 0.041666666666666664, "using": 0.041666666666666664, "tabs": 0.041666666666666664}, "118": {"we": 0.07142857142857142, "support": 0.07142857142857142, "some": 0.07142857142857142, "of": 0.07142857142857142, "the": 0.07142857142857142, "shiki": 0.07142857142857142, "transformers": 0.07142857142857142, "allowing": 0.07142857142857142, "you": 0.07142857142857142, "to": 0.07142857142857142, "highlight": 0.07142857142857142, "style": 0.07142857142857142, "specific": 0.07142857142857142, "lines": 0.07142857142857142}, "119": {"learn": 0.2, "more": 0.2, "about": 0.2, "twoslash": 0.2, "notations": 0.2}, "120": {"markdown": 0.5, "syntax": 0.5}, "121": {"text": 0.*****************, "title": 0.*****************, "and": 0.*****************, "styling": 0.*****************, "in": 0.*****************, "standard": 0.*****************, "markdown": 0.*****************}, "122": {"introduction": 1}, "123": {"mdx": 1}, "124": {"images": 1}, "125": {"auto": 0.5, "links": 0.5}, "126": {"cards": 1}, "127": {"callouts": 1}, "128": {"headings": 1}, "129": {"toc": 0.5, "settings": 0.5}, "130": {"custom": 0.5, "anchor": 0.5}, "131": {"tab": 0.5, "groups": 0.5}, "132": {"include": 1}, "133": {"mermaid": 1}, "134": {"latex": 1}, "135": {"fumadocs": 0.*****************, "provides": 0.*****************, "many": 0.*****************, "useful": 0.*****************, "extensions": 0.*****************, "to": 0.*****************, "mdx": 0.*****************, "a": 0.*****************, "markup": 0.*****************, "language": 0.*****************, "here": 0.*****************, "is": 0.*****************, "brief": 0.*****************, "introduction": 0.*****************, "the": 0.*****************, "default": 0.*****************, "syntax": 0.*****************, "of": 0.*****************}, "136": {"mdx": 0.047619047619047616, "is": 0.047619047619047616, "not": 0.047619047619047616, "the": 0.047619047619047616, "only": 0.047619047619047616, "supported": 0.047619047619047616, "format": 0.047619047619047616, "of": 0.047619047619047616, "fumadocs": 0.047619047619047616, "in": 0.047619047619047616, "fact": 0.047619047619047616, "you": 0.047619047619047616, "can": 0.047619047619047616, "use": 0.047619047619047616, "any": 0.047619047619047616, "renderers": 0.047619047619047616, "such": 0.047619047619047616, "as": 0.047619047619047616, "next-mdx-remote": 0.047619047619047616, "or": 0.047619047619047616, "cms": 0.047619047619047616}, "137": {"we": 0.037037037037037035, "recommend": 0.037037037037037035, "mdx": 0.037037037037037035, "a": 0.037037037037037035, "superset": 0.037037037037037035, "of": 0.037037037037037035, "markdown": 0.037037037037037035, "with": 0.037037037037037035, "jsx": 0.037037037037037035, "syntax": 0.037037037037037035, "it": 0.037037037037037035, "allows": 0.037037037037037035, "you": 0.037037037037037035, "to": 0.037037037037037035, "import": 0.037037037037037035, "components": 0.037037037037037035, "and": 0.037037037037037035, "use": 0.037037037037037035, "them": 0.037037037037037035, "right": 0.037037037037037035, "in": 0.037037037037037035, "the": 0.037037037037037035, "document": 0.037037037037037035, "or": 0.037037037037037035, "even": 0.037037037037037035, "export": 0.037037037037037035, "values": 0.037037037037037035}, "138": {"see": 1}, "139": {"mdx": 0.5, "syntax": 0.5}, "140": {"gfm": 0.****************, "github": 0.****************, "flavored": 0.****************, "markdown": 0.****************, "is": 0.****************, "also": 0.****************, "supported": 0.****************, "see": 0.****************, "specification": 0.****************}, "141": {"images": 0.*****************, "are": 0.*****************, "automatically": 0.*****************, "optimized": 0.*****************, "for": 0.*****************, "next": 0.*****************, "image": 0.*****************}, "142": {"internal": 0.*********92307693, "links": 0.*********92307693, "use": 0.*********92307693, "the": 0.*********92307693, "next": 0.*********92307693, "link": 0.*********92307693, "component": 0.*********92307693, "to": 0.*********92307693, "allow": 0.*********92307693, "prefetching": 0.*********92307693, "and": 0.*********92307693, "avoid": 0.*********92307693, "hard-reload": 0.*********92307693}, "143": {"external": 0.07142857142857142, "links": 0.07142857142857142, "will": 0.07142857142857142, "get": 0.07142857142857142, "the": 0.07142857142857142, "default": 0.07142857142857142, "rel": 0.07142857142857142, "noreferrer": 0.07142857142857142, "noopener": 0.07142857142857142, "target": 0.07142857142857142, "_blank": 0.07142857142857142, "attributes": 0.07142857142857142, "for": 0.07142857142857142, "security": 0.07142857142857142}, "144": {"useful": 0.25, "for": 0.25, "adding": 0.25, "links": 0.25}, "145": {"learn": 0.*****************, "more": 0.*****************, "about": 0.*****************, "caching": 0.*****************, "in": 0.*****************, "next": 0.*****************, "js": 0.*****************}, "146": {"you": 0.2, "can": 0.2, "include": 0.2, "icons": 0.2, "too": 0.2}, "147": {"useful": 0.058823529411764705, "for": 0.058823529411764705, "adding": 0.058823529411764705, "tips": 0.058823529411764705, "warnings": 0.058823529411764705, "it": 0.058823529411764705, "is": 0.058823529411764705, "included": 0.058823529411764705, "by": 0.058823529411764705, "default": 0.058823529411764705, "you": 0.058823529411764705, "can": 0.058823529411764705, "specify": 0.058823529411764705, "the": 0.058823529411764705, "type": 0.058823529411764705, "of": 0.058823529411764705, "callout": 0.058823529411764705}, "148": {"info": 0.5, "default": 0.5}, "149": {"warn": 1}, "150": {"error": 1}, "151": {"title": 1}, "152": {"title": 1}, "153": {"type": 0.5, "error": 0.5}, "154": {"hello": 0.5, "world": 0.5}, "155": {"an": 0.05263157894736842, "anchor": 0.05263157894736842, "is": 0.05263157894736842, "automatically": 0.05263157894736842, "applied": 0.05263157894736842, "to": 0.05263157894736842, "each": 0.05263157894736842, "heading": 0.05263157894736842, "it": 0.05263157894736842, "sanitizes": 0.05263157894736842, "invalid": 0.05263157894736842, "characters": 0.05263157894736842, "like": 0.05263157894736842, "spaces": 0.05263157894736842, "e": 0.05263157894736842, "g": 0.05263157894736842, "hello": 0.05263157894736842, "world": 0.05263157894736842, "hello-world": 0.05263157894736842}, "156": {"the": 0.0625, "table": 0.0625, "of": 0.0625, "contents": 0.0625, "toc": 0.0625, "will": 0.0625, "be": 0.0625, "generated": 0.0625, "based": 0.0625, "on": 0.0625, "headings": 0.0625, "you": 0.0625, "can": 0.0625, "also": 0.0625, "customize": 0.0625, "effects": 0.0625}, "157": {"you": 0.125, "can": 0.125, "add": 0.125, "slug": 0.125, "to": 0.125, "customize": 0.125, "heading": 0.125, "anchors": 0.125}, "158": {"you": 0.****************, "can": 0.****************, "also": 0.****************, "chain": 0.****************, "it": 0.****************, "with": 0.****************, "toc": 0.****************, "settings": 0.****************, "like": 0.****************}, "159": {"to": 0.*********92307693, "link": 0.*********92307693, "people": 0.*********92307693, "a": 0.*********92307693, "specific": 0.*********92307693, "heading": 0.*********92307693, "add": 0.*********92307693, "the": 0.*********92307693, "id": 0.*********92307693, "hash": 0.*********92307693, "fragment": 0.*********92307693, "page": 0.*********92307693, "my-heading-id": 0.*********92307693}, "160": {"you": 0.****************, "can": 0.****************, "use": 0.****************, "code": 0.****************, "blocks": 0.****************, "with": 0.****************, "the": 0.****************, "tab": 0.****************, "component": 0.****************}, "161": {"note": 0.*********92307693, "that": 0.*********92307693, "you": 0.*********92307693, "can": 0.*********92307693, "add": 0.*********92307693, "mdx": 0.*********92307693, "components": 0.*********92307693, "instead": 0.*********92307693, "of": 0.*********92307693, "importing": 0.*********92307693, "them": 0.*********92307693, "in": 0.*********92307693, "files": 0.*********92307693}, "162": {"reference": 0.05, "another": 0.05, "file": 0.05, "can": 0.05, "also": 0.05, "be": 0.05, "a": 0.05, "markdown": 0.05, "mdx": 0.05, "document": 0.05, "specify": 0.05, "the": 0.05, "target": 0.05, "path": 0.05, "in": 0.05, "include": 0.05, "tag": 0.05, "relative": 0.05, "to": 0.05, "itself": 0.05}, "163": {"see": 0.3333333333333333, "other": 0.3333333333333333, "usages": 0.3333333333333333}, "164": {"rendering": 0.2, "diagrams": 0.2, "in": 0.2, "your": 0.2, "docs": 0.2}, "165": {"fumadocs": 0.16666666666666666, "supports": 0.16666666666666666, "latex": 0.16666666666666666, "through": 0.16666666666666666, "the": 0.16666666666666666, "component": 0.16666666666666666}, "166": {"2": 0.*****************, "inline": 0.*****************, "c": 0.*****************, "pm": 0.*****************, "sqrt": 0.*****************, "a": 0.*****************, "b": 0.*****************}, "167": {"taylor": 0.1, "expansion": 0.1, "expressing": 0.1, "holomorphic": 0.1, "function": 0.1, "f": 0.1, "x": 0.1, "in": 0.1, "power": 0.1, "series": 0.1}, "168": {"title": 0.5, "tip": 0.5}, "169": {"you": 0.*****************, "can": 0.*****************, "actually": 0.*****************, "copy": 0.*****************, "equations": 0.*****************, "on": 0.*****************, "wikipedia": 0.*****************, "they": 0.*****************, "will": 0.*****************, "be": 0.*****************, "converted": 0.*****************, "into": 0.*****************, "a": 0.*****************, "katex": 0.*****************, "string": 0.*****************, "when": 0.*****************, "paste": 0.*****************, "it": 0.*****************}, "170": {"routing": 1}, "171": {"a": 0.*****************, "shared": 0.*****************, "convention": 0.*****************, "for": 0.*****************, "organizing": 0.*****************, "your": 0.*****************, "documents": 0.*****************}, "172": {"introduction": 1}, "173": {"file": 1}, "174": {"slugs": 1}, "175": {"folder": 1}, "176": {"folder": 0.5, "group": 0.5}, "177": {"meta": 1}, "178": {"pages": 1}, "179": {"rest": 1}, "180": {"extract": 1}, "181": {"link": 1}, "182": {"icons": 1}, "183": {"root": 0.5, "folder": 0.5}, "184": {"fumadocs": 0.*****************, "uses": 0.*****************, "a": 0.*****************, "file-system": 0.*****************, "based": 0.*****************, "routing": 0.*****************, "system": 0.*****************, "to": 0.*****************, "organize": 0.*****************, "your": 0.*****************, "documents": 0.*****************, "this": 0.*****************, "allows": 0.*****************, "you": 0.*****************, "create": 0.*****************, "clear": 0.*****************, "and": 0.*****************, "consistent": 0.*****************, "structure": 0.*****************, "for": 0.*****************, "documentation": 0.*****************, "making": 0.*****************, "it": 0.*****************, "easier": 0.*****************, "users": 0.*****************, "navigate": 0.*****************, "find": 0.*****************, "the": 0.*****************, "information": 0.*****************, "they": 0.*****************, "need": 0.*****************}, "185": {"a": 0.1, "mdx": 0.1, "or": 0.1, "markdown": 0.1, "file": 0.1, "you": 0.1, "can": 0.1, "customize": 0.1, "its": 0.1, "frontmatter": 0.1}, "186": {"name": 1}, "187": {"description": 1}, "188": {"title": 1}, "189": {"the": 0.25, "title": 0.25, "of": 0.25, "page": 0.25}, "190": {"description": 1}, "191": {"the": 0.25, "description": 0.25, "of": 0.25, "page": 0.25}, "192": {"icon": 1}, "193": {"the": 0.16666666666666666, "name": 0.16666666666666666, "of": 0.16666666666666666, "icon": 0.16666666666666666, "see": 0.16666666666666666, "icons": 0.16666666666666666}, "194": {"full": 1}, "195": {"fill": 0.****************, "all": 0.****************, "available": 0.****************, "space": 0.****************, "on": 0.****************, "the": 0.****************, "page": 0.****************, "fumadocs": 0.****************, "ui": 0.****************}, "196": {"the": 0.09090909090909091, "slugs": 0.09090909090909091, "of": 0.09090909090909091, "a": 0.09090909090909091, "page": 0.09090909090909091, "are": 0.09090909090909091, "generated": 0.09090909090909091, "from": 0.09090909090909091, "its": 0.09090909090909091, "file": 0.09090909090909091, "path": 0.09090909090909091}, "197": {"path": 0.2, "relative": 0.2, "to": 0.2, "content": 0.2, "folder": 0.2}, "198": {"slugs": 1}, "199": {"dir": 0.3333333333333333, "page": 0.3333333333333333, "mdx": 0.3333333333333333}, "200": {"'dir'": 0.5, "'page'": 0.5}, "201": {"dir": 0.3333333333333333, "index": 0.3333333333333333, "mdx": 0.3333333333333333}, "202": {"'dir'": 1}, "203": {"organize": 0.*****************, "multiple": 0.*****************, "pages": 0.*****************, "you": 0.*****************, "can": 0.*****************, "create": 0.*****************, "a": 0.*****************, "meta": 0.*****************, "file": 0.*****************, "to": 0.*****************, "customize": 0.*****************, "folders": 0.*****************}, "204": {"by": 0.041666666666666664, "default": 0.041666666666666664, "putting": 0.041666666666666664, "a": 0.041666666666666664, "file": 0.041666666666666664, "into": 0.041666666666666664, "folder": 0.041666666666666664, "will": 0.041666666666666664, "change": 0.041666666666666664, "its": 0.041666666666666664, "slugs": 0.041666666666666664, "you": 0.041666666666666664, "can": 0.041666666666666664, "wrap": 0.041666666666666664, "the": 0.041666666666666664, "name": 0.041666666666666664, "in": 0.041666666666666664, "parentheses": 0.041666666666666664, "to": 0.041666666666666664, "avoid": 0.041666666666666664, "impacting": 0.041666666666666664, "of": 0.041666666666666664, "child": 0.041666666666666664, "files": 0.041666666666666664}, "205": {"path": 0.2, "relative": 0.2, "to": 0.2, "content": 0.2, "folder": 0.2}, "206": {"slugs": 1}, "207": {"group-name": 0.3333333333333333, "page": 0.3333333333333333, "mdx": 0.3333333333333333}, "208": {"'page'": 1}, "209": {"customize": 0.09090909090909091, "folders": 0.09090909090909091, "by": 0.09090909090909091, "creating": 0.09090909090909091, "a": 0.09090909090909091, "meta": 0.09090909090909091, "json": 0.09090909090909091, "file": 0.09090909090909091, "in": 0.09090909090909091, "the": 0.09090909090909091, "folder": 0.09090909090909091}, "210": {"name": 1}, "211": {"description": 1}, "212": {"title": 1}, "213": {"display": 0.5, "name": 0.5}, "214": {"icon": 1}, "215": {"the": 0.16666666666666666, "name": 0.16666666666666666, "of": 0.16666666666666666, "icon": 0.16666666666666666, "see": 0.16666666666666666, "icons": 0.16666666666666666}, "216": {"pages": 1}, "217": {"folder": 0.25, "items": 0.25, "see": 0.25, "below": 0.25}, "218": {"defaultopen": 1}, "219": {"open": 0.2, "the": 0.2, "folder": 0.2, "by": 0.2, "default": 0.2}, "220": {"by": 0.*****************, "default": 0.*****************, "folder": 0.*****************, "items": 0.*****************, "are": 0.*****************, "sorted": 0.*****************, "alphabetically": 0.*****************}, "221": {"you": 0.*****************, "can": 0.*****************, "add": 0.*****************, "or": 0.*****************, "control": 0.*****************, "the": 0.*****************, "order": 0.*****************, "of": 0.*****************, "items": 0.*****************, "using": 0.*****************, "pages": 0.*****************, "are": 0.*****************, "not": 0.*****************, "included": 0.*****************, "unless": 0.*****************, "they": 0.*****************, "listed": 0.*****************, "inside": 0.*****************}, "222": {"add": 0.07142857142857142, "a": 0.07142857142857142, "item": 0.07142857142857142, "to": 0.07142857142857142, "include": 0.07142857142857142, "remaining": 0.07142857142857142, "pages": 0.07142857142857142, "sorted": 0.07142857142857142, "alphabetically": 0.07142857142857142, "or": 0.07142857142857142, "z": 0.07142857142857142, "for": 0.07142857142857142, "descending": 0.07142857142857142, "order": 0.07142857142857142}, "223": {"you": 0.09090909090909091, "can": 0.09090909090909091, "add": 0.09090909090909091, "name": 0.09090909090909091, "to": 0.09090909090909091, "prevent": 0.09090909090909091, "an": 0.09090909090909091, "item": 0.09090909090909091, "from": 0.09090909090909091, "being": 0.09090909090909091, "included": 0.09090909090909091}, "224": {"you": 0.1, "can": 0.1, "extract": 0.1, "the": 0.1, "items": 0.1, "from": 0.1, "a": 0.1, "folder": 0.1, "with": 0.1, "folder_name": 0.1}, "225": {"use": 0.09090909090909091, "the": 0.09090909090909091, "syntax": 0.09090909090909091, "text": 0.09090909090909091, "url": 0.09090909090909091, "to": 0.09090909090909091, "insert": 0.09090909090909091, "links": 0.09090909090909091, "or": 0.09090909090909091, "icon": 0.09090909090909091, "add": 0.09090909090909091}, "226": {"this": 0.*****************, "fumadocs": 0.*****************, "template": 0.*****************, "converts": 0.*****************, "the": 0.*****************, "icon": 0.*****************, "names": 0.*****************, "to": 0.*****************, "jsx": 0.*****************, "elements": 0.*****************, "in": 0.*****************, "runtime": 0.*****************, "and": 0.*****************, "renders": 0.*****************, "it": 0.*****************, "as": 0.*****************, "a": 0.*****************, "component": 0.*****************}, "227": {"marks": 0.*********92307693, "the": 0.*********92307693, "folder": 0.*********92307693, "as": 0.*********92307693, "a": 0.*********92307693, "root": 0.*********92307693, "only": 0.*********92307693, "items": 0.*********92307693, "in": 0.*********92307693, "opened": 0.*********92307693, "will": 0.*********92307693, "be": 0.*********92307693, "considered": 0.*********92307693}, "228": {"for": 0.043478260869565216, "example": 0.043478260869565216, "when": 0.043478260869565216, "you": 0.043478260869565216, "are": 0.043478260869565216, "opening": 0.043478260869565216, "a": 0.043478260869565216, "root": 0.043478260869565216, "folder": 0.043478260869565216, "framework": 0.043478260869565216, "the": 0.043478260869565216, "other": 0.043478260869565216, "folders": 0.043478260869565216, "e": 0.043478260869565216, "g": 0.043478260869565216, "headless": 0.043478260869565216, "not": 0.043478260869565216, "shown": 0.043478260869565216, "on": 0.043478260869565216, "sidebar": 0.043478260869565216, "and": 0.043478260869565216, "navigation": 0.043478260869565216, "elements": 0.043478260869565216}, "229": {"title": 0.3333333333333333, "fumadocs": 0.3333333333333333, "ui": 0.3333333333333333}, "230": {"fumadocs": 0.06666666666666667, "ui": 0.06666666666666667, "renders": 0.06666666666666667, "root": 0.06666666666666667, "folders": 0.06666666666666667, "as": 0.06666666666666667, "sidebar": 0.06666666666666667, "tabs": 0.06666666666666667, "which": 0.06666666666666667, "allows": 0.06666666666666667, "user": 0.06666666666666667, "to": 0.06666666666666667, "switch": 0.06666666666666667, "between": 0.06666666666666667, "them": 0.06666666666666667}, "231": {"ai": 0.5, "search": 0.5}, "232": {"configure": 0.125, "and": 0.125, "use": 0.125, "ai-powered": 0.125, "search": 0.125, "in": 0.125, "your": 0.125, "documentation": 0.125}, "233": {"introduction": 1}, "234": {"setting": 0.2, "up": 0.2, "the": 0.2, "api": 0.2, "key": 0.2}, "235": {"application": 0.5, "configuration": 0.5}, "236": {"how": 0.25, "queries": 0.25, "are": 0.25, "handled": 0.25}, "237": {"this": 0.04, "template": 0.04, "comes": 0.04, "with": 0.04, "built-in": 0.04, "ai-powered": 0.04, "search": 0.04, "capabilities": 0.04, "although": 0.04, "feature": 0.04, "is": 0.04, "enabled": 0.04, "by": 0.04, "default": 0.04, "you": 0.04, "ll": 0.04, "need": 0.04, "to": 0.04, "configure": 0.04, "an": 0.04, "api": 0.04, "key": 0.04, "for": 0.04, "full": 0.04, "functionality": 0.04}, "238": {"to": 0.03333333333333333, "activate": 0.03333333333333333, "ai": 0.03333333333333333, "search": 0.03333333333333333, "add": 0.03333333333333333, "your": 0.03333333333333333, "openai": 0.03333333333333333, "api": 0.03333333333333333, "key": 0.03333333333333333, "the": 0.03333333333333333, "env": 0.03333333333333333, "file": 0.03333333333333333, "at": 0.03333333333333333, "root": 0.03333333333333333, "of": 0.03333333333333333, "project": 0.03333333333333333, "if": 0.03333333333333333, "you": 0.03333333333333333, "don": 0.03333333333333333, "t": 0.03333333333333333, "already": 0.03333333333333333, "have": 0.03333333333333333, "one": 0.03333333333333333, "can": 0.03333333333333333, "generate": 0.03333333333333333, "a": 0.03333333333333333, "by": 0.03333333333333333, "signing": 0.03333333333333333, "up": 0.03333333333333333, "platform": 0.03333333333333333}, "239": {"the": 0.045454545454545456, "logic": 0.045454545454545456, "for": 0.045454545454545456, "ai": 0.045454545454545456, "search": 0.045454545454545456, "is": 0.045454545454545456, "pre-configured": 0.045454545454545456, "in": 0.045454545454545456, "components": 0.045454545454545456, "fumadocs": 0.045454545454545456, "directory": 0.045454545454545456, "you": 0.045454545454545456, "can": 0.045454545454545456, "explore": 0.045454545454545456, "or": 0.045454545454545456, "modify": 0.045454545454545456, "implementation": 0.045454545454545456, "there": 0.045454545454545456, "to": 0.045454545454545456, "suit": 0.045454545454545456, "your": 0.045454545454545456, "needs": 0.045454545454545456}, "240": {"ai": 0.045454545454545456, "search": 0.045454545454545456, "uses": 0.045454545454545456, "the": 0.045454545454545456, "openai": 0.045454545454545456, "api": 0.045454545454545456, "to": 0.045454545454545456, "process": 0.045454545454545456, "user": 0.045454545454545456, "queries": 0.045454545454545456, "instead": 0.045454545454545456, "of": 0.045454545454545456, "retrieval-augmented": 0.045454545454545456, "generation": 0.045454545454545456, "rag": 0.045454545454545456, "it": 0.045454545454545456, "relies": 0.045454545454545456, "on": 0.045454545454545456, "web": 0.045454545454545456, "for": 0.045454545454545456, "external": 0.045454545454545456, "information": 0.045454545454545456}, "241": {"tip": 0.03571428571428571, "you": 0.03571428571428571, "can": 0.03571428571428571, "replace": 0.03571428571428571, "the": 0.03571428571428571, "default": 0.03571428571428571, "openai": 0.03571428571428571, "integration": 0.03571428571428571, "with": 0.03571428571428571, "a": 0.03571428571428571, "custom": 0.03571428571428571, "setup": 0.03571428571428571, "such": 0.03571428571428571, "as": 0.03571428571428571, "inkeep": 0.03571428571428571, "for": 0.03571428571428571, "more": 0.03571428571428571, "efficient": 0.03571428571428571, "retrieval-augmented": 0.03571428571428571, "generation": 0.03571428571428571, "rag": 0.03571428571428571, "implementation": 0.03571428571428571, "tailored": 0.03571428571428571, "to": 0.03571428571428571, "your": 0.03571428571428571, "content": 0.03571428571428571, "and": 0.03571428571428571, "infrastructure": 0.03571428571428571}, "242": {"async": 0.5, "mode": 0.5}, "243": {"runtime": 0.2, "compilation": 0.2, "of": 0.2, "content": 0.2, "files": 0.2}, "244": {"introduction": 1}, "245": {"constraints": 1}, "246": {"by": 0.038461538461538464, "default": 0.038461538461538464, "all": 0.038461538461538464, "markdown": 0.038461538461538464, "and": 0.038461538461538464, "mdx": 0.038461538461538464, "files": 0.038461538461538464, "must": 0.038461538461538464, "be": 0.038461538461538464, "precompiled": 0.038461538461538464, "even": 0.038461538461538464, "when": 0.038461538461538464, "running": 0.038461538461538464, "the": 0.038461538461538464, "development": 0.038461538461538464, "server": 0.038461538461538464, "this": 0.038461538461538464, "requirement": 0.038461538461538464, "can": 0.038461538461538464, "increase": 0.038461538461538464, "startup": 0.038461538461538464, "time": 0.038461538461538464, "for": 0.038461538461538464, "large": 0.038461538461538464, "documentation": 0.038461538461538464, "sites": 0.038461538461538464}, "247": {"to": 0.*****************, "improve": 0.*****************, "performance": 0.*****************, "this": 0.*****************, "template": 0.*****************, "enables": 0.*****************, "async": 0.*****************, "mode": 0.*****************, "by": 0.*****************, "default": 0.*****************, "allowing": 0.*****************, "content": 0.*****************, "files": 0.*****************, "be": 0.*****************, "compiled": 0.*****************, "at": 0.*****************, "runtime": 0.*****************, "instead": 0.*****************}, "248": {"async": 0.125, "mode": 0.125, "introduces": 0.125, "some": 0.125, "limitations": 0.125, "to": 0.125, "mdx": 0.125, "features": 0.125}, "249": {"no": 0.045454545454545456, "import": 0.045454545454545456, "export": 0.045454545454545456, "statements": 0.045454545454545456, "are": 0.045454545454545456, "allowed": 0.045454545454545456, "inside": 0.045454545454545456, "mdx": 0.045454545454545456, "files": 0.045454545454545456, "if": 0.045454545454545456, "you": 0.045454545454545456, "need": 0.045454545454545456, "to": 0.045454545454545456, "use": 0.045454545454545456, "custom": 0.045454545454545456, "components": 0.045454545454545456, "pass": 0.045454545454545456, "them": 0.045454545454545456, "through": 0.045454545454545456, "the": 0.045454545454545456, "prop": 0.045454545454545456, "instead": 0.045454545454545456}, "250": {"images": 0.034482758620689655, "must": 0.034482758620689655, "be": 0.034482758620689655, "referenced": 0.034482758620689655, "using": 0.034482758620689655, "urls": 0.034482758620689655, "e": 0.034482758620689655, "g": 0.034482758620689655, "test": 0.034482758620689655, "png": 0.034482758620689655, "avoid": 0.034482758620689655, "relative": 0.034482758620689655, "file": 0.034482758620689655, "paths": 0.034482758620689655, "like": 0.034482758620689655, "image": 0.034482758620689655, "place": 0.034482758620689655, "your": 0.034482758620689655, "inside": 0.034482758620689655, "the": 0.034482758620689655, "public": 0.034482758620689655, "folder": 0.034482758620689655, "and": 0.034482758620689655, "reference": 0.034482758620689655, "them": 0.034482758620689655, "via": 0.034482758620689655, "their": 0.034482758620689655, "url": 0.034482758620689655, "path": 0.034482758620689655}, "251": {"llm": 0.5, "support": 0.5}, "252": {"provide": 0.125, "ai-friendly": 0.125, "endpoints": 0.125, "to": 0.125, "assist": 0.125, "large": 0.125, "language": 0.125, "models": 0.125}, "253": {"introduction": 1}, "254": {"llms": 0.5, "txt": 0.5}, "255": {"llms-full": 0.5, "txt": 0.5}, "256": {"llms": 0.5, "mdx": 0.5}, "257": {"large": 0.034482758620689655, "language": 0.034482758620689655, "models": 0.034482758620689655, "waste": 0.034482758620689655, "tokens": 0.034482758620689655, "crawling": 0.034482758620689655, "html": 0.034482758620689655, "js": 0.034482758620689655, "and": 0.034482758620689655, "site": 0.034482758620689655, "chrome": 0.034482758620689655, "by": 0.034482758620689655, "shipping": 0.034482758620689655, "a": 0.034482758620689655, "few": 0.034482758620689655, "simple": 0.034482758620689655, "endpoints": 0.034482758620689655, "you": 0.034482758620689655, "can": 0.034482758620689655, "provide": 0.034482758620689655, "more": 0.034482758620689655, "efficient": 0.034482758620689655, "way": 0.034482758620689655, "for": 0.034482758620689655, "llms": 0.034482758620689655, "to": 0.034482758620689655, "access": 0.034482758620689655, "your": 0.034482758620689655, "documentation": 0.034482758620689655}, "258": {"llms": 0.1, "txt": 0.1, "a": 0.1, "concise": 0.1, "sitemap": 0.1, "with": 0.1, "titles": 0.1, "urls": 0.1, "and": 0.1, "summaries": 0.1}, "259": {"llms-full": 0.****************, "txt": 0.****************, "a": 0.****************, "full": 0.****************, "markdown": 0.****************, "dump": 0.****************, "of": 0.****************, "every": 0.****************, "document": 0.****************}, "260": {"llms": 0.1, "mdx": 0.1, "the": 0.1, "raw": 0.1, "markdown": 0.1, "content": 0.1, "of": 0.1, "a": 0.1, "requested": 0.1, "page": 0.1}, "261": {"lists": 0.125, "each": 0.125, "section": 0.125, "and": 0.125, "page": 0.125, "in": 0.125, "your": 0.125, "documentation": 0.125}, "262": {"includes": 0.16666666666666666, "optional": 0.16666666666666666, "one-line": 0.16666666666666666, "descriptions": 0.16666666666666666, "from": 0.16666666666666666, "frontmatter": 0.16666666666666666}, "263": {"no": 0.*****************, "boilerplate": 0.*****************, "or": 0.*****************, "styling": 0.*****************, "just": 0.*****************, "the": 0.*****************, "outline": 0.*****************}, "264": {"this": 0.05, "file": 0.05, "is": 0.05, "generated": 0.05, "at": 0.05, "the": 0.05, "root": 0.05, "of": 0.05, "your": 0.05, "deployed": 0.05, "site": 0.05, "enabling": 0.05, "agents": 0.05, "to": 0.05, "discover": 0.05, "it": 0.05, "https": 0.05, "your-domain": 0.05, "llms": 0.05, "txt": 0.05}, "265": {"concatenates": 0.125, "the": 0.125, "raw": 0.125, "mdx": 0.125, "markdown": 0.125, "of": 0.125, "every": 0.125, "page": 0.125}, "266": {"preserves": 0.*****************, "headings": 0.*****************, "paragraphs": 0.*****************, "code": 0.*****************, "samples": 0.*****************, "and": 0.*****************, "frontmatter": 0.*****************}, "267": {"lets": 0.*****************, "an": 0.*****************, "llm": 0.*****************, "ingest": 0.*****************, "your": 0.*****************, "entire": 0.*****************, "documentation": 0.*****************, "corpus": 0.*****************, "in": 0.*****************, "a": 0.*****************, "single": 0.*****************, "fetch": 0.*****************}, "268": {"this": 0.*****************, "file": 0.*****************, "is": 0.*****************, "generated": 0.*****************, "at": 0.*****************, "build": 0.*****************, "time": 0.*****************, "by": 0.*****************, "globbing": 0.*****************, "content": 0.*****************, "docs": 0.*****************, "mdx": 0.*****************, "and": 0.*****************, "joining": 0.*****************, "files": 0.*****************, "in": 0.*****************, "sidebar": 0.*****************, "order": 0.*****************}, "269": {"provides": 0.1, "the": 0.1, "raw": 0.1, "mdx": 0.1, "markdown": 0.1, "content": 0.1, "of": 0.1, "a": 0.1, "requested": 0.1, "page": 0.1}, "270": {"preserves": 0.*****************, "headings": 0.*****************, "paragraphs": 0.*****************, "code": 0.*****************, "samples": 0.*****************, "and": 0.*****************, "frontmatter": 0.*****************}, "271": {"example": 0.25, "llms": 0.25, "mdx": 0.25, "app": 0.25}, "272": {"openapi": 1}, "273": {"generate": 0.16666666666666666, "and": 0.16666666666666666, "document": 0.16666666666666666, "your": 0.16666666666666666, "openapi": 0.16666666666666666, "schema": 0.16666666666666666}, "274": {"introduction": 1}, "275": {"features": 1}, "276": {"changing": 0.25, "the": 0.25, "openapi": 0.25, "schema": 0.25}, "277": {"fumadocs": 0.*****************, "provides": 0.*****************, "an": 0.*****************, "official": 0.*****************, "openapi": 0.*****************, "integration": 0.*****************, "to": 0.*****************, "generate": 0.*****************, "and": 0.*****************, "document": 0.*****************, "your": 0.*****************, "schema": 0.*****************}, "278": {"the": 0.2, "official": 0.2, "openapi": 0.2, "integration": 0.2, "supports": 0.2}, "279": {"basic": 0.25, "api": 0.25, "endpoint": 0.25, "information": 0.25}, "280": {"interactive": 0.3333333333333333, "api": 0.3333333333333333, "playground": 0.3333333333333333}, "281": {"example": 0.****************, "code": 0.****************, "to": 0.****************, "send": 0.****************, "request": 0.****************, "in": 0.****************, "different": 0.****************, "programming": 0.****************, "languages": 0.****************}, "282": {"response": 0.2, "samples": 0.2, "and": 0.2, "typescript": 0.2, "definitions": 0.2}, "283": {"request": 0.*****************, "parameters": 0.*****************, "and": 0.*****************, "body": 0.*****************, "generated": 0.*****************, "from": 0.*****************, "schemas": 0.*****************}, "284": {"the": 0.043478260869565216, "generate-docs": 0.043478260869565216, "script": 0.043478260869565216, "uses": 0.043478260869565216, "openapi": 0.043478260869565216, "schema": 0.043478260869565216, "to": 0.043478260869565216, "generate": 0.043478260869565216, "documentation": 0.043478260869565216, "you": 0.043478260869565216, "can": 0.043478260869565216, "change": 0.043478260869565216, "by": 0.043478260869565216, "modifying": 0.043478260869565216, "json": 0.043478260869565216, "file": 0.043478260869565216, "in": 0.043478260869565216, "content": 0.043478260869565216, "docs": 0.043478260869565216, "api-reference": 0.043478260869565216, "of": 0.043478260869565216, "your": 0.043478260869565216, "project": 0.043478260869565216}, "285": {"0": 0.125, "1": 0.125, "3": 0.125, "only": 0.125, "openapi": 0.125, "and": 0.125, "are": 0.125, "supported": 0.125}, "286": {"generate": 0.2, "docs": 0.2, "with": 0.2, "the": 0.2, "script": 0.2}, "287": {"adding": 0.25, "a": 0.25, "root": 0.25, "folder": 0.25}, "288": {"learn": 0.1, "how": 0.1, "to": 0.1, "add": 0.1, "a": 0.1, "new": 0.1, "root": 0.1, "folder": 0.1, "your": 0.1, "documentation": 0.1}, "289": {"introduction": 1}, "290": {"steps": 0.*****************, "to": 0.*****************, "add": 0.*****************, "a": 0.*****************, "new": 0.*****************, "root": 0.*****************, "folder": 0.*****************}, "291": {"1": 0.16666666666666666, "step": 0.16666666666666666, "create": 0.16666666666666666, "a": 0.16666666666666666, "new": 0.16666666666666666, "folder": 0.16666666666666666}, "292": {"2": 0.*****************, "step": 0.*****************, "update": 0.*****************, "the": 0.*****************, "meta": 0.*****************, "json": 0.*****************, "file": 0.*****************}, "293": {"3": 0.2, "step": 0.2, "update": 0.2, "the": 0.2, "homepage": 0.2}, "294": {"4": 0.2, "step": 0.2, "update": 0.2, "colors": 0.2, "optional": 0.2}, "295": {"5": 0.****************, "step": 0.****************, "create": 0.****************, "meta": 0.****************, "json": 0.****************, "in": 0.****************, "the": 0.****************, "new": 0.****************, "folder": 0.****************}, "296": {"6": 0.16666666666666666, "step": 0.16666666666666666, "create": 0.16666666666666666, "a": 0.16666666666666666, "new": 0.16666666666666666, "page": 0.16666666666666666}, "297": {"fumadocs": 0.034482758620689655, "allows": 0.034482758620689655, "you": 0.034482758620689655, "to": 0.034482758620689655, "create": 0.034482758620689655, "new": 0.034482758620689655, "root": 0.034482758620689655, "folders": 0.034482758620689655, "in": 0.034482758620689655, "your": 0.034482758620689655, "documentation": 0.034482758620689655, "this": 0.034482758620689655, "helps": 0.034482758620689655, "organize": 0.034482758620689655, "docs": 0.034482758620689655, "into": 0.034482758620689655, "clear": 0.034482758620689655, "sections": 0.034482758620689655, "or": 0.034482758620689655, "categories": 0.034482758620689655, "guide": 0.034482758620689655, "will": 0.034482758620689655, "walk": 0.034482758620689655, "through": 0.034482758620689655, "adding": 0.034482758620689655, "a": 0.034482758620689655, "folder": 0.034482758620689655, "step": 0.034482758620689655, "by": 0.034482758620689655}, "298": {"create": 0.*****************, "a": 0.*****************, "new": 0.*****************, "folder": 0.*****************, "in": 0.*****************, "the": 0.*****************, "content": 0.*****************, "docs": 0.*****************, "directory": 0.*****************, "you": 0.*****************, "can": 0.*****************, "name": 0.*****************, "it": 0.*****************, "anything": 0.*****************, "this": 0.*****************, "example": 0.*****************, "uses": 0.*****************, "cli": 0.*****************}, "299": {"open": 0.*****************, "the": 0.*****************, "meta": 0.*****************, "json": 0.*****************, "file": 0.*****************, "located": 0.*****************, "in": 0.*****************, "content": 0.*****************, "docs": 0.*****************, "directory": 0.*****************, "add": 0.*****************, "your": 0.*****************, "new": 0.*****************, "folder": 0.*****************, "name": 0.*****************, "to": 0.*****************, "pages": 0.*****************, "array": 0.*****************}, "300": {"open": 0.*********92307693, "page": 0.*********92307693, "tsx": 0.*********92307693, "in": 0.*********92307693, "the": 0.*********92307693, "app": 0.*********92307693, "home": 0.*********92307693, "directory": 0.*********92307693, "and": 0.*********92307693, "add": 0.*********92307693, "a": 0.*********92307693, "new": 0.*********92307693, "documentationitem": 0.*********92307693}, "301": {"edit": 0.07142857142857142, "globals": 0.07142857142857142, "css": 0.07142857142857142, "in": 0.07142857142857142, "the": 0.07142857142857142, "styles": 0.07142857142857142, "directory": 0.07142857142857142, "to": 0.07142857142857142, "define": 0.07142857142857142, "colors": 0.07142857142857142, "for": 0.07142857142857142, "your": 0.07142857142857142, "new": 0.07142857142857142, "folder": 0.07142857142857142}, "302": {"update": 0.125, "the": 0.125, "base": 0.125, "styles": 0.125, "to": 0.125, "apply": 0.125, "new": 0.125, "color": 0.125}, "303": {"create": 0.1, "a": 0.1, "meta": 0.1, "json": 0.1, "file": 0.1, "in": 0.1, "your": 0.1, "new": 0.1, "cli": 0.1, "folder": 0.1}, "304": {"add": 0.*****************, "the": 0.*****************, "following": 0.*****************, "content": 0.*****************, "to": 0.*****************, "meta": 0.*****************, "json": 0.*****************}, "305": {"this": 0.06666666666666667, "file": 0.06666666666666667, "defines": 0.06666666666666667, "the": 0.06666666666666667, "metadata": 0.06666666666666667, "for": 0.06666666666666667, "your": 0.06666666666666667, "new": 0.06666666666666667, "folder": 0.06666666666666667, "including": 0.06666666666666667, "its": 0.06666666666666667, "title": 0.06666666666666667, "description": 0.06666666666666667, "and": 0.06666666666666667, "icon": 0.06666666666666667}, "306": {"create": 0.1, "an": 0.1, "index": 0.1, "mdx": 0.1, "page": 0.1, "in": 0.1, "your": 0.1, "new": 0.1, "cli": 0.1, "folder": 0.1}, "307": {"add": 0.16666666666666666, "initial": 0.16666666666666666, "content": 0.16666666666666666, "to": 0.16666666666666666, "index": 0.16666666666666666, "mdx": 0.16666666666666666}, "308": {"that's": 0.05263157894736842, "it": 0.05263157894736842, "you've": 0.05263157894736842, "successfully": 0.05263157894736842, "added": 0.05263157894736842, "a": 0.05263157894736842, "new": 0.05263157894736842, "root": 0.05263157894736842, "folder": 0.05263157894736842, "to": 0.05263157894736842, "your": 0.05263157894736842, "documentation": 0.05263157894736842, "now": 0.05263157894736842, "navigate": 0.05263157894736842, "docs": 0.05263157894736842, "website": 0.05263157894736842, "view": 0.05263157894736842, "and": 0.05263157894736842, "content": 0.05263157894736842}, "309": {"create": 0.3333333333333333, "special": 0.3333333333333333, "events": 0.3333333333333333}, "310": {"creates": 0.125, "a": 0.125, "new": 0.125, "special": 0.125, "event": 0.125, "for": 0.125, "the": 0.125, "museum": 0.125}, "311": {"creates": 0.125, "a": 0.125, "new": 0.125, "special": 0.125, "event": 0.125, "for": 0.125, "the": 0.125, "museum": 0.125}, "312": {"delete": 0.3333333333333333, "special": 0.3333333333333333, "event": 0.3333333333333333}, "313": {"delete": 0.*********92307693, "a": 0.*********92307693, "special": 0.*********92307693, "event": 0.*********92307693, "from": 0.*********92307693, "the": 0.*********92307693, "collection": 0.*********92307693, "allows": 0.*********92307693, "museum": 0.*********92307693, "to": 0.*********92307693, "cancel": 0.*********92307693, "planned": 0.*********92307693, "events": 0.*********92307693}, "314": {"delete": 0.*********92307693, "a": 0.*********92307693, "special": 0.*********92307693, "event": 0.*********92307693, "from": 0.*********92307693, "the": 0.*********92307693, "collection": 0.*********92307693, "allows": 0.*********92307693, "museum": 0.*********92307693, "to": 0.*********92307693, "cancel": 0.*********92307693, "planned": 0.*********92307693, "events": 0.*********92307693}, "315": {"get": 0.3333333333333333, "special": 0.3333333333333333, "event": 0.3333333333333333}, "316": {"get": 0.16666666666666666, "details": 0.16666666666666666, "about": 0.16666666666666666, "a": 0.16666666666666666, "special": 0.16666666666666666, "event": 0.16666666666666666}, "317": {"get": 0.16666666666666666, "details": 0.16666666666666666, "about": 0.16666666666666666, "a": 0.16666666666666666, "special": 0.16666666666666666, "event": 0.16666666666666666}, "318": {"list": 0.3333333333333333, "special": 0.3333333333333333, "events": 0.3333333333333333}, "319": {"return": 0.1, "a": 0.1, "list": 0.1, "of": 0.1, "upcoming": 0.1, "special": 0.1, "events": 0.1, "at": 0.1, "the": 0.1, "museum": 0.1}, "320": {"return": 0.1, "a": 0.1, "list": 0.1, "of": 0.1, "upcoming": 0.1, "special": 0.1, "events": 0.1, "at": 0.1, "the": 0.1, "museum": 0.1}, "321": {"new": 0.25, "special": 0.25, "event": 0.25, "added": 0.25}, "322": {"publish": 0.125, "details": 0.125, "of": 0.125, "a": 0.125, "new": 0.125, "or": 0.125, "updated": 0.125, "event": 0.125}, "323": {"update": 0.3333333333333333, "special": 0.3333333333333333, "event": 0.3333333333333333}, "324": {"update": 0.*****************, "the": 0.*****************, "details": 0.*****************, "of": 0.*****************, "a": 0.*****************, "special": 0.*****************, "event": 0.*****************}, "325": {"update": 0.*****************, "the": 0.*****************, "details": 0.*****************, "of": 0.*****************, "a": 0.*****************, "special": 0.*****************, "event": 0.*****************}, "326": {"get": 0.3333333333333333, "museum": 0.3333333333333333, "hours": 0.3333333333333333}, "327": {"get": 0.2, "upcoming": 0.2, "museum": 0.2, "operating": 0.2, "hours": 0.2}, "328": {"get": 0.2, "upcoming": 0.2, "museum": 0.2, "operating": 0.2, "hours": 0.2}, "329": {"buy": 0.3333333333333333, "museum": 0.3333333333333333, "tickets": 0.3333333333333333}, "330": {"purchase": 0.****************, "museum": 0.****************, "tickets": 0.****************, "for": 0.****************, "general": 0.****************, "entry": 0.****************, "or": 0.****************, "special": 0.****************, "events": 0.****************}, "331": {"purchase": 0.****************, "museum": 0.****************, "tickets": 0.****************, "for": 0.****************, "general": 0.****************, "entry": 0.****************, "or": 0.****************, "special": 0.****************, "events": 0.****************}, "332": {"get": 0.25, "ticket": 0.25, "qr": 0.25, "code": 0.25}, "333": {"return": 0.07142857142857142, "an": 0.07142857142857142, "image": 0.07142857142857142, "of": 0.07142857142857142, "your": 0.07142857142857142, "ticket": 0.07142857142857142, "with": 0.07142857142857142, "scannable": 0.07142857142857142, "qr": 0.07142857142857142, "code": 0.07142857142857142, "used": 0.07142857142857142, "for": 0.07142857142857142, "event": 0.07142857142857142, "entry": 0.07142857142857142}, "334": {"return": 0.07142857142857142, "an": 0.07142857142857142, "image": 0.07142857142857142, "of": 0.07142857142857142, "your": 0.07142857142857142, "ticket": 0.07142857142857142, "with": 0.07142857142857142, "scannable": 0.07142857142857142, "qr": 0.07142857142857142, "code": 0.07142857142857142, "used": 0.07142857142857142, "for": 0.07142857142857142, "event": 0.07142857142857142, "entry": 0.07142857142857142}}, "page_id": {"1": {"docs": 0.5, "api-reference": 0.5}, "2": {"docs": 0.5, "api-reference": 0.5}, "3": {"docs": 0.5, "api-reference": 0.5}, "4": {"docs": 0.5, "app": 0.5}, "5": {"docs": 0.5, "app": 0.5}, "6": {"docs": 0.5, "app": 0.5}, "7": {"docs": 0.5, "app": 0.5}, "8": {"docs": 0.5, "app": 0.5}, "9": {"docs": 0.5, "app": 0.5}, "10": {"docs": 0.5, "app": 0.5}, "11": {"docs": 0.5, "app": 0.5}, "12": {"docs": 0.5, "app": 0.5}, "13": {"docs": 0.5, "app": 0.5}, "14": {"docs": 0.5, "app": 0.5}, "15": {"docs": 0.5, "app": 0.5}, "16": {"docs": 0.3333333333333333, "app": 0.3333333333333333, "quickstart": 0.3333333333333333}, "17": {"docs": 0.3333333333333333, "app": 0.3333333333333333, "quickstart": 0.3333333333333333}, "18": {"docs": 0.3333333333333333, "app": 0.3333333333333333, "quickstart": 0.3333333333333333}, "19": {"docs": 0.3333333333333333, "app": 0.3333333333333333, "quickstart": 0.3333333333333333}, "20": {"docs": 0.3333333333333333, "app": 0.3333333333333333, "quickstart": 0.3333333333333333}, "21": {"docs": 0.3333333333333333, "app": 0.3333333333333333, "quickstart": 0.3333333333333333}, "22": {"docs": 0.3333333333333333, "app": 0.3333333333333333, "quickstart": 0.3333333333333333}, "23": {"docs": 0.3333333333333333, "app": 0.3333333333333333, "quickstart": 0.3333333333333333}, "24": {"docs": 0.3333333333333333, "app": 0.3333333333333333, "quickstart": 0.3333333333333333}, "25": {"docs": 0.3333333333333333, "app": 0.3333333333333333, "quickstart": 0.3333333333333333}, "26": {"docs": 0.3333333333333333, "app": 0.3333333333333333, "quickstart": 0.3333333333333333}, "27": {"docs": 0.3333333333333333, "app": 0.3333333333333333, "quickstart": 0.3333333333333333}, "28": {"docs": 0.3333333333333333, "app": 0.3333333333333333, "quickstart": 0.3333333333333333}, "29": {"docs": 0.3333333333333333, "app": 0.3333333333333333, "quickstart": 0.3333333333333333}, "30": {"docs": 0.3333333333333333, "app": 0.3333333333333333, "quickstart": 0.3333333333333333}, "31": {"docs": 0.3333333333333333, "app": 0.3333333333333333, "quickstart": 0.3333333333333333}, "32": {"docs": 0.3333333333333333, "app": 0.3333333333333333, "quickstart": 0.3333333333333333}, "33": {"docs": 0.3333333333333333, "app": 0.3333333333333333, "quickstart": 0.3333333333333333}, "34": {"docs": 0.3333333333333333, "app": 0.3333333333333333, "quickstart": 0.3333333333333333}, "35": {"docs": 0.3333333333333333, "app": 0.3333333333333333, "quickstart": 0.3333333333333333}, "36": {"docs": 0.3333333333333333, "app": 0.3333333333333333, "quickstart": 0.3333333333333333}, "37": {"docs": 0.3333333333333333, "app": 0.3333333333333333, "quickstart": 0.3333333333333333}, "38": {"docs": 0.5, "changelog": 0.5}, "39": {"docs": 0.5, "changelog": 0.5}, "40": {"docs": 0.5, "changelog": 0.5}, "41": {"docs": 0.5, "changelog": 0.5}, "42": {"docs": 0.5, "changelog": 0.5}, "43": {"docs": 0.5, "changelog": 0.5}, "44": {"docs": 0.5, "changelog": 0.5}, "45": {"docs": 0.5, "changelog": 0.5}, "46": {"docs": 0.5, "changelog": 0.5}, "47": {"docs": 0.5, "changelog": 0.5}, "48": {"docs": 0.5, "changelog": 0.5}, "49": {"docs": 0.5, "changelog": 0.5}, "50": {"docs": 0.5, "changelog": 0.5}, "51": {"docs": 0.5, "changelog": 0.5}, "52": {"docs": 0.5, "changelog": 0.5}, "53": {"docs": 0.5, "changelog": 0.5}, "54": {"docs": 0.5, "changelog": 0.5}, "55": {"docs": 0.5, "changelog": 0.5}, "56": {"docs": 0.5, "changelog": 0.5}, "57": {"docs": 0.5, "changelog": 0.5}, "58": {"docs": 0.5, "changelog": 0.5}, "59": {"docs": 0.5, "changelog": 0.5}, "60": {"docs": 0.5, "changelog": 0.5}, "61": {"docs": 0.5, "changelog": 0.5}, "62": {"docs": 0.5, "changelog": 0.5}, "63": {"docs": 0.5, "changelog": 0.5}, "64": {"docs": 0.5, "changelog": 0.5}, "65": {"docs": 0.5, "changelog": 0.5}, "66": {"docs": 0.5, "changelog": 0.5}, "67": {"docs": 0.5, "changelog": 0.5}, "68": {"docs": 0.5, "changelog": 0.5}, "69": {"docs": 0.5, "changelog": 0.5}, "70": {"docs": 0.5, "changelog": 0.5}, "71": {"docs": 0.5, "changelog": 0.5}, "72": {"docs": 0.5, "changelog": 0.5}, "73": {"docs": 0.5, "changelog": 0.5}, "74": {"docs": 0.5, "changelog": 0.5}, "75": {"docs": 0.5, "changelog": 0.5}, "76": {"docs": 0.5, "changelog": 0.5}, "77": {"docs": 0.5, "changelog": 0.5}, "78": {"docs": 0.5, "changelog": 0.5}, "79": {"docs": 0.5, "changelog": 0.5}, "80": {"docs": 0.5, "changelog": 0.5}, "81": {"docs": 0.5, "changelog": 0.5}, "82": {"docs": 0.5, "changelog": 0.5}, "83": {"docs": 0.5, "changelog": 0.5}, "84": {"docs": 0.5, "changelog": 0.5}, "85": {"docs": 0.5, "changelog": 0.5}, "86": {"docs": 0.5, "changelog": 0.5}, "87": {"docs": 0.5, "changelog": 0.5}, "88": {"docs": 0.5, "changelog": 0.5}, "89": {"docs": 0.5, "changelog": 0.5}, "90": {"docs": 0.5, "changelog": 0.5}, "91": {"docs": 0.5, "changelog": 0.5}, "92": {"docs": 0.5, "changelog": 0.5}, "93": {"docs": 0.5, "changelog": 0.5}, "94": {"docs": 0.5, "changelog": 0.5}, "95": {"docs": 0.5, "changelog": 0.5}, "96": {"docs": 0.5, "changelog": 0.5}, "97": {"docs": 0.5, "changelog": 0.5}, "98": {"docs": 0.5, "changelog": 0.5}, "99": {"docs": 0.5, "changelog": 0.5}, "100": {"docs": 0.5, "changelog": 0.5}, "101": {"docs": 0.5, "changelog": 0.5}, "102": {"docs": 0.5, "changelog": 0.5}, "103": {"docs": 0.5, "changelog": 0.5}, "104": {"docs": 0.5, "changelog": 0.5}, "105": {"docs": 0.5, "changelog": 0.5}, "106": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "code": 0.25}, "107": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "code": 0.25}, "108": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "code": 0.25}, "109": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "code": 0.25}, "110": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "code": 0.25}, "111": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "code": 0.25}, "112": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "code": 0.25}, "113": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "code": 0.25}, "114": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "code": 0.25}, "115": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "code": 0.25}, "116": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "code": 0.25}, "117": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "code": 0.25}, "118": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "code": 0.25}, "119": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "code": 0.25}, "120": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "markdown": 0.25}, "121": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "markdown": 0.25}, "122": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "markdown": 0.25}, "123": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "markdown": 0.25}, "124": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "markdown": 0.25}, "125": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "markdown": 0.25}, "126": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "markdown": 0.25}, "127": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "markdown": 0.25}, "128": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "markdown": 0.25}, "129": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "markdown": 0.25}, "130": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "markdown": 0.25}, "131": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "markdown": 0.25}, "132": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "markdown": 0.25}, "133": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "markdown": 0.25}, "134": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "markdown": 0.25}, "135": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "markdown": 0.25}, "136": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "markdown": 0.25}, "137": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "markdown": 0.25}, "138": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "markdown": 0.25}, "139": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "markdown": 0.25}, "140": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "markdown": 0.25}, "141": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "markdown": 0.25}, "142": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "markdown": 0.25}, "143": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "markdown": 0.25}, "144": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "markdown": 0.25}, "145": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "markdown": 0.25}, "146": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "markdown": 0.25}, "147": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "markdown": 0.25}, "148": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "markdown": 0.25}, "149": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "markdown": 0.25}, "150": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "markdown": 0.25}, "151": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "markdown": 0.25}, "152": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "markdown": 0.25}, "153": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "markdown": 0.25}, "154": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "markdown": 0.25}, "155": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "markdown": 0.25}, "156": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "markdown": 0.25}, "157": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "markdown": 0.25}, "158": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "markdown": 0.25}, "159": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "markdown": 0.25}, "160": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "markdown": 0.25}, "161": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "markdown": 0.25}, "162": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "markdown": 0.25}, "163": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "markdown": 0.25}, "164": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "markdown": 0.25}, "165": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "markdown": 0.25}, "166": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "markdown": 0.25}, "167": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "markdown": 0.25}, "168": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "markdown": 0.25}, "169": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "markdown": 0.25}, "170": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "171": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "172": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "173": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "174": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "175": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "176": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "177": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "178": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "179": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "180": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "181": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "182": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "183": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "184": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "185": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "186": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "187": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "188": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "189": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "190": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "191": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "192": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "193": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "194": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "195": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "196": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "197": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "198": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "199": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "200": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "201": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "202": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "203": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "204": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "205": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "206": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "207": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "208": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "209": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "210": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "211": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "212": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "213": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "214": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "215": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "216": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "217": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "218": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "219": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "220": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "221": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "222": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "223": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "224": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "225": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "226": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "227": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "228": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "229": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "230": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "231": {"docs": 0.25, "app": 0.25, "features": 0.25, "ai-search": 0.25}, "232": {"docs": 0.25, "app": 0.25, "features": 0.25, "ai-search": 0.25}, "233": {"docs": 0.25, "app": 0.25, "features": 0.25, "ai-search": 0.25}, "234": {"docs": 0.25, "app": 0.25, "features": 0.25, "ai-search": 0.25}, "235": {"docs": 0.25, "app": 0.25, "features": 0.25, "ai-search": 0.25}, "236": {"docs": 0.25, "app": 0.25, "features": 0.25, "ai-search": 0.25}, "237": {"docs": 0.25, "app": 0.25, "features": 0.25, "ai-search": 0.25}, "238": {"docs": 0.25, "app": 0.25, "features": 0.25, "ai-search": 0.25}, "239": {"docs": 0.25, "app": 0.25, "features": 0.25, "ai-search": 0.25}, "240": {"docs": 0.25, "app": 0.25, "features": 0.25, "ai-search": 0.25}, "241": {"docs": 0.25, "app": 0.25, "features": 0.25, "ai-search": 0.25}, "242": {"docs": 0.25, "app": 0.25, "features": 0.25, "async-mode": 0.25}, "243": {"docs": 0.25, "app": 0.25, "features": 0.25, "async-mode": 0.25}, "244": {"docs": 0.25, "app": 0.25, "features": 0.25, "async-mode": 0.25}, "245": {"docs": 0.25, "app": 0.25, "features": 0.25, "async-mode": 0.25}, "246": {"docs": 0.25, "app": 0.25, "features": 0.25, "async-mode": 0.25}, "247": {"docs": 0.25, "app": 0.25, "features": 0.25, "async-mode": 0.25}, "248": {"docs": 0.25, "app": 0.25, "features": 0.25, "async-mode": 0.25}, "249": {"docs": 0.25, "app": 0.25, "features": 0.25, "async-mode": 0.25}, "250": {"docs": 0.25, "app": 0.25, "features": 0.25, "async-mode": 0.25}, "251": {"docs": 0.25, "app": 0.25, "features": 0.25, "llms": 0.25}, "252": {"docs": 0.25, "app": 0.25, "features": 0.25, "llms": 0.25}, "253": {"docs": 0.25, "app": 0.25, "features": 0.25, "llms": 0.25}, "254": {"docs": 0.25, "app": 0.25, "features": 0.25, "llms": 0.25}, "255": {"docs": 0.25, "app": 0.25, "features": 0.25, "llms": 0.25}, "256": {"docs": 0.25, "app": 0.25, "features": 0.25, "llms": 0.25}, "257": {"docs": 0.25, "app": 0.25, "features": 0.25, "llms": 0.25}, "258": {"docs": 0.25, "app": 0.25, "features": 0.25, "llms": 0.25}, "259": {"docs": 0.25, "app": 0.25, "features": 0.25, "llms": 0.25}, "260": {"docs": 0.25, "app": 0.25, "features": 0.25, "llms": 0.25}, "261": {"docs": 0.25, "app": 0.25, "features": 0.25, "llms": 0.25}, "262": {"docs": 0.25, "app": 0.25, "features": 0.25, "llms": 0.25}, "263": {"docs": 0.25, "app": 0.25, "features": 0.25, "llms": 0.25}, "264": {"docs": 0.25, "app": 0.25, "features": 0.25, "llms": 0.25}, "265": {"docs": 0.25, "app": 0.25, "features": 0.25, "llms": 0.25}, "266": {"docs": 0.25, "app": 0.25, "features": 0.25, "llms": 0.25}, "267": {"docs": 0.25, "app": 0.25, "features": 0.25, "llms": 0.25}, "268": {"docs": 0.25, "app": 0.25, "features": 0.25, "llms": 0.25}, "269": {"docs": 0.25, "app": 0.25, "features": 0.25, "llms": 0.25}, "270": {"docs": 0.25, "app": 0.25, "features": 0.25, "llms": 0.25}, "271": {"docs": 0.25, "app": 0.25, "features": 0.25, "llms": 0.25}, "272": {"docs": 0.25, "app": 0.25, "features": 0.25, "openapi": 0.25}, "273": {"docs": 0.25, "app": 0.25, "features": 0.25, "openapi": 0.25}, "274": {"docs": 0.25, "app": 0.25, "features": 0.25, "openapi": 0.25}, "275": {"docs": 0.25, "app": 0.25, "features": 0.25, "openapi": 0.25}, "276": {"docs": 0.25, "app": 0.25, "features": 0.25, "openapi": 0.25}, "277": {"docs": 0.25, "app": 0.25, "features": 0.25, "openapi": 0.25}, "278": {"docs": 0.25, "app": 0.25, "features": 0.25, "openapi": 0.25}, "279": {"docs": 0.25, "app": 0.25, "features": 0.25, "openapi": 0.25}, "280": {"docs": 0.25, "app": 0.25, "features": 0.25, "openapi": 0.25}, "281": {"docs": 0.25, "app": 0.25, "features": 0.25, "openapi": 0.25}, "282": {"docs": 0.25, "app": 0.25, "features": 0.25, "openapi": 0.25}, "283": {"docs": 0.25, "app": 0.25, "features": 0.25, "openapi": 0.25}, "284": {"docs": 0.25, "app": 0.25, "features": 0.25, "openapi": 0.25}, "285": {"docs": 0.25, "app": 0.25, "features": 0.25, "openapi": 0.25}, "286": {"docs": 0.25, "app": 0.25, "features": 0.25, "openapi": 0.25}, "287": {"docs": 0.25, "app": 0.25, "guides": 0.25, "adding-a-root-folder": 0.25}, "288": {"docs": 0.25, "app": 0.25, "guides": 0.25, "adding-a-root-folder": 0.25}, "289": {"docs": 0.25, "app": 0.25, "guides": 0.25, "adding-a-root-folder": 0.25}, "290": {"docs": 0.25, "app": 0.25, "guides": 0.25, "adding-a-root-folder": 0.25}, "291": {"docs": 0.25, "app": 0.25, "guides": 0.25, "adding-a-root-folder": 0.25}, "292": {"docs": 0.25, "app": 0.25, "guides": 0.25, "adding-a-root-folder": 0.25}, "293": {"docs": 0.25, "app": 0.25, "guides": 0.25, "adding-a-root-folder": 0.25}, "294": {"docs": 0.25, "app": 0.25, "guides": 0.25, "adding-a-root-folder": 0.25}, "295": {"docs": 0.25, "app": 0.25, "guides": 0.25, "adding-a-root-folder": 0.25}, "296": {"docs": 0.25, "app": 0.25, "guides": 0.25, "adding-a-root-folder": 0.25}, "297": {"docs": 0.25, "app": 0.25, "guides": 0.25, "adding-a-root-folder": 0.25}, "298": {"docs": 0.25, "app": 0.25, "guides": 0.25, "adding-a-root-folder": 0.25}, "299": {"docs": 0.25, "app": 0.25, "guides": 0.25, "adding-a-root-folder": 0.25}, "300": {"docs": 0.25, "app": 0.25, "guides": 0.25, "adding-a-root-folder": 0.25}, "301": {"docs": 0.25, "app": 0.25, "guides": 0.25, "adding-a-root-folder": 0.25}, "302": {"docs": 0.25, "app": 0.25, "guides": 0.25, "adding-a-root-folder": 0.25}, "303": {"docs": 0.25, "app": 0.25, "guides": 0.25, "adding-a-root-folder": 0.25}, "304": {"docs": 0.25, "app": 0.25, "guides": 0.25, "adding-a-root-folder": 0.25}, "305": {"docs": 0.25, "app": 0.25, "guides": 0.25, "adding-a-root-folder": 0.25}, "306": {"docs": 0.25, "app": 0.25, "guides": 0.25, "adding-a-root-folder": 0.25}, "307": {"docs": 0.25, "app": 0.25, "guides": 0.25, "adding-a-root-folder": 0.25}, "308": {"docs": 0.25, "app": 0.25, "guides": 0.25, "adding-a-root-folder": 0.25}, "309": {"docs": 0.25, "api-reference": 0.25, "events": 0.25, "createspecialevent": 0.25}, "310": {"docs": 0.25, "api-reference": 0.25, "events": 0.25, "createspecialevent": 0.25}, "311": {"docs": 0.25, "api-reference": 0.25, "events": 0.25, "createspecialevent": 0.25}, "312": {"docs": 0.25, "api-reference": 0.25, "events": 0.25, "deletespecialevent": 0.25}, "313": {"docs": 0.25, "api-reference": 0.25, "events": 0.25, "deletespecialevent": 0.25}, "314": {"docs": 0.25, "api-reference": 0.25, "events": 0.25, "deletespecialevent": 0.25}, "315": {"docs": 0.25, "api-reference": 0.25, "events": 0.25, "getspecialevent": 0.25}, "316": {"docs": 0.25, "api-reference": 0.25, "events": 0.25, "getspecialevent": 0.25}, "317": {"docs": 0.25, "api-reference": 0.25, "events": 0.25, "getspecialevent": 0.25}, "318": {"docs": 0.25, "api-reference": 0.25, "events": 0.25, "listspecialevents": 0.25}, "319": {"docs": 0.25, "api-reference": 0.25, "events": 0.25, "listspecialevents": 0.25}, "320": {"docs": 0.25, "api-reference": 0.25, "events": 0.25, "listspecialevents": 0.25}, "321": {"docs": 0.25, "api-reference": 0.25, "events": 0.25, "publishnewevent": 0.25}, "322": {"docs": 0.25, "api-reference": 0.25, "events": 0.25, "publishnewevent": 0.25}, "323": {"docs": 0.25, "api-reference": 0.25, "events": 0.25, "updatespecialevent": 0.25}, "324": {"docs": 0.25, "api-reference": 0.25, "events": 0.25, "updatespecialevent": 0.25}, "325": {"docs": 0.25, "api-reference": 0.25, "events": 0.25, "updatespecialevent": 0.25}, "326": {"docs": 0.25, "api-reference": 0.25, "operations": 0.25, "getmuseumhours": 0.25}, "327": {"docs": 0.25, "api-reference": 0.25, "operations": 0.25, "getmuseumhours": 0.25}, "328": {"docs": 0.25, "api-reference": 0.25, "operations": 0.25, "getmuseumhours": 0.25}, "329": {"docs": 0.25, "api-reference": 0.25, "tickets": 0.25, "buymuseumtickets": 0.25}, "330": {"docs": 0.25, "api-reference": 0.25, "tickets": 0.25, "buymuseumtickets": 0.25}, "331": {"docs": 0.25, "api-reference": 0.25, "tickets": 0.25, "buymuseumtickets": 0.25}, "332": {"docs": 0.25, "api-reference": 0.25, "tickets": 0.25, "getticketcode": 0.25}, "333": {"docs": 0.25, "api-reference": 0.25, "tickets": 0.25, "getticketcode": 0.25}, "334": {"docs": 0.25, "api-reference": 0.25, "tickets": 0.25, "getticketcode": 0.25}}, "type": {"1": {"page": 1}, "2": {"text": 1}, "3": {"text": 1}, "4": {"page": 1}, "5": {"text": 1}, "6": {"heading": 1}, "7": {"heading": 1}, "8": {"text": 1}, "9": {"text": 1}, "10": {"text": 1}, "11": {"text": 1}, "12": {"text": 1}, "13": {"text": 1}, "14": {"text": 1}, "15": {"text": 1}, "16": {"page": 1}, "17": {"text": 1}, "18": {"heading": 1}, "19": {"heading": 1}, "20": {"heading": 1}, "21": {"heading": 1}, "22": {"heading": 1}, "23": {"heading": 1}, "24": {"text": 1}, "25": {"text": 1}, "26": {"text": 1}, "27": {"text": 1}, "28": {"text": 1}, "29": {"text": 1}, "30": {"text": 1}, "31": {"text": 1}, "32": {"text": 1}, "33": {"text": 1}, "34": {"text": 1}, "35": {"text": 1}, "36": {"text": 1}, "37": {"text": 1}, "38": {"page": 1}, "39": {"text": 1}, "40": {"heading": 1}, "41": {"heading": 1}, "42": {"heading": 1}, "43": {"heading": 1}, "44": {"heading": 1}, "45": {"heading": 1}, "46": {"heading": 1}, "47": {"heading": 1}, "48": {"heading": 1}, "49": {"heading": 1}, "50": {"heading": 1}, "51": {"heading": 1}, "52": {"heading": 1}, "53": {"text": 1}, "54": {"text": 1}, "55": {"text": 1}, "56": {"text": 1}, "57": {"text": 1}, "58": {"text": 1}, "59": {"text": 1}, "60": {"text": 1}, "61": {"text": 1}, "62": {"text": 1}, "63": {"text": 1}, "64": {"text": 1}, "65": {"text": 1}, "66": {"text": 1}, "67": {"text": 1}, "68": {"text": 1}, "69": {"text": 1}, "70": {"text": 1}, "71": {"text": 1}, "72": {"text": 1}, "73": {"text": 1}, "74": {"text": 1}, "75": {"text": 1}, "76": {"text": 1}, "77": {"text": 1}, "78": {"text": 1}, "79": {"text": 1}, "80": {"text": 1}, "81": {"text": 1}, "82": {"text": 1}, "83": {"text": 1}, "84": {"text": 1}, "85": {"text": 1}, "86": {"text": 1}, "87": {"text": 1}, "88": {"text": 1}, "89": {"text": 1}, "90": {"text": 1}, "91": {"text": 1}, "92": {"text": 1}, "93": {"text": 1}, "94": {"text": 1}, "95": {"text": 1}, "96": {"text": 1}, "97": {"text": 1}, "98": {"text": 1}, "99": {"text": 1}, "100": {"text": 1}, "101": {"text": 1}, "102": {"text": 1}, "103": {"text": 1}, "104": {"text": 1}, "105": {"text": 1}, "106": {"page": 1}, "107": {"text": 1}, "108": {"heading": 1}, "109": {"heading": 1}, "110": {"heading": 1}, "111": {"heading": 1}, "112": {"heading": 1}, "113": {"heading": 1}, "114": {"heading": 1}, "115": {"text": 1}, "116": {"text": 1}, "117": {"text": 1}, "118": {"text": 1}, "119": {"text": 1}, "120": {"page": 1}, "121": {"text": 1}, "122": {"heading": 1}, "123": {"heading": 1}, "124": {"heading": 1}, "125": {"heading": 1}, "126": {"heading": 1}, "127": {"heading": 1}, "128": {"heading": 1}, "129": {"heading": 1}, "130": {"heading": 1}, "131": {"heading": 1}, "132": {"heading": 1}, "133": {"heading": 1}, "134": {"heading": 1}, "135": {"text": 1}, "136": {"text": 1}, "137": {"text": 1}, "138": {"text": 1}, "139": {"text": 1}, "140": {"text": 1}, "141": {"text": 1}, "142": {"text": 1}, "143": {"text": 1}, "144": {"text": 1}, "145": {"text": 1}, "146": {"text": 1}, "147": {"text": 1}, "148": {"text": 1}, "149": {"text": 1}, "150": {"text": 1}, "151": {"text": 1}, "152": {"text": 1}, "153": {"text": 1}, "154": {"text": 1}, "155": {"text": 1}, "156": {"text": 1}, "157": {"text": 1}, "158": {"text": 1}, "159": {"text": 1}, "160": {"text": 1}, "161": {"text": 1}, "162": {"text": 1}, "163": {"text": 1}, "164": {"text": 1}, "165": {"text": 1}, "166": {"text": 1}, "167": {"text": 1}, "168": {"text": 1}, "169": {"text": 1}, "170": {"page": 1}, "171": {"text": 1}, "172": {"heading": 1}, "173": {"heading": 1}, "174": {"heading": 1}, "175": {"heading": 1}, "176": {"heading": 1}, "177": {"heading": 1}, "178": {"heading": 1}, "179": {"heading": 1}, "180": {"heading": 1}, "181": {"heading": 1}, "182": {"heading": 1}, "183": {"heading": 1}, "184": {"text": 1}, "185": {"text": 1}, "186": {"text": 1}, "187": {"text": 1}, "188": {"text": 1}, "189": {"text": 1}, "190": {"text": 1}, "191": {"text": 1}, "192": {"text": 1}, "193": {"text": 1}, "194": {"text": 1}, "195": {"text": 1}, "196": {"text": 1}, "197": {"text": 1}, "198": {"text": 1}, "199": {"text": 1}, "200": {"text": 1}, "201": {"text": 1}, "202": {"text": 1}, "203": {"text": 1}, "204": {"text": 1}, "205": {"text": 1}, "206": {"text": 1}, "207": {"text": 1}, "208": {"text": 1}, "209": {"text": 1}, "210": {"text": 1}, "211": {"text": 1}, "212": {"text": 1}, "213": {"text": 1}, "214": {"text": 1}, "215": {"text": 1}, "216": {"text": 1}, "217": {"text": 1}, "218": {"text": 1}, "219": {"text": 1}, "220": {"text": 1}, "221": {"text": 1}, "222": {"text": 1}, "223": {"text": 1}, "224": {"text": 1}, "225": {"text": 1}, "226": {"text": 1}, "227": {"text": 1}, "228": {"text": 1}, "229": {"text": 1}, "230": {"text": 1}, "231": {"page": 1}, "232": {"text": 1}, "233": {"heading": 1}, "234": {"heading": 1}, "235": {"heading": 1}, "236": {"heading": 1}, "237": {"text": 1}, "238": {"text": 1}, "239": {"text": 1}, "240": {"text": 1}, "241": {"text": 1}, "242": {"page": 1}, "243": {"text": 1}, "244": {"heading": 1}, "245": {"heading": 1}, "246": {"text": 1}, "247": {"text": 1}, "248": {"text": 1}, "249": {"text": 1}, "250": {"text": 1}, "251": {"page": 1}, "252": {"text": 1}, "253": {"heading": 1}, "254": {"heading": 1}, "255": {"heading": 1}, "256": {"heading": 1}, "257": {"text": 1}, "258": {"text": 1}, "259": {"text": 1}, "260": {"text": 1}, "261": {"text": 1}, "262": {"text": 1}, "263": {"text": 1}, "264": {"text": 1}, "265": {"text": 1}, "266": {"text": 1}, "267": {"text": 1}, "268": {"text": 1}, "269": {"text": 1}, "270": {"text": 1}, "271": {"text": 1}, "272": {"page": 1}, "273": {"text": 1}, "274": {"heading": 1}, "275": {"heading": 1}, "276": {"heading": 1}, "277": {"text": 1}, "278": {"text": 1}, "279": {"text": 1}, "280": {"text": 1}, "281": {"text": 1}, "282": {"text": 1}, "283": {"text": 1}, "284": {"text": 1}, "285": {"text": 1}, "286": {"text": 1}, "287": {"page": 1}, "288": {"text": 1}, "289": {"heading": 1}, "290": {"heading": 1}, "291": {"heading": 1}, "292": {"heading": 1}, "293": {"heading": 1}, "294": {"heading": 1}, "295": {"heading": 1}, "296": {"heading": 1}, "297": {"text": 1}, "298": {"text": 1}, "299": {"text": 1}, "300": {"text": 1}, "301": {"text": 1}, "302": {"text": 1}, "303": {"text": 1}, "304": {"text": 1}, "305": {"text": 1}, "306": {"text": 1}, "307": {"text": 1}, "308": {"text": 1}, "309": {"page": 1}, "310": {"text": 1}, "311": {"text": 1}, "312": {"page": 1}, "313": {"text": 1}, "314": {"text": 1}, "315": {"page": 1}, "316": {"text": 1}, "317": {"text": 1}, "318": {"page": 1}, "319": {"text": 1}, "320": {"text": 1}, "321": {"page": 1}, "322": {"text": 1}, "323": {"page": 1}, "324": {"text": 1}, "325": {"text": 1}, "326": {"page": 1}, "327": {"text": 1}, "328": {"text": 1}, "329": {"page": 1}, "330": {"text": 1}, "331": {"text": 1}, "332": {"page": 1}, "333": {"text": 1}, "334": {"text": 1}}, "url": {"1": {"docs": 0.5, "api-reference": 0.5}, "2": {"docs": 0.5, "api-reference": 0.5}, "3": {"docs": 0.5, "api-reference": 0.5}, "4": {"docs": 0.5, "app": 0.5}, "5": {"docs": 0.5, "app": 0.5}, "6": {"docs": 0.3333333333333333, "app": 0.3333333333333333, "getting-started": 0.3333333333333333}, "7": {"docs": 0.3333333333333333, "app": 0.3333333333333333, "make-it-yours": 0.3333333333333333}, "8": {"docs": 0.3333333333333333, "app": 0.3333333333333333, "getting-started": 0.3333333333333333}, "9": {"docs": 0.3333333333333333, "app": 0.3333333333333333, "getting-started": 0.3333333333333333}, "10": {"docs": 0.3333333333333333, "app": 0.3333333333333333, "getting-started": 0.3333333333333333}, "11": {"docs": 0.3333333333333333, "app": 0.3333333333333333, "make-it-yours": 0.3333333333333333}, "12": {"docs": 0.3333333333333333, "app": 0.3333333333333333, "make-it-yours": 0.3333333333333333}, "13": {"docs": 0.3333333333333333, "app": 0.3333333333333333, "make-it-yours": 0.3333333333333333}, "14": {"docs": 0.3333333333333333, "app": 0.3333333333333333, "make-it-yours": 0.3333333333333333}, "15": {"docs": 0.3333333333333333, "app": 0.3333333333333333, "make-it-yours": 0.3333333333333333}, "16": {"docs": 0.3333333333333333, "app": 0.3333333333333333, "quickstart": 0.3333333333333333}, "17": {"docs": 0.3333333333333333, "app": 0.3333333333333333, "quickstart": 0.3333333333333333}, "18": {"docs": 0.25, "app": 0.25, "quickstart": 0.25, "setup-your-development-environment": 0.25}, "19": {"docs": 0.25, "app": 0.25, "quickstart": 0.25, "start-the-development-server": 0.25}, "20": {"docs": 0.25, "app": 0.25, "quickstart": 0.25, "add-your-content": 0.25}, "21": {"docs": 0.25, "app": 0.25, "quickstart": 0.25, "deploy-your-changes": 0.25}, "22": {"docs": 0.25, "app": 0.25, "quickstart": 0.25, "docker-deployment": 0.25}, "23": {"docs": 0.25, "app": 0.25, "quickstart": 0.25, "update-your-docs": 0.25}, "24": {"docs": 0.25, "app": 0.25, "quickstart": 0.25, "setup-your-development-environment": 0.25}, "25": {"docs": 0.25, "app": 0.25, "quickstart": 0.25, "setup-your-development-environment": 0.25}, "26": {"docs": 0.25, "app": 0.25, "quickstart": 0.25, "setup-your-development-environment": 0.25}, "27": {"docs": 0.25, "app": 0.25, "quickstart": 0.25, "start-the-development-server": 0.25}, "28": {"docs": 0.25, "app": 0.25, "quickstart": 0.25, "start-the-development-server": 0.25}, "29": {"docs": 0.25, "app": 0.25, "quickstart": 0.25, "add-your-content": 0.25}, "30": {"docs": 0.25, "app": 0.25, "quickstart": 0.25, "add-your-content": 0.25}, "31": {"docs": 0.25, "app": 0.25, "quickstart": 0.25, "add-your-content": 0.25}, "32": {"docs": 0.25, "app": 0.25, "quickstart": 0.25, "deploy-your-changes": 0.25}, "33": {"docs": 0.25, "app": 0.25, "quickstart": 0.25, "docker-deployment": 0.25}, "34": {"docs": 0.25, "app": 0.25, "quickstart": 0.25, "docker-deployment": 0.25}, "35": {"docs": 0.25, "app": 0.25, "quickstart": 0.25, "update-your-docs": 0.25}, "36": {"docs": 0.25, "app": 0.25, "quickstart": 0.25, "update-your-docs": 0.25}, "37": {"docs": 0.25, "app": 0.25, "quickstart": 0.25, "update-your-docs": 0.25}, "38": {"docs": 0.5, "changelog": 0.5}, "39": {"docs": 0.5, "changelog": 0.5}, "40": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "general-improvements": 0.3333333333333333}, "41": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "mobile-enhancements": 0.3333333333333333}, "42": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "integration-upgrades": 0.3333333333333333}, "43": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "analytics--reporting": 0.3333333333333333}, "44": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "ux--accessibility": 0.3333333333333333}, "45": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "year-end-stability-release": 0.3333333333333333}, "46": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "feature-updates": 0.3333333333333333}, "47": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "user-management": 0.3333333333333333}, "48": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "productivity-tools": 0.3333333333333333}, "49": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "search--navigation": 0.3333333333333333}, "50": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "security--compliance": 0.3333333333333333}, "51": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "performance--quality-updates": 0.3333333333333333}, "52": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "new-integrations": 0.3333333333333333}, "53": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "general-improvements": 0.3333333333333333}, "54": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "general-improvements": 0.3333333333333333}, "55": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "general-improvements": 0.3333333333333333}, "56": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "general-improvements": 0.3333333333333333}, "57": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "mobile-enhancements": 0.3333333333333333}, "58": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "mobile-enhancements": 0.3333333333333333}, "59": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "mobile-enhancements": 0.3333333333333333}, "60": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "mobile-enhancements": 0.3333333333333333}, "61": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "integration-upgrades": 0.3333333333333333}, "62": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "integration-upgrades": 0.3333333333333333}, "63": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "integration-upgrades": 0.3333333333333333}, "64": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "integration-upgrades": 0.3333333333333333}, "65": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "analytics--reporting": 0.3333333333333333}, "66": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "analytics--reporting": 0.3333333333333333}, "67": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "analytics--reporting": 0.3333333333333333}, "68": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "analytics--reporting": 0.3333333333333333}, "69": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "ux--accessibility": 0.3333333333333333}, "70": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "ux--accessibility": 0.3333333333333333}, "71": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "ux--accessibility": 0.3333333333333333}, "72": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "ux--accessibility": 0.3333333333333333}, "73": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "year-end-stability-release": 0.3333333333333333}, "74": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "year-end-stability-release": 0.3333333333333333}, "75": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "year-end-stability-release": 0.3333333333333333}, "76": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "year-end-stability-release": 0.3333333333333333}, "77": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "feature-updates": 0.3333333333333333}, "78": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "feature-updates": 0.3333333333333333}, "79": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "feature-updates": 0.3333333333333333}, "80": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "feature-updates": 0.3333333333333333}, "81": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "user-management": 0.3333333333333333}, "82": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "user-management": 0.3333333333333333}, "83": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "user-management": 0.3333333333333333}, "84": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "user-management": 0.3333333333333333}, "85": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "productivity-tools": 0.3333333333333333}, "86": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "productivity-tools": 0.3333333333333333}, "87": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "productivity-tools": 0.3333333333333333}, "88": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "productivity-tools": 0.3333333333333333}, "89": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "search--navigation": 0.3333333333333333}, "90": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "search--navigation": 0.3333333333333333}, "91": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "search--navigation": 0.3333333333333333}, "92": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "search--navigation": 0.3333333333333333}, "93": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "security--compliance": 0.3333333333333333}, "94": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "security--compliance": 0.3333333333333333}, "95": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "security--compliance": 0.3333333333333333}, "96": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "security--compliance": 0.3333333333333333}, "97": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "performance--quality-updates": 0.3333333333333333}, "98": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "performance--quality-updates": 0.3333333333333333}, "99": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "performance--quality-updates": 0.3333333333333333}, "100": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "performance--quality-updates": 0.3333333333333333}, "101": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "performance--quality-updates": 0.3333333333333333}, "102": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "new-integrations": 0.3333333333333333}, "103": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "new-integrations": 0.3333333333333333}, "104": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "new-integrations": 0.3333333333333333}, "105": {"docs": 0.3333333333333333, "changelog": 0.3333333333333333, "new-integrations": 0.3333333333333333}, "106": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "code": 0.25}, "107": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "code": 0.25}, "108": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "code": 0.2, "basic": 0.2}, "109": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "code": 0.2, "inline-code": 0.2}, "110": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "code": 0.2, "code-block": 0.2}, "111": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "code": 0.2, "advanced": 0.2}, "112": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "code": 0.2, "package-install": 0.2}, "113": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "code": 0.2, "shiki-transformers": 0.2}, "114": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "code": 0.2, "twoslash-notations": 0.2}, "115": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "code": 0.2, "inline-code": 0.2}, "116": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "code": 0.2, "code-block": 0.2}, "117": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "code": 0.2, "package-install": 0.2}, "118": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "code": 0.2, "shiki-transformers": 0.2}, "119": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "code": 0.2, "twoslash-notations": 0.2}, "120": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "markdown": 0.25}, "121": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "markdown": 0.25}, "122": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "markdown": 0.2, "introduction": 0.2}, "123": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "markdown": 0.2, "mdx": 0.2}, "124": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "markdown": 0.2, "images": 0.2}, "125": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "markdown": 0.2, "auto-links": 0.2}, "126": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "markdown": 0.2, "cards": 0.2}, "127": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "markdown": 0.2, "callouts": 0.2}, "128": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "markdown": 0.2, "headings": 0.2}, "129": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "markdown": 0.2, "toc-settings": 0.2}, "130": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "markdown": 0.2, "custom-anchor": 0.2}, "131": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "markdown": 0.2, "tab-groups": 0.2}, "132": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "markdown": 0.2, "include": 0.2}, "133": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "markdown": 0.2, "mermaid": 0.2}, "134": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "markdown": 0.2, "latex": 0.2}, "135": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "markdown": 0.2, "introduction": 0.2}, "136": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "markdown": 0.2, "introduction": 0.2}, "137": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "markdown": 0.2, "mdx": 0.2}, "138": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "markdown": 0.2, "mdx": 0.2}, "139": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "markdown": 0.2, "mdx": 0.2}, "140": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "markdown": 0.2, "mdx": 0.2}, "141": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "markdown": 0.2, "images": 0.2}, "142": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "markdown": 0.2, "auto-links": 0.2}, "143": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "markdown": 0.2, "auto-links": 0.2}, "144": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "markdown": 0.2, "cards": 0.2}, "145": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "markdown": 0.2, "cards": 0.2}, "146": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "markdown": 0.2, "cards": 0.2}, "147": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "markdown": 0.2, "callouts": 0.2}, "148": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "markdown": 0.2, "callouts": 0.2}, "149": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "markdown": 0.2, "callouts": 0.2}, "150": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "markdown": 0.2, "callouts": 0.2}, "151": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "markdown": 0.2, "callouts": 0.2}, "152": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "markdown": 0.2, "callouts": 0.2}, "153": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "markdown": 0.2, "callouts": 0.2}, "154": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "markdown": 0.2, "callouts": 0.2}, "155": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "markdown": 0.2, "headings": 0.2}, "156": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "markdown": 0.2, "toc-settings": 0.2}, "157": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "markdown": 0.2, "custom-anchor": 0.2}, "158": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "markdown": 0.2, "custom-anchor": 0.2}, "159": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "markdown": 0.2, "custom-anchor": 0.2}, "160": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "markdown": 0.2, "tab-groups": 0.2}, "161": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "markdown": 0.2, "tab-groups": 0.2}, "162": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "markdown": 0.2, "include": 0.2}, "163": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "markdown": 0.2, "include": 0.2}, "164": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "markdown": 0.2, "mermaid": 0.2}, "165": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "markdown": 0.2, "latex": 0.2}, "166": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "markdown": 0.2, "latex": 0.2}, "167": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "markdown": 0.2, "latex": 0.2}, "168": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "markdown": 0.2, "latex": 0.2}, "169": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "markdown": 0.2, "latex": 0.2}, "170": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "171": {"docs": 0.25, "app": 0.25, "essentials": 0.25, "routing": 0.25}, "172": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "routing": 0.2, "introduction": 0.2}, "173": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "routing": 0.2, "file": 0.2}, "174": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "routing": 0.2, "slugs": 0.2}, "175": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "routing": 0.2, "folder": 0.2}, "176": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "routing": 0.2, "folder-group": 0.2}, "177": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "routing": 0.2, "meta": 0.2}, "178": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "routing": 0.2, "pages": 0.2}, "179": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "routing": 0.2, "rest": 0.2}, "180": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "routing": 0.2, "extract": 0.2}, "181": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "routing": 0.2, "link": 0.2}, "182": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "routing": 0.2, "icons": 0.2}, "183": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "routing": 0.2, "root-folder": 0.2}, "184": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "routing": 0.2, "introduction": 0.2}, "185": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "routing": 0.2, "file": 0.2}, "186": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "routing": 0.2, "file": 0.2}, "187": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "routing": 0.2, "file": 0.2}, "188": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "routing": 0.2, "file": 0.2}, "189": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "routing": 0.2, "file": 0.2}, "190": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "routing": 0.2, "file": 0.2}, "191": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "routing": 0.2, "file": 0.2}, "192": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "routing": 0.2, "file": 0.2}, "193": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "routing": 0.2, "file": 0.2}, "194": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "routing": 0.2, "file": 0.2}, "195": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "routing": 0.2, "file": 0.2}, "196": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "routing": 0.2, "slugs": 0.2}, "197": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "routing": 0.2, "slugs": 0.2}, "198": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "routing": 0.2, "slugs": 0.2}, "199": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "routing": 0.2, "slugs": 0.2}, "200": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "routing": 0.2, "slugs": 0.2}, "201": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "routing": 0.2, "slugs": 0.2}, "202": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "routing": 0.2, "slugs": 0.2}, "203": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "routing": 0.2, "folder": 0.2}, "204": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "routing": 0.2, "folder-group": 0.2}, "205": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "routing": 0.2, "folder-group": 0.2}, "206": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "routing": 0.2, "folder-group": 0.2}, "207": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "routing": 0.2, "folder-group": 0.2}, "208": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "routing": 0.2, "folder-group": 0.2}, "209": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "routing": 0.2, "meta": 0.2}, "210": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "routing": 0.2, "meta": 0.2}, "211": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "routing": 0.2, "meta": 0.2}, "212": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "routing": 0.2, "meta": 0.2}, "213": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "routing": 0.2, "meta": 0.2}, "214": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "routing": 0.2, "meta": 0.2}, "215": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "routing": 0.2, "meta": 0.2}, "216": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "routing": 0.2, "meta": 0.2}, "217": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "routing": 0.2, "meta": 0.2}, "218": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "routing": 0.2, "meta": 0.2}, "219": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "routing": 0.2, "meta": 0.2}, "220": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "routing": 0.2, "pages": 0.2}, "221": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "routing": 0.2, "pages": 0.2}, "222": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "routing": 0.2, "rest": 0.2}, "223": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "routing": 0.2, "rest": 0.2}, "224": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "routing": 0.2, "extract": 0.2}, "225": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "routing": 0.2, "link": 0.2}, "226": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "routing": 0.2, "icons": 0.2}, "227": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "routing": 0.2, "root-folder": 0.2}, "228": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "routing": 0.2, "root-folder": 0.2}, "229": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "routing": 0.2, "root-folder": 0.2}, "230": {"docs": 0.2, "app": 0.2, "essentials": 0.2, "routing": 0.2, "root-folder": 0.2}, "231": {"docs": 0.25, "app": 0.25, "features": 0.25, "ai-search": 0.25}, "232": {"docs": 0.25, "app": 0.25, "features": 0.25, "ai-search": 0.25}, "233": {"docs": 0.2, "app": 0.2, "features": 0.2, "ai-search": 0.2, "introduction": 0.2}, "234": {"docs": 0.2, "app": 0.2, "features": 0.2, "ai-search": 0.2, "setting-up-the-api-key": 0.2}, "235": {"docs": 0.2, "app": 0.2, "features": 0.2, "ai-search": 0.2, "application-configuration": 0.2}, "236": {"docs": 0.2, "app": 0.2, "features": 0.2, "ai-search": 0.2, "how-queries-are-handled": 0.2}, "237": {"docs": 0.2, "app": 0.2, "features": 0.2, "ai-search": 0.2, "introduction": 0.2}, "238": {"docs": 0.2, "app": 0.2, "features": 0.2, "ai-search": 0.2, "setting-up-the-api-key": 0.2}, "239": {"docs": 0.2, "app": 0.2, "features": 0.2, "ai-search": 0.2, "application-configuration": 0.2}, "240": {"docs": 0.2, "app": 0.2, "features": 0.2, "ai-search": 0.2, "how-queries-are-handled": 0.2}, "241": {"docs": 0.2, "app": 0.2, "features": 0.2, "ai-search": 0.2, "how-queries-are-handled": 0.2}, "242": {"docs": 0.25, "app": 0.25, "features": 0.25, "async-mode": 0.25}, "243": {"docs": 0.25, "app": 0.25, "features": 0.25, "async-mode": 0.25}, "244": {"docs": 0.2, "app": 0.2, "features": 0.2, "async-mode": 0.2, "introduction": 0.2}, "245": {"docs": 0.2, "app": 0.2, "features": 0.2, "async-mode": 0.2, "constraints": 0.2}, "246": {"docs": 0.2, "app": 0.2, "features": 0.2, "async-mode": 0.2, "introduction": 0.2}, "247": {"docs": 0.2, "app": 0.2, "features": 0.2, "async-mode": 0.2, "introduction": 0.2}, "248": {"docs": 0.2, "app": 0.2, "features": 0.2, "async-mode": 0.2, "constraints": 0.2}, "249": {"docs": 0.2, "app": 0.2, "features": 0.2, "async-mode": 0.2, "constraints": 0.2}, "250": {"docs": 0.2, "app": 0.2, "features": 0.2, "async-mode": 0.2, "constraints": 0.2}, "251": {"docs": 0.25, "app": 0.25, "features": 0.25, "llms": 0.25}, "252": {"docs": 0.25, "app": 0.25, "features": 0.25, "llms": 0.25}, "253": {"docs": 0.2, "app": 0.2, "features": 0.2, "llms": 0.2, "introduction": 0.2}, "254": {"docs": 0.2, "app": 0.2, "features": 0.2, "llms": 0.2, "llmstxt": 0.2}, "255": {"docs": 0.2, "app": 0.2, "features": 0.2, "llms": 0.2, "llms-fulltxt": 0.2}, "256": {"docs": 0.2, "app": 0.2, "features": 0.2, "llms": 0.2, "llmsmdx": 0.2}, "257": {"docs": 0.2, "app": 0.2, "features": 0.2, "llms": 0.2, "introduction": 0.2}, "258": {"docs": 0.2, "app": 0.2, "features": 0.2, "llms": 0.2, "introduction": 0.2}, "259": {"docs": 0.2, "app": 0.2, "features": 0.2, "llms": 0.2, "introduction": 0.2}, "260": {"docs": 0.2, "app": 0.2, "features": 0.2, "llms": 0.2, "introduction": 0.2}, "261": {"docs": 0.2, "app": 0.2, "features": 0.2, "llms": 0.2, "llmstxt": 0.2}, "262": {"docs": 0.2, "app": 0.2, "features": 0.2, "llms": 0.2, "llmstxt": 0.2}, "263": {"docs": 0.2, "app": 0.2, "features": 0.2, "llms": 0.2, "llmstxt": 0.2}, "264": {"docs": 0.2, "app": 0.2, "features": 0.2, "llms": 0.2, "llmstxt": 0.2}, "265": {"docs": 0.2, "app": 0.2, "features": 0.2, "llms": 0.2, "llms-fulltxt": 0.2}, "266": {"docs": 0.2, "app": 0.2, "features": 0.2, "llms": 0.2, "llms-fulltxt": 0.2}, "267": {"docs": 0.2, "app": 0.2, "features": 0.2, "llms": 0.2, "llms-fulltxt": 0.2}, "268": {"docs": 0.2, "app": 0.2, "features": 0.2, "llms": 0.2, "llms-fulltxt": 0.2}, "269": {"docs": 0.2, "app": 0.2, "features": 0.2, "llms": 0.2, "llmsmdx": 0.2}, "270": {"docs": 0.2, "app": 0.2, "features": 0.2, "llms": 0.2, "llmsmdx": 0.2}, "271": {"docs": 0.2, "app": 0.2, "features": 0.2, "llms": 0.2, "llmsmdx": 0.2}, "272": {"docs": 0.25, "app": 0.25, "features": 0.25, "openapi": 0.25}, "273": {"docs": 0.25, "app": 0.25, "features": 0.25, "openapi": 0.25}, "274": {"docs": 0.2, "app": 0.2, "features": 0.2, "openapi": 0.2, "introduction": 0.2}, "275": {"docs": 0.25, "app": 0.25, "features": 0.25, "openapi": 0.25}, "276": {"docs": 0.2, "app": 0.2, "features": 0.2, "openapi": 0.2, "changing-the-openapi-schema": 0.2}, "277": {"docs": 0.2, "app": 0.2, "features": 0.2, "openapi": 0.2, "introduction": 0.2}, "278": {"docs": 0.25, "app": 0.25, "features": 0.25, "openapi": 0.25}, "279": {"docs": 0.25, "app": 0.25, "features": 0.25, "openapi": 0.25}, "280": {"docs": 0.25, "app": 0.25, "features": 0.25, "openapi": 0.25}, "281": {"docs": 0.25, "app": 0.25, "features": 0.25, "openapi": 0.25}, "282": {"docs": 0.25, "app": 0.25, "features": 0.25, "openapi": 0.25}, "283": {"docs": 0.25, "app": 0.25, "features": 0.25, "openapi": 0.25}, "284": {"docs": 0.2, "app": 0.2, "features": 0.2, "openapi": 0.2, "changing-the-openapi-schema": 0.2}, "285": {"docs": 0.2, "app": 0.2, "features": 0.2, "openapi": 0.2, "changing-the-openapi-schema": 0.2}, "286": {"docs": 0.2, "app": 0.2, "features": 0.2, "openapi": 0.2, "changing-the-openapi-schema": 0.2}, "287": {"docs": 0.25, "app": 0.25, "guides": 0.25, "adding-a-root-folder": 0.25}, "288": {"docs": 0.25, "app": 0.25, "guides": 0.25, "adding-a-root-folder": 0.25}, "289": {"docs": 0.2, "app": 0.2, "guides": 0.2, "adding-a-root-folder": 0.2, "introduction": 0.2}, "290": {"docs": 0.2, "app": 0.2, "guides": 0.2, "adding-a-root-folder": 0.2, "steps-to-add-a-new-root-folder": 0.2}, "291": {"docs": 0.2, "app": 0.2, "guides": 0.2, "adding-a-root-folder": 0.2, "step-1-create-a-new-folder": 0.2}, "292": {"docs": 0.2, "app": 0.2, "guides": 0.2, "adding-a-root-folder": 0.2, "step-2-update-the-metajson-file": 0.2}, "293": {"docs": 0.2, "app": 0.2, "guides": 0.2, "adding-a-root-folder": 0.2, "step-3-update-the-homepage": 0.2}, "294": {"docs": 0.2, "app": 0.2, "guides": 0.2, "adding-a-root-folder": 0.2, "step-4-update-colors-optional": 0.2}, "295": {"docs": 0.2, "app": 0.2, "guides": 0.2, "adding-a-root-folder": 0.2, "step-5-create-metajson-in-the-new-folder": 0.2}, "296": {"docs": 0.2, "app": 0.2, "guides": 0.2, "adding-a-root-folder": 0.2, "step-6-create-a-new-page": 0.2}, "297": {"docs": 0.2, "app": 0.2, "guides": 0.2, "adding-a-root-folder": 0.2, "introduction": 0.2}, "298": {"docs": 0.2, "app": 0.2, "guides": 0.2, "adding-a-root-folder": 0.2, "step-1-create-a-new-folder": 0.2}, "299": {"docs": 0.2, "app": 0.2, "guides": 0.2, "adding-a-root-folder": 0.2, "step-2-update-the-metajson-file": 0.2}, "300": {"docs": 0.2, "app": 0.2, "guides": 0.2, "adding-a-root-folder": 0.2, "step-3-update-the-homepage": 0.2}, "301": {"docs": 0.2, "app": 0.2, "guides": 0.2, "adding-a-root-folder": 0.2, "step-4-update-colors-optional": 0.2}, "302": {"docs": 0.2, "app": 0.2, "guides": 0.2, "adding-a-root-folder": 0.2, "step-4-update-colors-optional": 0.2}, "303": {"docs": 0.2, "app": 0.2, "guides": 0.2, "adding-a-root-folder": 0.2, "step-5-create-metajson-in-the-new-folder": 0.2}, "304": {"docs": 0.2, "app": 0.2, "guides": 0.2, "adding-a-root-folder": 0.2, "step-5-create-metajson-in-the-new-folder": 0.2}, "305": {"docs": 0.2, "app": 0.2, "guides": 0.2, "adding-a-root-folder": 0.2, "step-5-create-metajson-in-the-new-folder": 0.2}, "306": {"docs": 0.2, "app": 0.2, "guides": 0.2, "adding-a-root-folder": 0.2, "step-6-create-a-new-page": 0.2}, "307": {"docs": 0.2, "app": 0.2, "guides": 0.2, "adding-a-root-folder": 0.2, "step-6-create-a-new-page": 0.2}, "308": {"docs": 0.2, "app": 0.2, "guides": 0.2, "adding-a-root-folder": 0.2, "step-6-create-a-new-page": 0.2}, "309": {"docs": 0.25, "api-reference": 0.25, "events": 0.25, "createspecialevent": 0.25}, "310": {"docs": 0.25, "api-reference": 0.25, "events": 0.25, "createspecialevent": 0.25}, "311": {"docs": 0.25, "api-reference": 0.25, "events": 0.25, "createspecialevent": 0.25}, "312": {"docs": 0.25, "api-reference": 0.25, "events": 0.25, "deletespecialevent": 0.25}, "313": {"docs": 0.25, "api-reference": 0.25, "events": 0.25, "deletespecialevent": 0.25}, "314": {"docs": 0.25, "api-reference": 0.25, "events": 0.25, "deletespecialevent": 0.25}, "315": {"docs": 0.25, "api-reference": 0.25, "events": 0.25, "getspecialevent": 0.25}, "316": {"docs": 0.25, "api-reference": 0.25, "events": 0.25, "getspecialevent": 0.25}, "317": {"docs": 0.25, "api-reference": 0.25, "events": 0.25, "getspecialevent": 0.25}, "318": {"docs": 0.25, "api-reference": 0.25, "events": 0.25, "listspecialevents": 0.25}, "319": {"docs": 0.25, "api-reference": 0.25, "events": 0.25, "listspecialevents": 0.25}, "320": {"docs": 0.25, "api-reference": 0.25, "events": 0.25, "listspecialevents": 0.25}, "321": {"docs": 0.25, "api-reference": 0.25, "events": 0.25, "publishnewevent": 0.25}, "322": {"docs": 0.25, "api-reference": 0.25, "events": 0.25, "publishnewevent": 0.25}, "323": {"docs": 0.25, "api-reference": 0.25, "events": 0.25, "updatespecialevent": 0.25}, "324": {"docs": 0.25, "api-reference": 0.25, "events": 0.25, "updatespecialevent": 0.25}, "325": {"docs": 0.25, "api-reference": 0.25, "events": 0.25, "updatespecialevent": 0.25}, "326": {"docs": 0.25, "api-reference": 0.25, "operations": 0.25, "getmuseumhours": 0.25}, "327": {"docs": 0.25, "api-reference": 0.25, "operations": 0.25, "getmuseumhours": 0.25}, "328": {"docs": 0.25, "api-reference": 0.25, "operations": 0.25, "getmuseumhours": 0.25}, "329": {"docs": 0.25, "api-reference": 0.25, "tickets": 0.25, "buymuseumtickets": 0.25}, "330": {"docs": 0.25, "api-reference": 0.25, "tickets": 0.25, "buymuseumtickets": 0.25}, "331": {"docs": 0.25, "api-reference": 0.25, "tickets": 0.25, "buymuseumtickets": 0.25}, "332": {"docs": 0.25, "api-reference": 0.25, "tickets": 0.25, "getticketcode": 0.25}, "333": {"docs": 0.25, "api-reference": 0.25, "tickets": 0.25, "getticketcode": 0.25}, "334": {"docs": 0.25, "api-reference": 0.25, "tickets": 0.25, "getticketcode": 0.25}}}, "tokenOccurrences": {"content": {"0": 1, "1": 3, "2": 2, "3": 2, "4": 1, "5": 2, "6": 1, "15": 1, "18": 1, "23": 1, "25": 1, "40": 1, "3000": 1, "introduction": 10, "this": 13, "is": 15, "a": 53, "page": 16, "to": 65, "check": 2, "fumadocs's": 1, "openapi": 10, "example": 7, "welcome": 2, "the": 77, "you": 35, "can": 33, "update": 9, "in": 42, "'openapi": 1, "yml'": 1, "file": 23, "your": 46, "new": 27, "documentation": 17, "getting": 1, "started": 1, "make": 2, "it": 14, "yours": 1, "first": 2, "step": 8, "creating": 2, "amazing": 1, "setting": 2, "up": 6, "editing": 1, "environment": 2, "learn": 6, "how": 4, "set": 1, "docs": 17, "for": 47, "easy": 2, "local": 1, "development": 6, "structure": 3, "mdx": 29, "files": 10, "and": 50, "folders": 6, "define": 2, "sidebar": 6, "layout": 1, "fumadocs": 16, "customize": 7, "reflect": 1, "brand": 2, "include": 8, "meaningful": 1, "content": 21, "maximize": 1, "user": 10, "engagement": 2, "conversions": 1, "by": 20, "applying": 1, "colors": 3, "styles": 3, "auto-generate": 1, "interactive": 3, "endpoint": 2, "straight": 1, "from": 10, "spec": 1, "embed": 1, "elements": 3, "guides": 1, "improve": 2, "browse": 1, "our": 1, "showcase": 1, "creative": 1, "ideas": 1, "best": 1, "practices": 1, "quickstart": 1, "start": 3, "building": 1, "awesome": 1, "under": 1, "minutes": 1, "setup": 2, "server": 3, "add": 20, "deploy": 3, "changes": 1, "docker": 2, "deployment": 1, "minimum": 2, "version": 1, "of": 33, "node": 1, "js": 5, "required": 1, "note": 2, "that": 2, "might": 1, "have": 2, "issues": 1, "with": 22, "next": 6, "production": 1, "build": 2, "npx": 1, "create-next-app": 1, "-e": 1, "https": 2, "github": 2, "com": 1, "techwithanirudh": 1, "fumadocs-starternpx": 1, "fumadocs-starter": 1, "--use-pnpmnpx": 1, "--use-yarnnpx": 1, "--use-bun": 1, "template": 5, "should": 1, "now": 17, "be": 9, "initialized": 1, "run": 2, "app": 6, "mode": 6, "open": 4, "http": 1, "localhost": 1, "browser": 1, "npm": 2, "devpnpm": 1, "devyarn": 1, "devbun": 1, "dev": 1, "uses": 5, "allowing": 3, "write": 2, "markdown": 13, "combined": 1, "react": 2, "components": 7, "inside": 4, "directory": 6, "similar": 1, "standard": 2, "making": 2, "navigate": 3, "manage": 1, "more": 6, "about": 5, "out": 1, "create": 12, "folder": 28, "organizes": 1, "concerns": 1, "e": 4, "g": 4, "api-reference": 2, "changelog": 1, "etc": 1, "works": 1, "out-of-the-box": 1, "vercel": 1, "or": 15, "netlify": 1, "site": 3, "single": 2, "click": 1, "if": 4, "want": 1, "using": 6, "configured": 1, "sure": 1, "source": 1, "config": 1, "ts": 1, "workdir": 1, "dockerfile": 1, "following": 2, "snippet": 2, "taken": 1, "official": 3, "ensures": 1, "access": 3, "configuration": 2, "during": 3, "builds": 1, "directly": 2, "syntax": 9, "use": 9, "built-in": 2, "own": 1, "code": 14, "highlighting": 2, "product": 1, "updates": 6, "improvements": 2, "general": 3, "mobile": 1, "enhancements": 1, "integration": 7, "upgrades": 1, "analytics": 1, "reporting": 1, "ux": 1, "accessibility": 1, "year-end": 1, "stability": 1, "release": 2, "feature": 2, "management": 1, "productivity": 1, "tools": 1, "search": 9, "navigation": 5, "security": 2, "compliance": 2, "performance": 4, "quality": 1, "integrations": 1, "redesigned": 3, "notification": 2, "settings": 4, "better": 2, "control": 2, "over": 1, "email": 1, "in-app": 1, "alerts": 2, "fixed": 4, "issue": 2, "where": 2, "some": 3, "users": 6, "were": 2, "not": 4, "receiving": 1, "2fa": 1, "codes": 1, "via": 2, "sms": 1, "activity": 2, "log": 1, "includes": 3, "granular": 1, "details": 6, "api": 9, "usage": 2, "added": 12, "keyboard": 1, "shortcuts": 1, "dashboard": 3, "quicker": 1, "major": 1, "optimizations": 1, "older": 1, "android": 1, "devices": 1, "gesture": 1, "support": 7, "calendar": 2, "rescheduling": 1, "resolved": 1, "an": 9, "causing": 1, "data": 2, "sync": 3, "delays": 1, "when": 5, "switching": 1, "between": 3, "networks": 1, "push": 1, "reliability": 1, "improved": 7, "background": 2, "microsoft": 2, "teams": 1, "supports": 6, "deep-linking": 1, "projects": 2, "webhook": 1, "event": 16, "task": 3, "reassignment": 1, "jira": 1, "sprint": 1, "story": 1, "point": 1, "mapping": 1, "zapier": 1, "triggers": 1, "completed": 2, "tasks": 4, "uploads": 1, "custom": 4, "reports": 1, "scheduled": 1, "emailed": 1, "automatically": 5, "drill-down": 1, "charts": 1, "status": 1, "team": 1, "time": 4, "tracking": 1, "summary": 2, "project": 3, "export": 4, "options": 1, "pdf": 1, "format": 2, "styling": 3, "collapsible": 1, "sections": 2, "cleaner": 1, "screen": 1, "reader": 1, "form": 1, "fields": 1, "buttons": 1, "inconsistent": 1, "behavior": 1, "dropdowns": 1, "safari": 1, "color": 2, "contrast": 1, "visibility": 1, "dark": 1, "edge-case": 1, "bugs": 1, "reported": 1, "november": 1, "reduced": 3, "response": 3, "latency": 1, "peak": 1, "hours": 4, "backend": 1, "job": 1, "processing": 1, "has": 1, "retry": 2, "logic": 3, "alerting": 1, "on": 8, "failure": 1, "cleaned": 1, "deprecated": 1, "endpoints": 3, "refer": 1, "migration": 1, "guide": 2, "needed": 1, "smart": 1, "labels": 1, "categorize": 1, "based": 3, "batch": 1, "actions": 1, "checklists": 1, "subtasks": 1, "admins": 1, "view": 2, "login": 3, "history": 1, "per": 1, "kanban": 1, "board": 1, "remembers": 1, "last": 1, "viewed": 2, "filters": 2, "column": 1, "order": 4, "invite": 1, "flow": 2, "allow": 2, "bulk": 2, "imports": 1, "csv": 2, "deactivation": 1, "audit": 1, "events": 11, "request": 3, "private": 1, "pending": 2, "approval": 1, "sso": 1, "token": 1, "reuse": 1, "introduced": 1, "focus": 1, "hides": 1, "notifications": 1, "distraction-free": 1, "work": 1, "drag-and-drop": 1, "accuracy": 1, "overlapping": 1, "sticky": 1, "notes": 1, "formatting": 1, "weekly": 1, "emails": 1, "reviews": 1, "global": 1, "date": 1, "ranges": 1, "tags": 1, "relevance": 1, "ranking": 1, "document": 6, "results": 1, "quick": 1, "panel": 1, "press": 1, "activate": 2, "recently": 1, "items": 6, "show": 1, "suggestions": 1, "gdpr": 1, "delete": 4, "requests": 1, "ui": 5, "admin-level": 1, "controls": 1, "session": 1, "expiration": 1, "limits": 1, "enforced": 1, "password": 1, "strength": 1, "requirements": 1, "across": 1, "all": 3, "bug": 2, "fix": 1, "links": 7, "no": 3, "longer": 1, "accessible": 1, "after": 2, "permission": 1, "removal": 1, "load": 1, "times": 1, "through": 4, "caching": 2, "recurring": 1, "duplicated": 1, "edited": 1, "exporting": 1, "logs": 1, "as": 7, "updated": 2, "match": 1, "branding": 1, "guidelines": 1, "idle": 1, "memory": 1, "slack": 1, "direct": 1, "replies": 1, "native": 1, "notion": 1, "descriptions": 2, "webhooks": 1, "exponential": 1, "backoff": 1, "oauth": 1, "google": 1, "accounts": 1, "blocks": 4, "display": 2, "inline": 3, "basic": 2, "block": 2, "advanced": 1, "package": 2, "install": 2, "shiki": 2, "transformers": 2, "twoslash": 2, "notations": 2, "denote": 1, "word": 1, "phrase": 1, "enclose": 1, "backticks": 2, "fenced": 1, "enclosing": 1, "three": 1, "follow": 1, "leading": 1, "ticks": 1, "programming": 2, "language": 4, "get": 9, "optionally": 1, "also": 5, "name": 10, "supported": 4, "default": 12, "rehype": 1, "detects": 1, "common": 1, "managers": 1, "yarn": 1, "pnpm": 1, "displays": 1, "installation": 1, "commands": 1, "each": 3, "switch": 2, "different": 2, "tabs": 2, "we": 2, "highlight": 1, "style": 1, "specific": 2, "lines": 1, "text": 2, "title": 9, "images": 3, "auto": 1, "cards": 1, "callouts": 1, "headings": 4, "toc": 3, "anchor": 2, "tab": 2, "groups": 1, "mermaid": 1, "latex": 2, "provides": 3, "many": 1, "useful": 3, "extensions": 1, "markup": 1, "here": 1, "brief": 1, "only": 3, "fact": 1, "any": 1, "renderers": 1, "such": 2, "next-mdx-remote": 1, "cms": 1, "recommend": 1, "superset": 1, "jsx": 2, "allows": 6, "import": 2, "them": 5, "right": 1, "even": 2, "values": 1, "see": 6, "gfm": 1, "flavored": 1, "specification": 1, "are": 8, "optimized": 1, "image": 4, "internal": 1, "link": 3, "component": 4, "prefetching": 1, "avoid": 3, "hard-reload": 1, "external": 2, "will": 6, "rel": 1, "noreferrer": 1, "noopener": 1, "target": 2, "_blank": 1, "attributes": 1, "adding": 4, "icons": 4, "too": 1, "tips": 1, "warnings": 1, "included": 3, "specify": 2, "type": 2, "callout": 1, "info": 1, "warn": 1, "error": 2, "hello": 2, "world": 2, "applied": 1, "heading": 3, "sanitizes": 1, "invalid": 1, "characters": 1, "like": 3, "spaces": 1, "hello-world": 1, "table": 1, "contents": 1, "generated": 5, "effects": 1, "slug": 1, "anchors": 1, "chain": 1, "people": 1, "id": 1, "hash": 1, "fragment": 1, "my-heading-id": 1, "instead": 4, "importing": 1, "reference": 2, "another": 1, "path": 5, "tag": 1, "relative": 4, "itself": 1, "other": 2, "usages": 1, "rendering": 1, "diagrams": 1, "c": 1, "pm": 1, "sqrt": 1, "b": 1, "taylor": 1, "expansion": 1, "expressing": 1, "holomorphic": 1, "function": 1, "f": 1, "x": 1, "power": 1, "series": 1, "tip": 2, "actually": 1, "copy": 1, "equations": 1, "wikipedia": 1, "they": 3, "converted": 1, "into": 3, "katex": 1, "string": 1, "paste": 1, "routing": 2, "shared": 1, "convention": 1, "organizing": 1, "documents": 2, "slugs": 5, "group": 1, "meta": 8, "pages": 6, "rest": 1, "extract": 2, "root": 11, "file-system": 1, "system": 1, "organize": 3, "clear": 2, "consistent": 1, "easier": 1, "find": 1, "information": 3, "need": 3, "its": 4, "frontmatter": 4, "description": 5, "icon": 7, "full": 3, "fill": 1, "available": 1, "space": 1, "dir": 2, "'dir'": 2, "'page'": 2, "index": 3, "multiple": 1, "putting": 1, "change": 2, "wrap": 1, "parentheses": 1, "impacting": 1, "child": 1, "group-name": 1, "json": 7, "below": 1, "defaultopen": 1, "sorted": 2, "alphabetically": 2, "unless": 1, "listed": 1, "item": 2, "remaining": 1, "z": 1, "descending": 1, "prevent": 1, "being": 1, "folder_name": 1, "url": 2, "insert": 1, "converts": 1, "names": 1, "runtime": 3, "renders": 2, "marks": 1, "opened": 1, "considered": 1, "opening": 1, "framework": 1, "headless": 1, "shown": 1, "which": 1, "ai": 4, "configure": 2, "ai-powered": 2, "key": 3, "application": 1, "queries": 2, "handled": 1, "comes": 1, "capabilities": 1, "although": 1, "enabled": 1, "ll": 1, "functionality": 1, "openai": 3, "env": 1, "at": 6, "don": 1, "t": 1, "already": 1, "one": 1, "generate": 5, "signing": 1, "platform": 1, "pre-configured": 1, "explore": 1, "modify": 1, "implementation": 2, "there": 1, "suit": 1, "needs": 1, "process": 1, "retrieval-augmented": 2, "generation": 2, "rag": 2, "relies": 1, "web": 1, "replace": 1, "inkeep": 1, "efficient": 2, "tailored": 1, "infrastructure": 1, "async": 3, "compilation": 1, "constraints": 1, "must": 2, "precompiled": 1, "running": 1, "requirement": 1, "increase": 1, "startup": 1, "large": 3, "sites": 1, "enables": 1, "compiled": 1, "introduces": 1, "limitations": 1, "features": 2, "statements": 1, "allowed": 1, "pass": 1, "prop": 1, "referenced": 1, "urls": 2, "test": 1, "png": 1, "paths": 1, "place": 1, "public": 1, "their": 1, "llm": 2, "provide": 2, "ai-friendly": 1, "assist": 1, "models": 2, "llms": 7, "txt": 5, "llms-full": 2, "waste": 1, "tokens": 1, "crawling": 1, "html": 1, "chrome": 1, "shipping": 1, "few": 1, "simple": 1, "way": 1, "concise": 1, "sitemap": 1, "titles": 1, "summaries": 1, "dump": 1, "every": 2, "raw": 3, "requested": 2, "lists": 1, "section": 1, "optional": 2, "one-line": 1, "boilerplate": 1, "just": 1, "outline": 1, "deployed": 1, "enabling": 1, "agents": 1, "discover": 1, "your-domain": 1, "concatenates": 1, "preserves": 2, "paragraphs": 2, "samples": 3, "lets": 1, "ingest": 1, "entire": 1, "corpus": 1, "fetch": 1, "globbing": 1, "joining": 1, "schema": 4, "changing": 1, "playground": 1, "send": 1, "languages": 1, "typescript": 1, "definitions": 1, "parameters": 1, "body": 1, "schemas": 1, "generate-docs": 1, "script": 2, "modifying": 1, "steps": 1, "homepage": 1, "helps": 1, "categories": 1, "walk": 1, "anything": 1, "cli": 3, "located": 1, "array": 1, "tsx": 1, "home": 1, "documentationitem": 1, "edit": 1, "globals": 1, "css": 1, "base": 1, "apply": 1, "defines": 1, "metadata": 1, "including": 1, "initial": 1, "that's": 1, "you've": 1, "successfully": 1, "website": 1, "special": 18, "creates": 2, "museum": 12, "collection": 2, "cancel": 2, "planned": 2, "list": 3, "return": 4, "upcoming": 4, "publish": 1, "operating": 2, "buy": 1, "tickets": 3, "purchase": 2, "entry": 4, "ticket": 3, "qr": 3, "scannable": 2, "used": 2}, "page_id": {"docs": 334, "api-reference": 29, "app": 237, "quickstart": 22, "changelog": 68, "essentials": 125, "code": 14, "markdown": 50, "routing": 61, "features": 56, "ai-search": 11, "async-mode": 9, "llms": 21, "openapi": 15, "guides": 22, "adding-a-root-folder": 22, "events": 17, "createspecialevent": 3, "deletespecialevent": 3, "getspecialevent": 3, "listspecialevents": 3, "publishnewevent": 2, "updatespecialevent": 3, "operations": 3, "getmuseumhours": 3, "tickets": 6, "buymuseumtickets": 3, "getticketcode": 3}, "type": {"page": 21, "text": 239, "heading": 74}, "url": {"docs": 334, "api-reference": 29, "app": 237, "getting-started": 4, "make-it-yours": 6, "quickstart": 22, "setup-your-development-environment": 4, "start-the-development-server": 3, "add-your-content": 4, "deploy-your-changes": 2, "docker-deployment": 3, "update-your-docs": 4, "changelog": 68, "general-improvements": 5, "mobile-enhancements": 5, "integration-upgrades": 5, "analytics--reporting": 5, "ux--accessibility": 5, "year-end-stability-release": 5, "feature-updates": 5, "user-management": 5, "productivity-tools": 5, "search--navigation": 5, "security--compliance": 5, "performance--quality-updates": 6, "new-integrations": 5, "essentials": 125, "code": 14, "basic": 1, "inline-code": 2, "code-block": 2, "advanced": 1, "package-install": 2, "shiki-transformers": 2, "twoslash-notations": 2, "markdown": 50, "introduction": 19, "mdx": 5, "images": 2, "auto-links": 3, "cards": 4, "callouts": 9, "headings": 2, "toc-settings": 2, "custom-anchor": 4, "tab-groups": 3, "include": 3, "mermaid": 2, "latex": 6, "routing": 61, "file": 12, "slugs": 8, "folder": 2, "folder-group": 6, "meta": 12, "pages": 3, "rest": 3, "extract": 2, "link": 2, "icons": 2, "root-folder": 5, "features": 56, "ai-search": 11, "setting-up-the-api-key": 2, "application-configuration": 2, "how-queries-are-handled": 3, "async-mode": 9, "constraints": 4, "llms": 21, "llmstxt": 5, "llms-fulltxt": 5, "llmsmdx": 4, "openapi": 15, "changing-the-openapi-schema": 4, "guides": 22, "adding-a-root-folder": 22, "steps-to-add-a-new-root-folder": 1, "step-1-create-a-new-folder": 2, "step-2-update-the-metajson-file": 2, "step-3-update-the-homepage": 2, "step-4-update-colors-optional": 3, "step-5-create-metajson-in-the-new-folder": 4, "step-6-create-a-new-page": 4, "events": 17, "createspecialevent": 3, "deletespecialevent": 3, "getspecialevent": 3, "listspecialevents": 3, "publishnewevent": 2, "updatespecialevent": 3, "operations": 3, "getmuseumhours": 3, "tickets": 6, "buymuseumtickets": 3, "getticketcode": 3}}, "avgFieldLength": {"content": 7.81137724550898, "page_id": 3.437125748502994, "type": 1, "url": 4.26347305389222}, "fieldLengths": {"content": {"1": 1, "2": 9, "3": 12, "4": 1, "5": 5, "6": 2, "7": 3, "8": 13, "9": 11, "10": 15, "11": 14, "12": 9, "13": 9, "14": 9, "15": 9, "16": 1, "17": 8, "18": 4, "19": 4, "20": 3, "21": 3, "22": 2, "23": 3, "24": 21, "25": 12, "26": 11, "27": 14, "28": 6, "29": 42, "30": 9, "31": 12, "32": 17, "33": 33, "34": 11, "35": 19, "36": 8, "37": 9, "38": 2, "39": 4, "40": 2, "41": 2, "42": 2, "43": 2, "44": 2, "45": 3, "46": 2, "47": 2, "48": 2, "49": 2, "50": 2, "51": 3, "52": 2, "53": 11, "54": 12, "55": 10, "56": 9, "57": 7, "58": 6, "59": 11, "60": 7, "61": 8, "62": 6, "63": 10, "64": 9, "65": 9, "66": 11, "67": 8, "68": 9, "69": 8, "70": 9, "71": 7, "72": 10, "73": 7, "74": 9, "75": 11, "76": 11, "77": 9, "78": 7, "79": 8, "80": 10, "81": 10, "82": 5, "83": 10, "84": 9, "85": 10, "86": 7, "87": 6, "88": 10, "89": 11, "90": 7, "91": 7, "92": 8, "93": 9, "94": 9, "95": 8, "96": 10, "97": 11, "98": 12, "99": 9, "100": 8, "101": 8, "102": 8, "103": 9, "104": 8, "105": 8, "106": 2, "107": 5, "108": 1, "109": 2, "110": 2, "111": 1, "112": 2, "113": 2, "114": 2, "115": 12, "116": 36, "117": 24, "118": 14, "119": 5, "120": 2, "121": 7, "122": 1, "123": 1, "124": 1, "125": 2, "126": 1, "127": 1, "128": 1, "129": 2, "130": 2, "131": 2, "132": 1, "133": 1, "134": 1, "135": 18, "136": 21, "137": 27, "138": 1, "139": 2, "140": 9, "141": 7, "142": 13, "143": 14, "144": 4, "145": 7, "146": 5, "147": 17, "148": 2, "149": 1, "150": 1, "151": 1, "152": 1, "153": 2, "154": 2, "155": 19, "156": 16, "157": 8, "158": 9, "159": 13, "160": 9, "161": 13, "162": 20, "163": 3, "164": 5, "165": 6, "166": 7, "167": 10, "168": 2, "169": 18, "170": 1, "171": 7, "172": 1, "173": 1, "174": 1, "175": 1, "176": 2, "177": 1, "178": 1, "179": 1, "180": 1, "181": 1, "182": 1, "183": 2, "184": 31, "185": 10, "186": 1, "187": 1, "188": 1, "189": 4, "190": 1, "191": 4, "192": 1, "193": 6, "194": 1, "195": 9, "196": 11, "197": 5, "198": 1, "199": 3, "200": 2, "201": 3, "202": 1, "203": 12, "204": 24, "205": 5, "206": 1, "207": 3, "208": 1, "209": 11, "210": 1, "211": 1, "212": 1, "213": 2, "214": 1, "215": 6, "216": 1, "217": 4, "218": 1, "219": 5, "220": 7, "221": 18, "222": 14, "223": 11, "224": 10, "225": 11, "226": 18, "227": 13, "228": 23, "229": 3, "230": 15, "231": 2, "232": 8, "233": 1, "234": 5, "235": 2, "236": 4, "237": 25, "238": 30, "239": 22, "240": 22, "241": 28, "242": 2, "243": 5, "244": 1, "245": 1, "246": 26, "247": 18, "248": 8, "249": 22, "250": 29, "251": 2, "252": 8, "253": 1, "254": 2, "255": 2, "256": 2, "257": 29, "258": 10, "259": 9, "260": 10, "261": 8, "262": 6, "263": 7, "264": 20, "265": 8, "266": 7, "267": 12, "268": 18, "269": 10, "270": 7, "271": 4, "272": 1, "273": 6, "274": 1, "275": 1, "276": 4, "277": 12, "278": 5, "279": 4, "280": 3, "281": 9, "282": 5, "283": 7, "284": 23, "285": 8, "286": 5, "287": 4, "288": 10, "289": 1, "290": 7, "291": 6, "292": 7, "293": 5, "294": 5, "295": 9, "296": 6, "297": 29, "298": 18, "299": 18, "300": 13, "301": 14, "302": 8, "303": 10, "304": 7, "305": 15, "306": 10, "307": 6, "308": 19, "309": 3, "310": 8, "311": 8, "312": 3, "313": 13, "314": 13, "315": 3, "316": 6, "317": 6, "318": 3, "319": 10, "320": 10, "321": 4, "322": 8, "323": 3, "324": 7, "325": 7, "326": 3, "327": 5, "328": 5, "329": 3, "330": 9, "331": 9, "332": 4, "333": 14, "334": 14}, "page_id": {"1": 2, "2": 2, "3": 2, "4": 2, "5": 2, "6": 2, "7": 2, "8": 2, "9": 2, "10": 2, "11": 2, "12": 2, "13": 2, "14": 2, "15": 2, "16": 3, "17": 3, "18": 3, "19": 3, "20": 3, "21": 3, "22": 3, "23": 3, "24": 3, "25": 3, "26": 3, "27": 3, "28": 3, "29": 3, "30": 3, "31": 3, "32": 3, "33": 3, "34": 3, "35": 3, "36": 3, "37": 3, "38": 2, "39": 2, "40": 2, "41": 2, "42": 2, "43": 2, "44": 2, "45": 2, "46": 2, "47": 2, "48": 2, "49": 2, "50": 2, "51": 2, "52": 2, "53": 2, "54": 2, "55": 2, "56": 2, "57": 2, "58": 2, "59": 2, "60": 2, "61": 2, "62": 2, "63": 2, "64": 2, "65": 2, "66": 2, "67": 2, "68": 2, "69": 2, "70": 2, "71": 2, "72": 2, "73": 2, "74": 2, "75": 2, "76": 2, "77": 2, "78": 2, "79": 2, "80": 2, "81": 2, "82": 2, "83": 2, "84": 2, "85": 2, "86": 2, "87": 2, "88": 2, "89": 2, "90": 2, "91": 2, "92": 2, "93": 2, "94": 2, "95": 2, "96": 2, "97": 2, "98": 2, "99": 2, "100": 2, "101": 2, "102": 2, "103": 2, "104": 2, "105": 2, "106": 4, "107": 4, "108": 4, "109": 4, "110": 4, "111": 4, "112": 4, "113": 4, "114": 4, "115": 4, "116": 4, "117": 4, "118": 4, "119": 4, "120": 4, "121": 4, "122": 4, "123": 4, "124": 4, "125": 4, "126": 4, "127": 4, "128": 4, "129": 4, "130": 4, "131": 4, "132": 4, "133": 4, "134": 4, "135": 4, "136": 4, "137": 4, "138": 4, "139": 4, "140": 4, "141": 4, "142": 4, "143": 4, "144": 4, "145": 4, "146": 4, "147": 4, "148": 4, "149": 4, "150": 4, "151": 4, "152": 4, "153": 4, "154": 4, "155": 4, "156": 4, "157": 4, "158": 4, "159": 4, "160": 4, "161": 4, "162": 4, "163": 4, "164": 4, "165": 4, "166": 4, "167": 4, "168": 4, "169": 4, "170": 4, "171": 4, "172": 4, "173": 4, "174": 4, "175": 4, "176": 4, "177": 4, "178": 4, "179": 4, "180": 4, "181": 4, "182": 4, "183": 4, "184": 4, "185": 4, "186": 4, "187": 4, "188": 4, "189": 4, "190": 4, "191": 4, "192": 4, "193": 4, "194": 4, "195": 4, "196": 4, "197": 4, "198": 4, "199": 4, "200": 4, "201": 4, "202": 4, "203": 4, "204": 4, "205": 4, "206": 4, "207": 4, "208": 4, "209": 4, "210": 4, "211": 4, "212": 4, "213": 4, "214": 4, "215": 4, "216": 4, "217": 4, "218": 4, "219": 4, "220": 4, "221": 4, "222": 4, "223": 4, "224": 4, "225": 4, "226": 4, "227": 4, "228": 4, "229": 4, "230": 4, "231": 4, "232": 4, "233": 4, "234": 4, "235": 4, "236": 4, "237": 4, "238": 4, "239": 4, "240": 4, "241": 4, "242": 4, "243": 4, "244": 4, "245": 4, "246": 4, "247": 4, "248": 4, "249": 4, "250": 4, "251": 4, "252": 4, "253": 4, "254": 4, "255": 4, "256": 4, "257": 4, "258": 4, "259": 4, "260": 4, "261": 4, "262": 4, "263": 4, "264": 4, "265": 4, "266": 4, "267": 4, "268": 4, "269": 4, "270": 4, "271": 4, "272": 4, "273": 4, "274": 4, "275": 4, "276": 4, "277": 4, "278": 4, "279": 4, "280": 4, "281": 4, "282": 4, "283": 4, "284": 4, "285": 4, "286": 4, "287": 4, "288": 4, "289": 4, "290": 4, "291": 4, "292": 4, "293": 4, "294": 4, "295": 4, "296": 4, "297": 4, "298": 4, "299": 4, "300": 4, "301": 4, "302": 4, "303": 4, "304": 4, "305": 4, "306": 4, "307": 4, "308": 4, "309": 4, "310": 4, "311": 4, "312": 4, "313": 4, "314": 4, "315": 4, "316": 4, "317": 4, "318": 4, "319": 4, "320": 4, "321": 4, "322": 4, "323": 4, "324": 4, "325": 4, "326": 4, "327": 4, "328": 4, "329": 4, "330": 4, "331": 4, "332": 4, "333": 4, "334": 4}, "type": {"1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 1, "7": 1, "8": 1, "9": 1, "10": 1, "11": 1, "12": 1, "13": 1, "14": 1, "15": 1, "16": 1, "17": 1, "18": 1, "19": 1, "20": 1, "21": 1, "22": 1, "23": 1, "24": 1, "25": 1, "26": 1, "27": 1, "28": 1, "29": 1, "30": 1, "31": 1, "32": 1, "33": 1, "34": 1, "35": 1, "36": 1, "37": 1, "38": 1, "39": 1, "40": 1, "41": 1, "42": 1, "43": 1, "44": 1, "45": 1, "46": 1, "47": 1, "48": 1, "49": 1, "50": 1, "51": 1, "52": 1, "53": 1, "54": 1, "55": 1, "56": 1, "57": 1, "58": 1, "59": 1, "60": 1, "61": 1, "62": 1, "63": 1, "64": 1, "65": 1, "66": 1, "67": 1, "68": 1, "69": 1, "70": 1, "71": 1, "72": 1, "73": 1, "74": 1, "75": 1, "76": 1, "77": 1, "78": 1, "79": 1, "80": 1, "81": 1, "82": 1, "83": 1, "84": 1, "85": 1, "86": 1, "87": 1, "88": 1, "89": 1, "90": 1, "91": 1, "92": 1, "93": 1, "94": 1, "95": 1, "96": 1, "97": 1, "98": 1, "99": 1, "100": 1, "101": 1, "102": 1, "103": 1, "104": 1, "105": 1, "106": 1, "107": 1, "108": 1, "109": 1, "110": 1, "111": 1, "112": 1, "113": 1, "114": 1, "115": 1, "116": 1, "117": 1, "118": 1, "119": 1, "120": 1, "121": 1, "122": 1, "123": 1, "124": 1, "125": 1, "126": 1, "127": 1, "128": 1, "129": 1, "130": 1, "131": 1, "132": 1, "133": 1, "134": 1, "135": 1, "136": 1, "137": 1, "138": 1, "139": 1, "140": 1, "141": 1, "142": 1, "143": 1, "144": 1, "145": 1, "146": 1, "147": 1, "148": 1, "149": 1, "150": 1, "151": 1, "152": 1, "153": 1, "154": 1, "155": 1, "156": 1, "157": 1, "158": 1, "159": 1, "160": 1, "161": 1, "162": 1, "163": 1, "164": 1, "165": 1, "166": 1, "167": 1, "168": 1, "169": 1, "170": 1, "171": 1, "172": 1, "173": 1, "174": 1, "175": 1, "176": 1, "177": 1, "178": 1, "179": 1, "180": 1, "181": 1, "182": 1, "183": 1, "184": 1, "185": 1, "186": 1, "187": 1, "188": 1, "189": 1, "190": 1, "191": 1, "192": 1, "193": 1, "194": 1, "195": 1, "196": 1, "197": 1, "198": 1, "199": 1, "200": 1, "201": 1, "202": 1, "203": 1, "204": 1, "205": 1, "206": 1, "207": 1, "208": 1, "209": 1, "210": 1, "211": 1, "212": 1, "213": 1, "214": 1, "215": 1, "216": 1, "217": 1, "218": 1, "219": 1, "220": 1, "221": 1, "222": 1, "223": 1, "224": 1, "225": 1, "226": 1, "227": 1, "228": 1, "229": 1, "230": 1, "231": 1, "232": 1, "233": 1, "234": 1, "235": 1, "236": 1, "237": 1, "238": 1, "239": 1, "240": 1, "241": 1, "242": 1, "243": 1, "244": 1, "245": 1, "246": 1, "247": 1, "248": 1, "249": 1, "250": 1, "251": 1, "252": 1, "253": 1, "254": 1, "255": 1, "256": 1, "257": 1, "258": 1, "259": 1, "260": 1, "261": 1, "262": 1, "263": 1, "264": 1, "265": 1, "266": 1, "267": 1, "268": 1, "269": 1, "270": 1, "271": 1, "272": 1, "273": 1, "274": 1, "275": 1, "276": 1, "277": 1, "278": 1, "279": 1, "280": 1, "281": 1, "282": 1, "283": 1, "284": 1, "285": 1, "286": 1, "287": 1, "288": 1, "289": 1, "290": 1, "291": 1, "292": 1, "293": 1, "294": 1, "295": 1, "296": 1, "297": 1, "298": 1, "299": 1, "300": 1, "301": 1, "302": 1, "303": 1, "304": 1, "305": 1, "306": 1, "307": 1, "308": 1, "309": 1, "310": 1, "311": 1, "312": 1, "313": 1, "314": 1, "315": 1, "316": 1, "317": 1, "318": 1, "319": 1, "320": 1, "321": 1, "322": 1, "323": 1, "324": 1, "325": 1, "326": 1, "327": 1, "328": 1, "329": 1, "330": 1, "331": 1, "332": 1, "333": 1, "334": 1}, "url": {"1": 2, "2": 2, "3": 2, "4": 2, "5": 2, "6": 3, "7": 3, "8": 3, "9": 3, "10": 3, "11": 3, "12": 3, "13": 3, "14": 3, "15": 3, "16": 3, "17": 3, "18": 4, "19": 4, "20": 4, "21": 4, "22": 4, "23": 4, "24": 4, "25": 4, "26": 4, "27": 4, "28": 4, "29": 4, "30": 4, "31": 4, "32": 4, "33": 4, "34": 4, "35": 4, "36": 4, "37": 4, "38": 2, "39": 2, "40": 3, "41": 3, "42": 3, "43": 3, "44": 3, "45": 3, "46": 3, "47": 3, "48": 3, "49": 3, "50": 3, "51": 3, "52": 3, "53": 3, "54": 3, "55": 3, "56": 3, "57": 3, "58": 3, "59": 3, "60": 3, "61": 3, "62": 3, "63": 3, "64": 3, "65": 3, "66": 3, "67": 3, "68": 3, "69": 3, "70": 3, "71": 3, "72": 3, "73": 3, "74": 3, "75": 3, "76": 3, "77": 3, "78": 3, "79": 3, "80": 3, "81": 3, "82": 3, "83": 3, "84": 3, "85": 3, "86": 3, "87": 3, "88": 3, "89": 3, "90": 3, "91": 3, "92": 3, "93": 3, "94": 3, "95": 3, "96": 3, "97": 3, "98": 3, "99": 3, "100": 3, "101": 3, "102": 3, "103": 3, "104": 3, "105": 3, "106": 4, "107": 4, "108": 5, "109": 5, "110": 5, "111": 5, "112": 5, "113": 5, "114": 5, "115": 5, "116": 5, "117": 5, "118": 5, "119": 5, "120": 4, "121": 4, "122": 5, "123": 5, "124": 5, "125": 5, "126": 5, "127": 5, "128": 5, "129": 5, "130": 5, "131": 5, "132": 5, "133": 5, "134": 5, "135": 5, "136": 5, "137": 5, "138": 5, "139": 5, "140": 5, "141": 5, "142": 5, "143": 5, "144": 5, "145": 5, "146": 5, "147": 5, "148": 5, "149": 5, "150": 5, "151": 5, "152": 5, "153": 5, "154": 5, "155": 5, "156": 5, "157": 5, "158": 5, "159": 5, "160": 5, "161": 5, "162": 5, "163": 5, "164": 5, "165": 5, "166": 5, "167": 5, "168": 5, "169": 5, "170": 4, "171": 4, "172": 5, "173": 5, "174": 5, "175": 5, "176": 5, "177": 5, "178": 5, "179": 5, "180": 5, "181": 5, "182": 5, "183": 5, "184": 5, "185": 5, "186": 5, "187": 5, "188": 5, "189": 5, "190": 5, "191": 5, "192": 5, "193": 5, "194": 5, "195": 5, "196": 5, "197": 5, "198": 5, "199": 5, "200": 5, "201": 5, "202": 5, "203": 5, "204": 5, "205": 5, "206": 5, "207": 5, "208": 5, "209": 5, "210": 5, "211": 5, "212": 5, "213": 5, "214": 5, "215": 5, "216": 5, "217": 5, "218": 5, "219": 5, "220": 5, "221": 5, "222": 5, "223": 5, "224": 5, "225": 5, "226": 5, "227": 5, "228": 5, "229": 5, "230": 5, "231": 4, "232": 4, "233": 5, "234": 5, "235": 5, "236": 5, "237": 5, "238": 5, "239": 5, "240": 5, "241": 5, "242": 4, "243": 4, "244": 5, "245": 5, "246": 5, "247": 5, "248": 5, "249": 5, "250": 5, "251": 4, "252": 4, "253": 5, "254": 5, "255": 5, "256": 5, "257": 5, "258": 5, "259": 5, "260": 5, "261": 5, "262": 5, "263": 5, "264": 5, "265": 5, "266": 5, "267": 5, "268": 5, "269": 5, "270": 5, "271": 5, "272": 4, "273": 4, "274": 5, "275": 4, "276": 5, "277": 5, "278": 4, "279": 4, "280": 4, "281": 4, "282": 4, "283": 4, "284": 5, "285": 5, "286": 5, "287": 4, "288": 4, "289": 5, "290": 5, "291": 5, "292": 5, "293": 5, "294": 5, "295": 5, "296": 5, "297": 5, "298": 5, "299": 5, "300": 5, "301": 5, "302": 5, "303": 5, "304": 5, "305": 5, "306": 5, "307": 5, "308": 5, "309": 4, "310": 4, "311": 4, "312": 4, "313": 4, "314": 4, "315": 4, "316": 4, "317": 4, "318": 4, "319": 4, "320": 4, "321": 4, "322": 4, "323": 4, "324": 4, "325": 4, "326": 4, "327": 4, "328": 4, "329": 4, "330": 4, "331": 4, "332": 4, "333": 4, "334": 4}}}, "docs": {"docs": {"1": {"id": "/docs/api-reference", "page_id": "/docs/api-reference", "type": "page", "content": "Introduction", "tags": [], "url": "/docs/api-reference"}, "2": {"id": "/docs/api-reference-0", "page_id": "/docs/api-reference", "tags": [], "type": "text", "url": "/docs/api-reference", "content": "This is a page to check fumadocs's OpenAPI example."}, "3": {"id": "/docs/api-reference-1", "page_id": "/docs/api-reference", "tags": [], "type": "text", "url": "/docs/api-reference", "content": "Welcome to the OpenAPI example! You can update the openapi in the 'openapi.yml' file."}, "4": {"id": "/docs/app", "page_id": "/docs/app", "type": "page", "content": "Introduction", "tags": [], "url": "/docs/app"}, "5": {"id": "/docs/app-0", "page_id": "/docs/app", "tags": [], "type": "text", "url": "/docs/app", "content": "Welcome to your new documentation"}, "6": {"id": "/docs/app-1", "page_id": "/docs/app", "type": "heading", "tags": [], "url": "/docs/app#getting-started", "content": "Getting Started"}, "7": {"id": "/docs/app-2", "page_id": "/docs/app", "type": "heading", "tags": [], "url": "/docs/app#make-it-yours", "content": "Make it yours"}, "8": {"id": "/docs/app-3", "page_id": "/docs/app", "tags": [], "type": "text", "url": "/docs/app#getting-started", "content": "The first step to creating amazing documentation is setting up your editing environment."}, "9": {"id": "/docs/app-4", "page_id": "/docs/app", "tags": [], "type": "text", "url": "/docs/app#getting-started", "content": "Learn how to set up your docs for easy local development."}, "10": {"id": "/docs/app-5", "page_id": "/docs/app", "tags": [], "type": "text", "url": "/docs/app#getting-started", "content": "Learn how to structure your .mdx files and folders to define the sidebar\nlayout in Fumadocs"}, "11": {"id": "/docs/app-6", "page_id": "/docs/app", "tags": [], "type": "text", "url": "/docs/app#make-it-yours", "content": "Customize your documentation to reflect your brand and include meaningful content to maximize user engagement and conversions."}, "12": {"id": "/docs/app-7", "page_id": "/docs/app", "tags": [], "type": "text", "url": "/docs/app#make-it-yours", "content": "Customize your documentation by applying your brand colors and styles."}, "13": {"id": "/docs/app-8", "page_id": "/docs/app", "tags": [], "type": "text", "url": "/docs/app#make-it-yours", "content": "Auto-generate interactive endpoint docs straight from your OpenAPI spec."}, "14": {"id": "/docs/app-9", "page_id": "/docs/app", "tags": [], "type": "text", "url": "/docs/app#make-it-yours", "content": "Embed interactive elements and guides to improve user engagement."}, "15": {"id": "/docs/app-10", "page_id": "/docs/app", "tags": [], "type": "text", "url": "/docs/app#make-it-yours", "content": "Browse our showcase for creative ideas and best practices."}, "16": {"id": "/docs/app/quickstart", "page_id": "/docs/app/quickstart", "type": "page", "content": "Quickstart", "tags": [], "url": "/docs/app/quickstart"}, "17": {"id": "/docs/app/quickstart-0", "page_id": "/docs/app/quickstart", "tags": [], "type": "text", "url": "/docs/app/quickstart", "content": "Start building awesome documentation in under 5 minutes"}, "18": {"id": "/docs/app/quickstart-1", "page_id": "/docs/app/quickstart", "type": "heading", "tags": [], "url": "/docs/app/quickstart#setup-your-development-environment", "content": "Setup your development environment"}, "19": {"id": "/docs/app/quickstart-2", "page_id": "/docs/app/quickstart", "type": "heading", "tags": [], "url": "/docs/app/quickstart#start-the-development-server", "content": "Start the development server"}, "20": {"id": "/docs/app/quickstart-3", "page_id": "/docs/app/quickstart", "type": "heading", "tags": [], "url": "/docs/app/quickstart#add-your-content", "content": "Add your content"}, "21": {"id": "/docs/app/quickstart-4", "page_id": "/docs/app/quickstart", "type": "heading", "tags": [], "url": "/docs/app/quickstart#deploy-your-changes", "content": "Deploy your changes"}, "22": {"id": "/docs/app/quickstart-5", "page_id": "/docs/app/quickstart", "type": "heading", "tags": [], "url": "/docs/app/quickstart#docker-deployment", "content": "Docker Deployment"}, "23": {"id": "/docs/app/quickstart-6", "page_id": "/docs/app/quickstart", "type": "heading", "tags": [], "url": "/docs/app/quickstart#update-your-docs", "content": "Update your docs"}, "24": {"id": "/docs/app/quickstart-7", "page_id": "/docs/app/quickstart", "tags": [], "type": "text", "url": "/docs/app/quickstart#setup-your-development-environment", "content": "A minimum version of Node.js 18 is required. Note that Node.js 23.1 might have issues with the Next.js production build."}, "25": {"id": "/docs/app/quickstart-8", "page_id": "/docs/app/quickstart", "tags": [], "type": "text", "url": "/docs/app/quickstart#setup-your-development-environment", "content": "npx create-next-app -e https://github.com/techwithanirudh/fumadocs-starternpx create-next-app -e https://github.com/techwithanirudh/fumadocs-starter --use-pnpmnpx create-next-app -e https://github.com/techwithanirudh/fumadocs-starter --use-yarnnpx create-next-app -e https://github.com/techwithanirudh/fumadocs-starter --use-bun"}, "26": {"id": "/docs/app/quickstart-9", "page_id": "/docs/app/quickstart", "tags": [], "type": "text", "url": "/docs/app/quickstart#setup-your-development-environment", "content": "The Fumadocs template should now be initialized. You can start development!"}, "27": {"id": "/docs/app/quickstart-10", "page_id": "/docs/app/quickstart", "tags": [], "type": "text", "url": "/docs/app/quickstart#start-the-development-server", "content": "Run the app in development mode and open http://localhost:3000/docs/app in your browser."}, "28": {"id": "/docs/app/quickstart-11", "page_id": "/docs/app/quickstart", "tags": [], "type": "text", "url": "/docs/app/quickstart#start-the-development-server", "content": "npm run devpnpm run devyarn devbun run dev"}, "29": {"id": "/docs/app/quickstart-12", "page_id": "/docs/app/quickstart", "tags": [], "type": "text", "url": "/docs/app/quickstart#add-your-content", "content": "Fumadocs uses MDX for documentation, allowing you to write Markdown combined with React components. Add your content inside the content/docs directory. The structure is similar to a standard Next.js app, making it easy to navigate and manage your files. To learn more about MDX, check out the MDX page."}, "30": {"id": "/docs/app/quickstart-13", "page_id": "/docs/app/quickstart", "tags": [], "type": "text", "url": "/docs/app/quickstart#add-your-content", "content": "Create your first MDX file in the docs folder:"}, "31": {"id": "/docs/app/quickstart-14", "page_id": "/docs/app/quickstart", "tags": [], "type": "text", "url": "/docs/app/quickstart#add-your-content", "content": "The app organizes content by concerns, e.g., content/docs/api-reference, content/docs/app, content/docs/changelog, etc."}, "32": {"id": "/docs/app/quickstart-15", "page_id": "/docs/app/quickstart", "tags": [], "type": "text", "url": "/docs/app/quickstart#deploy-your-changes", "content": "This template works out-of-the-box with Vercel or Netlify. You can deploy your documentation site with a single click."}, "33": {"id": "/docs/app/quickstart-16", "page_id": "/docs/app/quickstart", "tags": [], "type": "text", "url": "/docs/app/quickstart#docker-deployment", "content": "If you want to deploy your Fumadocs app using Docker with Fumadocs MDX configured, make sure to add the source.config.ts file to the WORKDIR in the Dockerfile.\nThe following snippet is taken from the official Next.js Dockerfile Example:"}, "34": {"id": "/docs/app/quickstart-17", "page_id": "/docs/app/quickstart", "tags": [], "type": "text", "url": "/docs/app/quickstart#docker-deployment", "content": "This ensures Fumadocs MDX can access your configuration file during builds."}, "35": {"id": "/docs/app/quickstart-18", "page_id": "/docs/app/quickstart", "tags": [], "type": "text", "url": "/docs/app/quickstart#update-your-docs", "content": "Add content directly in your files using MDX syntax and React components. You can use built-in components or create your own."}, "36": {"id": "/docs/app/quickstart-19", "page_id": "/docs/app/quickstart", "tags": [], "type": "text", "url": "/docs/app/quickstart#update-your-docs", "content": "Add content to your docs with MDX syntax."}, "37": {"id": "/docs/app/quickstart-20", "page_id": "/docs/app/quickstart", "tags": [], "type": "text", "url": "/docs/app/quickstart#update-your-docs", "content": "Add code directly to your docs with syntax highlighting."}, "38": {"id": "/docs/changelog", "page_id": "/docs/changelog", "type": "page", "content": "Product Updates", "tags": [], "url": "/docs/changelog"}, "39": {"id": "/docs/changelog-0", "page_id": "/docs/changelog", "tags": [], "type": "text", "url": "/docs/changelog", "content": "New updates and improvements"}, "40": {"id": "/docs/changelog-1", "page_id": "/docs/changelog", "type": "heading", "tags": [], "url": "/docs/changelog#general-improvements", "content": "General Improvements"}, "41": {"id": "/docs/changelog-2", "page_id": "/docs/changelog", "type": "heading", "tags": [], "url": "/docs/changelog#mobile-enhancements", "content": "Mobile Enhancements"}, "42": {"id": "/docs/changelog-3", "page_id": "/docs/changelog", "type": "heading", "tags": [], "url": "/docs/changelog#integration-upgrades", "content": "Integration Upgrades"}, "43": {"id": "/docs/changelog-4", "page_id": "/docs/changelog", "type": "heading", "tags": [], "url": "/docs/changelog#analytics--reporting", "content": "Analytics & Reporting"}, "44": {"id": "/docs/changelog-5", "page_id": "/docs/changelog", "type": "heading", "tags": [], "url": "/docs/changelog#ux--accessibility", "content": "UX & Accessibility"}, "45": {"id": "/docs/changelog-6", "page_id": "/docs/changelog", "type": "heading", "tags": [], "url": "/docs/changelog#year-end-stability-release", "content": "Year-End Stability Release"}, "46": {"id": "/docs/changelog-7", "page_id": "/docs/changelog", "type": "heading", "tags": [], "url": "/docs/changelog#feature-updates", "content": "Feature Updates"}, "47": {"id": "/docs/changelog-8", "page_id": "/docs/changelog", "type": "heading", "tags": [], "url": "/docs/changelog#user-management", "content": "User Management"}, "48": {"id": "/docs/changelog-9", "page_id": "/docs/changelog", "type": "heading", "tags": [], "url": "/docs/changelog#productivity-tools", "content": "Productivity Tools"}, "49": {"id": "/docs/changelog-10", "page_id": "/docs/changelog", "type": "heading", "tags": [], "url": "/docs/changelog#search--navigation", "content": "Search & Navigation"}, "50": {"id": "/docs/changelog-11", "page_id": "/docs/changelog", "type": "heading", "tags": [], "url": "/docs/changelog#security--compliance", "content": "Security & Compliance"}, "51": {"id": "/docs/changelog-12", "page_id": "/docs/changelog", "type": "heading", "tags": [], "url": "/docs/changelog#performance--quality-updates", "content": "Performance & Quality Updates"}, "52": {"id": "/docs/changelog-13", "page_id": "/docs/changelog", "type": "heading", "tags": [], "url": "/docs/changelog#new-integrations", "content": "New Integrations"}, "53": {"id": "/docs/changelog-14", "page_id": "/docs/changelog", "tags": [], "type": "text", "url": "/docs/changelog#general-improvements", "content": "Redesigned notification settings for better control over email and in-app alerts."}, "54": {"id": "/docs/changelog-15", "page_id": "/docs/changelog", "tags": [], "type": "text", "url": "/docs/changelog#general-improvements", "content": "Fixed issue where some users were not receiving 2FA codes via SMS."}, "55": {"id": "/docs/changelog-16", "page_id": "/docs/changelog", "tags": [], "type": "text", "url": "/docs/changelog#general-improvements", "content": "Activity log now includes more granular details for API usage."}, "56": {"id": "/docs/changelog-17", "page_id": "/docs/changelog", "tags": [], "type": "text", "url": "/docs/changelog#general-improvements", "content": "Added keyboard shortcuts to the dashboard for quicker navigation."}, "57": {"id": "/docs/changelog-18", "page_id": "/docs/changelog", "tags": [], "type": "text", "url": "/docs/changelog#mobile-enhancements", "content": "Major performance optimizations for older Android devices."}, "58": {"id": "/docs/changelog-19", "page_id": "/docs/changelog", "tags": [], "type": "text", "url": "/docs/changelog#mobile-enhancements", "content": "Added gesture support for calendar rescheduling."}, "59": {"id": "/docs/changelog-20", "page_id": "/docs/changelog", "tags": [], "type": "text", "url": "/docs/changelog#mobile-enhancements", "content": "Resolved an issue causing data sync delays when switching between networks."}, "60": {"id": "/docs/changelog-21", "page_id": "/docs/changelog", "tags": [], "type": "text", "url": "/docs/changelog#mobile-enhancements", "content": "Push notification reliability improved for background updates."}, "61": {"id": "/docs/changelog-22", "page_id": "/docs/changelog", "tags": [], "type": "text", "url": "/docs/changelog#integration-upgrades", "content": "Microsoft Teams integration now supports deep-linking to projects."}, "62": {"id": "/docs/changelog-23", "page_id": "/docs/changelog", "tags": [], "type": "text", "url": "/docs/changelog#integration-upgrades", "content": "Added webhook event for task reassignment."}, "63": {"id": "/docs/changelog-24", "page_id": "/docs/changelog", "tags": [], "type": "text", "url": "/docs/changelog#integration-upgrades", "content": "Jira integration now includes sprint sync and story point mapping."}, "64": {"id": "/docs/changelog-25", "page_id": "/docs/changelog", "tags": [], "type": "text", "url": "/docs/changelog#integration-upgrades", "content": "New Zapier triggers for completed tasks and file uploads."}, "65": {"id": "/docs/changelog-26", "page_id": "/docs/changelog", "tags": [], "type": "text", "url": "/docs/changelog#analytics--reporting", "content": "Custom reports can now be scheduled and emailed automatically."}, "66": {"id": "/docs/changelog-27", "page_id": "/docs/changelog", "tags": [], "type": "text", "url": "/docs/changelog#analytics--reporting", "content": "Dashboard now supports drill-down charts for task status and team performance."}, "67": {"id": "/docs/changelog-28", "page_id": "/docs/changelog", "tags": [], "type": "text", "url": "/docs/changelog#analytics--reporting", "content": "Added time tracking summary by user and project."}, "68": {"id": "/docs/changelog-29", "page_id": "/docs/changelog", "tags": [], "type": "text", "url": "/docs/changelog#analytics--reporting", "content": "Export options now include PDF format with improved styling."}, "69": {"id": "/docs/changelog-30", "page_id": "/docs/changelog", "tags": [], "type": "text", "url": "/docs/changelog#ux--accessibility", "content": "Redesigned sidebar with collapsible sections for cleaner navigation."}, "70": {"id": "/docs/changelog-31", "page_id": "/docs/changelog", "tags": [], "type": "text", "url": "/docs/changelog#ux--accessibility", "content": "Improved screen reader support for form fields and buttons."}, "71": {"id": "/docs/changelog-32", "page_id": "/docs/changelog", "tags": [], "type": "text", "url": "/docs/changelog#ux--accessibility", "content": "Fixed inconsistent behavior of dropdowns in Safari."}, "72": {"id": "/docs/changelog-33", "page_id": "/docs/changelog", "tags": [], "type": "text", "url": "/docs/changelog#ux--accessibility", "content": "New color contrast settings for better visibility in dark mode."}, "73": {"id": "/docs/changelog-34", "page_id": "/docs/changelog", "tags": [], "type": "text", "url": "/docs/changelog#year-end-stability-release", "content": "Fixed edge-case bugs reported during November release."}, "74": {"id": "/docs/changelog-35", "page_id": "/docs/changelog", "tags": [], "type": "text", "url": "/docs/changelog#year-end-stability-release", "content": "Reduced server response latency during peak hours by 15%."}, "75": {"id": "/docs/changelog-36", "page_id": "/docs/changelog", "tags": [], "type": "text", "url": "/docs/changelog#year-end-stability-release", "content": "Backend job processing now has retry logic with alerting on failure."}, "76": {"id": "/docs/changelog-37", "page_id": "/docs/changelog", "tags": [], "type": "text", "url": "/docs/changelog#year-end-stability-release", "content": "Cleaned up deprecated API endpoints — refer to migration guide if needed."}, "77": {"id": "/docs/changelog-38", "page_id": "/docs/changelog", "tags": [], "type": "text", "url": "/docs/changelog#feature-updates", "content": "New “Smart Labels” automatically categorize tasks based on content."}, "78": {"id": "/docs/changelog-39", "page_id": "/docs/changelog", "tags": [], "type": "text", "url": "/docs/changelog#feature-updates", "content": "Added batch actions for checklists and subtasks."}, "79": {"id": "/docs/changelog-40", "page_id": "/docs/changelog", "tags": [], "type": "text", "url": "/docs/changelog#feature-updates", "content": "Admins can now view login history per user."}, "80": {"id": "/docs/changelog-41", "page_id": "/docs/changelog", "tags": [], "type": "text", "url": "/docs/changelog#feature-updates", "content": "Kanban board now remembers last viewed filters and column order."}, "81": {"id": "/docs/changelog-42", "page_id": "/docs/changelog", "tags": [], "type": "text", "url": "/docs/changelog#user-management", "content": "Invite flow redesigned to allow bulk user imports from CSV."}, "82": {"id": "/docs/changelog-43", "page_id": "/docs/changelog", "tags": [], "type": "text", "url": "/docs/changelog#user-management", "content": "Added user deactivation audit events."}, "83": {"id": "/docs/changelog-44", "page_id": "/docs/changelog", "tags": [], "type": "text", "url": "/docs/changelog#user-management", "content": "Users can now request access to private projects (pending approval)."}, "84": {"id": "/docs/changelog-45", "page_id": "/docs/changelog", "tags": [], "type": "text", "url": "/docs/changelog#user-management", "content": "SSO login time reduced by ~40% with token reuse."}, "85": {"id": "/docs/changelog-46", "page_id": "/docs/changelog", "tags": [], "type": "text", "url": "/docs/changelog#productivity-tools", "content": "Introduced “Focus Mode” – hides sidebar and notifications for distraction-free work."}, "86": {"id": "/docs/changelog-47", "page_id": "/docs/changelog", "tags": [], "type": "text", "url": "/docs/changelog#productivity-tools", "content": "Improved calendar drag-and-drop accuracy for overlapping events."}, "87": {"id": "/docs/changelog-48", "page_id": "/docs/changelog", "tags": [], "type": "text", "url": "/docs/changelog#productivity-tools", "content": "Sticky notes now support markdown formatting."}, "88": {"id": "/docs/changelog-49", "page_id": "/docs/changelog", "tags": [], "type": "text", "url": "/docs/changelog#productivity-tools", "content": "Weekly summary emails now include completed tasks and pending reviews."}, "89": {"id": "/docs/changelog-50", "page_id": "/docs/changelog", "tags": [], "type": "text", "url": "/docs/changelog#search--navigation", "content": "Global search now supports filters for date ranges, users, and tags."}, "90": {"id": "/docs/changelog-51", "page_id": "/docs/changelog", "tags": [], "type": "text", "url": "/docs/changelog#search--navigation", "content": "Improved relevance ranking for file/document results."}, "91": {"id": "/docs/changelog-52", "page_id": "/docs/changelog", "tags": [], "type": "text", "url": "/docs/changelog#search--navigation", "content": "Quick navigation panel added (press / to activate)."}, "92": {"id": "/docs/changelog-53", "page_id": "/docs/changelog", "tags": [], "type": "text", "url": "/docs/changelog#search--navigation", "content": "Recently viewed items show up in search suggestions."}, "93": {"id": "/docs/changelog-54", "page_id": "/docs/changelog", "tags": [], "type": "text", "url": "/docs/changelog#security--compliance", "content": "GDPR compliance updates: added data export + delete requests UI."}, "94": {"id": "/docs/changelog-55", "page_id": "/docs/changelog", "tags": [], "type": "text", "url": "/docs/changelog#security--compliance", "content": "New admin-level controls for session expiration and login limits."}, "95": {"id": "/docs/changelog-56", "page_id": "/docs/changelog", "tags": [], "type": "text", "url": "/docs/changelog#security--compliance", "content": "Enforced minimum password strength requirements across all users."}, "96": {"id": "/docs/changelog-57", "page_id": "/docs/changelog", "tags": [], "type": "text", "url": "/docs/changelog#security--compliance", "content": "Bug fix: File links no longer accessible after permission removal."}, "97": {"id": "/docs/changelog-58", "page_id": "/docs/changelog", "tags": [], "type": "text", "url": "/docs/changelog#performance--quality-updates", "content": "Improved load times on dashboard by 25% through API response caching."}, "98": {"id": "/docs/changelog-59", "page_id": "/docs/changelog", "tags": [], "type": "text", "url": "/docs/changelog#performance--quality-updates", "content": "Fixed a bug where recurring events were duplicated when edited in bulk."}, "99": {"id": "/docs/changelog-60", "page_id": "/docs/changelog", "tags": [], "type": "text", "url": "/docs/changelog#performance--quality-updates", "content": "Added support for exporting user activity logs as CSV."}, "100": {"id": "/docs/changelog-61", "page_id": "/docs/changelog", "tags": [], "type": "text", "url": "/docs/changelog#performance--quality-updates", "content": "Updated UI components to match new branding guidelines."}, "101": {"id": "/docs/changelog-62", "page_id": "/docs/changelog", "tags": [], "type": "text", "url": "/docs/changelog#performance--quality-updates", "content": "Reduced idle memory usage in background sync tasks."}, "102": {"id": "/docs/changelog-63", "page_id": "/docs/changelog", "tags": [], "type": "text", "url": "/docs/changelog#new-integrations", "content": "Slack integration now supports direct replies to alerts."}, "103": {"id": "/docs/changelog-64", "page_id": "/docs/changelog", "tags": [], "type": "text", "url": "/docs/changelog#new-integrations", "content": "Added native support for Notion links in task descriptions."}, "104": {"id": "/docs/changelog-65", "page_id": "/docs/changelog", "tags": [], "type": "text", "url": "/docs/changelog#new-integrations", "content": "Webhooks now include retry logic with exponential backoff."}, "105": {"id": "/docs/changelog-66", "page_id": "/docs/changelog", "tags": [], "type": "text", "url": "/docs/changelog#new-integrations", "content": "OAuth flow improved for Google and Microsoft accounts."}, "106": {"id": "/docs/app/essentials/code", "page_id": "/docs/app/essentials/code", "type": "page", "content": "Code Blocks", "tags": [], "url": "/docs/app/essentials/code"}, "107": {"id": "/docs/app/essentials/code-0", "page_id": "/docs/app/essentials/code", "tags": [], "type": "text", "url": "/docs/app/essentials/code", "content": "Display inline code and code blocks"}, "108": {"id": "/docs/app/essentials/code-1", "page_id": "/docs/app/essentials/code", "type": "heading", "tags": [], "url": "/docs/app/essentials/code#basic", "content": "Basic"}, "109": {"id": "/docs/app/essentials/code-2", "page_id": "/docs/app/essentials/code", "type": "heading", "tags": [], "url": "/docs/app/essentials/code#inline-code", "content": "Inline Code"}, "110": {"id": "/docs/app/essentials/code-3", "page_id": "/docs/app/essentials/code", "type": "heading", "tags": [], "url": "/docs/app/essentials/code#code-block", "content": "Code Block"}, "111": {"id": "/docs/app/essentials/code-4", "page_id": "/docs/app/essentials/code", "type": "heading", "tags": [], "url": "/docs/app/essentials/code#advanced", "content": "Advanced"}, "112": {"id": "/docs/app/essentials/code-5", "page_id": "/docs/app/essentials/code", "type": "heading", "tags": [], "url": "/docs/app/essentials/code#package-install", "content": "Package Install"}, "113": {"id": "/docs/app/essentials/code-6", "page_id": "/docs/app/essentials/code", "type": "heading", "tags": [], "url": "/docs/app/essentials/code#shiki-transformers", "content": "Shiki Transformers"}, "114": {"id": "/docs/app/essentials/code-7", "page_id": "/docs/app/essentials/code", "type": "heading", "tags": [], "url": "/docs/app/essentials/code#twoslash-notations", "content": "Twoslash Notations"}, "115": {"id": "/docs/app/essentials/code-8", "page_id": "/docs/app/essentials/code", "tags": [], "type": "text", "url": "/docs/app/essentials/code#inline-code", "content": "To denote a word or phrase as code, enclose it in backticks (`)."}, "116": {"id": "/docs/app/essentials/code-9", "page_id": "/docs/app/essentials/code", "tags": [], "type": "text", "url": "/docs/app/essentials/code#code-block", "content": "Use fenced code blocks by enclosing code in three backticks and follow the leading ticks with the programming language of your snippet to get syntax highlighting. Optionally, you can also write the name of your code after the programming language. Syntax Highlighting is supported by default using Rehype Code."}, "117": {"id": "/docs/app/essentials/code-10", "page_id": "/docs/app/essentials/code", "tags": [], "type": "text", "url": "/docs/app/essentials/code#package-install", "content": "The package install block automatically detects common package managers (npm, yarn, pnpm) and displays installation commands for each. Users can switch between different package managers using tabs."}, "118": {"id": "/docs/app/essentials/code-11", "page_id": "/docs/app/essentials/code", "tags": [], "type": "text", "url": "/docs/app/essentials/code#shiki-transformers", "content": "We support some of the Shiki Transformers, allowing you to highlight/style specific lines."}, "119": {"id": "/docs/app/essentials/code-12", "page_id": "/docs/app/essentials/code", "tags": [], "type": "text", "url": "/docs/app/essentials/code#twoslash-notations", "content": "Learn more about Twoslash notations."}, "120": {"id": "/docs/app/essentials/markdown", "page_id": "/docs/app/essentials/markdown", "type": "page", "content": "Markdown Syntax", "tags": [], "url": "/docs/app/essentials/markdown"}, "121": {"id": "/docs/app/essentials/markdown-0", "page_id": "/docs/app/essentials/markdown", "tags": [], "type": "text", "url": "/docs/app/essentials/markdown", "content": "Text, title, and styling in standard markdown"}, "122": {"id": "/docs/app/essentials/markdown-1", "page_id": "/docs/app/essentials/markdown", "type": "heading", "tags": [], "url": "/docs/app/essentials/markdown#introduction", "content": "Introduction"}, "123": {"id": "/docs/app/essentials/markdown-2", "page_id": "/docs/app/essentials/markdown", "type": "heading", "tags": [], "url": "/docs/app/essentials/markdown#mdx", "content": "MDX"}, "124": {"id": "/docs/app/essentials/markdown-3", "page_id": "/docs/app/essentials/markdown", "type": "heading", "tags": [], "url": "/docs/app/essentials/markdown#images", "content": "Images"}, "125": {"id": "/docs/app/essentials/markdown-4", "page_id": "/docs/app/essentials/markdown", "type": "heading", "tags": [], "url": "/docs/app/essentials/markdown#auto-links", "content": "Auto Links"}, "126": {"id": "/docs/app/essentials/markdown-5", "page_id": "/docs/app/essentials/markdown", "type": "heading", "tags": [], "url": "/docs/app/essentials/markdown#cards", "content": "Cards"}, "127": {"id": "/docs/app/essentials/markdown-6", "page_id": "/docs/app/essentials/markdown", "type": "heading", "tags": [], "url": "/docs/app/essentials/markdown#callouts", "content": "Callouts"}, "128": {"id": "/docs/app/essentials/markdown-7", "page_id": "/docs/app/essentials/markdown", "type": "heading", "tags": [], "url": "/docs/app/essentials/markdown#headings", "content": "Headings"}, "129": {"id": "/docs/app/essentials/markdown-8", "page_id": "/docs/app/essentials/markdown", "type": "heading", "tags": [], "url": "/docs/app/essentials/markdown#toc-settings", "content": "TOC Settings"}, "130": {"id": "/docs/app/essentials/markdown-9", "page_id": "/docs/app/essentials/markdown", "type": "heading", "tags": [], "url": "/docs/app/essentials/markdown#custom-anchor", "content": "Custom Anchor"}, "131": {"id": "/docs/app/essentials/markdown-10", "page_id": "/docs/app/essentials/markdown", "type": "heading", "tags": [], "url": "/docs/app/essentials/markdown#tab-groups", "content": "Tab Groups"}, "132": {"id": "/docs/app/essentials/markdown-11", "page_id": "/docs/app/essentials/markdown", "type": "heading", "tags": [], "url": "/docs/app/essentials/markdown#include", "content": "Include"}, "133": {"id": "/docs/app/essentials/markdown-12", "page_id": "/docs/app/essentials/markdown", "type": "heading", "tags": [], "url": "/docs/app/essentials/markdown#mermaid", "content": "Mermaid"}, "134": {"id": "/docs/app/essentials/markdown-13", "page_id": "/docs/app/essentials/markdown", "type": "heading", "tags": [], "url": "/docs/app/essentials/markdown#latex", "content": "LaTeX"}, "135": {"id": "/docs/app/essentials/markdown-14", "page_id": "/docs/app/essentials/markdown", "tags": [], "type": "text", "url": "/docs/app/essentials/markdown#introduction", "content": "Fumadocs provides many useful extensions to MDX, a markup language. Here is a brief introduction to the default MDX syntax of Fumadocs."}, "136": {"id": "/docs/app/essentials/markdown-15", "page_id": "/docs/app/essentials/markdown", "tags": [], "type": "text", "url": "/docs/app/essentials/markdown#introduction", "content": "MDX is not the only supported format of Fumadocs. In fact, you can use any renderers such as next-mdx-remote or CMS."}, "137": {"id": "/docs/app/essentials/markdown-16", "page_id": "/docs/app/essentials/markdown", "tags": [], "type": "text", "url": "/docs/app/essentials/markdown#mdx", "content": "We recommend MDX, a superset of Markdown with JSX syntax.\nIt allows you to import components, and use them right in the document, or even export values."}, "138": {"id": "/docs/app/essentials/markdown-17", "page_id": "/docs/app/essentials/markdown", "tags": [], "type": "text", "url": "/docs/app/essentials/markdown#mdx", "content": "See:"}, "139": {"id": "/docs/app/essentials/markdown-18", "page_id": "/docs/app/essentials/markdown", "tags": [], "type": "text", "url": "/docs/app/essentials/markdown#mdx", "content": "MDX Syntax."}, "140": {"id": "/docs/app/essentials/markdown-19", "page_id": "/docs/app/essentials/markdown", "tags": [], "type": "text", "url": "/docs/app/essentials/markdown#mdx", "content": "GFM (GitHub Flavored Markdown) is also supported, see GFM Specification."}, "141": {"id": "/docs/app/essentials/markdown-20", "page_id": "/docs/app/essentials/markdown", "tags": [], "type": "text", "url": "/docs/app/essentials/markdown#images", "content": "Images are automatically optimized for next/image."}, "142": {"id": "/docs/app/essentials/markdown-21", "page_id": "/docs/app/essentials/markdown", "tags": [], "type": "text", "url": "/docs/app/essentials/markdown#auto-links", "content": "Internal links use the next/link component to allow prefetching and avoid hard-reload."}, "143": {"id": "/docs/app/essentials/markdown-22", "page_id": "/docs/app/essentials/markdown", "tags": [], "type": "text", "url": "/docs/app/essentials/markdown#auto-links", "content": "External links will get the default rel=\"noreferrer noopener\" target=\"_blank\" attributes for security."}, "144": {"id": "/docs/app/essentials/markdown-23", "page_id": "/docs/app/essentials/markdown", "tags": [], "type": "text", "url": "/docs/app/essentials/markdown#cards", "content": "Useful for adding links."}, "145": {"id": "/docs/app/essentials/markdown-24", "page_id": "/docs/app/essentials/markdown", "tags": [], "type": "text", "url": "/docs/app/essentials/markdown#cards", "content": "Learn more about caching in Next.js"}, "146": {"id": "/docs/app/essentials/markdown-25", "page_id": "/docs/app/essentials/markdown", "tags": [], "type": "text", "url": "/docs/app/essentials/markdown#cards", "content": "You can include icons too."}, "147": {"id": "/docs/app/essentials/markdown-26", "page_id": "/docs/app/essentials/markdown", "tags": [], "type": "text", "url": "/docs/app/essentials/markdown#callouts", "content": "Useful for adding tips/warnings, it is included by default. You can specify the type of callout:"}, "148": {"id": "/docs/app/essentials/markdown-27", "page_id": "/docs/app/essentials/markdown", "tags": [], "type": "text", "url": "/docs/app/essentials/markdown#callouts", "content": "info (default)"}, "149": {"id": "/docs/app/essentials/markdown-28", "page_id": "/docs/app/essentials/markdown", "tags": [], "type": "text", "url": "/docs/app/essentials/markdown#callouts", "content": "warn"}, "150": {"id": "/docs/app/essentials/markdown-29", "page_id": "/docs/app/essentials/markdown", "tags": [], "type": "text", "url": "/docs/app/essentials/markdown#callouts", "content": "error"}, "151": {"id": "/docs/app/essentials/markdown-30", "page_id": "/docs/app/essentials/markdown", "tags": [], "type": "text", "url": "/docs/app/essentials/markdown#callouts", "content": "title: Title"}, "152": {"id": "/docs/app/essentials/markdown-31", "page_id": "/docs/app/essentials/markdown", "tags": [], "type": "text", "url": "/docs/app/essentials/markdown#callouts", "content": "title: Title"}, "153": {"id": "/docs/app/essentials/markdown-32", "page_id": "/docs/app/essentials/markdown", "tags": [], "type": "text", "url": "/docs/app/essentials/markdown#callouts", "content": "type: error"}, "154": {"id": "/docs/app/essentials/markdown-33", "page_id": "/docs/app/essentials/markdown", "tags": [], "type": "text", "url": "/docs/app/essentials/markdown#callouts", "content": "Hello World"}, "155": {"id": "/docs/app/essentials/markdown-34", "page_id": "/docs/app/essentials/markdown", "tags": [], "type": "text", "url": "/docs/app/essentials/markdown#headings", "content": "An anchor is automatically applied to each heading, it sanitizes invalid characters like spaces. (e.g. Hello World to hello-world)"}, "156": {"id": "/docs/app/essentials/markdown-35", "page_id": "/docs/app/essentials/markdown", "tags": [], "type": "text", "url": "/docs/app/essentials/markdown#toc-settings", "content": "The table of contents (TOC) will be generated based on headings, you can also customize the effects of headings:"}, "157": {"id": "/docs/app/essentials/markdown-36", "page_id": "/docs/app/essentials/markdown", "tags": [], "type": "text", "url": "/docs/app/essentials/markdown#custom-anchor", "content": "You can add [#slug] to customize heading anchors."}, "158": {"id": "/docs/app/essentials/markdown-37", "page_id": "/docs/app/essentials/markdown", "tags": [], "type": "text", "url": "/docs/app/essentials/markdown#custom-anchor", "content": "You can also chain it with TOC settings like:"}, "159": {"id": "/docs/app/essentials/markdown-38", "page_id": "/docs/app/essentials/markdown", "tags": [], "type": "text", "url": "/docs/app/essentials/markdown#custom-anchor", "content": "To link people to a specific heading, add the heading id to hash fragment: /page#my-heading-id."}, "160": {"id": "/docs/app/essentials/markdown-39", "page_id": "/docs/app/essentials/markdown", "tags": [], "type": "text", "url": "/docs/app/essentials/markdown#tab-groups", "content": "You can use code blocks with the <Tab /> component."}, "161": {"id": "/docs/app/essentials/markdown-40", "page_id": "/docs/app/essentials/markdown", "tags": [], "type": "text", "url": "/docs/app/essentials/markdown#tab-groups", "content": "Note that you can add MDX components instead of importing them in MDX files."}, "162": {"id": "/docs/app/essentials/markdown-41", "page_id": "/docs/app/essentials/markdown", "tags": [], "type": "text", "url": "/docs/app/essentials/markdown#include", "content": "Reference another file (can also be a Markdown/MDX document).\nSpecify the target file path in <include> tag (relative to the MDX file itself)."}, "163": {"id": "/docs/app/essentials/markdown-42", "page_id": "/docs/app/essentials/markdown", "tags": [], "type": "text", "url": "/docs/app/essentials/markdown#include", "content": "See other usages."}, "164": {"id": "/docs/app/essentials/markdown-43", "page_id": "/docs/app/essentials/markdown", "tags": [], "type": "text", "url": "/docs/app/essentials/markdown#mermaid", "content": "Rendering diagrams in your docs"}, "165": {"id": "/docs/app/essentials/markdown-44", "page_id": "/docs/app/essentials/markdown", "tags": [], "type": "text", "url": "/docs/app/essentials/markdown#latex", "content": "Fumadocs supports LaTeX through the Latex component."}, "166": {"id": "/docs/app/essentials/markdown-45", "page_id": "/docs/app/essentials/markdown", "tags": [], "type": "text", "url": "/docs/app/essentials/markdown#latex", "content": "Inline: c = \\pm\\sqrt{a^2 + b^2}"}, "167": {"id": "/docs/app/essentials/markdown-46", "page_id": "/docs/app/essentials/markdown", "tags": [], "type": "text", "url": "/docs/app/essentials/markdown#latex", "content": "Taylor Expansion (expressing holomorphic function f(x) in power series):"}, "168": {"id": "/docs/app/essentials/markdown-47", "page_id": "/docs/app/essentials/markdown", "tags": [], "type": "text", "url": "/docs/app/essentials/markdown#latex", "content": "title: <PERSON><PERSON>"}, "169": {"id": "/docs/app/essentials/markdown-48", "page_id": "/docs/app/essentials/markdown", "tags": [], "type": "text", "url": "/docs/app/essentials/markdown#latex", "content": "You can actually copy equations on Wikipedia, they will be converted into a KaTeX string when you paste it."}, "170": {"id": "/docs/app/essentials/routing", "page_id": "/docs/app/essentials/routing", "type": "page", "content": "Routing", "tags": [], "url": "/docs/app/essentials/routing"}, "171": {"id": "/docs/app/essentials/routing-0", "page_id": "/docs/app/essentials/routing", "tags": [], "type": "text", "url": "/docs/app/essentials/routing", "content": "A shared convention for organizing your documents"}, "172": {"id": "/docs/app/essentials/routing-1", "page_id": "/docs/app/essentials/routing", "type": "heading", "tags": [], "url": "/docs/app/essentials/routing#introduction", "content": "Introduction"}, "173": {"id": "/docs/app/essentials/routing-2", "page_id": "/docs/app/essentials/routing", "type": "heading", "tags": [], "url": "/docs/app/essentials/routing#file", "content": "File"}, "174": {"id": "/docs/app/essentials/routing-3", "page_id": "/docs/app/essentials/routing", "type": "heading", "tags": [], "url": "/docs/app/essentials/routing#slugs", "content": "Slugs"}, "175": {"id": "/docs/app/essentials/routing-4", "page_id": "/docs/app/essentials/routing", "type": "heading", "tags": [], "url": "/docs/app/essentials/routing#folder", "content": "Folder"}, "176": {"id": "/docs/app/essentials/routing-5", "page_id": "/docs/app/essentials/routing", "type": "heading", "tags": [], "url": "/docs/app/essentials/routing#folder-group", "content": "Folder Group"}, "177": {"id": "/docs/app/essentials/routing-6", "page_id": "/docs/app/essentials/routing", "type": "heading", "tags": [], "url": "/docs/app/essentials/routing#meta", "content": "Meta"}, "178": {"id": "/docs/app/essentials/routing-7", "page_id": "/docs/app/essentials/routing", "type": "heading", "tags": [], "url": "/docs/app/essentials/routing#pages", "content": "Pages"}, "179": {"id": "/docs/app/essentials/routing-8", "page_id": "/docs/app/essentials/routing", "type": "heading", "tags": [], "url": "/docs/app/essentials/routing#rest", "content": "Rest"}, "180": {"id": "/docs/app/essentials/routing-9", "page_id": "/docs/app/essentials/routing", "type": "heading", "tags": [], "url": "/docs/app/essentials/routing#extract", "content": "Extract"}, "181": {"id": "/docs/app/essentials/routing-10", "page_id": "/docs/app/essentials/routing", "type": "heading", "tags": [], "url": "/docs/app/essentials/routing#link", "content": "Link"}, "182": {"id": "/docs/app/essentials/routing-11", "page_id": "/docs/app/essentials/routing", "type": "heading", "tags": [], "url": "/docs/app/essentials/routing#icons", "content": "Icons"}, "183": {"id": "/docs/app/essentials/routing-12", "page_id": "/docs/app/essentials/routing", "type": "heading", "tags": [], "url": "/docs/app/essentials/routing#root-folder", "content": "Root Folder"}, "184": {"id": "/docs/app/essentials/routing-13", "page_id": "/docs/app/essentials/routing", "tags": [], "type": "text", "url": "/docs/app/essentials/routing#introduction", "content": "Fumadocs uses a file-system based routing system to organize your documents. This allows you to create a clear and consistent structure for your documentation, making it easier for users to navigate and find the information they need."}, "185": {"id": "/docs/app/essentials/routing-14", "page_id": "/docs/app/essentials/routing", "tags": [], "type": "text", "url": "/docs/app/essentials/routing#file", "content": "A MDX or Markdown file, you can customize its frontmatter."}, "186": {"id": "/docs/app/essentials/routing-15", "page_id": "/docs/app/essentials/routing", "tags": [], "type": "text", "url": "/docs/app/essentials/routing#file", "content": "name"}, "187": {"id": "/docs/app/essentials/routing-16", "page_id": "/docs/app/essentials/routing", "tags": [], "type": "text", "url": "/docs/app/essentials/routing#file", "content": "description"}, "188": {"id": "/docs/app/essentials/routing-17", "page_id": "/docs/app/essentials/routing", "tags": [], "type": "text", "url": "/docs/app/essentials/routing#file", "content": "title"}, "189": {"id": "/docs/app/essentials/routing-18", "page_id": "/docs/app/essentials/routing", "tags": [], "type": "text", "url": "/docs/app/essentials/routing#file", "content": "The title of page"}, "190": {"id": "/docs/app/essentials/routing-19", "page_id": "/docs/app/essentials/routing", "tags": [], "type": "text", "url": "/docs/app/essentials/routing#file", "content": "description"}, "191": {"id": "/docs/app/essentials/routing-20", "page_id": "/docs/app/essentials/routing", "tags": [], "type": "text", "url": "/docs/app/essentials/routing#file", "content": "The description of page"}, "192": {"id": "/docs/app/essentials/routing-21", "page_id": "/docs/app/essentials/routing", "tags": [], "type": "text", "url": "/docs/app/essentials/routing#file", "content": "icon"}, "193": {"id": "/docs/app/essentials/routing-22", "page_id": "/docs/app/essentials/routing", "tags": [], "type": "text", "url": "/docs/app/essentials/routing#file", "content": "The name of icon, see Icons"}, "194": {"id": "/docs/app/essentials/routing-23", "page_id": "/docs/app/essentials/routing", "tags": [], "type": "text", "url": "/docs/app/essentials/routing#file", "content": "full"}, "195": {"id": "/docs/app/essentials/routing-24", "page_id": "/docs/app/essentials/routing", "tags": [], "type": "text", "url": "/docs/app/essentials/routing#file", "content": "Fill all available space on the page (Fumadocs UI)"}, "196": {"id": "/docs/app/essentials/routing-25", "page_id": "/docs/app/essentials/routing", "tags": [], "type": "text", "url": "/docs/app/essentials/routing#slugs", "content": "The slugs of a page are generated from its file path."}, "197": {"id": "/docs/app/essentials/routing-26", "page_id": "/docs/app/essentials/routing", "tags": [], "type": "text", "url": "/docs/app/essentials/routing#slugs", "content": "path (relative to content folder)"}, "198": {"id": "/docs/app/essentials/routing-27", "page_id": "/docs/app/essentials/routing", "tags": [], "type": "text", "url": "/docs/app/essentials/routing#slugs", "content": "slugs"}, "199": {"id": "/docs/app/essentials/routing-28", "page_id": "/docs/app/essentials/routing", "tags": [], "type": "text", "url": "/docs/app/essentials/routing#slugs", "content": "./dir/page.mdx"}, "200": {"id": "/docs/app/essentials/routing-29", "page_id": "/docs/app/essentials/routing", "tags": [], "type": "text", "url": "/docs/app/essentials/routing#slugs", "content": "['dir', 'page']"}, "201": {"id": "/docs/app/essentials/routing-30", "page_id": "/docs/app/essentials/routing", "tags": [], "type": "text", "url": "/docs/app/essentials/routing#slugs", "content": "./dir/index.mdx"}, "202": {"id": "/docs/app/essentials/routing-31", "page_id": "/docs/app/essentials/routing", "tags": [], "type": "text", "url": "/docs/app/essentials/routing#slugs", "content": "['dir']"}, "203": {"id": "/docs/app/essentials/routing-32", "page_id": "/docs/app/essentials/routing", "tags": [], "type": "text", "url": "/docs/app/essentials/routing#folder", "content": "Organize multiple pages, you can create a Meta file to customize folders."}, "204": {"id": "/docs/app/essentials/routing-33", "page_id": "/docs/app/essentials/routing", "tags": [], "type": "text", "url": "/docs/app/essentials/routing#folder-group", "content": "By default, putting a file into folder will change its slugs.\nYou can wrap the folder name in parentheses to avoid impacting the slugs of child files."}, "205": {"id": "/docs/app/essentials/routing-34", "page_id": "/docs/app/essentials/routing", "tags": [], "type": "text", "url": "/docs/app/essentials/routing#folder-group", "content": "path (relative to content folder)"}, "206": {"id": "/docs/app/essentials/routing-35", "page_id": "/docs/app/essentials/routing", "tags": [], "type": "text", "url": "/docs/app/essentials/routing#folder-group", "content": "slugs"}, "207": {"id": "/docs/app/essentials/routing-36", "page_id": "/docs/app/essentials/routing", "tags": [], "type": "text", "url": "/docs/app/essentials/routing#folder-group", "content": "./(group-name)/page.mdx"}, "208": {"id": "/docs/app/essentials/routing-37", "page_id": "/docs/app/essentials/routing", "tags": [], "type": "text", "url": "/docs/app/essentials/routing#folder-group", "content": "['page']"}, "209": {"id": "/docs/app/essentials/routing-38", "page_id": "/docs/app/essentials/routing", "tags": [], "type": "text", "url": "/docs/app/essentials/routing#meta", "content": "Customize folders by creating a meta.json file in the folder."}, "210": {"id": "/docs/app/essentials/routing-39", "page_id": "/docs/app/essentials/routing", "tags": [], "type": "text", "url": "/docs/app/essentials/routing#meta", "content": "name"}, "211": {"id": "/docs/app/essentials/routing-40", "page_id": "/docs/app/essentials/routing", "tags": [], "type": "text", "url": "/docs/app/essentials/routing#meta", "content": "description"}, "212": {"id": "/docs/app/essentials/routing-41", "page_id": "/docs/app/essentials/routing", "tags": [], "type": "text", "url": "/docs/app/essentials/routing#meta", "content": "title"}, "213": {"id": "/docs/app/essentials/routing-42", "page_id": "/docs/app/essentials/routing", "tags": [], "type": "text", "url": "/docs/app/essentials/routing#meta", "content": "Display name"}, "214": {"id": "/docs/app/essentials/routing-43", "page_id": "/docs/app/essentials/routing", "tags": [], "type": "text", "url": "/docs/app/essentials/routing#meta", "content": "icon"}, "215": {"id": "/docs/app/essentials/routing-44", "page_id": "/docs/app/essentials/routing", "tags": [], "type": "text", "url": "/docs/app/essentials/routing#meta", "content": "The name of icon, see Icons"}, "216": {"id": "/docs/app/essentials/routing-45", "page_id": "/docs/app/essentials/routing", "tags": [], "type": "text", "url": "/docs/app/essentials/routing#meta", "content": "pages"}, "217": {"id": "/docs/app/essentials/routing-46", "page_id": "/docs/app/essentials/routing", "tags": [], "type": "text", "url": "/docs/app/essentials/routing#meta", "content": "Folder items (see below)"}, "218": {"id": "/docs/app/essentials/routing-47", "page_id": "/docs/app/essentials/routing", "tags": [], "type": "text", "url": "/docs/app/essentials/routing#meta", "content": "defaultOpen"}, "219": {"id": "/docs/app/essentials/routing-48", "page_id": "/docs/app/essentials/routing", "tags": [], "type": "text", "url": "/docs/app/essentials/routing#meta", "content": "Open the folder by default"}, "220": {"id": "/docs/app/essentials/routing-49", "page_id": "/docs/app/essentials/routing", "tags": [], "type": "text", "url": "/docs/app/essentials/routing#pages", "content": "By default, folder items are sorted alphabetically."}, "221": {"id": "/docs/app/essentials/routing-50", "page_id": "/docs/app/essentials/routing", "tags": [], "type": "text", "url": "/docs/app/essentials/routing#pages", "content": "You can add or control the order of items using pages, items are not included unless they are listed inside."}, "222": {"id": "/docs/app/essentials/routing-51", "page_id": "/docs/app/essentials/routing", "tags": [], "type": "text", "url": "/docs/app/essentials/routing#rest", "content": "Add a ... item to include remaining pages (sorted alphabetically), or z...a for descending order."}, "223": {"id": "/docs/app/essentials/routing-52", "page_id": "/docs/app/essentials/routing", "tags": [], "type": "text", "url": "/docs/app/essentials/routing#rest", "content": "You can add !name to prevent an item from being included."}, "224": {"id": "/docs/app/essentials/routing-53", "page_id": "/docs/app/essentials/routing", "tags": [], "type": "text", "url": "/docs/app/essentials/routing#extract", "content": "You can extract the items from a folder with ...folder_name."}, "225": {"id": "/docs/app/essentials/routing-54", "page_id": "/docs/app/essentials/routing", "tags": [], "type": "text", "url": "/docs/app/essentials/routing#link", "content": "Use the syntax [Text](url) to insert links, or [Icon][Text](url) to add icon."}, "226": {"id": "/docs/app/essentials/routing-55", "page_id": "/docs/app/essentials/routing", "tags": [], "type": "text", "url": "/docs/app/essentials/routing#icons", "content": "This Fumadocs template converts the icon names to JSX elements in runtime, and renders it as a component."}, "227": {"id": "/docs/app/essentials/routing-56", "page_id": "/docs/app/essentials/routing", "tags": [], "type": "text", "url": "/docs/app/essentials/routing#root-folder", "content": "Marks the folder as a root folder, only items in the opened root folder will be considered."}, "228": {"id": "/docs/app/essentials/routing-57", "page_id": "/docs/app/essentials/routing", "tags": [], "type": "text", "url": "/docs/app/essentials/routing#root-folder", "content": "For example, when you are opening a root folder framework, the other folders (e.g. headless) are not shown on the sidebar and other navigation elements."}, "229": {"id": "/docs/app/essentials/routing-58", "page_id": "/docs/app/essentials/routing", "tags": [], "type": "text", "url": "/docs/app/essentials/routing#root-folder", "content": "title: Fumadocs UI"}, "230": {"id": "/docs/app/essentials/routing-59", "page_id": "/docs/app/essentials/routing", "tags": [], "type": "text", "url": "/docs/app/essentials/routing#root-folder", "content": "Fumadocs UI renders root folders as Sidebar\nTabs, which\nallows user to switch between them."}, "231": {"id": "/docs/app/features/ai-search", "page_id": "/docs/app/features/ai-search", "type": "page", "content": "AI Search", "tags": [], "url": "/docs/app/features/ai-search"}, "232": {"id": "/docs/app/features/ai-search-0", "page_id": "/docs/app/features/ai-search", "tags": [], "type": "text", "url": "/docs/app/features/ai-search", "content": "Configure and use AI-powered search in your documentation"}, "233": {"id": "/docs/app/features/ai-search-1", "page_id": "/docs/app/features/ai-search", "type": "heading", "tags": [], "url": "/docs/app/features/ai-search#introduction", "content": "Introduction"}, "234": {"id": "/docs/app/features/ai-search-2", "page_id": "/docs/app/features/ai-search", "type": "heading", "tags": [], "url": "/docs/app/features/ai-search#setting-up-the-api-key", "content": "Setting Up the API Key"}, "235": {"id": "/docs/app/features/ai-search-3", "page_id": "/docs/app/features/ai-search", "type": "heading", "tags": [], "url": "/docs/app/features/ai-search#application-configuration", "content": "Application Configuration"}, "236": {"id": "/docs/app/features/ai-search-4", "page_id": "/docs/app/features/ai-search", "type": "heading", "tags": [], "url": "/docs/app/features/ai-search#how-queries-are-handled", "content": "How Queries Are Handled"}, "237": {"id": "/docs/app/features/ai-search-5", "page_id": "/docs/app/features/ai-search", "tags": [], "type": "text", "url": "/docs/app/features/ai-search#introduction", "content": "This template comes with built-in AI-powered search capabilities. Although this feature is enabled by default, you’ll need to configure an API key for full functionality."}, "238": {"id": "/docs/app/features/ai-search-6", "page_id": "/docs/app/features/ai-search", "tags": [], "type": "text", "url": "/docs/app/features/ai-search#setting-up-the-api-key", "content": "To activate AI search, add your OpenAI API key to the .env file at the root of your project. If you don’t already have one, you can generate a key by signing up at the OpenAI platform."}, "239": {"id": "/docs/app/features/ai-search-7", "page_id": "/docs/app/features/ai-search", "tags": [], "type": "text", "url": "/docs/app/features/ai-search#application-configuration", "content": "The logic for AI search is pre-configured in the components/fumadocs/ai directory. You can explore or modify the implementation there to suit your needs."}, "240": {"id": "/docs/app/features/ai-search-8", "page_id": "/docs/app/features/ai-search", "tags": [], "type": "text", "url": "/docs/app/features/ai-search#how-queries-are-handled", "content": "AI Search uses the OpenAI API to process user queries. Instead of Retrieval-Augmented Generation (RAG), it relies on Web Search for external information."}, "241": {"id": "/docs/app/features/ai-search-9", "page_id": "/docs/app/features/ai-search", "tags": [], "type": "text", "url": "/docs/app/features/ai-search#how-queries-are-handled", "content": "Tip: You can replace the default OpenAI integration with a custom setup (such as Inkeep) for a more efficient Retrieval-Augmented Generation (RAG) implementation tailored to your content and infrastructure."}, "242": {"id": "/docs/app/features/async-mode", "page_id": "/docs/app/features/async-mode", "type": "page", "content": "Async Mode", "tags": [], "url": "/docs/app/features/async-mode"}, "243": {"id": "/docs/app/features/async-mode-0", "page_id": "/docs/app/features/async-mode", "tags": [], "type": "text", "url": "/docs/app/features/async-mode", "content": "Runtime compilation of content files."}, "244": {"id": "/docs/app/features/async-mode-1", "page_id": "/docs/app/features/async-mode", "type": "heading", "tags": [], "url": "/docs/app/features/async-mode#introduction", "content": "Introduction"}, "245": {"id": "/docs/app/features/async-mode-2", "page_id": "/docs/app/features/async-mode", "type": "heading", "tags": [], "url": "/docs/app/features/async-mode#constraints", "content": "Constraints"}, "246": {"id": "/docs/app/features/async-mode-3", "page_id": "/docs/app/features/async-mode", "tags": [], "type": "text", "url": "/docs/app/features/async-mode#introduction", "content": "By default, all Markdown and MDX files must be precompiled, even when running the development server. This requirement can increase startup time for large documentation sites."}, "247": {"id": "/docs/app/features/async-mode-4", "page_id": "/docs/app/features/async-mode", "tags": [], "type": "text", "url": "/docs/app/features/async-mode#introduction", "content": "To improve performance, this template enables Async Mode by default, allowing content files to be compiled at runtime instead."}, "248": {"id": "/docs/app/features/async-mode-5", "page_id": "/docs/app/features/async-mode", "tags": [], "type": "text", "url": "/docs/app/features/async-mode#constraints", "content": "Async Mode introduces some limitations to MDX features:"}, "249": {"id": "/docs/app/features/async-mode-6", "page_id": "/docs/app/features/async-mode", "tags": [], "type": "text", "url": "/docs/app/features/async-mode#constraints", "content": "No import/export statements are allowed inside MDX files. If you need to use custom components, pass them through the components prop instead."}, "250": {"id": "/docs/app/features/async-mode-7", "page_id": "/docs/app/features/async-mode", "tags": [], "type": "text", "url": "/docs/app/features/async-mode#constraints", "content": "Images must be referenced using URLs (e.g. /images/test.png). Avoid relative file paths like ./image.png. Place your images inside the public folder and reference them via their public URL path."}, "251": {"id": "/docs/app/features/llms", "page_id": "/docs/app/features/llms", "type": "page", "content": "LLM Support", "tags": [], "url": "/docs/app/features/llms"}, "252": {"id": "/docs/app/features/llms-0", "page_id": "/docs/app/features/llms", "tags": [], "type": "text", "url": "/docs/app/features/llms", "content": "Provide AI-friendly endpoints to assist large language models"}, "253": {"id": "/docs/app/features/llms-1", "page_id": "/docs/app/features/llms", "type": "heading", "tags": [], "url": "/docs/app/features/llms#introduction", "content": "Introduction"}, "254": {"id": "/docs/app/features/llms-2", "page_id": "/docs/app/features/llms", "type": "heading", "tags": [], "url": "/docs/app/features/llms#llmstxt", "content": "/llms.txt"}, "255": {"id": "/docs/app/features/llms-3", "page_id": "/docs/app/features/llms", "type": "heading", "tags": [], "url": "/docs/app/features/llms#llms-fulltxt", "content": "/llms-full.txt"}, "256": {"id": "/docs/app/features/llms-4", "page_id": "/docs/app/features/llms", "type": "heading", "tags": [], "url": "/docs/app/features/llms#llmsmdx", "content": "/llms.mdx/*"}, "257": {"id": "/docs/app/features/llms-5", "page_id": "/docs/app/features/llms", "tags": [], "type": "text", "url": "/docs/app/features/llms#introduction", "content": "Large language models waste tokens crawling HTML, JS, and site chrome. By shipping a few simple endpoints, you can provide a more efficient way for LLMs to access your documentation."}, "258": {"id": "/docs/app/features/llms-6", "page_id": "/docs/app/features/llms", "tags": [], "type": "text", "url": "/docs/app/features/llms#introduction", "content": "/llms.txt: a concise sitemap with titles, URLs, and summaries"}, "259": {"id": "/docs/app/features/llms-7", "page_id": "/docs/app/features/llms", "tags": [], "type": "text", "url": "/docs/app/features/llms#introduction", "content": "/llms-full.txt: a full Markdown dump of every document"}, "260": {"id": "/docs/app/features/llms-8", "page_id": "/docs/app/features/llms", "tags": [], "type": "text", "url": "/docs/app/features/llms#introduction", "content": "/llms.mdx/*: the raw MDX/Markdown content of a requested page"}, "261": {"id": "/docs/app/features/llms-9", "page_id": "/docs/app/features/llms", "tags": [], "type": "text", "url": "/docs/app/features/llms#llmstxt", "content": "Lists each section and page in your documentation"}, "262": {"id": "/docs/app/features/llms-10", "page_id": "/docs/app/features/llms", "tags": [], "type": "text", "url": "/docs/app/features/llms#llmstxt", "content": "Includes optional one-line descriptions from frontmatter"}, "263": {"id": "/docs/app/features/llms-11", "page_id": "/docs/app/features/llms", "tags": [], "type": "text", "url": "/docs/app/features/llms#llmstxt", "content": "No boilerplate or styling—just the outline"}, "264": {"id": "/docs/app/features/llms-12", "page_id": "/docs/app/features/llms", "tags": [], "type": "text", "url": "/docs/app/features/llms#llmstxt", "content": "This file is generated at the root of your deployed site, enabling agents to discover it at https://<your-domain>/llms.txt."}, "265": {"id": "/docs/app/features/llms-13", "page_id": "/docs/app/features/llms", "tags": [], "type": "text", "url": "/docs/app/features/llms#llms-fulltxt", "content": "Concatenates the raw MDX/Markdown of every page"}, "266": {"id": "/docs/app/features/llms-14", "page_id": "/docs/app/features/llms", "tags": [], "type": "text", "url": "/docs/app/features/llms#llms-fulltxt", "content": "Preserves headings, paragraphs, code samples, and frontmatter"}, "267": {"id": "/docs/app/features/llms-15", "page_id": "/docs/app/features/llms", "tags": [], "type": "text", "url": "/docs/app/features/llms#llms-fulltxt", "content": "Lets an LLM ingest your entire documentation corpus in a single fetch"}, "268": {"id": "/docs/app/features/llms-16", "page_id": "/docs/app/features/llms", "tags": [], "type": "text", "url": "/docs/app/features/llms#llms-fulltxt", "content": "This file is generated at build time by globbing content/docs/**/*.mdx and joining files in sidebar order."}, "269": {"id": "/docs/app/features/llms-17", "page_id": "/docs/app/features/llms", "tags": [], "type": "text", "url": "/docs/app/features/llms#llmsmdx", "content": "Provides the raw MDX/Markdown content of a requested page"}, "270": {"id": "/docs/app/features/llms-18", "page_id": "/docs/app/features/llms", "tags": [], "type": "text", "url": "/docs/app/features/llms#llmsmdx", "content": "Preserves headings, paragraphs, code samples, and frontmatter"}, "271": {"id": "/docs/app/features/llms-19", "page_id": "/docs/app/features/llms", "tags": [], "type": "text", "url": "/docs/app/features/llms#llmsmdx", "content": "Example: /llms.mdx/app"}, "272": {"id": "/docs/app/features/openapi", "page_id": "/docs/app/features/openapi", "type": "page", "content": "OpenAPI", "tags": [], "url": "/docs/app/features/openapi"}, "273": {"id": "/docs/app/features/openapi-0", "page_id": "/docs/app/features/openapi", "tags": [], "type": "text", "url": "/docs/app/features/openapi", "content": "Generate and document your OpenAPI schema"}, "274": {"id": "/docs/app/features/openapi-1", "page_id": "/docs/app/features/openapi", "type": "heading", "tags": [], "url": "/docs/app/features/openapi#introduction", "content": "Introduction"}, "275": {"id": "/docs/app/features/openapi-2", "page_id": "/docs/app/features/openapi", "type": "heading", "tags": [], "url": "/docs/app/features/openapi#features", "content": "Features"}, "276": {"id": "/docs/app/features/openapi-3", "page_id": "/docs/app/features/openapi", "type": "heading", "tags": [], "url": "/docs/app/features/openapi#changing-the-openapi-schema", "content": "Changing the OpenAPI schema"}, "277": {"id": "/docs/app/features/openapi-4", "page_id": "/docs/app/features/openapi", "tags": [], "type": "text", "url": "/docs/app/features/openapi#introduction", "content": "Fumadocs provides an official OpenAPI integration to generate and document your OpenAPI schema."}, "278": {"id": "/docs/app/features/openapi-5", "page_id": "/docs/app/features/openapi", "tags": [], "type": "text", "url": "/docs/app/features/openapi#features", "content": "The official OpenAPI integration supports:"}, "279": {"id": "/docs/app/features/openapi-6", "page_id": "/docs/app/features/openapi", "tags": [], "type": "text", "url": "/docs/app/features/openapi#features", "content": "Basic API endpoint information"}, "280": {"id": "/docs/app/features/openapi-7", "page_id": "/docs/app/features/openapi", "tags": [], "type": "text", "url": "/docs/app/features/openapi#features", "content": "Interactive API playground"}, "281": {"id": "/docs/app/features/openapi-8", "page_id": "/docs/app/features/openapi", "tags": [], "type": "text", "url": "/docs/app/features/openapi#features", "content": "Example code to send request (in different programming languages)"}, "282": {"id": "/docs/app/features/openapi-9", "page_id": "/docs/app/features/openapi", "tags": [], "type": "text", "url": "/docs/app/features/openapi#features", "content": "Response samples and TypeScript definitions"}, "283": {"id": "/docs/app/features/openapi-10", "page_id": "/docs/app/features/openapi", "tags": [], "type": "text", "url": "/docs/app/features/openapi#features", "content": "Request parameters and body generated from schemas"}, "284": {"id": "/docs/app/features/openapi-11", "page_id": "/docs/app/features/openapi", "tags": [], "type": "text", "url": "/docs/app/features/openapi#changing-the-openapi-schema", "content": "The generate-docs script uses the OpenAPI schema to generate the documentation. You can change the OpenAPI schema by modifying the openapi.json file in content/docs/api-reference of your project."}, "285": {"id": "/docs/app/features/openapi-12", "page_id": "/docs/app/features/openapi", "tags": [], "type": "text", "url": "/docs/app/features/openapi#changing-the-openapi-schema", "content": "Only OpenAPI 3.0 and 3.1 are supported."}, "286": {"id": "/docs/app/features/openapi-13", "page_id": "/docs/app/features/openapi", "tags": [], "type": "text", "url": "/docs/app/features/openapi#changing-the-openapi-schema", "content": "Generate docs with the script:"}, "287": {"id": "/docs/app/guides/adding-a-root-folder", "page_id": "/docs/app/guides/adding-a-root-folder", "type": "page", "content": "Adding a Root Folder", "tags": [], "url": "/docs/app/guides/adding-a-root-folder"}, "288": {"id": "/docs/app/guides/adding-a-root-folder-0", "page_id": "/docs/app/guides/adding-a-root-folder", "tags": [], "type": "text", "url": "/docs/app/guides/adding-a-root-folder", "content": "Learn how to add a new root folder to your documentation"}, "289": {"id": "/docs/app/guides/adding-a-root-folder-1", "page_id": "/docs/app/guides/adding-a-root-folder", "type": "heading", "tags": [], "url": "/docs/app/guides/adding-a-root-folder#introduction", "content": "Introduction"}, "290": {"id": "/docs/app/guides/adding-a-root-folder-2", "page_id": "/docs/app/guides/adding-a-root-folder", "type": "heading", "tags": [], "url": "/docs/app/guides/adding-a-root-folder#steps-to-add-a-new-root-folder", "content": "Steps to Add a New Root Folder"}, "291": {"id": "/docs/app/guides/adding-a-root-folder-3", "page_id": "/docs/app/guides/adding-a-root-folder", "type": "heading", "tags": [], "url": "/docs/app/guides/adding-a-root-folder#step-1-create-a-new-folder", "content": "Step 1: Create a New Folder"}, "292": {"id": "/docs/app/guides/adding-a-root-folder-4", "page_id": "/docs/app/guides/adding-a-root-folder", "type": "heading", "tags": [], "url": "/docs/app/guides/adding-a-root-folder#step-2-update-the-metajson-file", "content": "Step 2: Update the meta.json File"}, "293": {"id": "/docs/app/guides/adding-a-root-folder-5", "page_id": "/docs/app/guides/adding-a-root-folder", "type": "heading", "tags": [], "url": "/docs/app/guides/adding-a-root-folder#step-3-update-the-homepage", "content": "Step 3: Update the Homepage"}, "294": {"id": "/docs/app/guides/adding-a-root-folder-6", "page_id": "/docs/app/guides/adding-a-root-folder", "type": "heading", "tags": [], "url": "/docs/app/guides/adding-a-root-folder#step-4-update-colors-optional", "content": "Step 4: Update Colors (Optional)"}, "295": {"id": "/docs/app/guides/adding-a-root-folder-7", "page_id": "/docs/app/guides/adding-a-root-folder", "type": "heading", "tags": [], "url": "/docs/app/guides/adding-a-root-folder#step-5-create-metajson-in-the-new-folder", "content": "Step 5: Create meta.json in the New Folder"}, "296": {"id": "/docs/app/guides/adding-a-root-folder-8", "page_id": "/docs/app/guides/adding-a-root-folder", "type": "heading", "tags": [], "url": "/docs/app/guides/adding-a-root-folder#step-6-create-a-new-page", "content": "Step 6: Create a New Page"}, "297": {"id": "/docs/app/guides/adding-a-root-folder-9", "page_id": "/docs/app/guides/adding-a-root-folder", "tags": [], "type": "text", "url": "/docs/app/guides/adding-a-root-folder#introduction", "content": "Fumadocs allows you to create new root folders in your documentation. This helps you organize your docs into clear sections or categories. This guide will walk you through adding a new root folder step by step."}, "298": {"id": "/docs/app/guides/adding-a-root-folder-10", "page_id": "/docs/app/guides/adding-a-root-folder", "tags": [], "type": "text", "url": "/docs/app/guides/adding-a-root-folder#step-1-create-a-new-folder", "content": "Create a new folder in the content/docs directory. You can name it anything; this example uses cli."}, "299": {"id": "/docs/app/guides/adding-a-root-folder-11", "page_id": "/docs/app/guides/adding-a-root-folder", "tags": [], "type": "text", "url": "/docs/app/guides/adding-a-root-folder#step-2-update-the-metajson-file", "content": "Open the meta.json file located in the content/docs directory. Add your new folder name to the pages array:"}, "300": {"id": "/docs/app/guides/adding-a-root-folder-12", "page_id": "/docs/app/guides/adding-a-root-folder", "tags": [], "type": "text", "url": "/docs/app/guides/adding-a-root-folder#step-3-update-the-homepage", "content": "Open page.tsx in the app/(home) directory and add a new DocumentationItem:"}, "301": {"id": "/docs/app/guides/adding-a-root-folder-13", "page_id": "/docs/app/guides/adding-a-root-folder", "tags": [], "type": "text", "url": "/docs/app/guides/adding-a-root-folder#step-4-update-colors-optional", "content": "Edit globals.css in the styles directory to define colors for your new folder:"}, "302": {"id": "/docs/app/guides/adding-a-root-folder-14", "page_id": "/docs/app/guides/adding-a-root-folder", "tags": [], "type": "text", "url": "/docs/app/guides/adding-a-root-folder#step-4-update-colors-optional", "content": "Update the base styles to apply the new color:"}, "303": {"id": "/docs/app/guides/adding-a-root-folder-15", "page_id": "/docs/app/guides/adding-a-root-folder", "tags": [], "type": "text", "url": "/docs/app/guides/adding-a-root-folder#step-5-create-metajson-in-the-new-folder", "content": "Create a meta.json file in your new cli folder:"}, "304": {"id": "/docs/app/guides/adding-a-root-folder-16", "page_id": "/docs/app/guides/adding-a-root-folder", "tags": [], "type": "text", "url": "/docs/app/guides/adding-a-root-folder#step-5-create-metajson-in-the-new-folder", "content": "Add the following content to meta.json:"}, "305": {"id": "/docs/app/guides/adding-a-root-folder-17", "page_id": "/docs/app/guides/adding-a-root-folder", "tags": [], "type": "text", "url": "/docs/app/guides/adding-a-root-folder#step-5-create-metajson-in-the-new-folder", "content": "This file defines the metadata for your new folder, including its title, description, and icon."}, "306": {"id": "/docs/app/guides/adding-a-root-folder-18", "page_id": "/docs/app/guides/adding-a-root-folder", "tags": [], "type": "text", "url": "/docs/app/guides/adding-a-root-folder#step-6-create-a-new-page", "content": "Create an index.mdx page in your new cli folder:"}, "307": {"id": "/docs/app/guides/adding-a-root-folder-19", "page_id": "/docs/app/guides/adding-a-root-folder", "tags": [], "type": "text", "url": "/docs/app/guides/adding-a-root-folder#step-6-create-a-new-page", "content": "Add initial content to index.mdx:"}, "308": {"id": "/docs/app/guides/adding-a-root-folder-20", "page_id": "/docs/app/guides/adding-a-root-folder", "tags": [], "type": "text", "url": "/docs/app/guides/adding-a-root-folder#step-6-create-a-new-page", "content": "That's it! You've successfully added a new root folder to your documentation. Now, navigate to your docs website to view your new folder and content."}, "309": {"id": "/docs/api-reference/events/createSpecialEvent", "page_id": "/docs/api-reference/events/createSpecialEvent", "type": "page", "content": "Create special events", "tags": [], "url": "/docs/api-reference/events/createSpecialEvent"}, "310": {"id": "/docs/api-reference/events/createSpecialEvent-0", "page_id": "/docs/api-reference/events/createSpecialEvent", "tags": [], "type": "text", "url": "/docs/api-reference/events/createSpecialEvent", "content": "Creates a new special event for the museum."}, "311": {"id": "/docs/api-reference/events/createSpecialEvent-1", "page_id": "/docs/api-reference/events/createSpecialEvent", "tags": [], "type": "text", "url": "/docs/api-reference/events/createSpecialEvent", "content": "Creates a new special event for the museum."}, "312": {"id": "/docs/api-reference/events/deleteSpecialEvent", "page_id": "/docs/api-reference/events/deleteSpecialEvent", "type": "page", "content": "Delete special event", "tags": [], "url": "/docs/api-reference/events/deleteSpecialEvent"}, "313": {"id": "/docs/api-reference/events/deleteSpecialEvent-0", "page_id": "/docs/api-reference/events/deleteSpecialEvent", "tags": [], "type": "text", "url": "/docs/api-reference/events/deleteSpecialEvent", "content": "Delete a special event from the collection. Allows museum to cancel planned events."}, "314": {"id": "/docs/api-reference/events/deleteSpecialEvent-1", "page_id": "/docs/api-reference/events/deleteSpecialEvent", "tags": [], "type": "text", "url": "/docs/api-reference/events/deleteSpecialEvent", "content": "Delete a special event from the collection. Allows museum to cancel planned events."}, "315": {"id": "/docs/api-reference/events/getSpecialEvent", "page_id": "/docs/api-reference/events/getSpecialEvent", "type": "page", "content": "Get special event", "tags": [], "url": "/docs/api-reference/events/getSpecialEvent"}, "316": {"id": "/docs/api-reference/events/getSpecialEvent-0", "page_id": "/docs/api-reference/events/getSpecialEvent", "tags": [], "type": "text", "url": "/docs/api-reference/events/getSpecialEvent", "content": "Get details about a special event."}, "317": {"id": "/docs/api-reference/events/getSpecialEvent-1", "page_id": "/docs/api-reference/events/getSpecialEvent", "tags": [], "type": "text", "url": "/docs/api-reference/events/getSpecialEvent", "content": "Get details about a special event."}, "318": {"id": "/docs/api-reference/events/listSpecialEvents", "page_id": "/docs/api-reference/events/listSpecialEvents", "type": "page", "content": "List special events", "tags": [], "url": "/docs/api-reference/events/listSpecialEvents"}, "319": {"id": "/docs/api-reference/events/listSpecialEvents-0", "page_id": "/docs/api-reference/events/listSpecialEvents", "tags": [], "type": "text", "url": "/docs/api-reference/events/listSpecialEvents", "content": "Return a list of upcoming special events at the museum."}, "320": {"id": "/docs/api-reference/events/listSpecialEvents-1", "page_id": "/docs/api-reference/events/listSpecialEvents", "tags": [], "type": "text", "url": "/docs/api-reference/events/listSpecialEvents", "content": "Return a list of upcoming special events at the museum."}, "321": {"id": "/docs/api-reference/events/publishNewEvent", "page_id": "/docs/api-reference/events/publishNewEvent", "type": "page", "content": "New special event added", "tags": [], "url": "/docs/api-reference/events/publishNewEvent"}, "322": {"id": "/docs/api-reference/events/publishNewEvent-0", "page_id": "/docs/api-reference/events/publishNewEvent", "tags": [], "type": "text", "url": "/docs/api-reference/events/publishNewEvent", "content": "Publish details of a new or updated event."}, "323": {"id": "/docs/api-reference/events/updateSpecialEvent", "page_id": "/docs/api-reference/events/updateSpecialEvent", "type": "page", "content": "Update special event", "tags": [], "url": "/docs/api-reference/events/updateSpecialEvent"}, "324": {"id": "/docs/api-reference/events/updateSpecialEvent-0", "page_id": "/docs/api-reference/events/updateSpecialEvent", "tags": [], "type": "text", "url": "/docs/api-reference/events/updateSpecialEvent", "content": "Update the details of a special event."}, "325": {"id": "/docs/api-reference/events/updateSpecialEvent-1", "page_id": "/docs/api-reference/events/updateSpecialEvent", "tags": [], "type": "text", "url": "/docs/api-reference/events/updateSpecialEvent", "content": "Update the details of a special event."}, "326": {"id": "/docs/api-reference/operations/getMuseumHours", "page_id": "/docs/api-reference/operations/getMuseumHours", "type": "page", "content": "Get museum hours", "tags": [], "url": "/docs/api-reference/operations/getMuseumHours"}, "327": {"id": "/docs/api-reference/operations/getMuseumHours-0", "page_id": "/docs/api-reference/operations/getMuseumHours", "tags": [], "type": "text", "url": "/docs/api-reference/operations/getMuseumHours", "content": "Get upcoming museum operating hours."}, "328": {"id": "/docs/api-reference/operations/getMuseumHours-1", "page_id": "/docs/api-reference/operations/getMuseumHours", "tags": [], "type": "text", "url": "/docs/api-reference/operations/getMuseumHours", "content": "Get upcoming museum operating hours."}, "329": {"id": "/docs/api-reference/tickets/buyMuseumTickets", "page_id": "/docs/api-reference/tickets/buyMuseumTickets", "type": "page", "content": "Buy museum tickets", "tags": [], "url": "/docs/api-reference/tickets/buyMuseumTickets"}, "330": {"id": "/docs/api-reference/tickets/buyMuseumTickets-0", "page_id": "/docs/api-reference/tickets/buyMuseumTickets", "tags": [], "type": "text", "url": "/docs/api-reference/tickets/buyMuseumTickets", "content": "Purchase museum tickets for general entry or special events."}, "331": {"id": "/docs/api-reference/tickets/buyMuseumTickets-1", "page_id": "/docs/api-reference/tickets/buyMuseumTickets", "tags": [], "type": "text", "url": "/docs/api-reference/tickets/buyMuseumTickets", "content": "Purchase museum tickets for general entry or special events."}, "332": {"id": "/docs/api-reference/tickets/getTicketCode", "page_id": "/docs/api-reference/tickets/getTicketCode", "type": "page", "content": "Get ticket QR code", "tags": [], "url": "/docs/api-reference/tickets/getTicketCode"}, "333": {"id": "/docs/api-reference/tickets/getTicketCode-0", "page_id": "/docs/api-reference/tickets/getTicketCode", "tags": [], "type": "text", "url": "/docs/api-reference/tickets/getTicketCode", "content": "Return an image of your ticket with scannable QR code. Used for event entry."}, "334": {"id": "/docs/api-reference/tickets/getTicketCode-1", "page_id": "/docs/api-reference/tickets/getTicketCode", "tags": [], "type": "text", "url": "/docs/api-reference/tickets/getTicketCode", "content": "Return an image of your ticket with scannable QR code. Used for event entry."}}, "count": 334}, "sorting": {"language": "english", "sortableProperties": ["content", "page_id", "type", "url"], "sortablePropertiesWithTypes": {"content": "string", "page_id": "string", "type": "string", "url": "string"}, "sorts": {"content": {"docs": {"1": 151, "2": 288, "3": 323, "4": 152, "5": 324, "6": 125, "7": 176, "8": 272, "9": 166, "10": 167, "11": 72, "12": 71, "13": 42, "14": 86, "15": 46, "16": 224, "17": 256, "18": 249, "19": 257, "20": 22, "21": 78, "22": 84, "23": 309, "24": 13, "25": 199, "26": 273, "27": 242, "28": 198, "29": 113, "30": 65, "31": 270, "32": 290, "33": 134, "34": 283, "35": 18, "36": 19, "37": 17, "38": 215, "39": 193, "40": 115, "41": 185, "42": 147, "43": 37, "44": 317, "45": 325, "46": 94, "47": 315, "48": 216, "49": 244, "50": 245, "51": 212, "52": 191, "53": 226, "54": 101, "55": 15, "56": 25, "57": 175, "58": 24, "59": 233, "60": 222, "61": 184, "62": 30, "63": 161, "64": 194, "65": 69, "66": 73, "67": 28, "68": 91, "69": 227, "70": 141, "71": 100, "72": 190, "73": 99, "74": 229, "75": 43, "76": 55, "77": 188, "78": 23, "79": 32, "80": 162, "81": 160, "82": 29, "83": 316, "84": 255, "85": 150, "86": 138, "87": 265, "88": 322, "89": 127, "90": 140, "91": 223, "92": 225, "93": 114, "94": 189, "95": 87, "96": 47, "97": 139, "98": 98, "99": 27, "100": 310, "101": 228, "102": 251, "103": 26, "104": 321, "105": 200, "106": 57, "107": 82, "108": 44, "109": 145, "110": 56, "111": 33, "112": 207, "113": 250, "114": 303, "115": 299, "116": 311, "117": 279, "118": 320, "119": 169, "120": 177, "121": 268, "122": 153, "123": 179, "124": 135, "125": 41, "126": 53, "127": 52, "128": 128, "129": 302, "130": 68, "131": 266, "132": 142, "133": 182, "134": 164, "135": 109, "136": 180, "137": 319, "138": 247, "139": 181, "140": 126, "141": 136, "142": 149, "143": 92, "144": 313, "145": 168, "146": 332, "147": 314, "148": 144, "149": 318, "150": 88, "151": 296, "152": 297, "153": 304, "154": 129, "155": 36, "156": 281, "157": 328, "158": 330, "159": 301, "160": 333, "161": 197, "162": 230, "163": 246, "164": 231, "165": 110, "166": 146, "167": 267, "168": 295, "169": 326, "170": 241, "171": 14, "172": 154, "173": 96, "174": 254, "175": 102, "176": 103, "177": 183, "178": 209, "179": 235, "180": 93, "181": 171, "182": 133, "183": 240, "184": 112, "185": 12, "186": 186, "187": 79, "188": 292, "189": 282, "190": 80, "191": 271, "192": 131, "193": 276, "194": 106, "195": 97, "196": 280, "197": 210, "198": 252, "199": 2, "200": 3, "201": 1, "202": 4, "203": 206, "204": 51, "205": 211, "206": 253, "207": 0, "208": 5, "209": 70, "210": 187, "211": 81, "212": 293, "213": 83, "214": 132, "215": 277, "216": 208, "217": 104, "218": 74, "219": 203, "220": 50, "221": 329, "222": 16, "223": 327, "224": 331, "225": 312, "226": 287, "227": 178, "228": 105, "229": 294, "230": 111, "231": 34, "232": 59, "233": 155, "234": 248, "235": 38, "236": 130, "237": 289, "238": 298, "239": 275, "240": 35, "241": 291, "242": 39, "243": 243, "244": 156, "245": 60, "246": 49, "247": 300, "248": 40, "249": 196, "250": 137, "251": 174, "252": 217, "253": 157, "254": 10, "255": 6, "256": 8, "257": 163, "258": 11, "259": 7, "260": 9, "261": 173, "262": 143, "263": 195, "264": 286, "265": 58, "266": 213, "267": 170, "268": 285, "269": 218, "270": 214, "271": 90, "272": 205, "273": 116, "274": 158, "275": 95, "276": 54, "277": 108, "278": 278, "279": 45, "280": 148, "281": 89, "282": 234, "283": 232, "284": 274, "285": 201, "286": 117, "287": 31, "288": 165, "289": 159, "290": 264, "291": 258, "292": 259, "293": 260, "294": 261, "295": 262, "296": 263, "297": 107, "298": 62, "299": 204, "300": 202, "301": 85, "302": 306, "303": 61, "304": 21, "305": 284, "306": 63, "307": 20, "308": 269, "309": 64, "310": 66, "311": 67, "312": 77, "313": 75, "314": 76, "315": 121, "316": 118, "317": 119, "318": 172, "319": 236, "320": 237, "321": 192, "322": 219, "323": 305, "324": 307, "325": 308, "326": 120, "327": 123, "328": 124, "329": 48, "330": 220, "331": 221, "332": 122, "333": 238, "334": 239}, "orderedDocs": [[207, "./(group-name)/page.mdx"], [201, "./dir/index.mdx"], [199, "./dir/page.mdx"], [200, "['dir', 'page']"], [202, "['dir']"], [208, "['page']"], [255, "/llms-full.txt"], [259, "/llms-full.txt: a full Markdown dump of every document"], [256, "/llms.mdx/*"], [260, "/llms.mdx/*: the raw MDX/Markdown content of a requested page"], [254, "/llms.txt"], [258, "/llms.txt: a concise sitemap with titles, URLs, and summaries"], [185, "A MDX or Markdown file, you can customize its frontmatter."], [24, "A minimum version of Node.js 18 is required. Note that Node.js 23.1 might have issues with the Next.js production build."], [171, "A shared convention for organizing your documents"], [55, "Activity log now includes more granular details for API usage."], [222, "Add a ... item to include remaining pages (sorted alphabetically), or z...a for descending order."], [37, "Add code directly to your docs with syntax highlighting."], [35, "Add content directly in your files using MDX syntax and React components. You can use built-in components or create your own."], [36, "Add content to your docs with MDX syntax."], [307, "Add initial content to index.mdx:"], [304, "Add the following content to meta.json:"], [20, "Add your content"], [78, "Added batch actions for checklists and subtasks."], [58, "Added gesture support for calendar rescheduling."], [56, "Added keyboard shortcuts to the dashboard for quicker navigation."], [103, "Added native support for Notion links in task descriptions."], [99, "Added support for exporting user activity logs as CSV."], [67, "Added time tracking summary by user and project."], [82, "Added user deactivation audit events."], [62, "Added webhook event for task reassignment."], [287, "Adding a Root Folder"], [79, "Admins can now view login history per user."], [111, "Advanced"], [231, "AI Search"], [240, "AI Search uses the OpenAI API to process user queries. Instead of Retrieval-Augmented Generation (RAG), it relies on Web Search for external information."], [155, "An anchor is automatically applied to each heading, it sanitizes invalid characters like spaces. (e.g. Hello World to hello-world)"], [43, "Analytics & Reporting"], [235, "Application Configuration"], [242, "Async Mode"], [248, "Async Mode introduces some limitations to MDX features:"], [125, "Auto Links"], [13, "Auto-generate interactive endpoint docs straight from your OpenAPI spec."], [75, "Backend job processing now has retry logic with alerting on failure."], [108, "Basic"], [279, "Basic API endpoint information"], [15, "Browse our showcase for creative ideas and best practices."], [96, "Bug fix: File links no longer accessible after permission removal."], [329, "Buy museum tickets"], [246, "By default, all Markdown and MDX files must be precompiled, even when running the development server. This requirement can increase startup time for large documentation sites."], [220, "By default, folder items are sorted alphabetically."], [204, "By default, putting a file into folder will change its slugs.\nYou can wrap the folder name in parentheses to avoid impacting the slugs of child files."], [127, "Callouts"], [126, "Cards"], [276, "Changing the OpenAPI schema"], [76, "Cleaned up deprecated API endpoints — refer to migration guide if needed."], [110, "Code Block"], [106, "Code Blocks"], [265, "Concatenates the raw MDX/Markdown of every page"], [232, "Configure and use AI-powered search in your documentation"], [245, "Constraints"], [303, "Create a meta.json file in your new cli folder:"], [298, "Create a new folder in the content/docs directory. You can name it anything; this example uses cli."], [306, "Create an index.mdx page in your new cli folder:"], [309, "Create special events"], [30, "Create your first MDX file in the docs folder:"], [310, "Creates a new special event for the museum."], [311, "Creates a new special event for the museum."], [130, "Custom Anchor"], [65, "Custom reports can now be scheduled and emailed automatically."], [209, "Customize folders by creating a meta.json file in the folder."], [12, "Customize your documentation by applying your brand colors and styles."], [11, "Customize your documentation to reflect your brand and include meaningful content to maximize user engagement and conversions."], [66, "Dashboard now supports drill-down charts for task status and team performance."], [218, "defaultOpen"], [313, "Delete a special event from the collection. Allows museum to cancel planned events."], [314, "Delete a special event from the collection. Allows museum to cancel planned events."], [312, "Delete special event"], [21, "Deploy your changes"], [187, "description"], [190, "description"], [211, "description"], [107, "Display inline code and code blocks"], [213, "Display name"], [22, "Docker Deployment"], [301, "Edit globals.css in the styles directory to define colors for your new folder:"], [14, "Embed interactive elements and guides to improve user engagement."], [95, "Enforced minimum password strength requirements across all users."], [150, "error"], [281, "Example code to send request (in different programming languages)"], [271, "Example: /llms.mdx/app"], [68, "Export options now include PDF format with improved styling."], [143, "External links will get the default rel=\"noreferrer noopener\" target=\"_blank\" attributes for security."], [180, "Extract"], [46, "Feature Updates"], [275, "Features"], [173, "File"], [195, "Fill all available space on the page (Fumadocs UI)"], [98, "Fixed a bug where recurring events were duplicated when edited in bulk."], [73, "Fixed edge-case bugs reported during November release."], [71, "Fixed inconsistent behavior of dropdowns in Safari."], [54, "Fixed issue where some users were not receiving 2FA codes via SMS."], [175, "Folder"], [176, "Folder Group"], [217, "Folder items (see below)"], [228, "For example, when you are opening a root folder framework, the other folders (e.g. headless) are not shown on the sidebar and other navigation elements."], [194, "full"], [297, "Fumadocs allows you to create new root folders in your documentation. This helps you organize your docs into clear sections or categories. This guide will walk you through adding a new root folder step by step."], [277, "Fumadocs provides an official OpenAPI integration to generate and document your OpenAPI schema."], [135, "Fumadocs provides many useful extensions to MDX, a markup language. Here is a brief introduction to the default MDX syntax of Fumadocs."], [165, "Fumadocs supports LaTeX through the Latex component."], [230, "Fumadocs UI renders root folders as Sidebar\nTabs, which\nallows user to switch between them."], [184, "Fumadocs uses a file-system based routing system to organize your documents. This allows you to create a clear and consistent structure for your documentation, making it easier for users to navigate and find the information they need."], [29, "Fumadocs uses MDX for documentation, allowing you to write Markdown combined with React components. Add your content inside the content/docs directory. The structure is similar to a standard Next.js app, making it easy to navigate and manage your files. To learn more about MDX, check out the MDX page."], [93, "GDPR compliance updates: added data export + delete requests UI."], [40, "General Improvements"], [273, "Generate and document your OpenAPI schema"], [286, "Generate docs with the script:"], [316, "Get details about a special event."], [317, "Get details about a special event."], [326, "Get museum hours"], [315, "Get special event"], [332, "Get ticket QR code"], [327, "Get upcoming museum operating hours."], [328, "Get upcoming museum operating hours."], [6, "Getting Started"], [140, "GFM (GitHub Flavored Markdown) is also supported, see GFM Specification."], [89, "Global search now supports filters for date ranges, users, and tags."], [128, "Headings"], [154, "Hello World"], [236, "How Queries Are Handled"], [192, "icon"], [214, "icon"], [182, "Icons"], [33, "If you want to deploy your Fumadocs app using Docker with Fumadocs MDX configured, make sure to add the source.config.ts file to the WORKDIR in the Dockerfile.\nThe following snippet is taken from the official Next.js Dockerfile Example:"], [124, "Images"], [141, "Images are automatically optimized for next/image."], [250, "Images must be referenced using URLs (e.g. /images/test.png). Avoid relative file paths like ./image.png. Place your images inside the public folder and reference them via their public URL path."], [86, "Improved calendar drag-and-drop accuracy for overlapping events."], [97, "Improved load times on dashboard by 25% through API response caching."], [90, "Improved relevance ranking for file/document results."], [70, "Improved screen reader support for form fields and buttons."], [132, "Include"], [262, "Includes optional one-line descriptions from frontmatter"], [148, "info (default)"], [109, "Inline Code"], [166, "Inline: c = \\pm\\sqrt{a^2 + b^2}"], [42, "Integration Upgrades"], [280, "Interactive API playground"], [142, "Internal links use the next/link component to allow prefetching and avoid hard-reload."], [85, "Introduced “Focus Mode” – hides sidebar and notifications for distraction-free work."], [1, "Introduction"], [4, "Introduction"], [122, "Introduction"], [172, "Introduction"], [233, "Introduction"], [244, "Introduction"], [253, "Introduction"], [274, "Introduction"], [289, "Introduction"], [81, "Invite flow redesigned to allow bulk user imports from CSV."], [63, "Jira integration now includes sprint sync and story point mapping."], [80, "Kanban board now remembers last viewed filters and column order."], [257, "Large language models waste tokens crawling HTML, JS, and site chrome. By shipping a few simple endpoints, you can provide a more efficient way for LLMs to access your documentation."], [134, "LaTeX"], [288, "Learn how to add a new root folder to your documentation"], [9, "Learn how to set up your docs for easy local development."], [10, "Learn how to structure your .mdx files and folders to define the sidebar\nlayout in Fumadocs"], [145, "Learn more about caching in Next.js"], [119, "Learn more about Twoslash notations."], [267, "Lets an LLM ingest your entire documentation corpus in a single fetch"], [181, "Link"], [318, "List special events"], [261, "Lists each section and page in your documentation"], [251, "LLM Support"], [57, "Major performance optimizations for older Android devices."], [7, "Make it yours"], [120, "Markdown Syntax"], [227, "Marks the folder as a root folder, only items in the opened root folder will be considered."], [123, "MDX"], [136, "MDX is not the only supported format of Fumadocs. In fact, you can use any renderers such as next-mdx-remote or CMS."], [139, "MDX Syntax."], [133, "Mermaid"], [177, "Meta"], [61, "Microsoft Teams integration now supports deep-linking to projects."], [41, "Mobile Enhancements"], [186, "name"], [210, "name"], [77, "New “Smart Labels” automatically categorize tasks based on content."], [94, "New admin-level controls for session expiration and login limits."], [72, "New color contrast settings for better visibility in dark mode."], [52, "New Integrations"], [321, "New special event added"], [39, "New updates and improvements"], [64, "New Zapier triggers for completed tasks and file uploads."], [263, "No boilerplate or styling—just the outline"], [249, "No import/export statements are allowed inside MDX files. If you need to use custom components, pass them through the components prop instead."], [161, "Note that you can add MDX components instead of importing them in MDX files."], [28, "npm run devpnpm run devyarn devbun run dev"], [25, "npx create-next-app -e https://github.com/techwithanirudh/fumadocs-starternpx create-next-app -e https://github.com/techwithanirudh/fumadocs-starter --use-pnpmnpx create-next-app -e https://github.com/techwithanirudh/fumadocs-starter --use-yarnnpx create-next-app -e https://github.com/techwithanirudh/fumadocs-starter --use-bun"], [105, "OAuth flow improved for Google and Microsoft accounts."], [285, "Only OpenAPI 3.0 and 3.1 are supported."], [300, "Open page.tsx in the app/(home) directory and add a new DocumentationItem:"], [219, "Open the folder by default"], [299, "Open the meta.json file located in the content/docs directory. Add your new folder name to the pages array:"], [272, "OpenAPI"], [203, "Organize multiple pages, you can create a Meta file to customize folders."], [112, "Package Install"], [216, "pages"], [178, "Pages"], [197, "path (relative to content folder)"], [205, "path (relative to content folder)"], [51, "Performance & Quality Updates"], [266, "Preserves headings, paragraphs, code samples, and frontmatter"], [270, "Preserves headings, paragraphs, code samples, and frontmatter"], [38, "Product Updates"], [48, "Productivity Tools"], [252, "Provide AI-friendly endpoints to assist large language models"], [269, "Provides the raw MDX/Markdown content of a requested page"], [322, "Publish details of a new or updated event."], [330, "Purchase museum tickets for general entry or special events."], [331, "Purchase museum tickets for general entry or special events."], [60, "Push notification reliability improved for background updates."], [91, "Quick navigation panel added (press / to activate)."], [16, "Quickstart"], [92, "Recently viewed items show up in search suggestions."], [53, "Redesigned notification settings for better control over email and in-app alerts."], [69, "Redesigned sidebar with collapsible sections for cleaner navigation."], [101, "Reduced idle memory usage in background sync tasks."], [74, "Reduced server response latency during peak hours by 15%."], [162, "Reference another file (can also be a Markdown/MDX document).\nSpecify the target file path in <include> tag (relative to the MDX file itself)."], [164, "Rendering diagrams in your docs"], [283, "Request parameters and body generated from schemas"], [59, "Resolved an issue causing data sync delays when switching between networks."], [282, "Response samples and TypeScript definitions"], [179, "Rest"], [319, "Return a list of upcoming special events at the museum."], [320, "Return a list of upcoming special events at the museum."], [333, "Return an image of your ticket with scannable QR code. Used for event entry."], [334, "Return an image of your ticket with scannable QR code. Used for event entry."], [183, "Root Folder"], [170, "Routing"], [27, "Run the app in development mode and open http://localhost:3000/docs/app in your browser."], [243, "Runtime compilation of content files."], [49, "Search & Navigation"], [50, "Security & Compliance"], [163, "See other usages."], [138, "See:"], [234, "Setting Up the API Key"], [18, "Setup your development environment"], [113, "Shiki Transformers"], [102, "Slack integration now supports direct replies to alerts."], [198, "slugs"], [206, "slugs"], [174, "Slugs"], [84, "SSO login time reduced by ~40% with token reuse."], [17, "Start building awesome documentation in under 5 minutes"], [19, "Start the development server"], [291, "Step 1: Create a New Folder"], [292, "Step 2: Update the meta.json File"], [293, "Step 3: Update the Homepage"], [294, "Step 4: Update Colors (Optional)"], [295, "Step 5: Create meta.json in the New Folder"], [296, "Step 6: Create a New Page"], [290, "Steps to Add a New Root Folder"], [87, "Sticky notes now support markdown formatting."], [131, "Tab Groups"], [167, "Taylor Expansion (expressing holomorphic function f(x) in power series):"], [121, "Text, title, and styling in standard markdown"], [308, "That's it! You've successfully added a new root folder to your documentation. Now, navigate to your docs website to view your new folder and content."], [31, "The app organizes content by concerns, e.g., content/docs/api-reference, content/docs/app, content/docs/changelog, etc."], [191, "The description of page"], [8, "The first step to creating amazing documentation is setting up your editing environment."], [26, "The Fumadocs template should now be initialized. You can start development!"], [284, "The generate-docs script uses the OpenAPI schema to generate the documentation. You can change the OpenAPI schema by modifying the openapi.json file in content/docs/api-reference of your project."], [239, "The logic for AI search is pre-configured in the components/fumadocs/ai directory. You can explore or modify the implementation there to suit your needs."], [193, "The name of icon, see Icons"], [215, "The name of icon, see Icons"], [278, "The official OpenAPI integration supports:"], [117, "The package install block automatically detects common package managers (npm, yarn, pnpm) and displays installation commands for each. Users can switch between different package managers using tabs."], [196, "The slugs of a page are generated from its file path."], [156, "The table of contents (TOC) will be generated based on headings, you can also customize the effects of headings:"], [189, "The title of page"], [34, "This ensures Fumadocs MDX can access your configuration file during builds."], [305, "This file defines the metadata for your new folder, including its title, description, and icon."], [268, "This file is generated at build time by globbing content/docs/**/*.mdx and joining files in sidebar order."], [264, "This file is generated at the root of your deployed site, enabling agents to discover it at https://<your-domain>/llms.txt."], [226, "This Fumadocs template converts the icon names to JSX elements in runtime, and renders it as a component."], [2, "This is a page to check fumadocs's OpenAPI example."], [237, "This template comes with built-in AI-powered search capabilities. Although this feature is enabled by default, you’ll need to configure an API key for full functionality."], [32, "This template works out-of-the-box with Vercel or Netlify. You can deploy your documentation site with a single click."], [241, "Tip: You can replace the default OpenAI integration with a custom setup (such as Inkeep) for a more efficient Retrieval-Augmented Generation (RAG) implementation tailored to your content and infrastructure."], [188, "title"], [212, "title"], [229, "title: Fumadocs UI"], [168, "title: <PERSON><PERSON>"], [151, "title: Title"], [152, "title: Title"], [238, "To activate AI search, add your OpenAI API key to the .env file at the root of your project. If you don’t already have one, you can generate a key by signing up at the OpenAI platform."], [115, "To denote a word or phrase as code, enclose it in backticks (`)."], [247, "To improve performance, this template enables Async Mode by default, allowing content files to be compiled at runtime instead."], [159, "To link people to a specific heading, add the heading id to hash fragment: /page#my-heading-id."], [129, "TOC Settings"], [114, "Twoslash Notations"], [153, "type: error"], [323, "Update special event"], [302, "Update the base styles to apply the new color:"], [324, "Update the details of a special event."], [325, "Update the details of a special event."], [23, "Update your docs"], [100, "Updated UI components to match new branding guidelines."], [116, "Use fenced code blocks by enclosing code in three backticks and follow the leading ticks with the programming language of your snippet to get syntax highlighting. Optionally, you can also write the name of your code after the programming language. Syntax Highlighting is supported by default using Rehype Code."], [225, "Use the syntax [Text](url) to insert links, or [Icon][Text](url) to add icon."], [144, "Useful for adding links."], [147, "Useful for adding tips/warnings, it is included by default. You can specify the type of callout:"], [47, "User Management"], [83, "Users can now request access to private projects (pending approval)."], [44, "UX & Accessibility"], [149, "warn"], [137, "We recommend MDX, a superset of Markdown with JSX syntax.\nIt allows you to import components, and use them right in the document, or even export values."], [118, "We support some of the Shiki Transformers, allowing you to highlight/style specific lines."], [104, "Webhooks now include retry logic with exponential backoff."], [88, "Weekly summary emails now include completed tasks and pending reviews."], [3, "Welcome to the OpenAPI example! You can update the openapi in the 'openapi.yml' file."], [5, "Welcome to your new documentation"], [45, "Year-End Stability Release"], [169, "You can actually copy equations on Wikipedia, they will be converted into a KaTeX string when you paste it."], [223, "You can add !name to prevent an item from being included."], [157, "You can add [#slug] to customize heading anchors."], [221, "You can add or control the order of items using pages, items are not included unless they are listed inside."], [158, "You can also chain it with TOC settings like:"], [224, "You can extract the items from a folder with ...folder_name."], [146, "You can include icons too."], [160, "You can use code blocks with the <Tab /> component."]], "type": "string"}, "page_id": {"docs": {"1": 0, "2": 1, "3": 2, "4": 29, "5": 30, "6": 31, "7": 32, "8": 33, "9": 34, "10": 35, "11": 36, "12": 37, "13": 38, "14": 39, "15": 40, "16": 244, "17": 245, "18": 246, "19": 247, "20": 248, "21": 249, "22": 250, "23": 251, "24": 252, "25": 253, "26": 254, "27": 255, "28": 256, "29": 257, "30": 258, "31": 259, "32": 260, "33": 261, "34": 262, "35": 263, "36": 264, "37": 265, "38": 266, "39": 267, "40": 268, "41": 269, "42": 270, "43": 271, "44": 272, "45": 273, "46": 274, "47": 275, "48": 276, "49": 277, "50": 278, "51": 279, "52": 280, "53": 281, "54": 282, "55": 283, "56": 284, "57": 285, "58": 286, "59": 287, "60": 288, "61": 289, "62": 290, "63": 291, "64": 292, "65": 293, "66": 294, "67": 295, "68": 296, "69": 297, "70": 298, "71": 299, "72": 300, "73": 301, "74": 302, "75": 303, "76": 304, "77": 305, "78": 306, "79": 307, "80": 308, "81": 309, "82": 310, "83": 311, "84": 312, "85": 313, "86": 314, "87": 315, "88": 316, "89": 317, "90": 318, "91": 319, "92": 320, "93": 321, "94": 322, "95": 323, "96": 324, "97": 325, "98": 326, "99": 327, "100": 328, "101": 329, "102": 330, "103": 331, "104": 332, "105": 333, "106": 41, "107": 42, "108": 43, "109": 44, "110": 45, "111": 46, "112": 47, "113": 48, "114": 49, "115": 50, "116": 51, "117": 52, "118": 53, "119": 54, "120": 55, "121": 56, "122": 57, "123": 58, "124": 59, "125": 60, "126": 61, "127": 62, "128": 63, "129": 64, "130": 65, "131": 66, "132": 67, "133": 68, "134": 69, "135": 70, "136": 71, "137": 72, "138": 73, "139": 74, "140": 75, "141": 76, "142": 77, "143": 78, "144": 79, "145": 80, "146": 81, "147": 82, "148": 83, "149": 84, "150": 85, "151": 86, "152": 87, "153": 88, "154": 89, "155": 90, "156": 91, "157": 92, "158": 93, "159": 94, "160": 95, "161": 96, "162": 97, "163": 98, "164": 99, "165": 100, "166": 101, "167": 102, "168": 103, "169": 104, "170": 105, "171": 106, "172": 107, "173": 108, "174": 109, "175": 110, "176": 111, "177": 112, "178": 113, "179": 114, "180": 115, "181": 116, "182": 117, "183": 118, "184": 119, "185": 120, "186": 121, "187": 122, "188": 123, "189": 124, "190": 125, "191": 126, "192": 127, "193": 128, "194": 129, "195": 130, "196": 131, "197": 132, "198": 133, "199": 134, "200": 135, "201": 136, "202": 137, "203": 138, "204": 139, "205": 140, "206": 141, "207": 142, "208": 143, "209": 144, "210": 145, "211": 146, "212": 147, "213": 148, "214": 149, "215": 150, "216": 151, "217": 152, "218": 153, "219": 154, "220": 155, "221": 156, "222": 157, "223": 158, "224": 159, "225": 160, "226": 161, "227": 162, "228": 163, "229": 164, "230": 165, "231": 166, "232": 167, "233": 168, "234": 169, "235": 170, "236": 171, "237": 172, "238": 173, "239": 174, "240": 175, "241": 176, "242": 177, "243": 178, "244": 179, "245": 180, "246": 181, "247": 182, "248": 183, "249": 184, "250": 185, "251": 186, "252": 187, "253": 188, "254": 189, "255": 190, "256": 191, "257": 192, "258": 193, "259": 194, "260": 195, "261": 196, "262": 197, "263": 198, "264": 199, "265": 200, "266": 201, "267": 202, "268": 203, "269": 204, "270": 205, "271": 206, "272": 207, "273": 208, "274": 209, "275": 210, "276": 211, "277": 212, "278": 213, "279": 214, "280": 215, "281": 216, "282": 217, "283": 218, "284": 219, "285": 220, "286": 221, "287": 222, "288": 223, "289": 224, "290": 225, "291": 226, "292": 227, "293": 228, "294": 229, "295": 230, "296": 231, "297": 232, "298": 233, "299": 234, "300": 235, "301": 236, "302": 237, "303": 238, "304": 239, "305": 240, "306": 241, "307": 242, "308": 243, "309": 3, "310": 4, "311": 5, "312": 6, "313": 7, "314": 8, "315": 9, "316": 10, "317": 11, "318": 12, "319": 13, "320": 14, "321": 15, "322": 16, "323": 17, "324": 18, "325": 19, "326": 20, "327": 21, "328": 22, "329": 23, "330": 24, "331": 25, "332": 26, "333": 27, "334": 28}, "orderedDocs": [[1, "/docs/api-reference"], [2, "/docs/api-reference"], [3, "/docs/api-reference"], [309, "/docs/api-reference/events/createSpecialEvent"], [310, "/docs/api-reference/events/createSpecialEvent"], [311, "/docs/api-reference/events/createSpecialEvent"], [312, "/docs/api-reference/events/deleteSpecialEvent"], [313, "/docs/api-reference/events/deleteSpecialEvent"], [314, "/docs/api-reference/events/deleteSpecialEvent"], [315, "/docs/api-reference/events/getSpecialEvent"], [316, "/docs/api-reference/events/getSpecialEvent"], [317, "/docs/api-reference/events/getSpecialEvent"], [318, "/docs/api-reference/events/listSpecialEvents"], [319, "/docs/api-reference/events/listSpecialEvents"], [320, "/docs/api-reference/events/listSpecialEvents"], [321, "/docs/api-reference/events/publishNewEvent"], [322, "/docs/api-reference/events/publishNewEvent"], [323, "/docs/api-reference/events/updateSpecialEvent"], [324, "/docs/api-reference/events/updateSpecialEvent"], [325, "/docs/api-reference/events/updateSpecialEvent"], [326, "/docs/api-reference/operations/getMuseumHours"], [327, "/docs/api-reference/operations/getMuseumHours"], [328, "/docs/api-reference/operations/getMuseumHours"], [329, "/docs/api-reference/tickets/buyMuseumTickets"], [330, "/docs/api-reference/tickets/buyMuseumTickets"], [331, "/docs/api-reference/tickets/buyMuseumTickets"], [332, "/docs/api-reference/tickets/getTicketCode"], [333, "/docs/api-reference/tickets/getTicketCode"], [334, "/docs/api-reference/tickets/getTicketCode"], [4, "/docs/app"], [5, "/docs/app"], [6, "/docs/app"], [7, "/docs/app"], [8, "/docs/app"], [9, "/docs/app"], [10, "/docs/app"], [11, "/docs/app"], [12, "/docs/app"], [13, "/docs/app"], [14, "/docs/app"], [15, "/docs/app"], [106, "/docs/app/essentials/code"], [107, "/docs/app/essentials/code"], [108, "/docs/app/essentials/code"], [109, "/docs/app/essentials/code"], [110, "/docs/app/essentials/code"], [111, "/docs/app/essentials/code"], [112, "/docs/app/essentials/code"], [113, "/docs/app/essentials/code"], [114, "/docs/app/essentials/code"], [115, "/docs/app/essentials/code"], [116, "/docs/app/essentials/code"], [117, "/docs/app/essentials/code"], [118, "/docs/app/essentials/code"], [119, "/docs/app/essentials/code"], [120, "/docs/app/essentials/markdown"], [121, "/docs/app/essentials/markdown"], [122, "/docs/app/essentials/markdown"], [123, "/docs/app/essentials/markdown"], [124, "/docs/app/essentials/markdown"], [125, "/docs/app/essentials/markdown"], [126, "/docs/app/essentials/markdown"], [127, "/docs/app/essentials/markdown"], [128, "/docs/app/essentials/markdown"], [129, "/docs/app/essentials/markdown"], [130, "/docs/app/essentials/markdown"], [131, "/docs/app/essentials/markdown"], [132, "/docs/app/essentials/markdown"], [133, "/docs/app/essentials/markdown"], [134, "/docs/app/essentials/markdown"], [135, "/docs/app/essentials/markdown"], [136, "/docs/app/essentials/markdown"], [137, "/docs/app/essentials/markdown"], [138, "/docs/app/essentials/markdown"], [139, "/docs/app/essentials/markdown"], [140, "/docs/app/essentials/markdown"], [141, "/docs/app/essentials/markdown"], [142, "/docs/app/essentials/markdown"], [143, "/docs/app/essentials/markdown"], [144, "/docs/app/essentials/markdown"], [145, "/docs/app/essentials/markdown"], [146, "/docs/app/essentials/markdown"], [147, "/docs/app/essentials/markdown"], [148, "/docs/app/essentials/markdown"], [149, "/docs/app/essentials/markdown"], [150, "/docs/app/essentials/markdown"], [151, "/docs/app/essentials/markdown"], [152, "/docs/app/essentials/markdown"], [153, "/docs/app/essentials/markdown"], [154, "/docs/app/essentials/markdown"], [155, "/docs/app/essentials/markdown"], [156, "/docs/app/essentials/markdown"], [157, "/docs/app/essentials/markdown"], [158, "/docs/app/essentials/markdown"], [159, "/docs/app/essentials/markdown"], [160, "/docs/app/essentials/markdown"], [161, "/docs/app/essentials/markdown"], [162, "/docs/app/essentials/markdown"], [163, "/docs/app/essentials/markdown"], [164, "/docs/app/essentials/markdown"], [165, "/docs/app/essentials/markdown"], [166, "/docs/app/essentials/markdown"], [167, "/docs/app/essentials/markdown"], [168, "/docs/app/essentials/markdown"], [169, "/docs/app/essentials/markdown"], [170, "/docs/app/essentials/routing"], [171, "/docs/app/essentials/routing"], [172, "/docs/app/essentials/routing"], [173, "/docs/app/essentials/routing"], [174, "/docs/app/essentials/routing"], [175, "/docs/app/essentials/routing"], [176, "/docs/app/essentials/routing"], [177, "/docs/app/essentials/routing"], [178, "/docs/app/essentials/routing"], [179, "/docs/app/essentials/routing"], [180, "/docs/app/essentials/routing"], [181, "/docs/app/essentials/routing"], [182, "/docs/app/essentials/routing"], [183, "/docs/app/essentials/routing"], [184, "/docs/app/essentials/routing"], [185, "/docs/app/essentials/routing"], [186, "/docs/app/essentials/routing"], [187, "/docs/app/essentials/routing"], [188, "/docs/app/essentials/routing"], [189, "/docs/app/essentials/routing"], [190, "/docs/app/essentials/routing"], [191, "/docs/app/essentials/routing"], [192, "/docs/app/essentials/routing"], [193, "/docs/app/essentials/routing"], [194, "/docs/app/essentials/routing"], [195, "/docs/app/essentials/routing"], [196, "/docs/app/essentials/routing"], [197, "/docs/app/essentials/routing"], [198, "/docs/app/essentials/routing"], [199, "/docs/app/essentials/routing"], [200, "/docs/app/essentials/routing"], [201, "/docs/app/essentials/routing"], [202, "/docs/app/essentials/routing"], [203, "/docs/app/essentials/routing"], [204, "/docs/app/essentials/routing"], [205, "/docs/app/essentials/routing"], [206, "/docs/app/essentials/routing"], [207, "/docs/app/essentials/routing"], [208, "/docs/app/essentials/routing"], [209, "/docs/app/essentials/routing"], [210, "/docs/app/essentials/routing"], [211, "/docs/app/essentials/routing"], [212, "/docs/app/essentials/routing"], [213, "/docs/app/essentials/routing"], [214, "/docs/app/essentials/routing"], [215, "/docs/app/essentials/routing"], [216, "/docs/app/essentials/routing"], [217, "/docs/app/essentials/routing"], [218, "/docs/app/essentials/routing"], [219, "/docs/app/essentials/routing"], [220, "/docs/app/essentials/routing"], [221, "/docs/app/essentials/routing"], [222, "/docs/app/essentials/routing"], [223, "/docs/app/essentials/routing"], [224, "/docs/app/essentials/routing"], [225, "/docs/app/essentials/routing"], [226, "/docs/app/essentials/routing"], [227, "/docs/app/essentials/routing"], [228, "/docs/app/essentials/routing"], [229, "/docs/app/essentials/routing"], [230, "/docs/app/essentials/routing"], [231, "/docs/app/features/ai-search"], [232, "/docs/app/features/ai-search"], [233, "/docs/app/features/ai-search"], [234, "/docs/app/features/ai-search"], [235, "/docs/app/features/ai-search"], [236, "/docs/app/features/ai-search"], [237, "/docs/app/features/ai-search"], [238, "/docs/app/features/ai-search"], [239, "/docs/app/features/ai-search"], [240, "/docs/app/features/ai-search"], [241, "/docs/app/features/ai-search"], [242, "/docs/app/features/async-mode"], [243, "/docs/app/features/async-mode"], [244, "/docs/app/features/async-mode"], [245, "/docs/app/features/async-mode"], [246, "/docs/app/features/async-mode"], [247, "/docs/app/features/async-mode"], [248, "/docs/app/features/async-mode"], [249, "/docs/app/features/async-mode"], [250, "/docs/app/features/async-mode"], [251, "/docs/app/features/llms"], [252, "/docs/app/features/llms"], [253, "/docs/app/features/llms"], [254, "/docs/app/features/llms"], [255, "/docs/app/features/llms"], [256, "/docs/app/features/llms"], [257, "/docs/app/features/llms"], [258, "/docs/app/features/llms"], [259, "/docs/app/features/llms"], [260, "/docs/app/features/llms"], [261, "/docs/app/features/llms"], [262, "/docs/app/features/llms"], [263, "/docs/app/features/llms"], [264, "/docs/app/features/llms"], [265, "/docs/app/features/llms"], [266, "/docs/app/features/llms"], [267, "/docs/app/features/llms"], [268, "/docs/app/features/llms"], [269, "/docs/app/features/llms"], [270, "/docs/app/features/llms"], [271, "/docs/app/features/llms"], [272, "/docs/app/features/openapi"], [273, "/docs/app/features/openapi"], [274, "/docs/app/features/openapi"], [275, "/docs/app/features/openapi"], [276, "/docs/app/features/openapi"], [277, "/docs/app/features/openapi"], [278, "/docs/app/features/openapi"], [279, "/docs/app/features/openapi"], [280, "/docs/app/features/openapi"], [281, "/docs/app/features/openapi"], [282, "/docs/app/features/openapi"], [283, "/docs/app/features/openapi"], [284, "/docs/app/features/openapi"], [285, "/docs/app/features/openapi"], [286, "/docs/app/features/openapi"], [287, "/docs/app/guides/adding-a-root-folder"], [288, "/docs/app/guides/adding-a-root-folder"], [289, "/docs/app/guides/adding-a-root-folder"], [290, "/docs/app/guides/adding-a-root-folder"], [291, "/docs/app/guides/adding-a-root-folder"], [292, "/docs/app/guides/adding-a-root-folder"], [293, "/docs/app/guides/adding-a-root-folder"], [294, "/docs/app/guides/adding-a-root-folder"], [295, "/docs/app/guides/adding-a-root-folder"], [296, "/docs/app/guides/adding-a-root-folder"], [297, "/docs/app/guides/adding-a-root-folder"], [298, "/docs/app/guides/adding-a-root-folder"], [299, "/docs/app/guides/adding-a-root-folder"], [300, "/docs/app/guides/adding-a-root-folder"], [301, "/docs/app/guides/adding-a-root-folder"], [302, "/docs/app/guides/adding-a-root-folder"], [303, "/docs/app/guides/adding-a-root-folder"], [304, "/docs/app/guides/adding-a-root-folder"], [305, "/docs/app/guides/adding-a-root-folder"], [306, "/docs/app/guides/adding-a-root-folder"], [307, "/docs/app/guides/adding-a-root-folder"], [308, "/docs/app/guides/adding-a-root-folder"], [16, "/docs/app/quickstart"], [17, "/docs/app/quickstart"], [18, "/docs/app/quickstart"], [19, "/docs/app/quickstart"], [20, "/docs/app/quickstart"], [21, "/docs/app/quickstart"], [22, "/docs/app/quickstart"], [23, "/docs/app/quickstart"], [24, "/docs/app/quickstart"], [25, "/docs/app/quickstart"], [26, "/docs/app/quickstart"], [27, "/docs/app/quickstart"], [28, "/docs/app/quickstart"], [29, "/docs/app/quickstart"], [30, "/docs/app/quickstart"], [31, "/docs/app/quickstart"], [32, "/docs/app/quickstart"], [33, "/docs/app/quickstart"], [34, "/docs/app/quickstart"], [35, "/docs/app/quickstart"], [36, "/docs/app/quickstart"], [37, "/docs/app/quickstart"], [38, "/docs/changelog"], [39, "/docs/changelog"], [40, "/docs/changelog"], [41, "/docs/changelog"], [42, "/docs/changelog"], [43, "/docs/changelog"], [44, "/docs/changelog"], [45, "/docs/changelog"], [46, "/docs/changelog"], [47, "/docs/changelog"], [48, "/docs/changelog"], [49, "/docs/changelog"], [50, "/docs/changelog"], [51, "/docs/changelog"], [52, "/docs/changelog"], [53, "/docs/changelog"], [54, "/docs/changelog"], [55, "/docs/changelog"], [56, "/docs/changelog"], [57, "/docs/changelog"], [58, "/docs/changelog"], [59, "/docs/changelog"], [60, "/docs/changelog"], [61, "/docs/changelog"], [62, "/docs/changelog"], [63, "/docs/changelog"], [64, "/docs/changelog"], [65, "/docs/changelog"], [66, "/docs/changelog"], [67, "/docs/changelog"], [68, "/docs/changelog"], [69, "/docs/changelog"], [70, "/docs/changelog"], [71, "/docs/changelog"], [72, "/docs/changelog"], [73, "/docs/changelog"], [74, "/docs/changelog"], [75, "/docs/changelog"], [76, "/docs/changelog"], [77, "/docs/changelog"], [78, "/docs/changelog"], [79, "/docs/changelog"], [80, "/docs/changelog"], [81, "/docs/changelog"], [82, "/docs/changelog"], [83, "/docs/changelog"], [84, "/docs/changelog"], [85, "/docs/changelog"], [86, "/docs/changelog"], [87, "/docs/changelog"], [88, "/docs/changelog"], [89, "/docs/changelog"], [90, "/docs/changelog"], [91, "/docs/changelog"], [92, "/docs/changelog"], [93, "/docs/changelog"], [94, "/docs/changelog"], [95, "/docs/changelog"], [96, "/docs/changelog"], [97, "/docs/changelog"], [98, "/docs/changelog"], [99, "/docs/changelog"], [100, "/docs/changelog"], [101, "/docs/changelog"], [102, "/docs/changelog"], [103, "/docs/changelog"], [104, "/docs/changelog"], [105, "/docs/changelog"]], "type": "string"}, "type": {"docs": {"1": 74, "2": 95, "3": 96, "4": 75, "5": 97, "6": 0, "7": 1, "8": 98, "9": 99, "10": 100, "11": 101, "12": 102, "13": 103, "14": 104, "15": 105, "16": 76, "17": 106, "18": 2, "19": 3, "20": 4, "21": 5, "22": 6, "23": 7, "24": 107, "25": 108, "26": 109, "27": 110, "28": 111, "29": 112, "30": 113, "31": 114, "32": 115, "33": 116, "34": 117, "35": 118, "36": 119, "37": 120, "38": 77, "39": 121, "40": 8, "41": 9, "42": 10, "43": 11, "44": 12, "45": 13, "46": 14, "47": 15, "48": 16, "49": 17, "50": 18, "51": 19, "52": 20, "53": 122, "54": 123, "55": 124, "56": 125, "57": 126, "58": 127, "59": 128, "60": 129, "61": 130, "62": 131, "63": 132, "64": 133, "65": 134, "66": 135, "67": 136, "68": 137, "69": 138, "70": 139, "71": 140, "72": 141, "73": 142, "74": 143, "75": 144, "76": 145, "77": 146, "78": 147, "79": 148, "80": 149, "81": 150, "82": 151, "83": 152, "84": 153, "85": 154, "86": 155, "87": 156, "88": 157, "89": 158, "90": 159, "91": 160, "92": 161, "93": 162, "94": 163, "95": 164, "96": 165, "97": 166, "98": 167, "99": 168, "100": 169, "101": 170, "102": 171, "103": 172, "104": 173, "105": 174, "106": 78, "107": 175, "108": 21, "109": 22, "110": 23, "111": 24, "112": 25, "113": 26, "114": 27, "115": 176, "116": 177, "117": 178, "118": 179, "119": 180, "120": 79, "121": 181, "122": 28, "123": 29, "124": 30, "125": 31, "126": 32, "127": 33, "128": 34, "129": 35, "130": 36, "131": 37, "132": 38, "133": 39, "134": 40, "135": 182, "136": 183, "137": 184, "138": 185, "139": 186, "140": 187, "141": 188, "142": 189, "143": 190, "144": 191, "145": 192, "146": 193, "147": 194, "148": 195, "149": 196, "150": 197, "151": 198, "152": 199, "153": 200, "154": 201, "155": 202, "156": 203, "157": 204, "158": 205, "159": 206, "160": 207, "161": 208, "162": 209, "163": 210, "164": 211, "165": 212, "166": 213, "167": 214, "168": 215, "169": 216, "170": 80, "171": 217, "172": 41, "173": 42, "174": 43, "175": 44, "176": 45, "177": 46, "178": 47, "179": 48, "180": 49, "181": 50, "182": 51, "183": 52, "184": 218, "185": 219, "186": 220, "187": 221, "188": 222, "189": 223, "190": 224, "191": 225, "192": 226, "193": 227, "194": 228, "195": 229, "196": 230, "197": 231, "198": 232, "199": 233, "200": 234, "201": 235, "202": 236, "203": 237, "204": 238, "205": 239, "206": 240, "207": 241, "208": 242, "209": 243, "210": 244, "211": 245, "212": 246, "213": 247, "214": 248, "215": 249, "216": 250, "217": 251, "218": 252, "219": 253, "220": 254, "221": 255, "222": 256, "223": 257, "224": 258, "225": 259, "226": 260, "227": 261, "228": 262, "229": 263, "230": 264, "231": 81, "232": 265, "233": 53, "234": 54, "235": 55, "236": 56, "237": 266, "238": 267, "239": 268, "240": 269, "241": 270, "242": 82, "243": 271, "244": 57, "245": 58, "246": 272, "247": 273, "248": 274, "249": 275, "250": 276, "251": 83, "252": 277, "253": 59, "254": 60, "255": 61, "256": 62, "257": 278, "258": 279, "259": 280, "260": 281, "261": 282, "262": 283, "263": 284, "264": 285, "265": 286, "266": 287, "267": 288, "268": 289, "269": 290, "270": 291, "271": 292, "272": 84, "273": 293, "274": 63, "275": 64, "276": 65, "277": 294, "278": 295, "279": 296, "280": 297, "281": 298, "282": 299, "283": 300, "284": 301, "285": 302, "286": 303, "287": 85, "288": 304, "289": 66, "290": 67, "291": 68, "292": 69, "293": 70, "294": 71, "295": 72, "296": 73, "297": 305, "298": 306, "299": 307, "300": 308, "301": 309, "302": 310, "303": 311, "304": 312, "305": 313, "306": 314, "307": 315, "308": 316, "309": 86, "310": 317, "311": 318, "312": 87, "313": 319, "314": 320, "315": 88, "316": 321, "317": 322, "318": 89, "319": 323, "320": 324, "321": 90, "322": 325, "323": 91, "324": 326, "325": 327, "326": 92, "327": 328, "328": 329, "329": 93, "330": 330, "331": 331, "332": 94, "333": 332, "334": 333}, "orderedDocs": [[6, "heading"], [7, "heading"], [18, "heading"], [19, "heading"], [20, "heading"], [21, "heading"], [22, "heading"], [23, "heading"], [40, "heading"], [41, "heading"], [42, "heading"], [43, "heading"], [44, "heading"], [45, "heading"], [46, "heading"], [47, "heading"], [48, "heading"], [49, "heading"], [50, "heading"], [51, "heading"], [52, "heading"], [108, "heading"], [109, "heading"], [110, "heading"], [111, "heading"], [112, "heading"], [113, "heading"], [114, "heading"], [122, "heading"], [123, "heading"], [124, "heading"], [125, "heading"], [126, "heading"], [127, "heading"], [128, "heading"], [129, "heading"], [130, "heading"], [131, "heading"], [132, "heading"], [133, "heading"], [134, "heading"], [172, "heading"], [173, "heading"], [174, "heading"], [175, "heading"], [176, "heading"], [177, "heading"], [178, "heading"], [179, "heading"], [180, "heading"], [181, "heading"], [182, "heading"], [183, "heading"], [233, "heading"], [234, "heading"], [235, "heading"], [236, "heading"], [244, "heading"], [245, "heading"], [253, "heading"], [254, "heading"], [255, "heading"], [256, "heading"], [274, "heading"], [275, "heading"], [276, "heading"], [289, "heading"], [290, "heading"], [291, "heading"], [292, "heading"], [293, "heading"], [294, "heading"], [295, "heading"], [296, "heading"], [1, "page"], [4, "page"], [16, "page"], [38, "page"], [106, "page"], [120, "page"], [170, "page"], [231, "page"], [242, "page"], [251, "page"], [272, "page"], [287, "page"], [309, "page"], [312, "page"], [315, "page"], [318, "page"], [321, "page"], [323, "page"], [326, "page"], [329, "page"], [332, "page"], [2, "text"], [3, "text"], [5, "text"], [8, "text"], [9, "text"], [10, "text"], [11, "text"], [12, "text"], [13, "text"], [14, "text"], [15, "text"], [17, "text"], [24, "text"], [25, "text"], [26, "text"], [27, "text"], [28, "text"], [29, "text"], [30, "text"], [31, "text"], [32, "text"], [33, "text"], [34, "text"], [35, "text"], [36, "text"], [37, "text"], [39, "text"], [53, "text"], [54, "text"], [55, "text"], [56, "text"], [57, "text"], [58, "text"], [59, "text"], [60, "text"], [61, "text"], [62, "text"], [63, "text"], [64, "text"], [65, "text"], [66, "text"], [67, "text"], [68, "text"], [69, "text"], [70, "text"], [71, "text"], [72, "text"], [73, "text"], [74, "text"], [75, "text"], [76, "text"], [77, "text"], [78, "text"], [79, "text"], [80, "text"], [81, "text"], [82, "text"], [83, "text"], [84, "text"], [85, "text"], [86, "text"], [87, "text"], [88, "text"], [89, "text"], [90, "text"], [91, "text"], [92, "text"], [93, "text"], [94, "text"], [95, "text"], [96, "text"], [97, "text"], [98, "text"], [99, "text"], [100, "text"], [101, "text"], [102, "text"], [103, "text"], [104, "text"], [105, "text"], [107, "text"], [115, "text"], [116, "text"], [117, "text"], [118, "text"], [119, "text"], [121, "text"], [135, "text"], [136, "text"], [137, "text"], [138, "text"], [139, "text"], [140, "text"], [141, "text"], [142, "text"], [143, "text"], [144, "text"], [145, "text"], [146, "text"], [147, "text"], [148, "text"], [149, "text"], [150, "text"], [151, "text"], [152, "text"], [153, "text"], [154, "text"], [155, "text"], [156, "text"], [157, "text"], [158, "text"], [159, "text"], [160, "text"], [161, "text"], [162, "text"], [163, "text"], [164, "text"], [165, "text"], [166, "text"], [167, "text"], [168, "text"], [169, "text"], [171, "text"], [184, "text"], [185, "text"], [186, "text"], [187, "text"], [188, "text"], [189, "text"], [190, "text"], [191, "text"], [192, "text"], [193, "text"], [194, "text"], [195, "text"], [196, "text"], [197, "text"], [198, "text"], [199, "text"], [200, "text"], [201, "text"], [202, "text"], [203, "text"], [204, "text"], [205, "text"], [206, "text"], [207, "text"], [208, "text"], [209, "text"], [210, "text"], [211, "text"], [212, "text"], [213, "text"], [214, "text"], [215, "text"], [216, "text"], [217, "text"], [218, "text"], [219, "text"], [220, "text"], [221, "text"], [222, "text"], [223, "text"], [224, "text"], [225, "text"], [226, "text"], [227, "text"], [228, "text"], [229, "text"], [230, "text"], [232, "text"], [237, "text"], [238, "text"], [239, "text"], [240, "text"], [241, "text"], [243, "text"], [246, "text"], [247, "text"], [248, "text"], [249, "text"], [250, "text"], [252, "text"], [257, "text"], [258, "text"], [259, "text"], [260, "text"], [261, "text"], [262, "text"], [263, "text"], [264, "text"], [265, "text"], [266, "text"], [267, "text"], [268, "text"], [269, "text"], [270, "text"], [271, "text"], [273, "text"], [277, "text"], [278, "text"], [279, "text"], [280, "text"], [281, "text"], [282, "text"], [283, "text"], [284, "text"], [285, "text"], [286, "text"], [288, "text"], [297, "text"], [298, "text"], [299, "text"], [300, "text"], [301, "text"], [302, "text"], [303, "text"], [304, "text"], [305, "text"], [306, "text"], [307, "text"], [308, "text"], [310, "text"], [311, "text"], [313, "text"], [314, "text"], [316, "text"], [317, "text"], [319, "text"], [320, "text"], [322, "text"], [324, "text"], [325, "text"], [327, "text"], [328, "text"], [330, "text"], [331, "text"], [333, "text"], [334, "text"]], "type": "string"}, "url": {"docs": {"1": 0, "2": 1, "3": 2, "4": 29, "5": 30, "6": 256, "7": 260, "8": 257, "9": 258, "10": 259, "11": 261, "12": 262, "13": 263, "14": 264, "15": 265, "16": 234, "17": 235, "18": 245, "19": 249, "20": 236, "21": 240, "22": 242, "23": 252, "24": 246, "25": 247, "26": 248, "27": 250, "28": 251, "29": 237, "30": 238, "31": 239, "32": 241, "33": 243, "34": 244, "35": 253, "36": 254, "37": 255, "38": 266, "39": 267, "40": 278, "41": 288, "42": 283, "43": 268, "44": 324, "45": 329, "46": 273, "47": 319, "48": 304, "49": 309, "50": 314, "51": 298, "52": 293, "53": 279, "54": 280, "55": 281, "56": 282, "57": 289, "58": 290, "59": 291, "60": 292, "61": 284, "62": 285, "63": 286, "64": 287, "65": 269, "66": 270, "67": 271, "68": 272, "69": 325, "70": 326, "71": 327, "72": 328, "73": 330, "74": 331, "75": 332, "76": 333, "77": 274, "78": 275, "79": 276, "80": 277, "81": 320, "82": 321, "83": 322, "84": 323, "85": 305, "86": 306, "87": 307, "88": 308, "89": 310, "90": 311, "91": 312, "92": 313, "93": 315, "94": 316, "95": 317, "96": 318, "97": 299, "98": 300, "99": 301, "100": 302, "101": 303, "102": 294, "103": 295, "104": 296, "105": 297, "106": 31, "107": 32, "108": 34, "109": 37, "110": 35, "111": 33, "112": 39, "113": 41, "114": 43, "115": 38, "116": 36, "117": 40, "118": 42, "119": 44, "120": 45, "121": 46, "122": 74, "123": 83, "124": 69, "125": 47, "126": 59, "127": 50, "128": 67, "129": 93, "130": 63, "131": 90, "132": 71, "133": 88, "134": 77, "135": 75, "136": 76, "137": 84, "138": 85, "139": 86, "140": 87, "141": 70, "142": 48, "143": 49, "144": 60, "145": 61, "146": 62, "147": 51, "148": 52, "149": 53, "150": 54, "151": 55, "152": 56, "153": 57, "154": 58, "155": 68, "156": 94, "157": 64, "158": 65, "159": 66, "160": 91, "161": 92, "162": 72, "163": 73, "164": 89, "165": 78, "166": 79, "167": 80, "168": 81, "169": 82, "170": 95, "171": 96, "172": 121, "173": 99, "174": 148, "175": 111, "176": 113, "177": 125, "178": 137, "179": 140, "180": 97, "181": 123, "182": 119, "183": 143, "184": 122, "185": 100, "186": 101, "187": 102, "188": 103, "189": 104, "190": 105, "191": 106, "192": 107, "193": 108, "194": 109, "195": 110, "196": 149, "197": 150, "198": 151, "199": 152, "200": 153, "201": 154, "202": 155, "203": 112, "204": 114, "205": 115, "206": 116, "207": 117, "208": 118, "209": 126, "210": 127, "211": 128, "212": 129, "213": 130, "214": 131, "215": 132, "216": 133, "217": 134, "218": 135, "219": 136, "220": 138, "221": 139, "222": 141, "223": 142, "224": 98, "225": 124, "226": 120, "227": 144, "228": 145, "229": 146, "230": 147, "231": 156, "232": 157, "233": 163, "234": 165, "235": 158, "236": 160, "237": 164, "238": 166, "239": 159, "240": 161, "241": 162, "242": 167, "243": 168, "244": 173, "245": 169, "246": 174, "247": 175, "248": 170, "249": 171, "250": 172, "251": 176, "252": 177, "253": 178, "254": 192, "255": 183, "256": 188, "257": 179, "258": 180, "259": 181, "260": 182, "261": 193, "262": 194, "263": 195, "264": 196, "265": 184, "266": 185, "267": 186, "268": 187, "269": 189, "270": 190, "271": 191, "272": 197, "273": 198, "274": 210, "275": 203, "276": 199, "277": 211, "278": 204, "279": 205, "280": 206, "281": 207, "282": 208, "283": 209, "284": 200, "285": 201, "286": 202, "287": 212, "288": 213, "289": 214, "290": 233, "291": 216, "292": 218, "293": 220, "294": 222, "295": 225, "296": 229, "297": 215, "298": 217, "299": 219, "300": 221, "301": 223, "302": 224, "303": 226, "304": 227, "305": 228, "306": 230, "307": 231, "308": 232, "309": 3, "310": 4, "311": 5, "312": 6, "313": 7, "314": 8, "315": 9, "316": 10, "317": 11, "318": 12, "319": 13, "320": 14, "321": 15, "322": 16, "323": 17, "324": 18, "325": 19, "326": 20, "327": 21, "328": 22, "329": 23, "330": 24, "331": 25, "332": 26, "333": 27, "334": 28}, "orderedDocs": [[1, "/docs/api-reference"], [2, "/docs/api-reference"], [3, "/docs/api-reference"], [309, "/docs/api-reference/events/createSpecialEvent"], [310, "/docs/api-reference/events/createSpecialEvent"], [311, "/docs/api-reference/events/createSpecialEvent"], [312, "/docs/api-reference/events/deleteSpecialEvent"], [313, "/docs/api-reference/events/deleteSpecialEvent"], [314, "/docs/api-reference/events/deleteSpecialEvent"], [315, "/docs/api-reference/events/getSpecialEvent"], [316, "/docs/api-reference/events/getSpecialEvent"], [317, "/docs/api-reference/events/getSpecialEvent"], [318, "/docs/api-reference/events/listSpecialEvents"], [319, "/docs/api-reference/events/listSpecialEvents"], [320, "/docs/api-reference/events/listSpecialEvents"], [321, "/docs/api-reference/events/publishNewEvent"], [322, "/docs/api-reference/events/publishNewEvent"], [323, "/docs/api-reference/events/updateSpecialEvent"], [324, "/docs/api-reference/events/updateSpecialEvent"], [325, "/docs/api-reference/events/updateSpecialEvent"], [326, "/docs/api-reference/operations/getMuseumHours"], [327, "/docs/api-reference/operations/getMuseumHours"], [328, "/docs/api-reference/operations/getMuseumHours"], [329, "/docs/api-reference/tickets/buyMuseumTickets"], [330, "/docs/api-reference/tickets/buyMuseumTickets"], [331, "/docs/api-reference/tickets/buyMuseumTickets"], [332, "/docs/api-reference/tickets/getTicketCode"], [333, "/docs/api-reference/tickets/getTicketCode"], [334, "/docs/api-reference/tickets/getTicketCode"], [4, "/docs/app"], [5, "/docs/app"], [106, "/docs/app/essentials/code"], [107, "/docs/app/essentials/code"], [111, "/docs/app/essentials/code#advanced"], [108, "/docs/app/essentials/code#basic"], [110, "/docs/app/essentials/code#code-block"], [116, "/docs/app/essentials/code#code-block"], [109, "/docs/app/essentials/code#inline-code"], [115, "/docs/app/essentials/code#inline-code"], [112, "/docs/app/essentials/code#package-install"], [117, "/docs/app/essentials/code#package-install"], [113, "/docs/app/essentials/code#shiki-transformers"], [118, "/docs/app/essentials/code#shiki-transformers"], [114, "/docs/app/essentials/code#twoslash-notations"], [119, "/docs/app/essentials/code#twoslash-notations"], [120, "/docs/app/essentials/markdown"], [121, "/docs/app/essentials/markdown"], [125, "/docs/app/essentials/markdown#auto-links"], [142, "/docs/app/essentials/markdown#auto-links"], [143, "/docs/app/essentials/markdown#auto-links"], [127, "/docs/app/essentials/markdown#callouts"], [147, "/docs/app/essentials/markdown#callouts"], [148, "/docs/app/essentials/markdown#callouts"], [149, "/docs/app/essentials/markdown#callouts"], [150, "/docs/app/essentials/markdown#callouts"], [151, "/docs/app/essentials/markdown#callouts"], [152, "/docs/app/essentials/markdown#callouts"], [153, "/docs/app/essentials/markdown#callouts"], [154, "/docs/app/essentials/markdown#callouts"], [126, "/docs/app/essentials/markdown#cards"], [144, "/docs/app/essentials/markdown#cards"], [145, "/docs/app/essentials/markdown#cards"], [146, "/docs/app/essentials/markdown#cards"], [130, "/docs/app/essentials/markdown#custom-anchor"], [157, "/docs/app/essentials/markdown#custom-anchor"], [158, "/docs/app/essentials/markdown#custom-anchor"], [159, "/docs/app/essentials/markdown#custom-anchor"], [128, "/docs/app/essentials/markdown#headings"], [155, "/docs/app/essentials/markdown#headings"], [124, "/docs/app/essentials/markdown#images"], [141, "/docs/app/essentials/markdown#images"], [132, "/docs/app/essentials/markdown#include"], [162, "/docs/app/essentials/markdown#include"], [163, "/docs/app/essentials/markdown#include"], [122, "/docs/app/essentials/markdown#introduction"], [135, "/docs/app/essentials/markdown#introduction"], [136, "/docs/app/essentials/markdown#introduction"], [134, "/docs/app/essentials/markdown#latex"], [165, "/docs/app/essentials/markdown#latex"], [166, "/docs/app/essentials/markdown#latex"], [167, "/docs/app/essentials/markdown#latex"], [168, "/docs/app/essentials/markdown#latex"], [169, "/docs/app/essentials/markdown#latex"], [123, "/docs/app/essentials/markdown#mdx"], [137, "/docs/app/essentials/markdown#mdx"], [138, "/docs/app/essentials/markdown#mdx"], [139, "/docs/app/essentials/markdown#mdx"], [140, "/docs/app/essentials/markdown#mdx"], [133, "/docs/app/essentials/markdown#mermaid"], [164, "/docs/app/essentials/markdown#mermaid"], [131, "/docs/app/essentials/markdown#tab-groups"], [160, "/docs/app/essentials/markdown#tab-groups"], [161, "/docs/app/essentials/markdown#tab-groups"], [129, "/docs/app/essentials/markdown#toc-settings"], [156, "/docs/app/essentials/markdown#toc-settings"], [170, "/docs/app/essentials/routing"], [171, "/docs/app/essentials/routing"], [180, "/docs/app/essentials/routing#extract"], [224, "/docs/app/essentials/routing#extract"], [173, "/docs/app/essentials/routing#file"], [185, "/docs/app/essentials/routing#file"], [186, "/docs/app/essentials/routing#file"], [187, "/docs/app/essentials/routing#file"], [188, "/docs/app/essentials/routing#file"], [189, "/docs/app/essentials/routing#file"], [190, "/docs/app/essentials/routing#file"], [191, "/docs/app/essentials/routing#file"], [192, "/docs/app/essentials/routing#file"], [193, "/docs/app/essentials/routing#file"], [194, "/docs/app/essentials/routing#file"], [195, "/docs/app/essentials/routing#file"], [175, "/docs/app/essentials/routing#folder"], [203, "/docs/app/essentials/routing#folder"], [176, "/docs/app/essentials/routing#folder-group"], [204, "/docs/app/essentials/routing#folder-group"], [205, "/docs/app/essentials/routing#folder-group"], [206, "/docs/app/essentials/routing#folder-group"], [207, "/docs/app/essentials/routing#folder-group"], [208, "/docs/app/essentials/routing#folder-group"], [182, "/docs/app/essentials/routing#icons"], [226, "/docs/app/essentials/routing#icons"], [172, "/docs/app/essentials/routing#introduction"], [184, "/docs/app/essentials/routing#introduction"], [181, "/docs/app/essentials/routing#link"], [225, "/docs/app/essentials/routing#link"], [177, "/docs/app/essentials/routing#meta"], [209, "/docs/app/essentials/routing#meta"], [210, "/docs/app/essentials/routing#meta"], [211, "/docs/app/essentials/routing#meta"], [212, "/docs/app/essentials/routing#meta"], [213, "/docs/app/essentials/routing#meta"], [214, "/docs/app/essentials/routing#meta"], [215, "/docs/app/essentials/routing#meta"], [216, "/docs/app/essentials/routing#meta"], [217, "/docs/app/essentials/routing#meta"], [218, "/docs/app/essentials/routing#meta"], [219, "/docs/app/essentials/routing#meta"], [178, "/docs/app/essentials/routing#pages"], [220, "/docs/app/essentials/routing#pages"], [221, "/docs/app/essentials/routing#pages"], [179, "/docs/app/essentials/routing#rest"], [222, "/docs/app/essentials/routing#rest"], [223, "/docs/app/essentials/routing#rest"], [183, "/docs/app/essentials/routing#root-folder"], [227, "/docs/app/essentials/routing#root-folder"], [228, "/docs/app/essentials/routing#root-folder"], [229, "/docs/app/essentials/routing#root-folder"], [230, "/docs/app/essentials/routing#root-folder"], [174, "/docs/app/essentials/routing#slugs"], [196, "/docs/app/essentials/routing#slugs"], [197, "/docs/app/essentials/routing#slugs"], [198, "/docs/app/essentials/routing#slugs"], [199, "/docs/app/essentials/routing#slugs"], [200, "/docs/app/essentials/routing#slugs"], [201, "/docs/app/essentials/routing#slugs"], [202, "/docs/app/essentials/routing#slugs"], [231, "/docs/app/features/ai-search"], [232, "/docs/app/features/ai-search"], [235, "/docs/app/features/ai-search#application-configuration"], [239, "/docs/app/features/ai-search#application-configuration"], [236, "/docs/app/features/ai-search#how-queries-are-handled"], [240, "/docs/app/features/ai-search#how-queries-are-handled"], [241, "/docs/app/features/ai-search#how-queries-are-handled"], [233, "/docs/app/features/ai-search#introduction"], [237, "/docs/app/features/ai-search#introduction"], [234, "/docs/app/features/ai-search#setting-up-the-api-key"], [238, "/docs/app/features/ai-search#setting-up-the-api-key"], [242, "/docs/app/features/async-mode"], [243, "/docs/app/features/async-mode"], [245, "/docs/app/features/async-mode#constraints"], [248, "/docs/app/features/async-mode#constraints"], [249, "/docs/app/features/async-mode#constraints"], [250, "/docs/app/features/async-mode#constraints"], [244, "/docs/app/features/async-mode#introduction"], [246, "/docs/app/features/async-mode#introduction"], [247, "/docs/app/features/async-mode#introduction"], [251, "/docs/app/features/llms"], [252, "/docs/app/features/llms"], [253, "/docs/app/features/llms#introduction"], [257, "/docs/app/features/llms#introduction"], [258, "/docs/app/features/llms#introduction"], [259, "/docs/app/features/llms#introduction"], [260, "/docs/app/features/llms#introduction"], [255, "/docs/app/features/llms#llms-fulltxt"], [265, "/docs/app/features/llms#llms-fulltxt"], [266, "/docs/app/features/llms#llms-fulltxt"], [267, "/docs/app/features/llms#llms-fulltxt"], [268, "/docs/app/features/llms#llms-fulltxt"], [256, "/docs/app/features/llms#llmsmdx"], [269, "/docs/app/features/llms#llmsmdx"], [270, "/docs/app/features/llms#llmsmdx"], [271, "/docs/app/features/llms#llmsmdx"], [254, "/docs/app/features/llms#llmstxt"], [261, "/docs/app/features/llms#llmstxt"], [262, "/docs/app/features/llms#llmstxt"], [263, "/docs/app/features/llms#llmstxt"], [264, "/docs/app/features/llms#llmstxt"], [272, "/docs/app/features/openapi"], [273, "/docs/app/features/openapi"], [276, "/docs/app/features/openapi#changing-the-openapi-schema"], [284, "/docs/app/features/openapi#changing-the-openapi-schema"], [285, "/docs/app/features/openapi#changing-the-openapi-schema"], [286, "/docs/app/features/openapi#changing-the-openapi-schema"], [275, "/docs/app/features/openapi#features"], [278, "/docs/app/features/openapi#features"], [279, "/docs/app/features/openapi#features"], [280, "/docs/app/features/openapi#features"], [281, "/docs/app/features/openapi#features"], [282, "/docs/app/features/openapi#features"], [283, "/docs/app/features/openapi#features"], [274, "/docs/app/features/openapi#introduction"], [277, "/docs/app/features/openapi#introduction"], [287, "/docs/app/guides/adding-a-root-folder"], [288, "/docs/app/guides/adding-a-root-folder"], [289, "/docs/app/guides/adding-a-root-folder#introduction"], [297, "/docs/app/guides/adding-a-root-folder#introduction"], [291, "/docs/app/guides/adding-a-root-folder#step-1-create-a-new-folder"], [298, "/docs/app/guides/adding-a-root-folder#step-1-create-a-new-folder"], [292, "/docs/app/guides/adding-a-root-folder#step-2-update-the-metajson-file"], [299, "/docs/app/guides/adding-a-root-folder#step-2-update-the-metajson-file"], [293, "/docs/app/guides/adding-a-root-folder#step-3-update-the-homepage"], [300, "/docs/app/guides/adding-a-root-folder#step-3-update-the-homepage"], [294, "/docs/app/guides/adding-a-root-folder#step-4-update-colors-optional"], [301, "/docs/app/guides/adding-a-root-folder#step-4-update-colors-optional"], [302, "/docs/app/guides/adding-a-root-folder#step-4-update-colors-optional"], [295, "/docs/app/guides/adding-a-root-folder#step-5-create-metajson-in-the-new-folder"], [303, "/docs/app/guides/adding-a-root-folder#step-5-create-metajson-in-the-new-folder"], [304, "/docs/app/guides/adding-a-root-folder#step-5-create-metajson-in-the-new-folder"], [305, "/docs/app/guides/adding-a-root-folder#step-5-create-metajson-in-the-new-folder"], [296, "/docs/app/guides/adding-a-root-folder#step-6-create-a-new-page"], [306, "/docs/app/guides/adding-a-root-folder#step-6-create-a-new-page"], [307, "/docs/app/guides/adding-a-root-folder#step-6-create-a-new-page"], [308, "/docs/app/guides/adding-a-root-folder#step-6-create-a-new-page"], [290, "/docs/app/guides/adding-a-root-folder#steps-to-add-a-new-root-folder"], [16, "/docs/app/quickstart"], [17, "/docs/app/quickstart"], [20, "/docs/app/quickstart#add-your-content"], [29, "/docs/app/quickstart#add-your-content"], [30, "/docs/app/quickstart#add-your-content"], [31, "/docs/app/quickstart#add-your-content"], [21, "/docs/app/quickstart#deploy-your-changes"], [32, "/docs/app/quickstart#deploy-your-changes"], [22, "/docs/app/quickstart#docker-deployment"], [33, "/docs/app/quickstart#docker-deployment"], [34, "/docs/app/quickstart#docker-deployment"], [18, "/docs/app/quickstart#setup-your-development-environment"], [24, "/docs/app/quickstart#setup-your-development-environment"], [25, "/docs/app/quickstart#setup-your-development-environment"], [26, "/docs/app/quickstart#setup-your-development-environment"], [19, "/docs/app/quickstart#start-the-development-server"], [27, "/docs/app/quickstart#start-the-development-server"], [28, "/docs/app/quickstart#start-the-development-server"], [23, "/docs/app/quickstart#update-your-docs"], [35, "/docs/app/quickstart#update-your-docs"], [36, "/docs/app/quickstart#update-your-docs"], [37, "/docs/app/quickstart#update-your-docs"], [6, "/docs/app#getting-started"], [8, "/docs/app#getting-started"], [9, "/docs/app#getting-started"], [10, "/docs/app#getting-started"], [7, "/docs/app#make-it-yours"], [11, "/docs/app#make-it-yours"], [12, "/docs/app#make-it-yours"], [13, "/docs/app#make-it-yours"], [14, "/docs/app#make-it-yours"], [15, "/docs/app#make-it-yours"], [38, "/docs/changelog"], [39, "/docs/changelog"], [43, "/docs/changelog#analytics--reporting"], [65, "/docs/changelog#analytics--reporting"], [66, "/docs/changelog#analytics--reporting"], [67, "/docs/changelog#analytics--reporting"], [68, "/docs/changelog#analytics--reporting"], [46, "/docs/changelog#feature-updates"], [77, "/docs/changelog#feature-updates"], [78, "/docs/changelog#feature-updates"], [79, "/docs/changelog#feature-updates"], [80, "/docs/changelog#feature-updates"], [40, "/docs/changelog#general-improvements"], [53, "/docs/changelog#general-improvements"], [54, "/docs/changelog#general-improvements"], [55, "/docs/changelog#general-improvements"], [56, "/docs/changelog#general-improvements"], [42, "/docs/changelog#integration-upgrades"], [61, "/docs/changelog#integration-upgrades"], [62, "/docs/changelog#integration-upgrades"], [63, "/docs/changelog#integration-upgrades"], [64, "/docs/changelog#integration-upgrades"], [41, "/docs/changelog#mobile-enhancements"], [57, "/docs/changelog#mobile-enhancements"], [58, "/docs/changelog#mobile-enhancements"], [59, "/docs/changelog#mobile-enhancements"], [60, "/docs/changelog#mobile-enhancements"], [52, "/docs/changelog#new-integrations"], [102, "/docs/changelog#new-integrations"], [103, "/docs/changelog#new-integrations"], [104, "/docs/changelog#new-integrations"], [105, "/docs/changelog#new-integrations"], [51, "/docs/changelog#performance--quality-updates"], [97, "/docs/changelog#performance--quality-updates"], [98, "/docs/changelog#performance--quality-updates"], [99, "/docs/changelog#performance--quality-updates"], [100, "/docs/changelog#performance--quality-updates"], [101, "/docs/changelog#performance--quality-updates"], [48, "/docs/changelog#productivity-tools"], [85, "/docs/changelog#productivity-tools"], [86, "/docs/changelog#productivity-tools"], [87, "/docs/changelog#productivity-tools"], [88, "/docs/changelog#productivity-tools"], [49, "/docs/changelog#search--navigation"], [89, "/docs/changelog#search--navigation"], [90, "/docs/changelog#search--navigation"], [91, "/docs/changelog#search--navigation"], [92, "/docs/changelog#search--navigation"], [50, "/docs/changelog#security--compliance"], [93, "/docs/changelog#security--compliance"], [94, "/docs/changelog#security--compliance"], [95, "/docs/changelog#security--compliance"], [96, "/docs/changelog#security--compliance"], [47, "/docs/changelog#user-management"], [81, "/docs/changelog#user-management"], [82, "/docs/changelog#user-management"], [83, "/docs/changelog#user-management"], [84, "/docs/changelog#user-management"], [44, "/docs/changelog#ux--accessibility"], [69, "/docs/changelog#ux--accessibility"], [70, "/docs/changelog#ux--accessibility"], [71, "/docs/changelog#ux--accessibility"], [72, "/docs/changelog#ux--accessibility"], [45, "/docs/changelog#year-end-stability-release"], [73, "/docs/changelog#year-end-stability-release"], [74, "/docs/changelog#year-end-stability-release"], [75, "/docs/changelog#year-end-stability-release"], [76, "/docs/changelog#year-end-stability-release"]], "type": "string"}}, "enabled": true, "isSorted": true}, "language": "english"}