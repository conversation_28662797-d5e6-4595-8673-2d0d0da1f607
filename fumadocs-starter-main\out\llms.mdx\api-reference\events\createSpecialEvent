# API Reference: Create special events
URL: /docs/api-reference/events/createSpecialEvent
Source: https://raw.githubusercontent.com/techwithanirudh/fumadocs-starter/refs/heads/main/content/docs/api-reference/(generated)/events/createSpecialEvent.mdx


        
{/* This file was generated by Fumadocs. Do not edit this file directly. Any changes should be made by running the generation command again. */}

Creates a new special event for the museum.

<APIPage document={"C:/Users/<USER>/Downloads/fumadocs-starter-main/fumadocs-starter-main/content/docs/api-reference/openapi.yml"} operations={[{"path":"/special-events","method":"post"}]} webhooks={[]} hasHead={false} />
