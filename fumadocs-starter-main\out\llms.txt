# Docs

## API Reference

- [Introduction](/docs/api-reference): This is a page to check fumadocs's OpenAPI example.
- [Create special events](/docs/api-reference/events/createSpecialEvent): undefined
- [Delete special event](/docs/api-reference/events/deleteSpecialEvent): undefined
- [Get special event](/docs/api-reference/events/getSpecialEvent): undefined
- [List special events](/docs/api-reference/events/listSpecialEvents): undefined
- [New special event added](/docs/api-reference/events/publishNewEvent): undefined
- [Update special event](/docs/api-reference/events/updateSpecialEvent): undefined
- [Get museum hours](/docs/api-reference/operations/getMuseumHours): undefined
- [Buy museum tickets](/docs/api-reference/tickets/buyMuseumTickets): undefined
- [Get ticket QR code](/docs/api-reference/tickets/getTicketCode): undefined

## undefined

- [Introduction](/docs/app): Welcome to your new documentation
- [Quickstart](/docs/app/quickstart): Start building awesome documentation in under 5 minutes
- [Code Blocks](/docs/app/essentials/code): Display inline code and code blocks
- [Markdown Syntax](/docs/app/essentials/markdown): Text, title, and styling in standard markdown
- [Routing](/docs/app/essentials/routing): A shared convention for organizing your documents
- [AI Search](/docs/app/features/ai-search): Configure and use AI-powered search in your documentation
- [Async Mode](/docs/app/features/async-mode): Runtime compilation of content files.
- [LLM Support](/docs/app/features/llms): Provide AI-friendly endpoints to assist large language models
- [OpenAPI](/docs/app/features/openapi): Generate and document your OpenAPI schema
- [Adding a Root Folder](/docs/app/guides/adding-a-root-folder): Learn how to add a new root folder to your documentation

## Changelog

- [Product Updates](/docs/changelog): New updates and improvements