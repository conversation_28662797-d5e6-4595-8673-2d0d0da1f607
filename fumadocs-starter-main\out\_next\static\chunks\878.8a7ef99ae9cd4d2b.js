"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[878],{10878:(e,t,s)=>{s.r(t),s.d(t,{default:()=>S});var n=s(22342),l=s(40674),a=s(89511),r=s(12507),o=s(1481),c=s(81501),i=s(89559),d=s(35299),u=s(18085),m=s(65229),f=s(33670),x=s(76445),p=s(7300),h=s(14487),g=s(17542),b=s(85037),v=s(76524),j=s(71824);function N(e){var t,s,l,a;let r=f.Children.only(e.children).props,o=null!=(l=null==(s=r.className)||null==(t=s.split(" ").find(e=>e.startsWith("language-")))?void 0:t.slice(9))?l:"text";return"mdx"===o&&(o="md"),(0,n.jsx)(p.Q,{lang:o,code:null!=(a=r.children)?a:""})}let y=function(){let e=(0,b.V)().use(v.A).use(j.A);return{async process(t){let s=e.parse({value:t}),l=await e.run(s);return(0,g.H)(l,{development:!1,jsx:n.jsx,jsxs:n.jsxs,Fragment:n.Fragment,components:{...h.A,pre:N,img:void 0}})}}}();function k(e){let{text:t}=e,s=(0,f.useDeferredValue)(t);return(0,n.jsx)(f.Suspense,{fallback:t,children:(0,n.jsx)(A,{text:s})})}let w=new Map;function A(e){var t;let{text:s}=e,n=null!=(t=w.get(s))?t:y.process(s);return w.set(s,n),(0,f.use)(n)}let C=(0,f.createContext)(null);function z(){return(0,f.use)(C)}function E(e){var t;let{messages:s,status:l,setMessages:a,regenerate:r}=z();return 0===s.length?null:(0,n.jsxs)("div",{...e,children:["streaming"!==l&&(null==(t=s.at(-1))?void 0:t.role)==="assistant"&&(0,n.jsxs)("button",{type:"button",className:(0,x.cn)((0,c.r)({color:"secondary",size:"sm",className:"gap-1.5 rounded-full"})),onClick:()=>r(),children:[(0,n.jsx)(i.A,{className:"size-4"}),"Retry"]}),(0,n.jsx)("button",{type:"button",className:(0,x.cn)((0,c.r)({color:"secondary",size:"sm",className:"rounded-full"})),onClick:()=>a([]),children:"Clear Chat"})]})}function I(e){let{status:t,sendMessage:s,stop:l}=z(),[a,r]=(0,f.useState)(""),o="streaming"===t||"submitted"===t,i=e=>{null==e||e.preventDefault(),s({text:a}),r("")};return(0,f.useEffect)(()=>{var e;o&&(null==(e=document.getElementById("nd-ai-input"))||e.focus())},[o]),(0,n.jsxs)("form",{...e,className:(0,x.cn)("flex items-start pe-2",e.className),onSubmit:i,children:[(0,n.jsx)(_,{value:a,placeholder:o?"AI is answering...":"Ask AI something",className:"max-h-60 min-h-10 p-3",disabled:"streaming"===t||"submitted"===t,onChange:e=>{r(e.target.value)},onKeyDown:e=>{e.shiftKey||"Enter"!==e.key||i(e)}}),o?(0,n.jsxs)("button",{type:"button",className:(0,x.cn)((0,c.r)({color:"secondary",className:"mt-2 gap-2 rounded-full"})),onClick:l,children:[(0,n.jsx)(d.A,{className:"size-4 animate-spin text-fd-muted-foreground"}),"Abort Answer"]}):(0,n.jsx)("button",{type:"submit",className:(0,x.cn)((0,c.r)({color:"ghost",className:"mt-2 rounded-full transition-colors",size:"icon-sm"})),disabled:0===a.length,children:(0,n.jsx)(u.A,{className:"size-4"})})]})}function R(e){let t=(0,f.useRef)(null);return(0,f.useEffect)(()=>{var e;if(!t.current)return;function s(){let e=t.current;e&&e.scrollTo({top:e.scrollHeight,behavior:"instant"})}let n=new ResizeObserver(s);s();let l=null==(e=t.current)?void 0:e.firstElementChild;return l&&n.observe(l),()=>{n.disconnect()}},[]),(0,n.jsx)("div",{ref:t,...e,className:(0,x.cn)("fd-scroll-container flex max-h-[calc(100dvh-240px)] min-w-0 flex-col overflow-y-auto",e.className),children:e.children})}function _(e){var t,s;let l=(0,f.useRef)(null),a=(0,x.cn)("col-start-1 row-start-1",e.className);return(0,n.jsxs)("div",{className:"grid flex-1",children:[(0,n.jsx)("textarea",{id:"nd-ai-input",...e,className:(0,x.cn)("resize-none bg-transparent placeholder:text-fd-muted-foreground focus-visible:outline-none",a)}),(0,n.jsx)("div",{ref:l,className:(0,x.cn)(a,"invisible break-all"),children:"".concat(null!=(s=null==(t=e.value)?void 0:t.toString())?s:"","\n")})]})}let D={user:"you",assistant:"fumadocs"};function L(e){var t,s;let{message:l,...a}=e,r="",c=[];for(let e of null!=(t=l.parts)?t:[]){if("text"===e.type){r+=e.text;continue}"tool-provideLinks"===e.type&&e.input&&(c=e.input.links)}return(0,n.jsxs)("div",{...a,children:[(0,n.jsx)("p",{className:(0,x.cn)("mb-1 font-medium text-fd-muted-foreground text-sm","assistant"===l.role&&"text-fd-primary"),children:null!=(s=D[l.role])?s:"unknown"}),(0,n.jsx)("div",{className:"prose text-sm",children:(0,n.jsx)(k,{text:r})}),c&&c.length>0?(0,n.jsx)("div",{className:"mt-2 flex flex-row flex-wrap items-center gap-1",children:c.map((e,t)=>(0,n.jsxs)(o.default,{href:e.url,className:"block rounded-lg border p-3 text-xs hover:bg-fd-accent hover:text-fd-accent-foreground",children:[(0,n.jsx)("p",{className:"font-medium",children:e.title}),(0,n.jsxs)("p",{className:"text-fd-muted-foreground",children:["Reference ",e.label]})]},t))}):null]})}function S(e){let t=(0,l.Y_)({id:"search",transport:new r.rL({api:"/api/chat"})}),s=t.messages.filter(e=>"system"!==e.role);return(0,n.jsxs)(a.lG,{...e,children:[e.children,(0,n.jsxs)(a.ZJ,{children:[(0,n.jsx)(a.LC,{className:"fixed inset-0 z-50 backdrop-blur-xs data-[state=closed]:animate-fd-fade-out data-[state=open]:animate-fd-fade-in"}),(0,n.jsx)(a.Cf,{onOpenAutoFocus:e=>{var t;null==(t=document.getElementById("nd-ai-input"))||t.focus(),e.preventDefault()},"aria-describedby":void 0,className:"-translate-x-1/2 fixed left-1/2 z-50 flex w-[calc(100%-1rem)] max-w-screen-sm flex-col rounded-2xl border bg-fd-popover/80 p-1 shadow-2xl backdrop-blur-xl focus-visible:outline-none data-[state=closed]:animate-fd-dialog-out data-[state=open]:animate-fd-dialog-in max-md:top-12 md:bottom-12",children:(0,n.jsxs)(C,{value:t,children:[(0,n.jsxs)("div",{className:"px-3 py-2",children:[(0,n.jsx)(a.L3,{className:"font-medium text-sm",children:"AI Chat"}),(0,n.jsx)(a.rr,{className:"text-fd-muted-foreground text-xs",children:"AI can be inaccurate, please verify the information."})]}),(0,n.jsx)(a.HM,{"aria-label":"Close",tabIndex:-1,className:(0,x.cn)((0,c.r)({size:"icon-sm",color:"ghost",className:"absolute end-1 top-1 text-fd-muted-foreground"})),children:(0,n.jsx)(m.A,{})}),s.length>0&&(0,n.jsx)(R,{style:{maskImage:"linear-gradient(to bottom, transparent, black 20px, black calc(100% - 20px), transparent)"},children:(0,n.jsx)("div",{className:"flex flex-col gap-4 p-3",children:s.map(e=>(0,n.jsx)(L,{message:e},e.id))})}),(0,n.jsxs)("div",{className:"overflow-hidden rounded-xl border border-fd-foreground/20 text-fd-popover-foreground",children:[(0,n.jsx)(I,{}),(0,n.jsx)(E,{className:"flex flex-row items-center gap-1.5 p-1 empty:hidden"})]})]})})]})]})}}}]);