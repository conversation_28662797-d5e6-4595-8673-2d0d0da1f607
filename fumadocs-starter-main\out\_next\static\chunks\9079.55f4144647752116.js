"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9079],{6540:(e,t,a)=>{function l(e,t){e.accDescr&&t.setAccDescription?.(e.accDescr),e.accTitle&&t.setAccTitle?.(e.accTitle),e.title&&t.setDiagramTitle?.(e.title)}a.d(t,{S:()=>l}),(0,a(74504).K2)(l,"populateCommonDb")},9949:(e,t,a)=>{a.d(t,{P:()=>r});var l=a(74504),r=(0,l.K2)((e,t,a,r)=>{e.attr("class",a);let{width:n,height:o,x:c,y:d}=s(e,t);(0,l.a$)(e,o,n,r);let p=i(c,d,n,o,t);e.attr("viewBox",p),l.Rm.debug(`viewBox configured: ${p} with padding: ${t}`)},"setupViewPortForSVG"),s=(0,l.K2)((e,t)=>{let a=e.node()?.getBBox()||{width:0,height:0,x:0,y:0};return{width:a.width+2*t,height:a.height+2*t,x:a.x,y:a.y}},"calculateDimensionsWithPadding"),i=(0,l.K2)((e,t,a,l,r)=>`${e-r} ${t-r} ${a} ${l}`,"createViewBox")},69079:(e,t,a)=>{a.d(t,{diagram:()=>x});var l=a(9949),r=a(41973),s=a(6540),i=a(75868),n=a(10314),o=a(74504),c=a(34290),d=a(55508),p=class{constructor(){this.nodes=[],this.levels=new Map,this.outerNodes=[],this.classes=new Map,this.setAccTitle=o.SV,this.getAccTitle=o.iN,this.setDiagramTitle=o.ke,this.getDiagramTitle=o.ab,this.getAccDescription=o.m7,this.setAccDescription=o.EI}static{(0,o.K2)(this,"TreeMapDB")}getNodes(){return this.nodes}getConfig(){let e=o.UI,t=(0,o.zj)();return(0,i.$t)({...e.treemap,...t.treemap??{}})}addNode(e,t){this.nodes.push(e),this.levels.set(e,t),0===t&&(this.outerNodes.push(e),this.root??=e)}getRoot(){return{name:"",children:this.outerNodes}}addClass(e,t){let a=this.classes.get(e)??{id:e,styles:[],textStyles:[]},l=t.replace(/\\,/g,"\xa7\xa7\xa7").replace(/,/g,";").replace(/§§§/g,",").split(";");l&&l.forEach(e=>{(0,r.KX)(e)&&(a?.textStyles?a.textStyles.push(e):a.textStyles=[e]),a?.styles?a.styles.push(e):a.styles=[e]}),this.classes.set(e,a)}getClasses(){return this.classes}getStylesForClass(e){return this.classes.get(e)?.styles??[]}clear(){(0,o.IU)(),this.nodes=[],this.levels=new Map,this.outerNodes=[],this.classes=new Map,this.root=void 0}};function h(e){if(!e.length)return[];let t=[],a=[];return e.forEach(e=>{let l={name:e.name,children:"Leaf"===e.type?void 0:[]};for(l.classSelector=e?.classSelector,e?.cssCompiledStyles&&(l.cssCompiledStyles=[e.cssCompiledStyles]),"Leaf"===e.type&&void 0!==e.value&&(l.value=e.value);a.length>0&&a[a.length-1].level>=e.level;)a.pop();if(0===a.length)t.push(l);else{let e=a[a.length-1].node;e.children?e.children.push(l):e.children=[l]}"Leaf"!==e.type&&a.push({node:l,level:e.level})}),t}(0,o.K2)(h,"buildHierarchy");var m=(0,o.K2)((e,t)=>{(0,s.S)(e,t);let a=[];for(let a of e.TreemapRows??[])"ClassDefStatement"===a.$type&&t.addClass(a.className??"",a.styleText??"");for(let l of e.TreemapRows??[]){let e=l.item;if(!e)continue;let r=l.indent?parseInt(l.indent):0,s=y(e),i=e.classSelector?t.getStylesForClass(e.classSelector):[],n=i.length>0?i.join(";"):void 0,o={level:r,name:s,type:e.$type,value:e.value,classSelector:e.classSelector,cssCompiledStyles:n};a.push(o)}let l=h(a),r=(0,o.K2)((e,a)=>{for(let l of e)t.addNode(l,a),l.children&&l.children.length>0&&r(l.children,a+1)},"addNodesRecursively");r(l,0)},"populate"),y=(0,o.K2)(e=>e.name?String(e.name):"","getItemName"),f={parser:{yy:void 0},parse:(0,o.K2)(async e=>{try{let t=c.qg,a=await t("treemap",e);o.Rm.debug("Treemap AST:",a);let l=f.parser?.yy;if(!(l instanceof p))throw Error("parser.parser?.yy was not a TreemapDB. This is due to a bug within Mermaid, please report this issue at https://github.com/mermaid-js/mermaid/issues.");m(a,l)}catch(e){throw o.Rm.error("Error parsing treemap:",e),e}},"parse")},u=(0,o.K2)((e,t,a,s)=>{let i,c=s.db,p=c.getConfig(),h=p.padding??10,m=c.getDiagramTitle(),y=c.getRoot(),{themeVariables:f}=(0,o.zj)();if(!y)return;let u=30*!!m,S=(0,n.D)(t),g=p.nodeWidth?10*p.nodeWidth:960,x=p.nodeHeight?10*p.nodeHeight:500,b=x+u;S.attr("viewBox",`0 0 ${g} ${b}`),(0,o.a$)(S,b,g,p.useMaxWidth);try{let e=p.valueFormat||",";if("$0,0"===e)i=(0,o.K2)(e=>"$"+(0,d.GPZ)(",")(e),"valueFormat");else if(e.startsWith("$")&&e.includes(",")){let t=/\.\d+/.exec(e),a=t?t[0]:"";i=(0,o.K2)(e=>"$"+(0,d.GPZ)(","+a)(e),"valueFormat")}else if(e.startsWith("$")){let t=e.substring(1);i=(0,o.K2)(e=>"$"+(0,d.GPZ)(t||"")(e),"valueFormat")}else i=(0,d.GPZ)(e)}catch(e){o.Rm.error("Error creating format function:",e),i=(0,d.GPZ)(",")}let $=(0,d.UMr)().range(["transparent",f.cScale0,f.cScale1,f.cScale2,f.cScale3,f.cScale4,f.cScale5,f.cScale6,f.cScale7,f.cScale8,f.cScale9,f.cScale10,f.cScale11]),v=(0,d.UMr)().range(["transparent",f.cScalePeer0,f.cScalePeer1,f.cScalePeer2,f.cScalePeer3,f.cScalePeer4,f.cScalePeer5,f.cScalePeer6,f.cScalePeer7,f.cScalePeer8,f.cScalePeer9,f.cScalePeer10,f.cScalePeer11]),C=(0,d.UMr)().range([f.cScaleLabel0,f.cScaleLabel1,f.cScaleLabel2,f.cScaleLabel3,f.cScaleLabel4,f.cScaleLabel5,f.cScaleLabel6,f.cScaleLabel7,f.cScaleLabel8,f.cScaleLabel9,f.cScaleLabel10,f.cScaleLabel11]);m&&S.append("text").attr("x",g/2).attr("y",u/2).attr("class","treemapTitle").attr("text-anchor","middle").attr("dominant-baseline","middle").text(m);let w=S.append("g").attr("transform",`translate(0, ${u})`).attr("class","treemapContainer"),L=(0,d.Sk5)(y).sum(e=>e.value??0).sort((e,t)=>(t.value??0)-(e.value??0)),k=(0,d.hkb)().size([g,x]).paddingTop(e=>e.children&&e.children.length>0?35:0).paddingInner(h).paddingLeft(e=>e.children&&e.children.length>0?10:0).paddingRight(e=>e.children&&e.children.length>0?10:0).paddingBottom(e=>e.children&&e.children.length>0?10:0).round(!0)(L),T=k.descendants().filter(e=>e.children&&e.children.length>0),M=w.selectAll(".treemapSection").data(T).enter().append("g").attr("class","treemapSection").attr("transform",e=>`translate(${e.x0},${e.y0})`);M.append("rect").attr("width",e=>e.x1-e.x0).attr("height",25).attr("class","treemapSectionHeader").attr("fill","none").attr("fill-opacity",.6).attr("stroke-width",.6).attr("style",e=>0===e.depth?"display: none;":""),M.append("clipPath").attr("id",(e,a)=>`clip-section-${t}-${a}`).append("rect").attr("width",e=>Math.max(0,e.x1-e.x0-12)).attr("height",25),M.append("rect").attr("width",e=>e.x1-e.x0).attr("height",e=>e.y1-e.y0).attr("class",(e,t)=>`treemapSection section${t}`).attr("fill",e=>$(e.data.name)).attr("fill-opacity",.6).attr("stroke",e=>v(e.data.name)).attr("stroke-width",2).attr("stroke-opacity",.4).attr("style",e=>{if(0===e.depth)return"display: none;";let t=(0,r.GX)({cssCompiledStyles:e.data.cssCompiledStyles});return t.nodeStyles+";"+t.borderStyles.join(";")}),M.append("text").attr("class","treemapSectionLabel").attr("x",6).attr("y",12.5).attr("dominant-baseline","middle").text(e=>0===e.depth?"":e.data.name).attr("font-weight","bold").attr("style",e=>0===e.depth?"display: none;":"dominant-baseline: middle; font-size: 12px; fill:"+C(e.data.name)+"; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;"+(0,r.GX)({cssCompiledStyles:e.data.cssCompiledStyles}).labelStyles.replace("color:","fill:")).each(function(e){if(0===e.depth)return;let t=(0,d.Ltv)(this),a=e.data.name;t.text(a);let l=e.x1-e.x0,r=Math.max(15,!1!==p.showValues&&e.value?l-10-30-10-6:l-6-6),s=t.node();if(s.getComputedTextLength()>r){let e=a;for(;e.length>0;){if(0===(e=a.substring(0,e.length-1)).length){t.text("..."),s.getComputedTextLength()>r&&t.text("");break}if(t.text(e+"..."),s.getComputedTextLength()<=r)break}}}),!1!==p.showValues&&M.append("text").attr("class","treemapSectionValue").attr("x",e=>e.x1-e.x0-10).attr("y",12.5).attr("text-anchor","end").attr("dominant-baseline","middle").text(e=>e.value?i(e.value):"").attr("font-style","italic").attr("style",e=>0===e.depth?"display: none;":"text-anchor: end; dominant-baseline: middle; font-size: 10px; fill:"+C(e.data.name)+"; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;"+(0,r.GX)({cssCompiledStyles:e.data.cssCompiledStyles}).labelStyles.replace("color:","fill:"));let P=k.leaves(),z=w.selectAll(".treemapLeafGroup").data(P).enter().append("g").attr("class",(e,t)=>`treemapNode treemapLeafGroup leaf${t}${e.data.classSelector?` ${e.data.classSelector}`:""}x`).attr("transform",e=>`translate(${e.x0},${e.y0})`);z.append("rect").attr("width",e=>e.x1-e.x0).attr("height",e=>e.y1-e.y0).attr("class","treemapLeaf").attr("fill",e=>e.parent?$(e.parent.data.name):$(e.data.name)).attr("style",e=>(0,r.GX)({cssCompiledStyles:e.data.cssCompiledStyles}).nodeStyles).attr("fill-opacity",.3).attr("stroke",e=>e.parent?$(e.parent.data.name):$(e.data.name)).attr("stroke-width",3),z.append("clipPath").attr("id",(e,a)=>`clip-${t}-${a}`).append("rect").attr("width",e=>Math.max(0,e.x1-e.x0-4)).attr("height",e=>Math.max(0,e.y1-e.y0-4)),z.append("text").attr("class","treemapLabel").attr("x",e=>(e.x1-e.x0)/2).attr("y",e=>(e.y1-e.y0)/2).attr("style",e=>"text-anchor: middle; dominant-baseline: middle; font-size: 38px;fill:"+C(e.data.name)+";"+(0,r.GX)({cssCompiledStyles:e.data.cssCompiledStyles}).labelStyles.replace("color:","fill:")).attr("clip-path",(e,a)=>`url(#clip-${t}-${a})`).text(e=>e.data.name).each(function(e){let t=(0,d.Ltv)(this),a=e.x1-e.x0,l=e.y1-e.y0,r=t.node(),s=a-8,i=l-8;if(s<10||i<10)return void t.style("display","none");let n=parseInt(t.style("font-size"),10);for(;r.getComputedTextLength()>s&&n>8;)n--,t.style("font-size",`${n}px`);let o=Math.max(6,Math.min(28,Math.round(.6*n))),c=n+2+o;for(;c>i&&n>8&&(!((o=Math.max(6,Math.min(28,Math.round(.6*--n))))<6)||8!==n);)t.style("font-size",`${n}px`),c=n+2+o;t.style("font-size",`${n}px`),(r.getComputedTextLength()>s||n<8||i<n)&&t.style("display","none")}),!1!==p.showValues&&z.append("text").attr("class","treemapValue").attr("x",e=>(e.x1-e.x0)/2).attr("y",function(e){return(e.y1-e.y0)/2}).attr("style",e=>"text-anchor: middle; dominant-baseline: hanging; font-size: 28px;fill:"+C(e.data.name)+";"+(0,r.GX)({cssCompiledStyles:e.data.cssCompiledStyles}).labelStyles.replace("color:","fill:")).attr("clip-path",(e,a)=>`url(#clip-${t}-${a})`).text(e=>e.value?i(e.value):"").each(function(e){let t=(0,d.Ltv)(this),a=this.parentNode;if(!a)return void t.style("display","none");let l=(0,d.Ltv)(a).select(".treemapLabel");if(l.empty()||"none"===l.style("display"))return void t.style("display","none");let r=parseFloat(l.style("font-size")),s=Math.max(6,Math.min(28,Math.round(.6*r)));t.style("font-size",`${s}px`);let i=(e.y1-e.y0)/2+r/2+2;t.attr("y",i);let n=e.x1-e.x0,o=e.y1-e.y0;t.node().getComputedTextLength()>n-8||i+s>o-4||s<6?t.style("display","none"):t.style("display",null)});let F=p.diagramPadding??8;(0,l.P)(S,F,"flowchart",p?.useMaxWidth||!1)},"draw"),S=(0,o.K2)(function(e,t){return t.db.getClasses()},"getClasses"),g={sectionStrokeColor:"black",sectionStrokeWidth:"1",sectionFillColor:"#efefef",leafStrokeColor:"black",leafStrokeWidth:"1",leafFillColor:"#efefef",labelColor:"black",labelFontSize:"12px",valueFontSize:"10px",valueColor:"black",titleColor:"black",titleFontSize:"14px"},x={parser:f,get db(){return new p},renderer:{draw:u,getClasses:S},styles:(0,o.K2)(({treemap:e}={})=>{let t=(0,i.$t)(g,e);return`
  .treemapNode.section {
    stroke: ${t.sectionStrokeColor};
    stroke-width: ${t.sectionStrokeWidth};
    fill: ${t.sectionFillColor};
  }
  .treemapNode.leaf {
    stroke: ${t.leafStrokeColor};
    stroke-width: ${t.leafStrokeWidth};
    fill: ${t.leafFillColor};
  }
  .treemapLabel {
    fill: ${t.labelColor};
    font-size: ${t.labelFontSize};
  }
  .treemapValue {
    fill: ${t.valueColor};
    font-size: ${t.valueFontSize};
  }
  .treemapTitle {
    fill: ${t.titleColor};
    font-size: ${t.titleFontSize};
  }
  `},"getStyles")}}}]);