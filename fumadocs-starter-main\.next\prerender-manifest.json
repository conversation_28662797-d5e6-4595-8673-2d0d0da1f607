{"version": 4, "routes": {"/_not-found": {"initialStatus": 404, "experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/_not-found", "dataRoute": "/_not-found.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/icon.png": {"initialHeaders": {"cache-control": "public, immutable, no-transform, max-age=********", "content-type": "image/png", "x-next-cache-tags": "_N_T_/layout,_N_T_/icon.png/layout,_N_T_/icon.png/route,_N_T_/icon.png/"}, "experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/icon.png", "dataRoute": null, "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/": {"experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/llms.mdx/api-reference": {"initialHeaders": {"content-type": "text/plain;charset=UTF-8", "x-next-cache-tags": "_N_T_/layout,_N_T_/llms.mdx/layout,_N_T_/llms.mdx/[...slug]/layout,_N_T_/llms.mdx/[...slug]/route,_N_T_/llms.mdx/api-reference/"}, "experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/llms.mdx/[...slug]", "dataRoute": null, "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/llms.mdx/api-reference/events/createSpecialEvent": {"initialHeaders": {"content-type": "text/plain;charset=UTF-8", "x-next-cache-tags": "_N_T_/layout,_N_T_/llms.mdx/layout,_N_T_/llms.mdx/[...slug]/layout,_N_T_/llms.mdx/[...slug]/route,_N_T_/llms.mdx/api-reference/events/createSpecialEvent/"}, "experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/llms.mdx/[...slug]", "dataRoute": null, "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/llms.mdx/api-reference/events/deleteSpecialEvent": {"initialHeaders": {"content-type": "text/plain;charset=UTF-8", "x-next-cache-tags": "_N_T_/layout,_N_T_/llms.mdx/layout,_N_T_/llms.mdx/[...slug]/layout,_N_T_/llms.mdx/[...slug]/route,_N_T_/llms.mdx/api-reference/events/deleteSpecialEvent/"}, "experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/llms.mdx/[...slug]", "dataRoute": null, "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/llms.mdx/api-reference/events/getSpecialEvent": {"initialHeaders": {"content-type": "text/plain;charset=UTF-8", "x-next-cache-tags": "_N_T_/layout,_N_T_/llms.mdx/layout,_N_T_/llms.mdx/[...slug]/layout,_N_T_/llms.mdx/[...slug]/route,_N_T_/llms.mdx/api-reference/events/getSpecialEvent/"}, "experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/llms.mdx/[...slug]", "dataRoute": null, "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/llms.mdx/api-reference/events/listSpecialEvents": {"initialHeaders": {"content-type": "text/plain;charset=UTF-8", "x-next-cache-tags": "_N_T_/layout,_N_T_/llms.mdx/layout,_N_T_/llms.mdx/[...slug]/layout,_N_T_/llms.mdx/[...slug]/route,_N_T_/llms.mdx/api-reference/events/listSpecialEvents/"}, "experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/llms.mdx/[...slug]", "dataRoute": null, "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/llms.mdx/api-reference/events/publishNewEvent": {"initialHeaders": {"content-type": "text/plain;charset=UTF-8", "x-next-cache-tags": "_N_T_/layout,_N_T_/llms.mdx/layout,_N_T_/llms.mdx/[...slug]/layout,_N_T_/llms.mdx/[...slug]/route,_N_T_/llms.mdx/api-reference/events/publishNewEvent/"}, "experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/llms.mdx/[...slug]", "dataRoute": null, "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/llms.mdx/api-reference/events/updateSpecialEvent": {"initialHeaders": {"content-type": "text/plain;charset=UTF-8", "x-next-cache-tags": "_N_T_/layout,_N_T_/llms.mdx/layout,_N_T_/llms.mdx/[...slug]/layout,_N_T_/llms.mdx/[...slug]/route,_N_T_/llms.mdx/api-reference/events/updateSpecialEvent/"}, "experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/llms.mdx/[...slug]", "dataRoute": null, "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/llms.mdx/api-reference/operations/getMuseumHours": {"initialHeaders": {"content-type": "text/plain;charset=UTF-8", "x-next-cache-tags": "_N_T_/layout,_N_T_/llms.mdx/layout,_N_T_/llms.mdx/[...slug]/layout,_N_T_/llms.mdx/[...slug]/route,_N_T_/llms.mdx/api-reference/operations/getMuseumHours/"}, "experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/llms.mdx/[...slug]", "dataRoute": null, "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/llms.mdx/api-reference/tickets/buyMuseumTickets": {"initialHeaders": {"content-type": "text/plain;charset=UTF-8", "x-next-cache-tags": "_N_T_/layout,_N_T_/llms.mdx/layout,_N_T_/llms.mdx/[...slug]/layout,_N_T_/llms.mdx/[...slug]/route,_N_T_/llms.mdx/api-reference/tickets/buyMuseumTickets/"}, "experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/llms.mdx/[...slug]", "dataRoute": null, "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/llms.mdx/api-reference/tickets/getTicketCode": {"initialHeaders": {"content-type": "text/plain;charset=UTF-8", "x-next-cache-tags": "_N_T_/layout,_N_T_/llms.mdx/layout,_N_T_/llms.mdx/[...slug]/layout,_N_T_/llms.mdx/[...slug]/route,_N_T_/llms.mdx/api-reference/tickets/getTicketCode/"}, "experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/llms.mdx/[...slug]", "dataRoute": null, "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/llms.mdx/app": {"initialHeaders": {"content-type": "text/plain;charset=UTF-8", "x-next-cache-tags": "_N_T_/layout,_N_T_/llms.mdx/layout,_N_T_/llms.mdx/[...slug]/layout,_N_T_/llms.mdx/[...slug]/route,_N_T_/llms.mdx/app/"}, "experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/llms.mdx/[...slug]", "dataRoute": null, "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/llms.mdx/app/essentials/code": {"initialHeaders": {"content-type": "text/plain;charset=UTF-8", "x-next-cache-tags": "_N_T_/layout,_N_T_/llms.mdx/layout,_N_T_/llms.mdx/[...slug]/layout,_N_T_/llms.mdx/[...slug]/route,_N_T_/llms.mdx/app/essentials/code/"}, "experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/llms.mdx/[...slug]", "dataRoute": null, "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/llms.mdx/app/essentials/markdown": {"initialHeaders": {"content-type": "text/plain;charset=UTF-8", "x-next-cache-tags": "_N_T_/layout,_N_T_/llms.mdx/layout,_N_T_/llms.mdx/[...slug]/layout,_N_T_/llms.mdx/[...slug]/route,_N_T_/llms.mdx/app/essentials/markdown/"}, "experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/llms.mdx/[...slug]", "dataRoute": null, "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/llms.mdx/app/essentials/routing": {"initialHeaders": {"content-type": "text/plain;charset=UTF-8", "x-next-cache-tags": "_N_T_/layout,_N_T_/llms.mdx/layout,_N_T_/llms.mdx/[...slug]/layout,_N_T_/llms.mdx/[...slug]/route,_N_T_/llms.mdx/app/essentials/routing/"}, "experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/llms.mdx/[...slug]", "dataRoute": null, "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/llms.mdx/app/features/ai-search": {"initialHeaders": {"content-type": "text/plain;charset=UTF-8", "x-next-cache-tags": "_N_T_/layout,_N_T_/llms.mdx/layout,_N_T_/llms.mdx/[...slug]/layout,_N_T_/llms.mdx/[...slug]/route,_N_T_/llms.mdx/app/features/ai-search/"}, "experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/llms.mdx/[...slug]", "dataRoute": null, "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/llms.mdx/app/features/async-mode": {"initialHeaders": {"content-type": "text/plain;charset=UTF-8", "x-next-cache-tags": "_N_T_/layout,_N_T_/llms.mdx/layout,_N_T_/llms.mdx/[...slug]/layout,_N_T_/llms.mdx/[...slug]/route,_N_T_/llms.mdx/app/features/async-mode/"}, "experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/llms.mdx/[...slug]", "dataRoute": null, "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/llms.mdx/app/features/llms": {"initialHeaders": {"content-type": "text/plain;charset=UTF-8", "x-next-cache-tags": "_N_T_/layout,_N_T_/llms.mdx/layout,_N_T_/llms.mdx/[...slug]/layout,_N_T_/llms.mdx/[...slug]/route,_N_T_/llms.mdx/app/features/llms/"}, "experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/llms.mdx/[...slug]", "dataRoute": null, "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/llms.mdx/app/features/openapi": {"initialHeaders": {"content-type": "text/plain;charset=UTF-8", "x-next-cache-tags": "_N_T_/layout,_N_T_/llms.mdx/layout,_N_T_/llms.mdx/[...slug]/layout,_N_T_/llms.mdx/[...slug]/route,_N_T_/llms.mdx/app/features/openapi/"}, "experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/llms.mdx/[...slug]", "dataRoute": null, "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/llms.mdx/app/guides/adding-a-root-folder": {"initialHeaders": {"content-type": "text/plain;charset=UTF-8", "x-next-cache-tags": "_N_T_/layout,_N_T_/llms.mdx/layout,_N_T_/llms.mdx/[...slug]/layout,_N_T_/llms.mdx/[...slug]/route,_N_T_/llms.mdx/app/guides/adding-a-root-folder/"}, "experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/llms.mdx/[...slug]", "dataRoute": null, "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/llms.mdx/app/quickstart": {"initialHeaders": {"content-type": "text/plain;charset=UTF-8", "x-next-cache-tags": "_N_T_/layout,_N_T_/llms.mdx/layout,_N_T_/llms.mdx/[...slug]/layout,_N_T_/llms.mdx/[...slug]/route,_N_T_/llms.mdx/app/quickstart/"}, "experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/llms.mdx/[...slug]", "dataRoute": null, "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/llms.mdx/changelog": {"initialHeaders": {"content-type": "text/plain;charset=UTF-8", "x-next-cache-tags": "_N_T_/layout,_N_T_/llms.mdx/layout,_N_T_/llms.mdx/[...slug]/layout,_N_T_/llms.mdx/[...slug]/route,_N_T_/llms.mdx/changelog/"}, "experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/llms.mdx/[...slug]", "dataRoute": null, "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/sitemap.xml": {"initialHeaders": {"cache-control": "public, max-age=0, must-revalidate", "content-type": "application/xml", "x-next-cache-tags": "_N_T_/layout,_N_T_/sitemap.xml/layout,_N_T_/sitemap.xml/route,_N_T_/sitemap.xml/"}, "experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/sitemap.xml", "dataRoute": null, "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/og/api-reference/events/createSpecialEvent/image.png": {"initialHeaders": {"cache-control": "public, immutable, no-transform, max-age=********", "content-type": "image/png", "x-next-cache-tags": "_N_T_/layout,_N_T_/og/layout,_N_T_/og/[...slug]/layout,_N_T_/og/[...slug]/route,_N_T_/og/api-reference/events/createSpecialEvent/image.png/"}, "experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/og/[...slug]", "dataRoute": null, "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/og/api-reference/events/deleteSpecialEvent/image.png": {"initialHeaders": {"cache-control": "public, immutable, no-transform, max-age=********", "content-type": "image/png", "x-next-cache-tags": "_N_T_/layout,_N_T_/og/layout,_N_T_/og/[...slug]/layout,_N_T_/og/[...slug]/route,_N_T_/og/api-reference/events/deleteSpecialEvent/image.png/"}, "experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/og/[...slug]", "dataRoute": null, "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/og/api-reference/events/getSpecialEvent/image.png": {"initialHeaders": {"cache-control": "public, immutable, no-transform, max-age=********", "content-type": "image/png", "x-next-cache-tags": "_N_T_/layout,_N_T_/og/layout,_N_T_/og/[...slug]/layout,_N_T_/og/[...slug]/route,_N_T_/og/api-reference/events/getSpecialEvent/image.png/"}, "experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/og/[...slug]", "dataRoute": null, "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/og/api-reference/events/listSpecialEvents/image.png": {"initialHeaders": {"cache-control": "public, immutable, no-transform, max-age=********", "content-type": "image/png", "x-next-cache-tags": "_N_T_/layout,_N_T_/og/layout,_N_T_/og/[...slug]/layout,_N_T_/og/[...slug]/route,_N_T_/og/api-reference/events/listSpecialEvents/image.png/"}, "experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/og/[...slug]", "dataRoute": null, "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/og/api-reference/events/publishNewEvent/image.png": {"initialHeaders": {"cache-control": "public, immutable, no-transform, max-age=********", "content-type": "image/png", "x-next-cache-tags": "_N_T_/layout,_N_T_/og/layout,_N_T_/og/[...slug]/layout,_N_T_/og/[...slug]/route,_N_T_/og/api-reference/events/publishNewEvent/image.png/"}, "experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/og/[...slug]", "dataRoute": null, "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/og/api-reference/events/updateSpecialEvent/image.png": {"initialHeaders": {"cache-control": "public, immutable, no-transform, max-age=********", "content-type": "image/png", "x-next-cache-tags": "_N_T_/layout,_N_T_/og/layout,_N_T_/og/[...slug]/layout,_N_T_/og/[...slug]/route,_N_T_/og/api-reference/events/updateSpecialEvent/image.png/"}, "experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/og/[...slug]", "dataRoute": null, "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/og/api-reference/image.png": {"initialHeaders": {"cache-control": "public, immutable, no-transform, max-age=********", "content-type": "image/png", "x-next-cache-tags": "_N_T_/layout,_N_T_/og/layout,_N_T_/og/[...slug]/layout,_N_T_/og/[...slug]/route,_N_T_/og/api-reference/image.png/"}, "experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/og/[...slug]", "dataRoute": null, "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/og/api-reference/operations/getMuseumHours/image.png": {"initialHeaders": {"cache-control": "public, immutable, no-transform, max-age=********", "content-type": "image/png", "x-next-cache-tags": "_N_T_/layout,_N_T_/og/layout,_N_T_/og/[...slug]/layout,_N_T_/og/[...slug]/route,_N_T_/og/api-reference/operations/getMuseumHours/image.png/"}, "experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/og/[...slug]", "dataRoute": null, "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/og/api-reference/tickets/buyMuseumTickets/image.png": {"initialHeaders": {"cache-control": "public, immutable, no-transform, max-age=********", "content-type": "image/png", "x-next-cache-tags": "_N_T_/layout,_N_T_/og/layout,_N_T_/og/[...slug]/layout,_N_T_/og/[...slug]/route,_N_T_/og/api-reference/tickets/buyMuseumTickets/image.png/"}, "experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/og/[...slug]", "dataRoute": null, "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/og/api-reference/tickets/getTicketCode/image.png": {"initialHeaders": {"cache-control": "public, immutable, no-transform, max-age=********", "content-type": "image/png", "x-next-cache-tags": "_N_T_/layout,_N_T_/og/layout,_N_T_/og/[...slug]/layout,_N_T_/og/[...slug]/route,_N_T_/og/api-reference/tickets/getTicketCode/image.png/"}, "experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/og/[...slug]", "dataRoute": null, "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/og/app/essentials/code/image.png": {"initialHeaders": {"cache-control": "public, immutable, no-transform, max-age=********", "content-type": "image/png", "x-next-cache-tags": "_N_T_/layout,_N_T_/og/layout,_N_T_/og/[...slug]/layout,_N_T_/og/[...slug]/route,_N_T_/og/app/essentials/code/image.png/"}, "experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/og/[...slug]", "dataRoute": null, "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/og/app/essentials/markdown/image.png": {"initialHeaders": {"cache-control": "public, immutable, no-transform, max-age=********", "content-type": "image/png", "x-next-cache-tags": "_N_T_/layout,_N_T_/og/layout,_N_T_/og/[...slug]/layout,_N_T_/og/[...slug]/route,_N_T_/og/app/essentials/markdown/image.png/"}, "experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/og/[...slug]", "dataRoute": null, "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/og/app/essentials/routing/image.png": {"initialHeaders": {"cache-control": "public, immutable, no-transform, max-age=********", "content-type": "image/png", "x-next-cache-tags": "_N_T_/layout,_N_T_/og/layout,_N_T_/og/[...slug]/layout,_N_T_/og/[...slug]/route,_N_T_/og/app/essentials/routing/image.png/"}, "experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/og/[...slug]", "dataRoute": null, "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/og/app/features/ai-search/image.png": {"initialHeaders": {"cache-control": "public, immutable, no-transform, max-age=********", "content-type": "image/png", "x-next-cache-tags": "_N_T_/layout,_N_T_/og/layout,_N_T_/og/[...slug]/layout,_N_T_/og/[...slug]/route,_N_T_/og/app/features/ai-search/image.png/"}, "experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/og/[...slug]", "dataRoute": null, "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/og/app/features/async-mode/image.png": {"initialHeaders": {"cache-control": "public, immutable, no-transform, max-age=********", "content-type": "image/png", "x-next-cache-tags": "_N_T_/layout,_N_T_/og/layout,_N_T_/og/[...slug]/layout,_N_T_/og/[...slug]/route,_N_T_/og/app/features/async-mode/image.png/"}, "experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/og/[...slug]", "dataRoute": null, "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/og/app/features/llms/image.png": {"initialHeaders": {"cache-control": "public, immutable, no-transform, max-age=********", "content-type": "image/png", "x-next-cache-tags": "_N_T_/layout,_N_T_/og/layout,_N_T_/og/[...slug]/layout,_N_T_/og/[...slug]/route,_N_T_/og/app/features/llms/image.png/"}, "experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/og/[...slug]", "dataRoute": null, "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/og/app/features/openapi/image.png": {"initialHeaders": {"cache-control": "public, immutable, no-transform, max-age=********", "content-type": "image/png", "x-next-cache-tags": "_N_T_/layout,_N_T_/og/layout,_N_T_/og/[...slug]/layout,_N_T_/og/[...slug]/route,_N_T_/og/app/features/openapi/image.png/"}, "experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/og/[...slug]", "dataRoute": null, "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/og/app/guides/adding-a-root-folder/image.png": {"initialHeaders": {"cache-control": "public, immutable, no-transform, max-age=********", "content-type": "image/png", "x-next-cache-tags": "_N_T_/layout,_N_T_/og/layout,_N_T_/og/[...slug]/layout,_N_T_/og/[...slug]/route,_N_T_/og/app/guides/adding-a-root-folder/image.png/"}, "experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/og/[...slug]", "dataRoute": null, "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/og/app/image.png": {"initialHeaders": {"cache-control": "public, immutable, no-transform, max-age=********", "content-type": "image/png", "x-next-cache-tags": "_N_T_/layout,_N_T_/og/layout,_N_T_/og/[...slug]/layout,_N_T_/og/[...slug]/route,_N_T_/og/app/image.png/"}, "experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/og/[...slug]", "dataRoute": null, "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/og/app/quickstart/image.png": {"initialHeaders": {"cache-control": "public, immutable, no-transform, max-age=********", "content-type": "image/png", "x-next-cache-tags": "_N_T_/layout,_N_T_/og/layout,_N_T_/og/[...slug]/layout,_N_T_/og/[...slug]/route,_N_T_/og/app/quickstart/image.png/"}, "experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/og/[...slug]", "dataRoute": null, "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/og/changelog/image.png": {"initialHeaders": {"cache-control": "public, immutable, no-transform, max-age=********", "content-type": "image/png", "x-next-cache-tags": "_N_T_/layout,_N_T_/og/layout,_N_T_/og/[...slug]/layout,_N_T_/og/[...slug]/route,_N_T_/og/changelog/image.png/"}, "experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/og/[...slug]", "dataRoute": null, "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/llms.txt": {"initialHeaders": {"content-type": "text/plain;charset=UTF-8", "x-next-cache-tags": "_N_T_/layout,_N_T_/llms.txt/layout,_N_T_/llms.txt/route,_N_T_/llms.txt/"}, "experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/llms.txt", "dataRoute": null, "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/llms-full.txt": {"initialHeaders": {"content-type": "text/plain;charset=UTF-8", "x-next-cache-tags": "_N_T_/layout,_N_T_/llms-full.txt/layout,_N_T_/llms-full.txt/route,_N_T_/llms-full.txt/"}, "experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/llms-full.txt", "dataRoute": null, "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/docs/api-reference": {"experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/docs/[[...slug]]", "dataRoute": "/docs/api-reference.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/docs/api-reference/events/createSpecialEvent": {"experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/docs/[[...slug]]", "dataRoute": "/docs/api-reference/events/createSpecialEvent.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/docs/api-reference/events/deleteSpecialEvent": {"experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/docs/[[...slug]]", "dataRoute": "/docs/api-reference/events/deleteSpecialEvent.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/docs/api-reference/events/getSpecialEvent": {"experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/docs/[[...slug]]", "dataRoute": "/docs/api-reference/events/getSpecialEvent.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/docs/api-reference/events/listSpecialEvents": {"experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/docs/[[...slug]]", "dataRoute": "/docs/api-reference/events/listSpecialEvents.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/docs/api-reference/events/publishNewEvent": {"experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/docs/[[...slug]]", "dataRoute": "/docs/api-reference/events/publishNewEvent.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/docs/api-reference/events/updateSpecialEvent": {"experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/docs/[[...slug]]", "dataRoute": "/docs/api-reference/events/updateSpecialEvent.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/docs/api-reference/operations/getMuseumHours": {"experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/docs/[[...slug]]", "dataRoute": "/docs/api-reference/operations/getMuseumHours.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/docs/api-reference/tickets/buyMuseumTickets": {"experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/docs/[[...slug]]", "dataRoute": "/docs/api-reference/tickets/buyMuseumTickets.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/docs/api-reference/tickets/getTicketCode": {"experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/docs/[[...slug]]", "dataRoute": "/docs/api-reference/tickets/getTicketCode.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/docs/app": {"experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/docs/[[...slug]]", "dataRoute": "/docs/app.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/docs/app/essentials/code": {"experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/docs/[[...slug]]", "dataRoute": "/docs/app/essentials/code.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/docs/app/essentials/markdown": {"experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/docs/[[...slug]]", "dataRoute": "/docs/app/essentials/markdown.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/docs/app/essentials/routing": {"experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/docs/[[...slug]]", "dataRoute": "/docs/app/essentials/routing.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/docs/app/features/ai-search": {"experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/docs/[[...slug]]", "dataRoute": "/docs/app/features/ai-search.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/docs/app/features/async-mode": {"experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/docs/[[...slug]]", "dataRoute": "/docs/app/features/async-mode.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/docs/app/features/llms": {"experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/docs/[[...slug]]", "dataRoute": "/docs/app/features/llms.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/docs/app/features/openapi": {"experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/docs/[[...slug]]", "dataRoute": "/docs/app/features/openapi.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/docs/app/guides/adding-a-root-folder": {"experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/docs/[[...slug]]", "dataRoute": "/docs/app/guides/adding-a-root-folder.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/docs/app/quickstart": {"experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/docs/[[...slug]]", "dataRoute": "/docs/app/quickstart.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/docs/changelog": {"experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/docs/[[...slug]]", "dataRoute": "/docs/changelog.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/search.json": {"initialHeaders": {"content-type": "application/json", "x-next-cache-tags": "_N_T_/layout,_N_T_/search.json/layout,_N_T_/search.json/route,_N_T_/search.json/"}, "experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/search.json", "dataRoute": null, "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}}, "dynamicRoutes": {"/llms.mdx/[...slug]": {"experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "routeRegex": "^/llms\\.mdx/(.+?)(?:/)?$", "dataRoute": null, "fallback": null, "dataRouteRegex": null, "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/og/[...slug]": {"experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "routeRegex": "^/og/(.+?)(?:/)?$", "dataRoute": null, "fallback": null, "dataRouteRegex": null, "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}, "/docs/[[...slug]]": {"experimentalBypassFor": [{"type": "header", "key": "next-action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "routeRegex": "^/docs(?:/(.+?))?(?:/)?$", "dataRoute": "/docs/[[...slug]].rsc", "fallback": null, "dataRouteRegex": "^/docs(?:/(.+?))?\\.rsc$", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}}, "notFoundRoutes": [], "preview": {"previewModeId": "4bcb6f2aaf1691980c5923ff6b67ee25", "previewModeSigningKey": "9ffc130b98c1ef3c10d29bd779290450d851b7d93bae86bf88b4a35fc87f0ebd", "previewModeEncryptionKey": "9eecd90e60f23399f0d285e738e27a5096df646dcf31e6367d3e542ee2a15d65"}}